import { request, replaceUrl } from "sn-base-utils";

export default class ProjBizConstructionImageMApi {
    static config = {
        add: {
            url: '/project/constructionImage/add',
            method: 'POST'
        },
        remove: {
            url: '/project/constructionImage/delete',
            method: 'DELETE'
        },
        update: {
            url: '/project/constructionImage/update',
            method: 'PUT'
        },
        view: {
            url: '/project/constructionImage/get/{id}',
            method: 'GET'
        },
        pageList: {
            url: '/project/constructionImage/page',
            method: "POST"
        },
        list: {
            url: '/project/constructionImage/list',
            method: "POST"
        },
        upload: {
            url: '/project/document/storage/add',
            method: "POST"
        },
        getNumGroupByImageCategory: {
            url: '/project/constructionImage/getNumGroupByImageCategory',
            method: "POST"
        },
        imageDetailsPage: {
            url: '/project/constructionImage/imageDetailsPage',
            method: "POST"
        }
    };

    /**
     * 新增施工影像记录
     * @param data
     * @returns {*}
     */
    static add(data) {
        return request({
            url: this.config.add.url,
            method: this.config.add.method,
            data: data
        });
    }

    /**
     * 删除施工影像记录
     * @param data
     * @returns {*}
     */
    static remove(data) {
        return request({
            url: this.config.remove.url,
            method: this.config.remove.method,
            data: data
        });
    }

    /**
     * 更新施工影像记录
     * @param data
     * @returns {*}
     */
    static update(data) {
        return request({
            url: this.config.update.url,
            method: this.config.update.method,
            data: data
        });
    }

    /**
     * 查询施工影像记录详细
     * @param id
     * @returns {*}
     */
    static view(id) {
        return request({
            url: replaceUrl(this.config.view.url, { id }),
            method: this.config.view.method
        });
    }

    /**
     * 分页查询施工影像记录列表
     * @param data
     * @returns {*}
     */
    static pageList(data) {
        return request({
            url: this.config.pageList.url,
            method: this.config.pageList.method,
            data: data
        });
    }

    /**
     * 全部施工影像记录列表
     * @returns {*}
     */
    static list(data) {
        return request({
            url: this.config.list.url,
            method: this.config.list.method,
            data: data
        });
    }

    /**
     * 文件上传
     * @returns {*}
     */
    static upload(data) {
        return request({
            url: this.config.upload.url,
            method: this.config.upload.method,
            data: data
        });
    }

    /**
     * 大屏获取分类数量
     * @returns {*}
     */
    static getNumGroupByImageCategory() {
        return request({
            url: this.config.getNumGroupByImageCategory.url,
            method: this.config.getNumGroupByImageCategory.method,
        });
    }

    /**
     * 大屏施工图像详情列表
     * @returns {*}
     */
    static imageDetailsPage(data) {
        return request({
            url: this.config.imageDetailsPage.url,
            method: this.config.imageDetailsPage.method,
            data: data
        });
    }
}
