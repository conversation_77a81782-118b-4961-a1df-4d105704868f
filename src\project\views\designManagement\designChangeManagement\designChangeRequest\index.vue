<template>
  <div>
    <sn-crud :data="listData" :option="option" v-model:page="queryForm.page" v-model:search="queryForm.filter" @on-load="getPageList" @row-excel="onExportData" @search-change="onChangeSearch" @search-reset="onResetSearch" @addBtnHandle="onEditData" @row-del="onDelData">
      <template #docCode="scope">
        <el-link type="primary" @click="onEditData(scope.row,'view')">{{ scope.row.docCode }}</el-link>
      </template>
      <template #menu="{ row, index, size }">
          <!--草稿-->
          <el-button type="primary" :size="size" v-if="row.approvalStatus === '1'" icon="el-icon-edit" link @click="onEditData(row,'edit')">编辑</el-button>
          <el-button type="primary" :size="size" v-if="row.approvalStatus === '1'" icon="el-icon-download" link @click="onDownloadData(row)">下载</el-button>
          <el-button type="danger" :size="size" v-if="row.approvalStatus === '1' && row.procInstanceId" icon="el-icon-edit" link @click="onCancelData(row,'stop')">作废</el-button>
          <el-button type="danger" :size="size" v-if="row.approvalStatus === '1' && !row.procInstanceId" icon="el-icon-delete" link @click="onDelData([row])">删除</el-button>
          <!--已提交-->
          <el-button type="danger" :size="size" v-if="row.approvalStatus === '2'" icon="el-icon-edit" link @click="onCancelData(row,'revoke')">撤回</el-button>
          <el-button type="primary" :size="size" v-if="row.approvalStatus === '2'" icon="el-icon-download" link @click="onDownloadData(row)">下载</el-button>
          <!--驳回上一节点-->
          <el-button type="primary" :size="size" v-if="row.approvalStatus === '3'" icon="el-icon-download" link @click="onDownloadData(row)">下载</el-button>
          <!--审批中-->
          <el-button type="primary" :size="size" v-if="row.approvalStatus === '4'" icon="el-icon-download" link @click="onDownloadData(row)">下载</el-button>
          <!--已作废-->
          <el-button type="primary" :size="size" v-if="row.approvalStatus === '5'" icon="el-icon-download" link @click="onDownloadData(row)">下载</el-button>
          <!--已审批-->
          <el-button type="primary" :size="size" v-if="row.approvalStatus === '6'" icon="el-icon-download" link @click="onDownloadData(row)">下载</el-button>
          <!--已驳回提交人-->
          <el-button type="primary" :size="size" v-if="row.approvalStatus === '7'" icon="el-icon-edit" link @click="onEditData(row,'edit')">编辑</el-button>
          <el-button type="primary" :size="size" v-if="row.approvalStatus === '7'" icon="el-icon-download" link @click="onDownloadData(row)">下载</el-button>
          <el-button type="danger" :size="size" v-if="row.approvalStatus === '7' && row.procInstanceId" icon="el-icon-edit" link @click="onCancelData(row,'stop')">作废</el-button>
      </template>
    </sn-crud>
    <div ref="myRef"></div>
  </div>

</template>

<script>
import ProjBizDesignChangeRequestMApi from '@/project/api/designManagement/designChangeRequest/ProjBizDesignChangeRequestM.js'
  import EditProjBizDesignChangeRequestM from "./components/EditProjBizDesignChangeRequestM.vue";
  export const routerConfig = [{
    menuType: "C",
    menuName: "变更验收单",
  }, {
    menuType: "F",
    menuName: "查看",
    perms: "show",
    api: [ProjBizDesignChangeRequestMApi.config.pageList],
  }, {
    menuType: "F",
    menuName: "新增",
    perms: "add",
    api: [ProjBizDesignChangeRequestMApi.config.add],
  }, {
    menuType: "F",
    menuName: "修改",
    perms: "update",
    api: [ProjBizDesignChangeRequestMApi.config.update, ProjBizDesignChangeRequestMApi.config.view],
  }, {
    menuType: "F",
    menuName: "删除",
    perms: "del",
    api: [ProjBizDesignChangeRequestMApi.config.remove],
  }];
</script>

<script setup>
import {
  ref,
  getCurrentInstance
} from 'vue';
import { dayjs } from 'element-plus';
const {
  proxy
} = getCurrentInstance()
let option = ref({
  tip: false,
  dialogType: "page",
  border: true,
  index: true,
  stripe: true,
  menu: true,
  menuWidth: 200,
  header: true,
  height: "auto",
  searchSpan: 6,
  searchIcon: true,
  searchMenuSpan: 6,
  searchIndex: 3,
  selection: true,
  showTree: false,
  excelBtn: true,
  excelBtnText: "列表导出",
  delBtn: false,
  delBtns: false, //批量删除开关
  editBtn: false,
  delBtnsText: "批量删除",
  addBtnText: "新增",
  customEditBtnText: "编辑",
  customDelBtnText: "删除",
  customViewBtnText: "查看",
  searchLabelWidth: 150, //搜索标题宽度
  column: [
  {
    label: "变更申请单编号",
    prop: "docCode",
    width: 180,
    align: "center", //表头对齐
  },
  {
    label: "变更类型",
    width: 100,
    headerAlign: "center", //表头对齐
    prop: "changeType",
    search: true,
    type: 'select',
    dicData: [
      { label: '一般变更', value: '一般变更' },
      { label: '重大变更', value: '重大变更' },
      { label: '较大变更', value: '较大变更' }
    ]
  },
  {
    label: "变更原因",
    headerAlign: "center", //表头对齐
    overHidden: true,
    prop: "changeReason"
  },
  {
    label: "变更内容",
    headerAlign: "center", //表头对齐
    overHidden: true,
    prop: "changeContent",
    search: true
  },
  {
    label: "预估变更金额(万元)",
    width: 150,
    align: "center", //对齐方式：居中
    prop: "estimatedChangeAmount"
  },
  {
    label: "编制单位",
    width: 200,
    align: "center", //表头对齐
    prop: "reportOrganization"
  },
  {
    label: "录入人",
    width: 130,
    prop: "createName"
  },
    {
      label: "录入时间",
      prop: "createTime",
      width: 180,
      align: "center",
      formatter:(row, column, value) => {
        if (!value) return '';
        return dayjs(value).format('YYYY-MM-DD');
      }
    },
    {
      label: "审批状态",
      prop: "approvalStatus",
      search: true,
      width: 100,
      align: "center", //对齐方式：居中
      type: 'select',
      dicUrl: "/system/dict/data/type/global_biz_flow_status",
      dicMethod: "get",
      props: {
        label: "dictLabel",
        value: "dictValue"
      },
      html: true,
      formatter: (val, value) => {
        if(value === '1'){
          return `<span style="color:#4871C0">草稿</span>`;
        } else if(value === '2'){
          return `<span style="color:#4871C0">已提交</span>`;
        } else if(value === '4'){
          return `<span style="color:#4871C0">审批中</span>`;
        } else if(value === '3'){
          return `<span style="color:red">上级驳回</span>`;
        } else if(value === '5'){
          return `<span style="color:red">已作废</span>`;
        } else if(value === '6'){
          return `<span style="color:dodgerblue">已审批</span>`;
        } else if(value === '7'){
          return `<span style="color:red">已驳回</span>`;
        }
        return `<p></p>`;
      }
    }
  ]
});
let listData = ref([]);
let queryForm = ref({
  page: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  filter: {
    createTime: null,
    changeType: null,
  }
});
let formRules = ref({});
const myRef = ref(null);
function getPageList() {
  //查询分页列表
  // 查询前处理参数
  const params = handleQueryForm();
  ProjBizDesignChangeRequestMApi.pageList(params).then((res) => {
    listData.value = res.data.dataList;
    queryForm.value.page.total = res.data.totalCount;
  });
}

function onResetSearch() {
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
}

function onExportData() {
  const params = handleQueryForm();
  let queryForm = JSON.parse(JSON.stringify(params));
  proxy.download("project/designChangeRequest/export", queryForm, '变更申请单列表导出.xlsx');
}

function handleQueryForm() {
  // 处理参数
  const {
    pageSize,
    pageNum
  } = queryForm.value.page;
  const filter = {}
  for (let key in queryForm.value.filter) {
    if (queryForm.value.filter[key]?.length) {
      // sn-curd搜索select会生成$prop的变量
      if (key.startsWith("$")) continue;
      filter[key] = queryForm.value.filter[key];
    }
  }
  delete filter.createTime;
  if (Array.isArray(queryForm.value.filter.createTime) && queryForm.value.filter.createTime?.length === 2) {
    filter["beginCreateTime"] = queryForm.value.filter.createTime[0];
    filter["endCreateTime"] = queryForm.value.filter.createTime[1];
  }
  const searchParams = {
    page: {
      pageSize,
      pageNum,
    },
    filter,
  }
  return searchParams
}

function onChangeSearch(params, done) {
  //搜索按钮操作
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
  done && done();
}
async function onEditData(row,type) {
  //编辑,新增按钮操作
  let editType =type?type: (row?.procInstanceId ? 'view' : (row?.id ? 'edit' : 'add'));
  let rowInfo = await (editType !== "add" ? ProjBizDesignChangeRequestMApi.view(row.id) : {});
  const formData = editType !== "add" ? rowInfo.data : rowInfo;
  delete formData.beginCreateTime;
  // delete formData.createTime;
  delete formData.endCreateTime;
  proxy.$DialogForm.show({
    title: editType == "edit" ? "编辑" : (editType == "view" ? "查看" : "新增"),
    type: option.value.dialogType,
    el: myRef.value,
    width: "80%",
    content: EditProjBizDesignChangeRequestM,
    data: editType !== "add" ? {
      formData: rowInfo.data,
      isShowCloseBtn: false,
      approvalOption: ref({
        isShowApprovalList: true,
        isShowFlowDiagram: true,
        procInstId: rowInfo.data.procInstanceId
      }),
      type: editType,
      el: myRef.value,
      closeDialog: closeDialog,
    } : {
      approvalOption: ref({
        isShowApprovalList: false,
        isShowFlowDiagram: false,
        procInstId: null
      }),
      type: editType,
      el: myRef.value,
      closeDialog: closeDialog,
    },
    option: {
      submitBtn: false,
      emptyBtn: false,
      submitText: '保存',
      emptyText: '关闭',
      extendButton: generateButtons(editType, row)
    },
    callback: (res) => {
      let vm = res.dialogRefs
      if (vm) {
        let formData = vm.getFormData();
        switch (res.type) {
          //直接取消
          // 取消
          case 'cancelDirect':
            getPageList();
            res.close();
            break;
          // 取消
          case 'cancel':
            proxy.$modal.confirm("确认关闭？", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }).then(() => {
              getPageList();
              res.close();
            }).catch(() => {});
            break;
          // 保存
          case 'save':
            if(!formData.approvalStatus){
              formData.approvalStatus = '1'
            }
            vm.getSaveFormData(formData).then(() => {
              proxy.$message.success("保存成功");
              getPageList();
              // res.close();
            })
            break;
          // 编辑
          case 'edit':
            res.close();
            onEditData(row)
            break;
          // 发起流程
          case 'afterProcessIsInitiated':
            let startProcDto = {
              businessKey: null,
              clientId: null
            };
            if(formData.procInstanceId){
              let taskInfo = vm.getTaskInfo();
              vm.handlerOpenConfirm(taskInfo,res);
              getPageList();
            }else {
              vm.getStartFlow(formData, startProcDto).then(() => {
                let taskInfo = vm.getTaskInfo();
                vm.handlerOpenConfirm(taskInfo,res);
                getPageList();
              })
            }
            break;
          //提交流程
          case 'submitFlowTaskProcess':
            vm.submitFlowTask()
            break;
        }
      }
    },
  });
}
function closeDialog(e) {
  getPageList();
  e.close()
}
function generateButtons(editType, row) {
  if (editType !== "view") {
    const hasProcId = row?.procInstanceId;
    const buttons = [
      { key: 'cancel', text: '关闭', buttonType: '', icon: 'el-icon-close' }
    ];

    if (hasProcId) {
      buttons.push({
        key: 'save',
        text: '保存',
        buttonType: 'primary',
        icon: 'el-icon-check'
      }, {
        key: 'submitFlowTaskProcess',
        text: '提交',
        buttonType: 'primary',
        icon: 'sn-button-fasong'
      });
    } else {
      buttons.push({
        key: 'save',
        text: '保存',
        buttonType: 'primary',
        icon: 'el-icon-check'
      }, {
        key: 'afterProcessIsInitiated',
        text: '发起流程',
        buttonType: 'primary',
        icon: 'sn-button-fasong'
      });
    }
    return buttons;
  }
  return row.approvalStatus != '1' ? [
    { key: 'cancelDirect', text: '关闭', buttonType: '', icon: 'el-icon-close' }
  ] : [
    { key: 'edit', text: '编辑', buttonType: 'primary', icon: 'el-icon-edit' },
    { key: 'cancelDirect', text: '关闭', buttonType: '', icon: 'el-icon-close' }
  ];
}

function onDownloadData(rows) {
    let params = {
      relevantId: rows.id,
      type: 'designChangeRequest',
    };
    proxy.download("/project/document/storage/exportZipByRelevantId", params, "变更申请单_"+rows.docCode+"_附件.zip");
}

function onDelData(rows) {
  //删除行
  if (!rows || rows.length == 0) {
    proxy.$message.info("请勾选数据！");
    return false;
  }
  let ids = rows.map((item) => {
    return item.id;
  });
  proxy.$modal.confirm("确认删除数据项？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    ProjBizDesignChangeRequestMApi.remove(ids).then((res) => {
      proxy.$message.success("已删除");
      getPageList();
    });
  }).catch(() => {});
}

/*流程作废*/
function onCancelData(row,type) {
  //删除行
  let formDate = {
    id: row.id,
    operateFlowStatus: type,
  }
  let typeName;
  if(type == 'revoke'){
    typeName = '撤回';
  }else{
    typeName = '作废';
  }
  proxy.$modal.confirm("确认"+typeName+"流程？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    ProjBizDesignChangeRequestMApi.operateFlow(formDate).then((res) => {
      proxy.$message.success("已"+typeName);
      getPageList();
    });
  }).catch(() => {});
}

</script>

<style lang="scss" scoped>

</style>
