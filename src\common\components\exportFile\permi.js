
  let queryClasUpd = (el) => {
    el?.classList.add('is-disabled')
    el.querySelector('div')?.classList?.add('is-disabled')
    el.querySelector('span')?.classList?.add('is-disabled')
    el?.querySelectorAll('input')[0]?.setAttribute('disabled', 'disabled')
    let elSufEl = el?.querySelectorAll('.el-input__suffix-inner')[0]
    if (elSufEl) {
      elSufEl.style.pointerEvents = 'none'
    }
  }
  const findFormItem = (data, state, childVnode) => {
    if (data) {
      // 如果data是数组的话, 则为slot内的组件, 不是slot的组件则为对象形式
      if (Array.isArray(data)) {
        data.forEach((item) => {
          let el = item.el
          if (state) {
            el.style.pointerEvents = 'none'
            el.setAttribute('class', el.className + ' controlReadonly')
            el.style.cursor = 'not-allowed'
            queryClasUpd(el)
            el.querySelectorAll('.ep-radio__input').forEach((e) => {
              queryClasUpd(e)
            })
          }
        })
      } else {
        if (
          data.children &&
          data.type.name != 'ElInput' &&
          data.type.name != 'ElSelect' &&
          data.type.name != 'ElRadio' &&
          data.type.name != 'ElCheckbox' &&
          data.type.name != 'ElCheckboxGroup' &&
          data.type.name != 'ElInputNumber' &&
          data.type.name != 'ElCascader' &&
          data.type.name != 'ElSwitch' &&
          data.type.name != 'ElSlider' &&
          data.type.name != 'ElTimeSelect' &&
          data.type.name != 'ElDatePicker' &&
          data.type.name != 'ElDatePicker'
        ) {
          for (let i = 0; i < data.children.length; i++) {
            findFormItem(data.children[i], state, data.children[i])
          }
        } else {
          data.props.disabled = state
          data.type.props.disabled = state
           queryClasUpd(data.el)
          if (childVnode) {
              let el = childVnode.el
              el.classList.add('is-disabled')
              el.querySelector('input').disabled = 'disabled'
              el.querySelector('div')?.classList?.add('is-disabled')
              el.querySelector('span')?.classList?.add('is-disabled')
              let elSufEl = el?.querySelectorAll('.el-input__suffix-inner')[0]
              if (elSufEl) {
                elSufEl.style.pointerEvents = 'none'
              }
          }
        }
      }
    }
  }
  //1: 可编辑 2:disabled 3:隐藏
export default {
    mounted(el, binding, vnode) {
      // console.log('bind!!', el, binding, vnode)
      if (binding.value) {
        if (binding.value == 3) {
          const comment = document.createComment(' ')
          // 设置value值
          Object.defineProperty(comment, 'setAttribute', {
            value: () => undefined
          })
          // 用注释节点替换 当前页面元素
          vnode.elm = comment
          // 下面作为初始化操作 赋值为空
          vnode.text = ' '
          vnode.isComment = true
          vnode.context = undefined
          vnode.tag = undefined
          // vnode.data.directives = undefined
          // 判断当前元素是否是组件  如果是组件的话也替换成 注释元素
          if (vnode.componentInstance) {
            vnode.componentInstance.$el = comment
          }
          // 判断当前元素是否是文档节点或者是文档碎片节点
          if (el.parentNode) {
            // 从 DOM 树中删除 node 节点，除非它已经被删除了。
            el.parentNode.replaceChild(comment, el)
          }
        } else if (binding.value == 2) {
          findFormItem(
            vnode ? vnode : vnode.children,
            true,
            vnode
          )
        } else if (binding.value == 1) {
          findFormItem(
            vnode ? vnode : vnode.children,
            false,
            vnode
          )
        }
      }
    },
    /* 2隐藏 1 只读 0 读写*/
    updated(el, binding, vnode) {
      // console.log('update!!',el, binding, vnode)
      if (binding.value == 3) {
        const comment = document.createComment(' ')
        // 设置value值
        Object.defineProperty(comment, 'setAttribute', {
          value: () => undefined
        })
        // 用注释节点替换 当前页面元素
        vnode.el = comment
        // 下面作为初始化操作 赋值为空
        vnode.text = ' '
        vnode.isComment = true
        vnode.context = undefined
        vnode.tag = undefined
        // vnode.data.directives = undefined
        // 判断当前元素是否是组件  如果是组件的话也替换成 注释元素
        if (vnode.componentInstance) {
          vnode.componentInstance.$el = comment
        }
        // 判断当前元素是否是文档节点或者是文档碎片节点
        if (el.parentNode) {
          // 从 DOM 树中删除 node 节点，除非它已经被删除了。
          el.parentNode.replaceChild(comment, el)
        }
      } else if (binding.value == 2) {
        findFormItem(vnode, true)
      } else if (binding.value == 1) {
        findFormItem(vnode, false)
      }
    }
}
