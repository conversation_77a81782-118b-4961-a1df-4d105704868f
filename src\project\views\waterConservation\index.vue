<template>
  <sn-crud :data="listData" :option="option" v-model:page="queryForm.page" v-model:search="queryForm.filter"
    @on-load="getPageList" @search-change="onChangeSearch" @search-reset="onResetSearch" @addBtnHandle="onEditData"
    @row-del="onDelData">
    <template #menu="{ row, index, size }">
      <template v-if="row.state === '0' || !row.state">
        <el-button type="primary" :size="size" icon="el-icon-edit" link @click="onEditData(row)">编辑</el-button>
        <el-button type="danger" :size="size" icon="el-icon-delete" link @click="onDelData([row])">删除</el-button>
      </template>
      <!-- 已发布：撤回 + 下载 -->
      <template v-else>
        <el-button type="warning" :size="size" icon="el-icon-refresh-left" link @click="onRevoke(row)">撤回</el-button>
        <el-button type="primary" :size="size" icon="el-icon-download" link @click="onDownData(row)">下载</el-button>
      </template>
    </template>
    <template #state="{ row }">
      <span :style="{ color: row.state && row.state === '1' ? 'green' : 'red' }">
        {{ row.state && row.state === '1' ? '已发布' : '草稿' }}
      </span>
    </template>
    <template #code="{ row }">
      <el-button type="text" @click="onDetailClick(row)">{{ row.code }}</el-button>
    </template>
    <template #header>
      <el-button type="primary" icon="el-icon-plus" style="margin-left: 10px;" @click="onEditData(row)">新增</el-button>
      <!--        <el-button type="primary" icon="el-icon-document" @click="onListTemplate">列表模板</el-button>-->
      <!--        <el-button type="primary" icon="el-icon-upload" @click="onListImport">列表导入</el-button>-->
      <el-button type="primary" icon="el-icon-download" @click="onExportData">列表导出</el-button>
    </template>
  </sn-crud>
  <div ref="myRef"></div>
</template>

<script>
import ProjBizWaterConservationApi from '@/project/api/waterConservation/ProjBizWaterConservation.js'
import EditProjBizWaterConservation from "./components/EditProjBizWaterConservation.vue";
import { getToken } from "sn-base-utils";
import { dayjs } from 'element-plus';
export const routerConfig = [{
  menuType: "C",
  menuName: "环保水保管理",
}, {
  menuType: "F",
  menuName: "查看",
  perms: "show",
  api: [ProjBizWaterConservationApi.config.pageList],
}, {
  menuType: "F",
  menuName: "新增",
  perms: "add",
  api: [ProjBizWaterConservationApi.config.add],
}, {
  menuType: "F",
  menuName: "修改",
  perms: "update",
  api: [ProjBizWaterConservationApi.config.update, ProjBizWaterConservationApi.config.view],
}, {
  menuType: "F",
  menuName: "删除",
  perms: "del",
  api: [ProjBizWaterConservationApi.config.remove],
}];
</script>

<script setup>
import {
  ref,
  getCurrentInstance
} from 'vue';
const myRef = ref(null);
const {
  proxy
} = getCurrentInstance()
let option = ref({
  tip: false,
  dialogType: "page",
  border: true,
  index: true,
  stripe: true,
  menu: true,
  header: false,
  height: "auto",
  searchSpan: 6,
  searchIcon: true,
  searchMenuSpan: 6,
  searchIndex: 3,
  selection: false,
  showTree: false,
  excelBtn: false,
  delBtn: false,
  editBtn: false,
  delBtnsText: "批量删除",
  addBtnText: "新增",
  customEditBtnText: "编辑",
  customDelBtnText: "删除",
  customViewBtnText: "查看",
  column: [{
    label: "业务编号",
    prop: "code",
    search: true,
    queryType: "LIKE"
  },
  {
    label: "演练名称",
    prop: "drillName",
    search: true,
    queryType: "LIKE"
  }, {
    label: "演练日期",
    prop: "drillDate",
    search: false,
    // queryType: "BETWEEN",
    searchType: "date",
    formatter: (row, column, value) => {
      // 使用dayjs进行时间格式化，确保value存在
      if (!value) return '';
      // 默认格式为YYYY-MM-DD HH:mm:ss，您可以根据需要修改
      return dayjs(value).format('YYYY-MM-DD');
    }
  }, {
    label: "文件名称",
    prop: "fileName",
    search: true,
    queryType: "LIKE"
  }, {
    label: "创建人",
    prop: "createName"
  }, {
    label: "创建时间",
    prop: "createTime",
    formatter: (row, column, value) => {
      // 使用dayjs进行时间格式化，确保value存在
      if (!value) return '';
      // 默认格式为YYYY-MM-DD HH:mm:ss，您可以根据需要修改
      return dayjs(value).format('YYYY-MM-DD');
    }
  }, {
    label: "发布状态",
    prop: "state",
    search: false,
    columnSlot: false,
    searchSlot: false
  }]
});
let listData = ref([]);
let queryForm = ref({
  page: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  filter: {
    createTime: null
  }
});
let formRules = ref({});


function onExportData() {
  const params = handleQueryForm();
  let queryForm = JSON.parse(JSON.stringify(params));
  proxy.download("/project/waterConservation/export", queryForm.filter, '数据导出.xlsx');
}
function getPageList() {
  //查询分页列表
  // 查询前处理参数
  const params = handleQueryForm();
  ProjBizWaterConservationApi.pageList(params).then((res) => {
    listData.value = res.data.dataList;
    queryForm.value.page.total = res.data.totalCount;
  });
}
async function onDetailClick(row) {
  //编辑,新增按钮操作
  let editType = "view";
  console.log('editType:', editType)
  let rowInfo = await (editType !== "add" ? ProjBizWaterConservationApi.view(row.id) : {});
  const formData = editType !== "add" ? rowInfo.data : rowInfo;

  let extendButtons = [
    {
      key: 'close',
      text: '关闭',
      icon: 'el-icon-close',
      buttonType: '',
    },
  ];
  if (row.state === '0') {
     extendButtons = [
      {
      key: 'edit',
      text: '编辑',
      icon: 'el-icon-edit',
      buttonType: 'primary',
      click: () => {
        onEditData(row);
      }
    },
      {
      key: 'close',
      text: '关闭',
      icon: 'el-icon-close',
      buttonType: '',
    },
    ]
  }
  //编辑,新增按钮操作
  proxy.$DialogForm.show({
    title: editType == "view" ? "查看" : "编辑",
    type: option.value.dialogType,
    el: myRef.value,
    width: "80%",
    content: EditProjBizWaterConservation,
    data: {
      formData: formData,
      type: editType,
    },
    option: {
      submitBtn: false,
      emptyBtn: false,
      emptyText: '取消',
      extendButton: extendButtons
    },
    callback: (res) => {
      if (res.type === 'edit') {
        // 当文档状态为草稿时，显示编辑按钮
        onEditData(row);
      } else if (res.type && res.type !== 'close') {
        if (res.dialogRefs) {
          console.log('res.type==', res.type)
          res.dialogRefs.submitData(res.type).then((flag) => {
            if (flag) {
              getPageList();
              res.close();
            }
          });
        }
      } else {
        res.close();
      }
    }
  });
}
function onResetSearch() {
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
}

function handleQueryForm() {
  // 处理参数
  const {
    pageSize,
    pageNum
  } = queryForm.value.page;
  const filter = {}
  for (let key in queryForm.value.filter) {
    if (queryForm.value.filter[key]?.length) {
      // sn-curd搜索select会生成$prop的变量
      if (key.startsWith("$")) continue;
      filter[key] = queryForm.value.filter[key];
    }
  }
  delete filter.createTime;
  if (Array.isArray(queryForm.value.filter.createTime) && queryForm.value.filter.createTime?.length === 2) {
    filter["beginCreateTime"] = queryForm.value.filter.createTime[0];
    filter["endCreateTime"] = queryForm.value.filter.createTime[1];
  }
      filter.projectId = sessionStorage.getItem('projectId')
  const searchParams = {
    page: {
      pageSize,
      pageNum,
    },
    filter,
  }
  return searchParams
}
function onRevoke(row) {
  proxy.$modal.confirm("确认撤回该条数据项？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    //编辑操作
    // const formData = getFormData();
    // getPageList();
    row.state = '0'
    ProjBizWaterConservationApi.update(row).then((res) => {
      proxy.$message.success("已撤回");
      getPageList();
    });
  }).catch(() => { });
}
function onChangeSearch(params, done) {
  //搜索按钮操作
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
  done && done();
}


/**
 * 下载文件
 * @param row
 */
// function onDownData(row) {
//   proxy.download("/project/waterConservation/exportZip", [row.id], 'file_' + row.id + '.zip');
// }

function onDownData(row) {
  let params = {
    relevantId: row.id,
    type: 'waterConservation'
  }
  let timestamp = proxy.parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')
  proxy.download('/project/document/storage/exportZipByRelevantId', params, '环保水保管理文件-' + row.code + '-' + timestamp + '.zip')
}

async function onEditData(row) {
  //编辑,新增按钮操作
  let editType = row ? "edit" : "add";
  let rowInfo = await (editType !== "add" ? ProjBizWaterConservationApi.view(row.id) : {});
  const formData = editType !== "add" ? rowInfo.data : rowInfo;
  //编辑,新增按钮操作
  proxy.$DialogForm.show({
    title: editType == "edit" ? "编辑" : "新增",
    type: option.value.dialogType,
    el: myRef.value,
    // width: "80%",
    content: EditProjBizWaterConservation,
    data: {
      formData: formData,
      type: editType,
    },
    option: {
      submitBtn: false,
      emptyBtn: false,
      emptyText: '取消',
      extendButton: [
        {
          key: 'save',
          text: '保存',
          icon: 'el-icon-plus',
          buttonType: 'primary',
        },
        {
          key: 'submit',
          text: '发布',
          icon: 'el-icon-check',
          buttonType: 'primary',
        },
        {
          key: 'close',
          text: '关闭',
          icon: 'el-icon-close',
          buttonType: '',
        },
      ],
    },
    callback: (res) => {
      if (res.type && res.type !== 'close') {
        if (res.dialogRefs) {
          res.dialogRefs.submitData(res.type).then((flag) => {
            if (flag) {
              getPageList();
              if (res.type === 'submit') {
                res.close();
              }

            }
          });
        }
      } else {
        if (res.type === 'close') {
          // 当点击关闭按钮且处于编辑模式时，弹出确认对话框
          proxy.$confirm('确认关闭？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            res.close();
          }).catch(() => {
            // 用户点击取消，不关闭弹窗
          });
        }
      }
    }
  });
}

function onDelData(rows) {
  //删除行
  if (!rows || rows.length == 0) {
    proxy.$message.info("请勾选数据！");
    return false;
  }
  let ids = rows.map((item) => {
    return item.id;
  });
  proxy.$modal.confirm("确认删除数据项？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    ProjBizWaterConservationApi.remove(ids).then((res) => {
      proxy.$message.success("已删除");
      getPageList();
    });
  }).catch(() => { });
}
</script>

<style lang="scss" scoped></style>
