import { request, replaceUrl } from "sn-base-utils";

export default class ProjBizIrPrMApi {
    static config = {
        view: {
            url: '/project/irFlow/get/{id}',
            method: 'GET'
        },
        list: {
            url: '/project/irFlow/list',
            method: "POST"
        },
        startFlow: {
            url: '/project/irFlow/saveAndSubmitProc',
            method: "POST"
        },
        submitTask: {
            url: '/project/irFlow/saveAndSubmitTask',
            method: "POST"
        },
        printTemplate: {
            url: `/project/irFlow/printTemplate`,
            method: "POST"
        },
        revoke: {
            url: '/project/irFlow/revoke/{id}',
            method: 'GET'
        },
        cancellation: {
            url: '/project/irFlow/cancellation/{id}',
            method: 'GET'
        },
        operateFlow: {
            url: `/project/irFlow/operateFlow`,
            method: "POST"
        }
    };

    /**
     * 查询检查整改流程详细
     * @param id
     * @returns {*}
     */
    static view(id) {
        return request({
            url: replaceUrl(this.config.view.url, { id }),
            method: this.config.view.method
        });
    }


    /**
     * 全部检查整改流程列表
     * @returns {*}
     */
    static list(data) {
        return request({
            url: this.config.list.url,
            method: this.config.list.method,
            data: data
        });
    }

    /**
     * 工作流-启动流程
     * @returns {*}
     */
    static startFlow(data) {
        return request({
            url: this.config.startFlow.url,
            method: this.config.startFlow.method,
            data: data
        });
    }

    /**
     * 工作流-完成任务
     * @returns {*}
     */
    static submitTask(data) {
        return request({
            url: this.config.submitTask.url,
            method: this.config.submitTask.method,
            data: data
        });
    }

    /**
     * 工作流-打印模板
     */
    static printTemplate(data) {
        return request({
            url: this.config.printTemplate.url,
            method: this.config.printTemplate.method,
            data: data
        });
    }

    /**
     * 更新变更申请单
     * @param data
     * @returns {*}
     */
    static operateFlow(data) {
        return request({
            url: this.config.operateFlow.url,
            method: this.config.operateFlow.method,
            data: data
        });
    }
}
