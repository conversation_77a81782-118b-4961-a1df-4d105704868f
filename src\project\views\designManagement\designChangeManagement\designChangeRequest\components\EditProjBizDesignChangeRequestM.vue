<template>
  <flow-page-container
    ref="flowPageContainerRef"
    @handlerAction="handlerAction"
    @initData="initData"
    @handlerPrint="handlerPrint"
    :closeBtn="isShowCloseBtn"
    :approvalOption="approvalOption"
    @approvalOptionCallback="getApprovalOption"
  >
    <el-form
      :model="formData"
      :rules="formRules"
      ref="formRef"
      label-width="150px"
      label-position="right"
      :disabled="type == 'view'"
    >
      <el-row :gutter="16" :span="24">
        <el-card class="box-card" style="width: 100%">
          <fieldset class="fieldset2">
            <legend><span class="el-button--primary"></span>基本信息</legend>
            <el-row :gutter="16" :span="24">
              <el-col
                :span="6"
                v-permi="
                  formPermi(
                    {
                      fieldModelId: 'projBizDesignChangeRequestM',
                      field: 'docCode',
                      fieldName: '变更申请单编号'
                    },
                    customFormPermi
                  )
                "
              >
                <el-form-item label="变更申请单编号" prop="docCode">
                  <el-input v-model="formData.docCode" type="text" placeholder="请输入" clearable>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col
                :span="6"
                v-permi="
                  formPermi(
                    {
                      fieldModelId: 'projBizDesignChangeRequestM',
                      field: 'estimatedChangeAmount',
                      fieldName: '预估变更金额(万元)'
                    },
                    customFormPermi
                  )
                "
              >
                <el-form-item label="预估变更金额(万元)" prop="estimatedChangeAmount">
                  <el-input
                    v-model="formData.estimatedChangeAmount"
                    style="width: 100%"
                    @change="changeType"
                  />
                </el-form-item>
              </el-col>
              <el-col
                :span="6"
                v-permi="
                  formPermi(
                    {
                      fieldModelId: 'projBizDesignChangeRequestM',
                      field: 'changeType',
                      fieldName: '变更类型'
                    },
                    customFormPermi
                  )
                "
              >
                <el-form-item label="变更类型" prop="changeType">
                  <el-input v-model="formData.changeType" type="text" :disabled="true" clearable>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col
                :span="6"
                v-permi="
                  formPermi(
                    {
                      fieldModelId: 'projBizDesignChangeRequestM',
                      field: 'reportOrganization',
                      fieldName: '编制单位'
                    },
                    customFormPermi
                  )
                "
              >
                <el-form-item label="编制单位" prop="reportOrganization">
                  <el-input
                    v-model="formData.reportOrganization"
                    type="text"
                    :disabled="true"
                    clearable
                  >
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col
                :span="24"
                v-permi="
                  formPermi(
                    {
                      fieldModelId: 'projBizDesignChangeRequestM',
                      field: 'changeReason',
                      fieldName: '变更原因'
                    },
                    customFormPermi
                  )
                "
              >
                <el-form-item label="变更原因" prop="changeReason">
                  <el-input
                    type="textarea"
                    v-model="formData.changeReason"
                    placeholder="请输入"
                    rows="3"
                    clearable
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col
                :span="24"
                v-permi="
                  formPermi(
                    {
                      fieldModelId: 'projBizDesignChangeRequestM',
                      field: 'changeContent',
                      fieldName: '变更内容'
                    },
                    customFormPermi
                  )
                "
              >
                <el-form-item label="变更内容" prop="changeContent">
                  <el-input
                    type="textarea"
                    v-model="formData.changeContent"
                    placeholder="请输入"
                    rows="3"
                    clearable
                  ></el-input>
                </el-form-item>
              </el-col>

              <el-col
                :span="24"
                v-permi="
                  formPermi(
                    {
                      fieldModelId: 'projBizDesignChangeRequestM',
                      field: 'remarks',
                      fieldName: '备注'
                    },
                    customFormPermi
                  )
                "
              >
                <el-form-item label="备注" prop="remarks">
                  <el-input
                    v-model="formData.remarks"
                    type="textarea"
                    placeholder="请输入"
                    rows="3"
                    clearable
                  >
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </fieldset>
        </el-card>
        <el-card class="box-card" style="width: 100%">
          <fieldset class="fieldset2">
            <legend><span class="el-button--primary"></span>附件信息</legend>
            <project-document-storage-ui-table
              ref="childRef"
              :type="fileType"
              :relevantId="relevantId"
              :isPageSearch="false"
              :isDeleteMinio="isDeleteMinio"
              :isHasAi="isHasAi"
              :file-serial-number-builder="fileSerialNumberBuilder"
              :preview-config="previewConfig"
              :isShowAddBtn="isShowAddBtn"
              :isShowDelBtn="isShowDelBtn"
              :isShowLinkBtn="false"
            ></project-document-storage-ui-table>
          </fieldset>
        </el-card>
        <el-card class="box-card" style="width: 100%">
          <fieldset class="fieldset2">
            <legend><span class="el-button--primary"></span>单据信息</legend>
            <el-row :gutter="16" :span="24">
              <el-col
                :span="8"
                v-permi="
                  formPermi(
                    {
                      fieldModelId: 'projBizDesignChangeRequestM',
                      field: 'createName',
                      fieldName: '创建人'
                    },
                    customFormPermi
                  )
                "
              >
                <el-form-item label="创建人" prop="createName">
                  <el-input v-model="formData.createName" type="text" :disabled="true" clearable>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col
                :span="8"
                v-permi="
                  formPermi(
                    {
                      fieldModelId: 'projBizDesignChangeRequestM',
                      field: 'createTime',
                      fieldName: '创建时间'
                    },
                    customFormPermi
                  )
                "
              >
                <el-form-item label="创建时间" prop="createTime">
                  <el-input v-model="formData.createTime" type="text" :disabled="true" clearable>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col
                :span="8"
                v-permi="
                  formPermi(
                    {
                      fieldModelId: 'projBizDesignChangeRequestM',
                      field: 'approvalStatus',
                      fieldName: '单据状态'
                    },
                    customFormPermi
                  )
                "
              >
                <el-form-item label="单据状态" prop="approvalStatus">
                  <el-select v-model="formData.approvalStatus" clearable placeholder="" :disabled="true" filterable remote>
                    <el-option v-for="(item, index) in global_biz_flow_status" :key="index" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col
                :span="8"
                v-permi="
                  formPermi(
                    {
                      fieldModelId: 'projBizDesignChangeRequestM',
                      field: 'updateName',
                      fieldName: '最近修改人'
                    },
                    customFormPermi
                  )
                "
              >
                <el-form-item label="最近修改人" prop="updateName">
                  <el-input v-model="formData.updateName" type="text" :disabled="true" clearable />
                </el-form-item>
              </el-col>
              <el-col
                :span="8"
                v-permi="
                  formPermi(
                    {
                      fieldModelId: 'projBizDesignChangeRequestM',
                      field: 'updateTime',
                      fieldName: '最近修改时间'
                    },
                    customFormPermi
                  )
                "
              >
                <el-form-item label="最近修改时间" prop="updateTime">
                  <el-input v-model="formData.updateTime" type="text" :disabled="true" clearable>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </fieldset>
        </el-card>
      </el-row>
    </el-form>
  </flow-page-container>
</template>

<script setup>
import ProjBizDesignChangeRequestMApi from '@/project/api/designManagement/designChangeRequest/ProjBizDesignChangeRequestM.js'
import FlowPageContainer from '@/common/components/exportFile/FlowPageContainer.vue'
import { btnHandle } from '@/project/components/hooks/buttonChangeName'
import {
  defineExpose,
  defineProps,
  getCurrentInstance,
  nextTick,
  onMounted,
  reactive,
  ref,
  toRef
} from 'vue'
import { useRoute, useRouter } from 'vue-router'
import store from '@/store'
import { getCurrentFormattedTime } from '@/common/utils/datetime'
import { kkFileViewUrl } from '@/config'
import BpmTaskApi from '@/project/api/bpm/bpmTask'
import { useDicts } from '@/common/hooks/useDicts'

let auth = new Map()
const props = defineProps({
  data: Object
})
const flowPageContainerRef = ref();
// 文件组件定义参数
const fileType = ref("designChangeRequest"); // 业务类型标识
const relevantId = ref(props.data?.formData?.id ?? null) // 关联业务ID，新增业务的时候传null（⽰例值）
const isDeleteMinio = ref(true); // 删除Minio⽂件开关
const isShowAddBtn = ref(false); //上传
const isShowDelBtn = ref(false); //下载
const isHasAi = ref(false); // AI审查功能开关
const childRef = ref(null); // 组件实例引⽤
const res=ref()
const previewConfig = ref({
  // 是否外置预览,必传
  isExternalPreview: false,
  // 内置预览返回的替换字符,akey代表要替换的字符串，avalue代码替换的字符串
  substitute: {
    akey: 'avalue'
  },
  // 内置预览服务地址
  previewServerUrl: kkFileViewUrl
})
const { proxy } = getCurrentInstance()
const route = useRoute()
const router = useRouter()
const formRef = ref()
const taskInfo = ref()
const isShowCloseBtn = ref(false)
const FlowActionType = ref(proxy.FlowActionType)
let approvalOption = props.data?.approvalOption
  ? props.data?.approvalOption
  : route.query?.procInstId
    ? ref({
        isShowApprovalList: true,
        isShowFlowDiagram: true,
        procInstId: route.query?.procInstId
      })
    : ref({
        isShowApprovalList: false,
        isShowFlowDiagram: false,
        procInstId: ''
      })
const state = reactive({
  flowData: {
    businessKey: '',
    procInstId: '',
    procDefKey: '',
    taskId: '',
    formConfig: [],
    variableList: [],
    customPropertyConfigList: []
  },
  formAuth: {},
  taskFlag: 1,
  firstAct: false,
  handlerClose: null,
  rootFields: {},
  fullFieldNameMap: {},
  formAuthList: {}
})
const type = toRef(props.data?.type)
const {global_biz_flow_status} = useDicts(["global_biz_flow_status"])
let formData = ref({
  docCode: '',
  changeReason: '',
  changeContent: '',
  estimatedChangeAmount: '',
  changeType: '',
  remarks: '',
  createName:
    store.state.user && store.state.user.userInfo.userName
      ? store.state.user.userInfo.userName
      : '-',
  createTime: getCurrentFormattedTime(),
  reportOrganization: JSON.parse(store.state.user.orgName)?.find(
    (item) => item.id === store.state.user.defaultOrg
  )?.orgName,
  status: '',
  updateName: '',
  updateTime: ''
})
let formRules = ref({
  docCode: [
    {
      required: true,
      message: '请填写变更申请单编号'
    },
    {
      pattern: /^(?=.*[a-zA-Z])(?=.*\d)[A-Za-z0-9]+$/,
      trigger: ['blur', 'change'],
      message: '变更申请单编号必须包含字母和数字'
    }
  ],
  changeReason: [
    {
      required: true,
      message: '请填写变更原因'
    }
  ],
  changeContent: [
    {
      required: true,
      message: '请填写变更内容'
    }
  ],
  estimatedChangeAmount: [
    {
      required: true,
      message: '请填写预估变更金额'
    },
    {
      validator: (rule, value, callback) => {
        if (value) {
          if (!/^[0-9]+([.]{1}[0-9]{1,2})?$/.test(value) || parseFloat(value) === 0) {
            callback(new Error('请输入整数或最多两位小数'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      },
      trigger: ['blur', 'change'],
    }
  ],
  changeType: [],
  remarks: [
    {
      required: true,
      message: '请填写备注'
    }
  ],
  createName: [],
  createTime: [],
  status: [],
  updateName: [],
  updateTime: []
})
formData = toRef(
  Object.keys(props.data?.formData || {}).length > 0
    ? {
        ...props.data?.formData
      }
    : toRef(formData.value)
)

function getFormData() {
  return formData.value
}

function getTaskInfo() {
  return taskInfo.value
}

function processAuthFieldForm(fieldList) {
  /**
   * 工作流表单权限功能：在扫描完成之前，业务可对表单字段进行修改。
   */
  //TODO 这里可以对权限表单的字段进行任意的修改,如果不满足业务需求，可以在这里手动添加你想要的字段
  return fieldList
}

function getApprovalOption(fun) {
  /**
   * 工作流表单权限功能(重要)：应对弹窗无法及时相应的问题，特别增加该函数修复该bug
   */
  nextTick(() => {
    fun(approvalOption.value)
  })
}

function changeType(value) {
  if (value < 10) {
    formData.value.changeType = '一般变更'
  } else if (value >= 300) {
    formData.value.changeType = '重大变更'
  } else {
    formData.value.changeType = '较大变更'
  }
}

function customFormPermi(obj) {
  /**
   * 工作流表单权限功能：自定义表单权限处理函数，将对应的对象的属性还原成对应的权限值
   */
  let { field, fieldModelId } = obj
  let fullFieldName = `${fieldModelId}_${field}`
  //TODO 这里可以自定义处理，默认返回0即可
  return auth.get(fullFieldName) || 0
}

function handlerPrint(taskComment) {
  /**
   * 工作流打印模板功能
   */
  ProjBizDesignChangeRequestMApi.printTemplate({
    id: formData.value.id,
    templateId: taskComment.templateId,
    formData: formData.value
  }).then((res) => {
    router.push({
      name: 'PrintDoc',
      query: {
        fileId: res.data
      }
    })
    taskComment.close && taskComment.close()
  })
}

function getSubmitTask(taskActionDto) {
  /**
   * 工作流提交任务功能
   */
  return ProjBizDesignChangeRequestMApi.submitTask({
    busiDto: formData.value,
    taskActionDto: taskActionDto
  })
}

function getStartFlow(formData, startProcDto) {
  return new Promise((resolve) => {
    formRef.value.validate((flag) => {
      if (!flag) {
        return false
      } else {
        formData.fileList = getListData()
        formData.fileList.map((item) => {
          item.type = fileType.value
        })
        /* 工作流启动流程功能*/
        resolve(
          ProjBizDesignChangeRequestMApi.startFlow({
            busiData: formData,
            startProcDto: startProcDto
          }).then((respon) => {
            state.flowData.procDefKey = respon.data[0].procDefKey
            state.flowData.procInstId = respon.data[0].procInstId
            state.flowData.businessKey = respon.data[0].businessKey
            state.flowData.taskId = respon.data[0].taskId
            taskInfo.value = respon.data[0];
            nextTick(() => {
              btnHandle(props.data.el.lastChild)
            })
          })
        )
      }
    })
  })
}
//打开确认框
function handlerOpenConfirm(taskInfo,resp) {
  ProjBizDesignChangeRequestMApi.view(taskInfo.businessKey).then((resp) => {
    if (resp.data) {
      formData.value = resp.data;
      relevantId.value = resp.data.id;
      isShowAddBtn.value = true;
      isShowDelBtn.value = true;
    }
  })
  res.value=resp;
  flowPageContainerRef.value.handlerActionSubmit(taskInfo,1);
}

function submitFlowTask(resp) {
  res.value=resp;
  return proxy.$confirm('确定提交当前单据?', '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
    const formDatas = getFormData();
    return BpmTaskApi.listRuTaskByProcInstId({
      procInstId: formDatas.procInstanceId
    }).then((params) => {
      flowPageContainerRef.value.handlerActionSubmit(params.data[0],1);
    })
  }).catch(() => {
      return true;
    })
}


function handlerAction(operation, taskComment, handlerClose) {
  /**
   * 工作流操作功能
   */
  // 启动并保存草稿后再次提交
  if (
    (operation.type == FlowActionType.value.SAVESTART ||
      operation.type == FlowActionType.value.SAVE ||
      operation.type == FlowActionType.value.START) &&
    !formData.value.taskId
  ) {
    let startProcDto = {
      procDefKey: formData.value.procDefKey,
      businessKey: null,
      clientId: null,
      ...taskComment?.dialogRefs?.getFormData()
    }
    let httpCall = null
    if (operation.type == FlowActionType.value.SAVESTART) {
      httpCall = getStartFlow(formData, startProcDto)
    } else if (operation.type == FlowActionType.value.SAVE) {
      httpCall = getSaveFormData(formData)
    }
    httpCall.then(() => {
      proxy.$modal.msgSuccess('提交成功')
      taskComment.close && taskComment.close()
      handlerClose()
    })
  } else {
    operation.type == FlowActionType.value.START || operation.type == FlowActionType.value.SAVESTART
      ? (operation.type = FlowActionType.value.AGREE)
      : operation.type
    let taskActionDto = {
      taskId: state.flowData.taskId,
      procInstId: state.flowData.procInstId,
      actionType: operation.type,
      ...taskComment?.dialogRefs?.getFormData(),
      taskAssignees:taskComment?.dialogRefs?.getFormData()?.taskAssignees ? taskComment.dialogRefs.getFormData().taskAssignees.join(',') : ''
    }
      getSubmitTask(taskActionDto)
      .then(() => {
        proxy.$modal.msgSuccess('任务办理成功')
        taskComment.close && taskComment.close()
        let businessKey = route.query.businessKey
        if(res){
          if (businessKey) {
            handlerClose()
          } else {
            props.data.closeDialog(proxy)
          }
        }else {
          handlerClose()
        }
      })
      .catch(() => {
        taskComment.close && taskComment.close()
      })
  }
}

function initBusiForm() {
  /**
   * 工作流初始化表单功能
   */
  let that = proxy
  let routerQueryParams = route.query
  auth = new Map(Object.entries(state.formAuth))
  if (routerQueryParams) {
    if (routerQueryParams.busiData) {
      formData.value = routerQueryParams.busiData
    } else {
      if (routerQueryParams.businessKey) {
        let businessKey = route.query.businessKey
        ProjBizDesignChangeRequestMApi.view(businessKey).then((resp) => {
            if (resp.data) {
                formData.value = resp.data;
                relevantId.value = resp.data.id;
            }
        })
      } else {
        that.$message.error('初始化失败,因为工作流未将流程信息传入！')
      }
    }
  }
}

function getSaveFormData(formData) {
  return new Promise((resolve) => {
    formRef.value.validate((flag) => {
      if (!flag) {
        return false
      } else {
        if (formData.id) {
          formData.createTime = null
          formData.updateTime = null
          resolve(ProjBizDesignChangeRequestMApi.update(formData))
        } else {
          formData.fileList = getListData()
          formData.fileList.map((item) => {
            item.type = fileType.value
          })
          resolve(ProjBizDesignChangeRequestMApi.add(formData).then((resp) => {
            if(resp.data) {
              formData.id = resp.data.id;
            }
          }))
        }
      }
    })
  })
}

function getFieldForm() {
  let rootFields = {
    ...state.rootFields
  }
  let fieldList = []
  let keys = []
  for (let key in rootFields) {
    keys.push(key)
    fieldList.push(rootFields[key])
  }
  const result = processAuthFieldForm ? processAuthFieldForm(fieldList) : fieldList
  const message = {
    type: 'getTemplateRoot',
    pathname: window.location.pathname,
    actKey: getQueryParams('actKey'),
    content: result
  }
  window.parent.postMessage(JSON.parse(JSON.stringify(message)), '*')

  if(type.value != 'view' && (formData.value.approvalStatus == '1' || !formData.value.approvalStatus )){
    isShowAddBtn.value = true;
    isShowDelBtn.value = true;
  }
}

function getQueryParams(key) {
  let url = window.location.href
  // 使用正则表达式解析URL中的查询字符串
  var queryString = url.split('?')[1]
  if (!queryString) {
    return {}
  }
  var params = {}
  // 分割查询字符串成单个参数
  var vars = queryString.split('&')
  for (var i = 0; i < vars.length; i++) {
    var pair = vars[i].split('=')
    params[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1])
  }
  return params[key]
}

function initData(urlParams, taskInfo, handlerClose) {
  // 如果用户从流程设计点击启动, 如果是在线表单, 则阻止执行initData, 防止在线表单报错
  if (urlParams.menuId && urlParams.openInStart) {
    return
  }
  state.flowData.procDefKey = taskInfo.procDefKey
  state.flowData.procInstId = urlParams.procInstId
  state.flowData.businessKey = urlParams.businessKey
  state.flowData.taskId = urlParams.taskId
  state.flowData.fiedPermission = taskInfo.fiedPermission
  state.flowData.variableList = taskInfo.variableList
  state.taskFlag = urlParams.taskFlag
  state.firstAct = taskInfo.firstAct
  state.handlerClose = handlerClose
  state.flowData.customPropertyConfigList = taskInfo.customPropertyConfigList
  let fieldPerList = taskInfo.formConfig?.formFieldConfig?.fieldPerList || []
  handleFormAuth(fieldPerList)
  initBusiForm()
}

function handleFormAuth(data) {
  let formAuth = {}
  for (let item of data) {
    let permi = 1
    if (item.readonly) {
      permi = 2
    }
    if (item.hidden) {
      permi = 3
    }
    if (!isCamelCase(item.fieldModelId)) {
      item.fieldModelId = toCamelCase(item.fieldModelId)
    }
    formAuth = {
      ...formAuth,
      [`${item.fieldModelId}_${item.field}`]: permi
    }
  }
  state.formAuth = formAuth
  state.formAuthList = JSON.parse(JSON.stringify(formAuth))
}

/**
 * 处理表单权限相关操作, 开发者在template的v-permi设置中必须使用此函数
 * @param {Object} obj - 包含字段名称的对象
 * @param {Function} [callback] - 可选的回调函数，接收 obj 作为参数
 * 如果传入对象的 fieldName 不在 rootFields 中，则将其添加到 rootFields 数组，然后执行回调函数（如果存在）
 */
function formPermi(obj, callback) {
  if (!state.rootFields[obj.fieldModelId + '__' + obj.field]) {
    state.rootFields[obj.fieldModelId + '__' + obj.field] = obj
  }
  return callback && callback(obj)
}

function toCamelCase(s) {
  return s.toLowerCase().replace(/_(.)/g, function (match, group1) {
    return group1.toUpperCase()
  })
}

function isCamelCase(str) {
  return /^[a-z][a-zA-Z0-9]*$/.test(str)
}

function getListData() {
  if (childRef.value) {
    return childRef.value.getListData()
  }
}

// 文件序列号生成函数
function fileSerialNumberBuilder() {
  return 'TEST' + Math.floor(Math.random() * 10000)
}

defineExpose({
  getFormData,
  getSaveFormData,
  getStartFlow,
  submitFlowTask,
  handlerOpenConfirm,
  getTaskInfo
})
onMounted(() => {
  nextTick(() => {
    getFieldForm()
  })
})
</script>

<style lang="scss" scoped>
.bgW {
  height: 100%;
}
</style>
