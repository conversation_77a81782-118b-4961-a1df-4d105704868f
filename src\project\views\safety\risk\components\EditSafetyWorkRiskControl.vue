<template>
  <el-form :model="formData" :rules="formRules" ref="formRef" label-width="150px" label-position="right" :disabled="type == 'view'">
    <el-row :gutter="16" :span="24">
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>
          </legend>
          <el-row :gutter="16" :span="24">
            <el-col :span='8'>
              <el-form-item label="业务编码" prop="code">
                <el-input v-model="formData.code" :disabled="type.value && type.value != 'view'" type="text" placeholder="请输入业务编码" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="风险等级" prop="riskGrade">
                <el-select v-model="formData.riskGrade" :disabled="type.value && type.value != 'view'" clearable placeholder="请选择风险等级">
                  <el-option v-for="(item, index) in risk_level" :key="index" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="风险分类" prop="riskClass">
                <el-select v-model="formData.riskClass" :disabled="type.value && type.value != 'view'" clearable placeholder="请选择风险分类">
                  <el-option v-for="(item, index) in risk_class" :key="index" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="风险管控单位" prop="riskControlUnit">
                <el-select :disabled="type == 'view'" v-model="formData.riskControlUnit" placeholder="请选择" clearable filterable >
                  <el-option v-for="(item, index) in orgData" :key="index" :label="item.name" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16" :span="24">
            <el-col :span='8'>
              <el-form-item label="风险发现时间" prop="riskDiscoveryTime">
                <el-date-picker type="date" v-model="formData.riskDiscoveryTime" :disabled="type.value && type.value != 'view'" style="width: 100%;" format="YYYY-MM-DD" value-format="YYYY-MM-DD" clearable></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="预计风险解除时间" prop="riskPlanClearTime">
                <el-date-picker type="date" v-model="formData.riskPlanClearTime" :disabled="type.value && type.value != 'view'" style="width: 100%;" format="YYYY-MM-DD" value-format="YYYY-MM-DD" clearable></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="实际解除时间" prop="riskClearTime">
                <el-date-picker type="date" v-model="formData.riskClearTime" :disabled="type.value && type.value != 'view'" style="width: 100%;" format="YYYY-MM-DD" value-format="YYYY-MM-DD" clearable></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16" :span="24">
            <el-col :span='24'>
              <el-form-item label="风险基本情况描述" prop="riskBasicInfoDesc">
                <el-input type="textarea" v-model="formData.riskBasicInfoDesc" :disabled="type.value && type.value != 'view'" placeholder="请输入风险基本情况描述" rows="3" clearable></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16" :span="24">
            <el-col :span='24'>
              <el-form-item label="管控措施" prop="riskControlMeasures">
                <el-input type="textarea" v-model="formData.riskControlMeasures" :disabled="type.value && type.value != 'view'" placeholder="请输入管控措施" rows="3" clearable></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16" :span="24">
            <el-col :span='24'>
              <el-form-item label="风险解决措施" prop="riskCountermeasure">
                <el-input type="textarea" v-model="formData.riskCountermeasure" :disabled="type.value && type.value != 'view'" placeholder="请输入风险解决措施" rows="3" clearable></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </fieldset>
      </el-card>
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>单据信息
          </legend>
          <el-row :gutter="16" :span="24">
            <el-col :span='8'>
              <el-form-item label="创建人" prop="createName">
                <el-input v-model="formData.createName" :disabled="true" type="text" placeholder="" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="创建时间" prop="createTime">
                <el-date-picker type="dateTime" v-model="formData.createTime" disabled style="width: 100%;" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" clearable></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="单据状态" prop="status">
                <el-select v-model="formData.status" :disabled="true" clearable placeholder="请选择单据状态">
                  <el-option v-for="(item, index) in statusOption" :key="index" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="最近修改人" prop="updateName">
                <el-input v-model="formData.updateName" :disabled="true" type="text" placeholder="" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="最近修改时间" prop="updateTime">
                <el-date-picker type="dateTime" v-model="formData.updateTime" disabled style="width: 100%;" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" clearable></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
        </fieldset>
      </el-card>
    </el-row>
  </el-form>
</template>

<script setup>
import SafetyWorkRiskControlApi from '@/project/api/safety/SafetyWorkRiskControl.js'
import {
  nextTick,
  onMounted,
  ref,
  reactive,
  toRef,
  defineProps,
  defineExpose,
  getCurrentInstance
} from 'vue';
import {
  useRoute,
  useRouter
} from 'vue-router'
const {
  proxy
} = getCurrentInstance()
const route = useRoute();
const router = useRouter();
const props = defineProps({
  data: Object,
});
const formRef = ref()
const type = toRef(props.data?.type);
import {
  useDicts
} from "@/common/hooks/useDicts";
import store from "@/store";
import {getCurrentFormattedTime} from "@/common/utils/datetime";
import ProjBizProjectTeamOrgApi from "@/project/api/projectTeam/ProjBizProjectTeamOrg";
const {
  risk_class,
  risk_level
} = useDicts(["risk_class", "risk_level"])
let formData = ref({
  projectId: sessionStorage.getItem('projectId'),
  projectName: "",
  projectDept: "",
  riskGrade: "",
  riskClass: "",
  riskControlUnit: "",
  code: "",
  riskDiscoveryTime: "",
  riskPlanClearTime: "",
  riskClearTime: "",
  riskBasicInfoDesc: "",
  riskControlMeasures: "",
  riskCountermeasure: "",
  createName: store.state.user && store.state.user.userInfo.userName ? store.state.user.userInfo.userName : null,
  createTime: getCurrentFormattedTime(),
  status: "0",
  updateName: "",
  updateTime: ""
});
let formRules = ref({
  riskGrade: [{
    required: true,
    message: "请选择风险等级"
  }],
  riskClass: [{
    required: true,
    message: "请选择风险分类"
  }],
  riskControlUnit: [{
    required: true,
    message: "风险管控单位不能为空"
  }],
  code: [{
    required: true,
    message: "业务编码不能为空"
  }],
  riskDiscoveryTime: [{
    required: true,
    message: "风险发现时间不能为空"
  }],
  riskPlanClearTime: [{
    required: true,
    message: "预计风险解除时间不能为空"
  }],
  riskClearTime: [],
  riskBasicInfoDesc: [{
    required: true,
    message: "风险基本情况描述不能为空"
  }],
  riskControlMeasures: [{
    required: true,
    message: "管控措施不能为空"
  }],
  riskCountermeasure: [],
  createName: [],
  createTime: [],
  status: [],
  updateName: [],
  updateTime: []
});
let statusOption = ref([{
  label: "草稿",
  value: "0"
}, {
  label: "已发布",
  value: "1"
}]);
formData = toRef(Object.keys(props.data?.formData || {}).length > 0 ? {
  ...props.data?.formData
} : toRef(formData.value));

let orgData = ref([]);

ProjBizProjectTeamOrgApi.queryOrgCompanyList({projectId : sessionStorage.getItem('projectId')}).then((res) => {
  orgData.value = res.data;
})

function getFormData() {
  return formData.value
};

const buttonType = ref()
function submitData(btnType) {
  return new Promise((resolve) => {
    formRef.value.validate((flag) => {
      if (!flag) {
        return false;
      } else {
        buttonType.value = btnType
        if (type.value === "add") {
          resolve(saveData());
        } else {
          resolve(editData());
        }
      }
    });
  });
}

function saveData() {
  //新增操作
  const formData1 = getFormData();
  if (buttonType.value == 'submit') {
    formData1.status = '1'
  }
  return SafetyWorkRiskControlApi.add(formData1).then((res) => {
    if(res.data){
      proxy.$message.success("保存成功");
      formData.value.id = res.data.id;
      type.value = "edit"
      return true;
    }
  });
}

function editData() {
  //编辑操作
  const formData = getFormData();
  if (buttonType.value == 'submit') {
    formData.status = '1'
  }
  return SafetyWorkRiskControlApi.update(formData).then(() => {
    proxy.$message.success("修改成功");
    return true;
  });
}
defineExpose({
  getFormData,
  submitData,
});
</script>

<style lang="scss" scoped>

</style>
