<template>
  <el-form :model="formData" :rules="formRules" ref="formRef" label-width="100px" label-position="right"
    :disabled="type == 'view'">
    <el-row :gutter="16" :span="24">
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>主表信息
          </legend>
          <el-row :gutter="16" :span="24">
            <!-- <el-col :span='8'>
              <el-form-item label="项目编号" prop="projectCode">
                <el-input v-model="formData.projectCode" type="text" placeholder="请输入" :disabled="true" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="项目名称" prop="projectName">
                <el-input v-model="formData.projectName" type="text" placeholder="请输入" :disabled="true" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="项目单位" prop="projectUnit">
                <el-input v-model="formData.projectUnit" type="text" placeholder="请输入" :disabled="true" clearable>
                </el-input>
              </el-form-item>
            </el-col> -->
            <el-col :span='8'>
              <el-form-item label="业务编码" prop="code">
                <el-input v-model="formData.code" type="text" placeholder="请输入" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="文件名称" prop="fileName">
                <el-input v-model="formData.fileName" type="text" placeholder="请输入" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="演练名称" prop="drillName">
                <el-input v-model="formData.drillName" type="text" placeholder="请输入" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="演练日期" prop="drillDate">


                <el-date-picker type="date" style="width: 100%;"  v-model="formData.drillDate" format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD HH:mm:ss" clearable></el-date-picker>

              </el-form-item>
            </el-col>
          </el-row>
        </fieldset>
      </el-card>
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>附件
          </legend>
          <el-row :gutter="16" :span="24"></el-row>
          <project-document-storage-ui-table ref="childRef" :type="fileType" :relevantId="formData.id"
            :isPageSearch="false" :isDeleteMinio="false" :isHasAi="false" @on-add-data="onAddData"
            :file-serial-number-builder="fileSerialNumberBuilder" :preview-config="previewConfig" :isShowAddBtn="true"
            :isShowDelBtn="true" :isShowPreviewBtn="true" :isShowDownloadBtn="true" :isShowLinkBtn="false"
            :isShowDownloadBatchBtn="true"></project-document-storage-ui-table>
        </fieldset>
        <!-- <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>子表信息
          </legend>
          <el-row :gutter="16" :span="24">
            <sn-crud :data="formData.projBizDmStgMDtoList" :option="projBizDmStgMOption" @row-save="subRowSave" @row-update="subRowUpdate" @row-del="(row, index) => {subDelRow(row, index, 'projBizDmStgMDtoList');}">
              <template #empty>
                <div>无数据</div>
              </template>
</sn-crud>
<el-button @click="subAddRow('projBizDmStgMDtoList')" type="primary" plain
  style="display: block; width: 100%; margin-top: 10px">新增一行数据</el-button>
</el-row>
</fieldset> -->
      </el-card>
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>单据信息
          </legend>
          <el-row :gutter="16" :span="24">
            <el-col :span='8'>
              <el-form-item label="创建人" prop="createName">
                <el-input v-model="formData.createName" type="text" placeholder="请输入"  disabled  clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="创建时间" prop="createTime">
                <!-- <el-time-picker v-model="formData.createTime" format="YYYY-MM-DD" disabled value-format="YYYY-MM-DD"
                  placeholder="选择时间" clearable></el-time-picker> -->
                                <el-input v-model="formData.createTime" type="text" placeholder="" :disabled="true" clearable>
                </el-input>  
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="单据状态" prop="docStatus">

                <el-select v-model="formData.state" disabled placeholder="" clearable>
                <el-option v-for="(item,index) in statusOption"  :key="index" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="最近修改人" prop="updateName">
                <el-input v-model="formData.updateName" disabled type="text" placeholder="" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="最近修改时间" prop="updateTime">
                <!-- <el-time-picker v-model="formData.updateTime" disabled format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                  placeholder="选择时间" clearable></el-time-picker> -->
                         <el-input v-model="formData.updateTime" type="text" placeholder="" :disabled="true" clearable>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </fieldset>
      </el-card>
    </el-row>
  </el-form>
</template>

<script setup>
import ProjBizWaterConservationApi from '@/project/api/waterConservation/ProjBizWaterConservation.js'
import ProjInfoMApi from '@/project/api/projectInfo/ProjInfoM.js'
import {getCurrentFormattedTime} from "@/common/utils/datetime";
import store from "@/store";
import {
  nextTick,
  onMounted,
  ref,
  reactive,
  toRef,
  defineProps,
  defineExpose,
  getCurrentInstance
} from 'vue';
import {
  useRoute,
  useRouter
} from 'vue-router'
const {
  proxy
} = getCurrentInstance()
const route = useRoute();
const router = useRouter();
const props = defineProps({
  data: Object,
});
const fileType = ref('waterConservation');
const childRef = ref(null);
const formRef = ref()
const type = toRef(props.data?.type);
let formData = ref({
  projectCode: "",
  projectName: "",
  projectUnit: "",
  code: "",
  state: "0",
  fileName: "",
  drillName: "",
  drillDate: "",
  projBizDmStgMDtoList: [],
  createName: "",
  createTime: "",
  updateName: "",
  updateTime: ""
});
let buttonType = ref();
let statusOption = ref([{
  label: "草稿",
  value: "0"
}, {
  label: "已发布",
  value: "1"
}]);
let formRules = ref({
  projectCode: [],
  projectName: [],
  projectUnit: [],
 
  drillDate: [],


  code: [
    { required: true, message: '业务编号不能为空', trigger: 'blur' }
  ],
    fileName: [
    { required: true, message: '文件名称不能为空', trigger: 'blur' }
  ],
  drillName: [
    { required: true, message: '演练名称不能为空', trigger: 'blur' }
  ],
  drillDate: [
    { required: true, message: '请选择演练日期', trigger: 'change' }
  ],


  createName: [],
  createTime: [],
  updateName: [],
  updateTime: []
});
let delRowData = ref({});
function fileSerialNumberBuilder() {
  return "inspection" + Math.floor(Math.random() * 10000);
}
function initializer(){
  console.log("执行初始化打印type",type.value)
  if(type.value==='add'){
      console.log("执行初始化打印type",initializer)

    formData.value.createName =  store.state.user && store.state.user.userInfo.userName ? store.state.user.userInfo.userName : null;//获取到登陆人
    formData.value.createTime = getCurrentFormattedTime();//获取到登录时间
    // formData.value.state = 0
  }
  //判断是否为查看进来，是则禁用输入框及按钮
  // console.log('getPageList.type==',type.value)
  // if(type.value==='show'){
  //   dialogdisabled = true
  // }
}
onMounted(() => {
  nextTick(() => {

  })
  initializer()
  getProjectInfo()
})
function getProjectInfo() {
  
  ProjInfoMApi.view(sessionStorage.getItem('projectId')).then((resp) => {
    console.log(resp);
    if (!!resp.data) {
      console.log("打印project参数", resp.data)
      if (resp.data.projCode?.trim()) {
        // 覆盖：null、undefined、""、"   "
        formData.value.projectCode = resp.data.projCode
      } else {
        formData.value.projectCode = '默认项目编号'
      }
      if (resp.data.projName?.trim()) {
        // 覆盖：null、undefined、""、"   "

        formData.value.projectName = resp.data.projName
      } else {
        formData.value.projectName = '默认项目名称'
      }
      if (resp.data.projorg?.trim()) {
        // 覆盖：null、undefined、""、"   "
        formData.value.projectUnit = resp.data.projorg
      } else {
        formData.value.projectUnit = '默认项目单位'
      }
    }
  })
}
const previewConfig = ref({
  // 是否外置预览,必传
  isExternalPreview: false,
  // 内置预览返回的替换字符,akey代表要替换的字符串，avalue代码替换的字符串
  substitute: {
    "akey": "avalue"
  },
  // 内置预览服务地址
  previewServerUrl: "http://*************:8012/onlinePreview",
})
let projBizDmStgMOption = ref({
  tip: false,
  border: true,
  index: true,
  stripe: true,
  menu: true,
  header: false,
  menuType: "text",
  addBtn: true,
  addBtnText: "新增",
  editBtn: true,
  editBtnText: "编辑",
  delBtn: true,
  delBtnText: "删除",
  cellBtn: true,
  maxHeight: "200px",
  column: [{
    label: "文件名称",
    prop: "fileName",
    type: "input",
    cell: true
  }, {
    label: "上传人",
    prop: "createName",
    type: "input",
    cell: true
  }, {
    label: "上传时间",
    prop: "createTime",
    type: "input",
    cell: true
  }, {
    label: "文件大小",
    prop: "fileSize",
    type: "input",
    cell: true
  }]
});

function subRowSave(form, done) {
  //编辑行
  done();
}

function subAddRow(name) {
  //新增一行
  if (formData.value[name]) {
    formData.value[name].push({
      $cellEdit: true,
    });
  } else {
    let arr = new Array();
    arr.push({
      $cellEdit: true
    });
    formData.value[name] = arr
  }
}

function subRowUpdate(form, index, done, loading) {
  //编辑行
  done();
}

function subDelRow(row, index, name) {
  //删除行
  if (row[0].id) {
    let data = JSON.parse(JSON.stringify(row[0]));
    if (delRowData.value[name]) {
      delRowData.value[name].push(Object.assign(data, {
        delFlag: 1,
      }));
    } else {
      delRowData.value[name] = [
        Object.assign(data, {
          delFlag: 1,
        }),
      ]
    }
  }
  formData.value[name].splice(index, 1);
}
function getListData() {
  if (childRef.value) {
    let list = childRef.value.getListData()
    formData.value.projBizDmStgMDtoList = list
    console.log(list, "组件数据。。。。。。。。。")
  }
}
function getFormData() {
  //   getListData();
  // return ;
  //获取formData数据
  for (let item in delRowData.value) {
    formData.value[item] = formData.value[item].concat(delRowData.value[item]);
  }
  getListData();
  formData.value.projectId =    sessionStorage.getItem('projectId')
  console.log("打印formdate", formData.value)

  return formData.value;
}
formData = toRef(Object.keys(props.data?.formData || {}).length > 0 ? {
  ...props.data?.formData
} : toRef(formData.value));

function submitData(buttontype) {
     buttonType.value = buttontype

  return new Promise((resolve) => {
    formRef.value.validate((flag) => {
      if (!flag) {
        return false;
      } else {
    
        if (type.value === "add") {
          resolve(saveData());
        } else {
          resolve(editData());
        }
      }
    });
  });
}

function saveData() {
  //新增操作
  const formData = getFormData();
  if(buttonType.value==='submit'){
    formData.state = '1'
  }else{
    formData.state = '0'
  }

    if(buttonType.value==='submit'){
  return ProjBizWaterConservationApi.add(formData).then(() => {
    proxy.$message.success("新增成功");
    return true;
  });
    }else{
   

    console.log('formData.id==',formData.id)
    if(formData.id===null || formData.id === undefined){
      return ProjBizWaterConservationApi.add(formData).then((resp) => {
        console.log('resp.data==',resp.data)
        if(resp.data){
          proxy.$message.success("保存成功");
          formData.id = resp.data.id;
          return true;
        }
      });
    }else{
      console.log('进入保存的更新')
      return ProjBizWaterConservationApi.update(formData).then(() => {
        proxy.$message.success("保存成功");
        return true;
      });
    }
 }

}

function editData() {
  //编辑操作
  const formData = getFormData();
  if(buttonType.value==='submit'){
    formData.state = '1'
  }else{
    formData.state = '0'
  }
  return ProjBizWaterConservationApi.update(formData).then(() => {
    proxy.$message.success("修改成功");
    return true;
  });
}
defineExpose({
  getFormData,
  submitData,
});
</script>

<style lang="scss" scoped></style>
