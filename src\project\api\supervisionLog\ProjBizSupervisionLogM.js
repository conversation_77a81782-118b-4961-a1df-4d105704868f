import { request, replaceUrl } from "sn-base-utils";

export default class ProjBizSupervisionLogMApi {
    static config = {
        add: {
            url: '/project/supervisionLog/add',
            method: 'POST'
        },
        remove: {
            url: '/project/supervisionLog/delete',
            method: 'DELETE'
        },
        update: {
            url: '/project/supervisionLog/update',
            method: 'PUT'
        },
        view: {
            url: '/project/supervisionLog/get/{id}',
            method: 'GET'
        },
        pageList: {
            url: '/project/supervisionLog/page',
            method: "POST"
        },
        list: {
            url: '/project/supervisionLog/list',
            method: "POST"
        },
        uploadAttachment: {
            url: '/project/document/storage/add',
            method: "POST"
        },
        exportFile: {
            url: '/project/document/storage/exportFile',
            method: "POST"
        },
        getAttachmentUrl: {
            url: '/project/supervisionLog/getAttachmentUrl',
            method: "POST"
        },
        download: {
            url: '/project/document/storage/exportFile',
            method: "POST"
        }

    };

    /**
     * 新增监理日志
     * @param data
     * @returns {*}
     */
    static add(data) {
        return request({
            url: this.config.add.url,
            method: this.config.add.method,
            data: data
        });
    }

    /**
     * 删除监理日志
     * @param data
     * @returns {*}
     */
    static remove(data) {
        return request({
            url: this.config.remove.url,
            method: this.config.remove.method,
            data: data
        });
    }

    /**
     * 更新监理日志
     * @param data
     * @returns {*}
     */
    static update(data) {
        return request({
            url: this.config.update.url,
            method: this.config.update.method,
            data: data
        });
    }

    /**
     * 查询监理日志详细
     * @param id
     * @returns {*}
     */
    static view(id) {
        return request({
            url: replaceUrl(this.config.view.url, { id }),
            method: this.config.view.method
        });
    }

    /**
     * 分页查询监理日志列表
     * @param data
     * @returns {*}
     */
    static pageList(data) {
        return request({
            url: this.config.pageList.url,
            method: this.config.pageList.method,
            data: data
        });
    }

    /**
     * 全部监理日志列表
     * @returns {*}
     */
    static list(data) {
        return request({
            url: this.config.list.url,
            method: this.config.list.method,
            data: data
        });
    }

    /**
     * 文件上传
     * @returns {*}
     */
    static uploadAttachment(data) {
        return request({
            url: this.config.uploadAttachment.url,
            method: this.config.uploadAttachment.method,
            data: data
        });
    }
    /**
     * 文件下载
     * @returns {*}
     */
    static exportFile(data) {
        return request({
            url: this.config.exportFile.url,
            method: this.config.exportFile.method,
            data: data
        });
    }

    /**
     * 获取文件url
     * @returns {*}
     */
    static getAttachmentUrl(data) {
        return request({
            url: this.config.getAttachmentUrl.url,
            method: this.config.getAttachmentUrl.method,
            data: data
        });
    }

    /**
     * 下载文件
     * @returns {*}
     */
    static download(data) {
        return request({
            url: this.config.download.url,
            method: this.config.download.method,
            data: data
        });
    }
}
