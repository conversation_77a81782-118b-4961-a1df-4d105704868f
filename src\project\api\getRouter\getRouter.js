import { request, replaceUrl } from "sn-base-utils";
import {systemCode} from '@/config.js'
// import {default as request} from '@/project/utils/TestRequest'

export default class getRoutersApi {
    static config = {
        getRouters: {
            url: '/system/permission/getRouters',
            method: 'GET'
        },
    };
    /**
     * 获取文档目录
     * @returns {*}
     */
    static getRouters(data) {
        return request({
            url: this.config.getRouters.url,
            method: this.config.getRouters.method,
            params: data,
            requestPrefix: systemCode
        });
    }

}
