import { request, replaceUrl } from 'sn-base-utils'

export default class ProjBizConsStartWorkCommandApi {
  static config = {
    add: {
      url: '/project/sceneStartWorkCommand/add',
      method: 'POST'
    },
    remove: {
      url: '/project/sceneStartWorkCommand/delete',
      method: 'DELETE'
    },
    update: {
      url: '/project/sceneStartWorkCommand/update',
      method: 'PUT'
    },
    view: {
      url: '/project/sceneStartWorkCommand/get/{id}',
      method: 'GET'
    },
    pageList: {
      url: '/project/sceneStartWorkCommand/page',
      method: 'POST'
    },
    list: {
      url: '/project/sceneStartWorkCommand/list',
      method: 'POST'
    },
    fillTemplateAndDownload: {
      url: `/project/sceneStartWorkCommand/fillTemplateAndDownload/{id}`,
      method: 'POST'
    }
  }

  /**
   * 新增项目开工令
   * @param data
   * @returns {*}
   */
  static add(data) {
    return request({
      url: this.config.add.url,
      method: this.config.add.method,
      data: data
    })
  }

  /**
   * 删除项目开工令
   * @param data
   * @returns {*}
   */
  static remove(data) {
    return request({
      url: this.config.remove.url,
      method: this.config.remove.method,
      data: data
    })
  }

  /**
   * 更新项目开工令
   * @param data
   * @returns {*}
   */
  static update(data) {
    return request({
      url: this.config.update.url,
      method: this.config.update.method,
      data: data
    })
  }

  /**
   * 查询项目开工令详细
   * @param id
   * @returns {*}
   */
  static view(id) {
    return request({
      url: replaceUrl(this.config.view.url, { id }),
      method: this.config.view.method
    })
  }

  /**
   * 分页查询项目开工令列表
   * @param data
   * @returns {*}
   */
  static pageList(data) {
    return request({
      url: this.config.pageList.url,
      method: this.config.pageList.method,
      data: data
    })
  }

  /**
   * 全部项目开工令列表
   * @returns {*}
   */
  static list(data) {
    return request({
      url: this.config.list.url,
      method: this.config.list.method,
      data: data
    })
  }

  /**
   * 填充模版并导出所有附件
   * @param id
   * @returns {*}
   */
  static fillTemplateAndDownload(id) {
    return request({
      url: replaceUrl(this.config.fillTemplateAndDownload.url, { id }),
      method: this.config.fillTemplateAndDownload.method
    })
  }
}
