import CryptoJS from 'crypto-js';

export function generateRandomString(length) {
    let result = '';
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const charactersLength = characters.length;
    for (let i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }
    return result;
}

export function generateHash() {
    const randomString = generateRandomString(10) + Date.now(); // 生成一个长度为10的随机字符串
    return CryptoJS.MD5(randomString).toString();
}
