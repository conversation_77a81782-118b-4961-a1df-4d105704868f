<template>
  <sn-crud
    ref="crud"
    :data="listData"
    :option="option"
    v-model:page="queryForm.page"
    v-model:search="queryForm.filter"
    @on-load="getPageList"
    @search-change="onChangeSearch"
    @search-reset="onResetSearch"
    @row-save="rowSave"
    @row-update="rowUpdate"
  >
    <template #journalDoc="scope">
      <!--       <el-button v-if="scope.row.$editBusinessNumber" type="primary" :size="size" icon="el-icon-upload" link @click="openUploadDialog(scope.row)"> 上传 </el-button> -->
      <el-input v-if="scope.row.$editBusinessNumber" v-model="scope.row.journalDoc" disabled="false" size="small" @keyup.enter="saveBusinessNumber(scope.row)" />

      <el-link v-else type="primary" @click="toPreview(scope.row)">
        {{ scope.row.journalDoc }}
      </el-link>
    </template>

    <template #businessNumber="scope">
      <el-input v-if="scope.row.$editBusinessNumber" v-model="scope.row.businessNumber" size="small" @keyup.enter="saveBusinessNumber(scope.row)" />
      <span v-else>{{ scope.row.businessNumber }}</span>
    </template>

    <template #menuLeft="{ row, size }">
      <el-button type="primary" :size="size" icon="el-icon-plus" @click="onAddData(row)">新增</el-button>
      <el-button type="primary" :size="size" icon="el-icon-download" @click="onExportData(row)">列表导出</el-button>
    </template>

    <template #publishStatus="scope">
      <span :class="['status-text', scope.row.publishStatus === '1' ? 'draft' : 'published']">
        {{ scope.row.publishStatus === '1' ? '草稿' : '已发布' }}
      </span>
    </template>

    <template #menu="{ row, size }">
      <!--未发布-->
      <template v-if="row.publishStatus === '1'">
        <template v-if="!row.$editBusinessNumber">
          <el-button type="primary" :size="size" icon="el-icon-edit" link @click="startEditBusinessNumber(row)">编辑 </el-button>
          <el-button type="primary" :size="size" icon="el-icon-plus" link @click="onPutData(row)">发布</el-button>

          <el-button type="danger" :size="size" icon="el-icon-delete" link @click="onDelData([row])">删除</el-button>
        </template>
        <template v-else>
          <el-button v-if="row.$editBusinessNumber" type="primary" :size="size" icon="el-icon-upload" link @click="openUploadDialog(row)"> 上传 </el-button>
          <el-button type="primary" :size="size" icon="CirclePlusFilled" link @click="saveBusinessNumber(row)">保存 </el-button>
          <el-button type="primary" :size="size" icon="CircleClose" link @click="cancelEditBusinessNumber(row)">取消 </el-button>
        </template>
      </template>

      <!--已发布-->
      <template v-if="row.publishStatus === '2'">
        <el-button type="danger" :size="size" icon="el-icon-minus" link @click="onPutData(row)">撤回</el-button>
        <el-button type="primary" :size="size" icon="el-icon-download" link @click="onDownloadData(row)">下载 </el-button>
      </template>
    </template>
  </sn-crud>

  <!-- 文件上传对话框 -->
  <el-dialog v-model="uploadDialogVisible" title="上传文件" width="50%">
    <sn-upload v-if="uploadDialogVisible" :drag="true" @input="uploadSuccess" :multiple="false" :limit="1" autoUpload="true" />
    <el-button @click="uploadDialogVisible = false">关闭</el-button>
  </el-dialog>
</template>

<script>
import ProjBizConsJournalApi from '@/project/api/constructionManagement/buildJournal/ProjBizConsJournal.js'
import { getToken } from 'sn-base-utils'
import ProjBizConsStartWorkReportApi from '@/project/api/constructionManagement/sceneStartWorkReport/ProjBizConsStartWorkReport'
import { kkFileViewUrl } from '@/config'

export const routerConfig = [
  {
    menuType: 'C',
    menuName: '施工日志'
  },
  {
    menuType: 'F',
    menuName: '查看',
    perms: 'buildJournal:show',
    api: [ProjBizConsJournalApi.config.pageList]
  },
  {
    menuType: 'F',
    menuName: '新增',
    perms: 'buildJournal:add',
    api: [ProjBizConsJournalApi.config.add]
  },
  {
    menuType: 'F',
    menuName: '修改',
    perms: 'buildJournal:update',
    api: [ProjBizConsJournalApi.config.update, ProjBizConsJournalApi.config.view]
  },
  {
    menuType: 'F',
    menuName: '删除',
    perms: 'buildJournal:del',
    api: [ProjBizConsJournalApi.config.remove]
  },
  {
    menuType: 'F',
    menuName: '上传',
    perms: 'buildJournal:upload',
    api: [ProjBizConsJournalApi.config.view, ProjBizConsStartWorkReportApi.config.upload]
  },
  {
    menuType: 'F',
    menuName: '下载',
    perms: 'buildJournal:download',
    api: [ProjBizConsJournalApi.config.view, ProjBizConsJournalApi.config.pageList, ProjBizConsStartWorkReportApi.config.download]
  },
  {
    menuType: 'F',
    menuName: '发布',
    perms: 'buildJournal:publish',
    api: [ProjBizConsJournalApi.config.update]
  },
  {
    menuType: 'F',
    menuName: '取消发布',
    perms: 'buildJournal:unpublish',
    api: [ProjBizConsJournalApi.config.update]
  }
]
</script>

<script setup>
import ProjBizConsJournalApi from '@/project/api/constructionManagement/buildJournal/ProjBizConsJournal.js'
import EditProjBizConsJournal from '@/project/views/constructionManagement/recordManagement/buildJournal/components/EditProjBizConsJournal.vue'
import { ref, getCurrentInstance, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getCurrentFormattedTime } from '@/common/utils/datetime'
import store from '@/store'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getData } from 'ajv/dist/compile/validate'
import FileApi from 'sn-base-layout-vue3/packLayout/api/File'

const { proxy } = getCurrentInstance()
const route = useRoute()
const router = useRouter()

// 文件上传相关
const fileList = ref([])
const uploadDialogVisible = ref(false)
const currentRow = ref(null)

// 监听 uploadDialogVisible 的变化
watch(uploadDialogVisible, (newVal, oldVal) => {
  if (oldVal === true && newVal === false) {
    // 当对话框关闭时（从 true 变为 false）
    if (currentRow.value && fileList.value.length > 0) {
      const file = fileList.value[0]
      currentRow.value.journalDoc = file.fileName
      currentRow.value.fileList = [
        {
          fileName: file.fileName,
          id: '',
          type: file.type,
          fileId: file.id,
          fileSize: file.size.toString(),
          relevantId: '',
          fileSerialNumber: '',
          fileType: 'buildJournal'
        }
      ]
    } else if (currentRow.value && fileList.value.length === 0) {
      currentRow.value.journalDoc = ''
      currentRow.value.fileList = []
    }

    // 清空 fileList 以便下次上传
    fileList.value = []
  }
})

// 打开文件上传对话框
const openUploadDialog = (row) => {
  currentRow.value = row
  uploadDialogVisible.value = true
}

// 上传成功回调
const uploadSuccess = (files) => {
  console.log(files, 'uploaded files')
  fileList.value = files
  // 不在这里处理 currentRow，留给 watch 处理
}
// 处理文件上传
const handleFileUpload = (file) => {
  const formData = new FormData()
  formData.append('file', file.file)
}

// 预览文件
const toPreview = async (row) => {
  console.log(row.fileList, 'row')
  if (!row.fileList[0].fileId) return
  const fileInfo = await FileApi.getFileNameAndUrlById([row.fileList[0].fileId])
  console.log(fileInfo, 'fileInfo')
  //window.open(fileInfo.data.filePreviewUrl, '_blank')
  // 对原始URL进行base64编码
  const encodedUrl = btoa(fileInfo.data.filePreviewUrl)
  // 构建新的预览URL
  //const previewUrl = `http://*************:8012/onlinePreview?url=${encodeURIComponent(encodedUrl)}`
  const previewUrl = `${kkFileViewUrl}?url=${encodeURIComponent(encodedUrl)}`
  window.open(previewUrl, '_blank')
}

let option = ref({
  tip: false,
  dialogType: 'dialog',
  border: true,
  index: true,
  stripe: true,
  menu: true,
  header: true,
  height: 'auto',
  searchSpan: 6,
  searchIcon: true,
  searchMenuSpan: 6,
  searchIndex: 3,
  selection: true,
  showTree: false,
  excelBtn: false,
  delBtn: false,
  delBtns: false,
  editBtn: false,
  cellBtn: false,
  addRowBtn: false,
  delBtnsText: '批量删除',
  addBtn: false,
  // addBtnText: '新增',
  // editBtnText: '编辑',
  // customDelBtnText: '删除',
  // customViewBtnText: '查看',
  // excelBtnText: '批量导出',
  menuWidth: 300,
  defaultSort: {
    prop: 'recordDate'
  },
  column: [
    {
      label: '业务编号',
      prop: 'businessNumber',
      search: false,
      cell: true,
      slot: true,
      overHidden: true,
      rules: [
        {
          required: true,
          message: '请输入业务编号',
          trigger: 'blur'
        }
      ]
    },
    {
      label: '施工单位',
      prop: 'constructionCompany',
      search: true,
      overHidden: true
    },
    {
      label: '记录日期',
      width: 140,
      prop: 'recordDate',
      search: true,
      type: 'date',
      valueFormat: 'YYYY-MM-DD',
      searchRange: true,
      sortable: true,
      overHidden: true
    },
    {
      label: '日志文件',
      minWidth: 200,
      prop: 'journalDoc',
      search: false,
      overHidden: true,
      cell: true,
      slot: true, // 使用插槽
      rules: [
        {
          required: true,
          message: '请上传日志文件',
          trigger: 'blur'
        }
      ]
    },
    {
      label: '记录人',
      width: 120,
      prop: 'recoder',
      search: true,
      overHidden: true
    },
    {
      label: '发布状态',
      width: 120,
      prop: 'publishStatus',
      search: true,
      overHidden: true,
      type: 'select',
      dicData: [
        { label: '全部', value: '' },
        { label: '草稿', value: '1' },
        { label: '已发布', value: '2' }
      ],
      formatter: (val, value) => {
        if (value === '1') {
          return '草稿'
        } else {
          return '已发布'
        }
      }
    }
  ]
})

let listData = ref([])
let queryForm = ref({
  page: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  filter: {
    createTime: null
  }
})
let formRules = ref({})

function getPageList() {
  const params = handleQueryForm()
  ProjBizConsJournalApi.pageList(params).then((res) => {
    listData.value = res.data.dataList
    queryForm.value.page.total = res.data.totalCount
  })
}

function onResetSearch() {
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20
  }
  getPageList()
}

function handleQueryForm() {
  const { pageSize, pageNum } = queryForm.value.page
  const filter = {}
  for (let key in queryForm.value.filter) {
    if (queryForm.value.filter[key]?.length) {
      if (key.startsWith('$')) continue
      filter[key] = queryForm.value.filter[key]
    }
  }
  delete filter.createTime
  if (Array.isArray(queryForm.value.filter.createTime) && queryForm.value.filter.createTime?.length === 2) {
    filter['beginCreateTime'] = queryForm.value.filter.createTime[0]
    filter['endCreateTime'] = queryForm.value.filter.createTime[1]
  }

  delete filter.recordDate
  if (Array.isArray(queryForm.value.filter.recordDate) && queryForm.value.filter.recordDate?.length === 2) {
    filter['recordDateStart'] = queryForm.value.filter.recordDate[0]
    filter['recordDateEnd'] = queryForm.value.filter.recordDate[1]
  }

  const searchParams = {
    page: {
      pageSize,
      pageNum
    },
    filter
  }
  return searchParams
}

function onChangeSearch(params, done) {
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20
  }
  getPageList()
  done && done()
}

const crud = ref(null)

function getCurrentFormattedTimeymd() {
  const date = new Date()
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

const onAddData = () => {
  setTimeout(() => {
    const newRow = {
      businessNumber: '',
      constructionCompany: JSON.parse(store.state.user.orgName)?.find((item) => item.id === store.state.user.defaultOrg)?.orgName,
      recoder: store.state.user && store.state.user.userInfo.userName ? store.state.user.userInfo.userName : '-',
      publishStatus: '1',
      recordDate: getCurrentFormattedTimeymd(),
      $editBusinessNumber: true
    }
    // 使用 unshift 将新行添加到数组开头
    listData.value.unshift(newRow)

    // 如果需要保持分页总数同步
    queryForm.value.page.total += 1
  }, 500)
}

// 编辑业务编号相关方法
const startEditBusinessNumber = (row) => {
  // 保存原始值以便取消时恢复
  row.$originalBusinessNumber = row.businessNumber
  row.$editBusinessNumber = true
}

const saveBusinessNumber = (row) => {
  // 这里可以添加验证逻辑
  if (!row.businessNumber || row.businessNumber.trim() === '') {
    ElMessage.warning('业务编号不能为空')
    return
  }

  // 调用保存方法
  const form = {
    ...row,
    id: row.id,
    businessNumber: row.businessNumber,
    fileList: row.fileList
  }
  if (row.id) {
    ProjBizConsJournalApi.update(form)
      .then(() => {
        ElMessage.success('保存成功')
        row.$editBusinessNumber = false
        delete row.$originalBusinessNumber
        getPageList()
      })
      .catch((error) => {
        ElMessage.error('保存失败')
        console.error(error)
      })
  } else {
    ProjBizConsJournalApi.add(form)
      .then(() => {
        ElMessage.success('保存成功')
        row.$editBusinessNumber = false
        delete row.$originalBusinessNumber
        getPageList()
      })
      .catch((error) => {
        ElMessage.error('保存失败')
        console.error(error)
      })
  }
}

const cancelEditBusinessNumber = (row) => {
  // 如果是新增的行（没有id），则从列表中移除
  if (!row.id) {
    const index = listData.value.findIndex((item) => item === row)
    if (index !== -1) {
      listData.value.splice(index, 1)
      // 更新分页总数
      queryForm.value.page.total -= 1
    }
  } else {
    // 如果是编辑已有行，恢复原始值
    if (row.$originalBusinessNumber !== undefined) {
      row.businessNumber = row.$originalBusinessNumber
    }
    row.$editBusinessNumber = false
    delete row.$originalBusinessNumber
  }
}

const rowSave = (form, done) => {
  console.log('form', form)
  if (!form.journalDoc) {
    done()
    return ElMessage.warning('请上传施工日志')
  }
  ProjBizConsJournalApi.add(form)
    .then(() => {
      ElMessage.success('新增成功')
      done()
      getPageList()
    })
    .catch((error) => {
      ElMessage.error('保存失败')
      done()
    })
}

const rowUpdate = (form, index, done, loading) => {
  form.$cellEdit = false
  console.log('form', form)
  ProjBizConsJournalApi.update(form)
    .then(() => {
      ElMessage.success('更新成功')
      done()
      getPageList()
    })
    .catch((error) => {
      ElMessage.error('更新失败')
      done(false)
    })
}

function onPutData(row) {
  if (row.publishStatus === '1') {
    proxy.$modal
      .confirm('确认发布数据项？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      .then(() => {
        row.publishStatus = '2'
        ProjBizConsJournalApi.update(row)
          .then(() => {
            ElMessage.success('发布成功')

            getPageList()
          })
          .catch((error) => {
            ElMessage.error('发布失败')
          })
      })
      .catch(() => {})
  } else if (row.publishStatus === '2') {
    proxy.$modal
      .confirm('确认撤回数据项？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      .then(() => {
        row.publishStatus = '1'
        ProjBizConsJournalApi.update(row)
          .then(() => {
            ElMessage.success('撤回成功')

            getPageList()
          })
          .catch((error) => {
            ElMessage.error('撤回失败')
          })
      })
      .catch(() => {})
  }
}

function addUpdate(form, index, done, loading) {
  ProjBizConsJournalApi.update(form)
    .then(() => {
      ElMessage.success('更新成功')
      done()
      getPageList()
    })
    .catch((error) => {
      ElMessage.error('更新失败')
      done(false)
    })
}

function onEditData(row) {}

async function onEditData1(row) {
  let editType = row ? 'edit' : 'add'
  let rowInfo = await (editType !== 'add' ? ProjBizConsJournalApi.view(row.id) : {})
  const formData = editType !== 'add' ? rowInfo.data : rowInfo

  proxy.$DialogForm.show({
    title: editType == 'edit' ? '编辑' : '新增',
    type: option.value.dialogType,
    width: '80%',
    content: EditProjBizConsJournal,
    data: {
      formData: formData,
      type: editType
    },
    callback: (res) => {
      if (res.type && res.type !== 'close') {
        if (res.dialogRefs) {
          res.dialogRefs.submitData().then((flag) => {
            if (flag) {
              getPageList()
              res.close()
            }
          })
        }
      } else {
        res.close()
      }
    }
  })
}

function onDelData(rows) {
  if (!rows || rows.length == 0) {
    proxy.$message.info('请勾选数据！')
    return false
  }
  let ids = rows.map((item) => {
    return item.id
  })
  proxy.$modal
    .confirm('确认删除数据项？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    .then(() => {
      ProjBizConsJournalApi.remove(ids).then((res) => {
        proxy.$message.success('已删除')
        getPageList()
      })
    })
    .catch(() => {})
}

function onExportData() {
  const params = handleQueryForm()
  let queryForm = JSON.parse(JSON.stringify(params))
  let timestamp = proxy.parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')
  proxy.download('/project/buildJournal/export', queryForm, '施工日志-' + timestamp + '.xlsx')
}

function onDownloadData(row) {
  let params = {
    id: row.id,
    type: 'buildJournal'
  }
  let timestamp = proxy.parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')
  proxy.download('/project/sceneStartWorkReport/export/attachments', params, '施工日志-' + row.businessNumber + '-' + timestamp + '.zip')
}
</script>

<style lang="scss" scoped>
.upload-demo {
  text-align: center;
  padding: 20px;
}

.status-text {
  &.draft {
    color: #ff0000; // 红色
  }
  &.published {
    color: #008000; // 绿色
  }
}
</style>
