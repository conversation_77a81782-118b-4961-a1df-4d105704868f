<template>
  <el-form ref="formRef" label-width="100px" label-position="right" :disabled="type == 'view'">
    <el-row :gutter="16" :span="24" style="height: calc(100vh - 120px)">
      <el-col :span='6' style="height: 100%;">
        <div style="height: 100%; background-color: #ffffff;">
          <el-input placeholder="搜索" v-model="treeOrgFilter" @input="treeOrgChange" clearable
            style="padding: 10px 0"></el-input>
          <el-tree ref="treeOrgRef" :data="orgData" :props="orgDataProps" :show-checkbox="false" node-key="id"
            :highlight-current="true" :filter-node-method="treeOrgFilterNode" @node-click="handleNodeClick"
            style="height:  calc(100vh - 180px);overflow: auto; ">
            <template #default="{ node, data }">
              <div class="custom-tree-node">
                <el-tooltip class="box-item" effect="dark" :content="node.label" placement="top-start">
                  <span>{{ node.label }}</span>
                </el-tooltip>
                <div>
                  <el-dropdown>
                    <div class="more-box">
                      <span class="el-icon-more more"></span>
                    </div>
                    <template #dropdown>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item icon="el-icon-plus"
                          @click.native="onEditData(data, 'add')">添加子组织</el-dropdown-item>
                        <el-dropdown-item icon="el-icon-edit" @click.native="onEditData(data, 'edit')">编辑当前组织
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </div>
            </template>
          </el-tree>

        </div>

      </el-col>
      <el-col :span='18' style="height: 100%;">
        <div style="height: 100%;  background-color: #ffffff;" v-if="!!orgId">
          <sn-crud :data="listData" :option="option" v-model:page="queryForm.page" v-model:search="queryForm.filter"
            @search-change="onChangeSearch" @search-reset="onResetSearch"
            @addBtnHandle="() => { onEditDetailData({}, 'add') }">
            <template #menu="{ row, index, size }">
              <el-button type="primary" :size="size" icon="el-icon-edit" link
                @click="onEditDetailData(row, 'edit')">编辑</el-button>
            </template>
          </sn-crud>
        </div>
        <div v-else>
          <el-empty description="请选择一个组织进行操作~" />

        </div>

      </el-col>

    </el-row>

    <div ref="myRef"></div>
  </el-form>
</template>
<script>
import ProjBizProjectTeamOrgApi from '@/project/api/projectTeam/ProjBizProjectTeamOrg.js'
import EditProjBizProjectTeamOrgUser from "./components/EditProjBizProjectTeamOrgUser.vue";
import EditOrgInfo from "@/project/views/projectTeam/components/EditOrgInfo.vue";
export const routerConfig = [{
  menuType: "C",
  menuName: "项目团队",
}, {
  menuType: "F",
  menuName: "查看",
  perms: "show",
  api: [ProjBizProjectTeamOrgApi.config.pageList],
}, {
  menuType: "F",
  menuName: "新增",
  perms: "add",
  api: [ProjBizProjectTeamOrgApi.config.add],
}, {
  menuType: "F",
  menuName: "修改",
  perms: "update",
  api: [ProjBizProjectTeamOrgApi.config.update, ProjBizProjectTeamOrgApi.config.view],
}, {
  menuType: "F",
  menuName: "删除",
  perms: "del",
  api: [ProjBizProjectTeamOrgApi.config.remove],
}];
</script>

<script setup>
//vue
import { onMounted, ref, getCurrentInstance } from 'vue';

//全局
const { proxy } = getCurrentInstance()

//组织id
const orgId = ref("");
//对话框容器
const myRef = ref("")
//组织数据
let orgData = ref([]);
//组织树映射
let orgDataProps = ref({
  children: "children",
  label: "name"
});
//组织树过滤
let treeOrgFilter = ref("");

//组织人员配置
let option = ref({
  tip: false,
  dialogType: "dialog",
  border: true,
  index: true,
  stripe: true,
  menu: true,
  menuWidth: "150px",
  header: true,
  height: "calc(100vh - 320px)",
  searchSpan: 6,
  searchIcon: true,
  searchMenuSpan: 6,
  searchIndex: 3,
  selection: true,
  showTree: false,
  excelBtn: false,
  delBtn: false,
  editBtn: false,
  delBtns: false,
  delBtnsText: "批量删除",
  addBtnText: "新增",
  customEditBtnText: "编辑",
  column: [
    {
      label: "角色名称",
      prop: "roleName",
      width: "150px",
      headerAlign: "center",
      align: "center",
      overHidden: true,
      search: true
    }, {
      label: "姓名",
      prop: "userName",
      width: "80px",
      headerAlign: "center",
      align: "center",
      search: true
    }, {
      label: "工号",
      prop: "workNo",
      width: "120px",
      headerAlign: "center",
      align: "center",
      search: true
    }, {
      label: "手机",
      prop: "phone",
      width: "120px",
      headerAlign: "center",
      align: "center",
      earch: true
    },
    {
      label: "单位类型",
      prop: "orgNatureName",
      overHidden: true,
      earch: true
    }, {
      label: "所属组织",
      prop: "fullPathName",
      overHidden: true,
      earch: true
    },
  ]
});
//组织人员数据
let listData = ref([]);
//组织人员查看
let queryForm = ref({
  page: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  filter: {
    createTime: null
  }
});

onMounted(() => {
  //加载组织树
  getOrgTreeInfo();

})

//获取组织机构数据
function getOrgTreeInfo() {
  ProjBizProjectTeamOrgApi.queryOrgTreeList({ projectId: sessionStorage.getItem('projectId') }).then((res) => {
    orgData.value = res.data;
  });
}

//编辑组织机构
async function onEditData(row, type) {
  let editType = type;
  let upOrgName = row.fullPathName;
  if (editType == "edit") {
    const fullPathName = row.fullPathName.split("/");
    if (fullPathName.length > 1) {
      upOrgName = fullPathName.slice(0, fullPathName.length - 1).join("/");
    } else {
      upOrgName = "/"
    }
  }
  proxy.$DialogForm.show({
    title: editType == "edit" ? "编辑" : "新增",
    type: option.value.dialogType,
    el: myRef.value,
    width: "80%",
    content: EditOrgInfo,
    data: {
      rowId: row.id,
      upOrgName: upOrgName,
      type: editType
    },
    option: {
      submitBtn: true,
      emptyBtn: true,
      submitText: '保存',
      emptyText: '关闭',
    },
    callback: (res) => {
      console.log(res);
      let vm = res.dialogRefs
      if (vm) {
        switch (res.type) {
          // 保存
          case 'submit':
            vm.submitData().then(() => {
              proxy.$message.success("操作成功");
              getOrgTreeInfo();
              res.close();
            })
            break;
          case false:
            res.close();
            break;
        }
      }
    },
  });
}

//组织树过滤字段改变
function treeOrgChange(value) {
  treeOrgRef.value.filter(value);
}

//组织树过滤节点
function treeOrgFilterNode(value, data) {
  if (!value) return true;
  return data["name"].indexOf(value) !== -1;
}

//组织机构树点击
function handleNodeClick(data, node) {
  orgId.value = data.id;
  getPageList();

}

//分页获取组织用户
function getPageList() {
  const params = handleQueryForm();
  ProjBizProjectTeamOrgApi.pageDetailList(params).then((res) => {
    listData.value = res.data.dataList;
    queryForm.value.page.total = res.data.totalCount;
  });
}

function onResetSearch() {
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
}

// 处理参数
function handleQueryForm() {
  const { pageSize, pageNum } = queryForm.value.page;
  const filter = {
    orgId: orgId.value,
    projectId: sessionStorage.getItem('projectId')
  }
  for (let key in queryForm.value.filter) {
    if (queryForm.value.filter[key]?.length) {
      // sn-curd搜索select会生成$prop的变量
      if (key.startsWith("$")) continue;
      filter[key] = queryForm.value.filter[key];
    }
  }
  const searchParams = {
    page: {
      pageSize,
      pageNum,
    },
    filter,
  }
  return searchParams
}

//搜索按钮操作
function onChangeSearch(params, done) {
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
  done && done();
}

//编辑组织人员
async function onEditDetailData(row, type) {
  let editType = type;
  proxy.$DialogForm.show({
    title: editType == "edit" ? "编辑" : "新增",
    type: option.value.dialogType,
    el: myRef.value,
    width: "80%",
    content: EditProjBizProjectTeamOrgUser,
    data: {
      rowId: row.id,
      orgId: orgId.value,
      type: editType
    },
    option: {
      submitBtn: true,
      emptyBtn: true,
      submitText: '保存',
      emptyText: '关闭',
    },
    callback: (res) => {
      console.log(res);
      let vm = res.dialogRefs
      if (vm) {
        switch (res.type) {
          // 保存
          case 'submit':
            vm.submitData().then(() => {
              proxy.$message.success("操作成功");
              onResetSearch();
              res.close();
            })
            break;
          case false:
            res.close();
            break;
        }
      }
    },
  });
}
</script>

<style lang="scss" scoped>
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
</style>
