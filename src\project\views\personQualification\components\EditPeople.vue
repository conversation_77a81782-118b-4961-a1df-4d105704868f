<template>
  <el-form :model="formData" :rules="formRules" ref="formRef" label-width="100px" label-position="right"
    :disabled="type == 'view'">
    <el-row :gutter="16" :span="24">
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>人员添加
          </legend>
          <el-row :gutter="16" :span="24">
            <el-col :span='8'>
              <el-form-item label="人员角色" prop="personRole">
                <el-input v-model="formData.personRole"
                @click="show"
                type="text" placeholder="请输入" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="姓名" prop="fullName">
                <el-input v-model="formData.fullName" type="text" placeholder="请输入" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="身份证号" prop="idCard">
                <el-input v-model="formData.idCard" type="text" placeholder="请输入" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="年龄" prop="age">
                <el-input v-model="formData.age" type="number" placeholder="请输入" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="联系方式" prop="contact">
                <el-input v-model="formData.contact" type="number" placeholder="请输入" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="入场年龄" prop="entryAge">
                <el-input v-model="formData.entryAge" type="number" placeholder="请输入" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="入场时间" prop="entryTime" >
                <!-- <el-input v-model="formData.entryTime" type="text" placeholder="请输入" clearable>
                </el-input> -->
              <el-date-picker type="datetime" v-model="formData.entryTime" format="YYYY-MM-DD HH:mm:ss" 
              value-format="YYYY-MM-DDTHH:mm:ss" clearable style="width: 100%;"></el-date-picker>
          </el-form-item>
            
            </el-col>
            <el-col :span='8'>
              <el-form-item label="是否安全交底" prop="safetyEducation">
                <!-- <el-input v-model="formData.safetyEducation" type="text" placeholder="请输入" clearable>
                </el-input> -->

                <el-select v-model="formData.safetyEducation" placeholder="请选择" clearable>
                  <el-option label="是" :value="1"></el-option>
                  <el-option label="否" :value="0"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="岗位" prop="position">
                <!-- <el-input v-model="formData.position" type="text" placeholder="请输入" clearable>
                </el-input> -->

                <el-select v-model="formData.position" clearable placeholder="请选择">
                    <el-option v-for="(item, index) in positionType " :key="index" :label="item.label"
                      :value="item.value"></el-option>
                </el-select> 
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="部门" prop="position">
                     <el-tree-select
    v-model="formData.deptQualificationId"
    default-expand-all ="true"
    :data="data"
    check-strictly
    :render-after-expand="false"
    @change="handleTreeSelectChange" 
    style="width: 100%"
  />
  <el-divider />
              </el-form-item>
            </el-col>
   
            <el-col :span='8'>
              <el-form-item label="考试情况" prop="examResult">
                <el-input v-model="formData.examResult" type="text" placeholder="请输入" clearable>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </fieldset>
      </el-card>
    </el-row>
  </el-form>
</template>

<script setup>
// import ProjBizPersonQualificationPersonApi from '@/project/api/person/ProjBizPersonQualificationPerson.js' 
import ProjBizUnitQualificationApi from '@/project/api/unitQualification/ProjBizUnitQualification.js'
import {
  nextTick,
  onMounted,
  ref,
  reactive,
  watch,
  toRef,
  defineProps,
  defineExpose,
  getCurrentInstance
} from 'vue';
import {
  useDicts
} from "@/common/hooks/useDicts";
import {
  useRoute,
  useRouter
} from 'vue-router'
const {
  proxy
} = getCurrentInstance()
const route = useRoute();
const router = useRouter();
const props = defineProps({
  data: Object,
});

const {
  positionType
} = useDicts(["positionType"])
console.log("字典:", positionType)


  function show() {
    proxy.$SelectRole({
        multiple:false,
        onSuccess(res) {
          console.log(res.data[0])
         formData.value.personRole =   res.data[0].roleName
         formData.value.personRoleId =   res.data[0].id
        //  console.log(res.data[0].roleName)
            // res.data[0].roleName
        }
      })
  }

const formRef = ref()
const type = toRef(props.data?.type);
let formData = ref({
  personRole: "",
  personRoleId: "",
  fullName: "",
  deptQualificationId:"",
  deptQualificationName:"",
  idCard: "",
  age: "",
  contact: "",
  entryAge: "",
  entryTime: "",
  safetyEducation: "",
  position: "",
  examResult: ""
});
// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal) {
    formData.value = { ...newVal }; // 深拷贝避免污染原始数据
  }
}, { immediate: true });
let formRules = ref({
  personRole: [],
  fullName: [],
  idCard: [],
  age: [],
  contact: [],
  entryAge: [],
  entryTime: [],
  safetyEducation: [],
  position: [],
  examResult: []
});
formData = toRef(Object.keys(props.data?.formData || {}).length > 0 ? {
  ...props.data?.formData
} : toRef(formData.value));

let data = ref([])

function getFormData() {
  return formData.value
};
// 递归查找树节点
function findNodeById(tree, id) {
  for (const node of tree) {
    if (node.value === id) return node;
    if (node.children) {
      const found = findNodeById(node.children, id);
      if (found) return found;
    }
  }
  return null;
}
function handleTreeSelectChange(value) {
  const selectedNode = findNodeById(data.value, value);
  if (selectedNode) {
    formData.value.deptQualificationName = selectedNode.label;
  }
}
onMounted(() => {

 
   if(props.data.unitQualificationId){
    ProjBizUnitQualificationApi.getTreeById(props.data.unitQualificationId).then(resp => {
      // 1. 初始化dataSource
      if (resp.data && resp.data.length) {
         console.log("执行1")
        data.value = transformTreeData(resp.data);

      } 
       console.log("执行2")
    });
   }


  


})
function transformTreeData(rawData) {
  return rawData.map(item => ({
    value: String(item.id),  // 确保和 v-model 类型一致
    label: item.contractorName,
    children: item.children ? transformTreeData(item.children) : []
  }));
}
function submitData() {

  return new Promise((resolve) => {
    formRef.value.validate((flag) => {
      if (!flag) {
        return false;
      } else {
        if (type.value === "add") {
          resolve(saveData());
        } else {
          resolve(editData());
        }
      }
    });
  });
}

function saveData() {
  //新增操作
  const formData = getFormData();
  return ProjBizPersonQualificationPersonApi.add(formData).then(() => {
    proxy.$message.success("新增成功");
    return true;
  });
}

function editData() {
  //编辑操作
  const formData = getFormData();
  return ProjBizPersonQualificationPersonApi.update(formData).then(() => {
    proxy.$message.success("修改成功");
    return true;
  });
}
defineExpose({
  getFormData,
  submitData,
  getFormData
});
</script>

<style lang="scss" scoped>
// 统一所有表单控件的宽度（包括el-input、el-select、el-date-picker、el-tree-select等）

</style>
