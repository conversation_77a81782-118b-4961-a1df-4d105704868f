import { request, replaceUrl } from "sn-base-utils";

export default class ProjBizCommunicationEngineeringMApi {
    static config = {
        add: {
            url: '/communication/engineering/add',
            method: 'POST'
        },
        remove: {
            url: '/communication/engineering/delete',
            method: 'DELETE'
        },
        update: {
            url: '/communication/engineering/update',
            method: 'PUT'
        },
        view: {
            url: '/communication/engineering/get/{id}',
            method: 'GET'
        },
        pageList: {
            url: '/communication/engineering/page',
            method: "POST"
        },
        list: {
            url: '/communication/engineering/list',
            method: "POST"
        },
        startFlow: {
            url: '/communication/engineering/saveAndSubmitProc',
            method: "POST"
        },
        submitTask: {
            url: '/communication/engineering/saveAndSubmitTask',
            method: "POST"
        },
        operateFlow: {
            url: `/communication/engineering/operateFlow`,
            method: "POST"
        }
    };

    /**
     * 新增工程联系单
     * @param data
     * @returns {*}
     */
    static add(data) {
        return request({
            url: this.config.add.url,
            method: this.config.add.method,
            data: data
        });
    }

    /**
     * 删除工程联系单
     * @param data
     * @returns {*}
     */
    static remove(data) {
        return request({
            url: this.config.remove.url,
            method: this.config.remove.method,
            data: data
        });
    }

    /**
     * 工作流-启动流程
     * @returns {*}
     */
    static startFlow(data) {
        return request({
            url: this.config.startFlow.url,
            method: this.config.startFlow.method,
            data: data
        });
    }

    /**
     * 工作流-完成任务
     * @returns {*}
     */
    static submitTask(data) {
        return request({
            url: this.config.submitTask.url,
            method: this.config.submitTask.method,
            data: data
        });
    }

    /**
     * 更新工程联系单
     * @param data
     * @returns {*}
     */
    static update(data) {
        return request({
            url: this.config.update.url,
            method: this.config.update.method,
            data: data
        });
    }

    /**
     * 查询工程联系单详细
     * @param id
     * @returns {*}
     */
    static view(id) {
        return request({
            url: replaceUrl(this.config.view.url, { id }),
            method: this.config.view.method
        });
    }

    /**
     * 分页查询工程联系单列表
     * @param data
     * @returns {*}
     */
    static pageList(data) {
        return request({
            url: this.config.pageList.url,
            method: this.config.pageList.method,
            data: data
        });
    }

    /**
     * 全部工程联系单列表
     * @returns {*}
     */
    static list(data) {
        return request({
            url: this.config.list.url,
            method: this.config.list.method,
            data: data
        });
    }
    /**
     * 更新变更申请单
     * @param data
     * @returns {*}
     */
    static operateFlow(data) {
        return request({
            url: this.config.operateFlow.url,
            method: this.config.operateFlow.method,
            data: data
        });
    }
}
