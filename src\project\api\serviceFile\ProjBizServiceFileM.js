import { request, replaceUrl } from "sn-base-utils";

export default class ProjBizServiceFileMApi {
    static config = {
        add: {
            url: '/project/serviceFile/add',
            method: 'POST'
        },
        remove: {
            url: '/project/serviceFile/delete',
            method: 'DELETE'
        },
        update: {
            url: '/project/serviceFile/update',
            method: 'PUT'
        },
        view: {
            url: '/project/serviceFile/get/{id}',
            method: 'GET'
        },
        pageList: {
            url: '/project/serviceFile/page',
            method: "POST"
        },
        list: {
            url: '/project/serviceFile/list',
            method: "POST"
        },
        downloadZip: {
            url: '/project/serviceFile/downloadZip',
            method: "POST"
        }
    };

    /**
     * 新增设计服务文件
     * @param data
     * @returns {*}
     */
    static add(data) {
        return request({
            url: this.config.add.url,
            method: this.config.add.method,
            data: data
        });
    }

    /**
     * 删除设计服务文件
     * @param data
     * @returns {*}
     */
    static remove(data) {
        return request({
            url: this.config.remove.url,
            method: this.config.remove.method,
            data: data
        });
    }

    /**
     * 更新设计服务文件
     * @param data
     * @returns {*}
     */
    static update(data) {
        return request({
            url: this.config.update.url,
            method: this.config.update.method,
            data: data
        });
    }

    /**
     * 查询设计服务文件详细
     * @param id
     * @returns {*}
     */
    static view(id) {
        return request({
            url: replaceUrl(this.config.view.url, { id }),
            method: this.config.view.method
        });
    }

    /**
     * 分页查询设计服务文件列表
     * @param data
     * @returns {*}
     */
    static pageList(data) {
        return request({
            url: this.config.pageList.url,
            method: this.config.pageList.method,
            data: data
        });
    }

    /**
     * 全部设计服务文件列表
     * @returns {*}
     */
    static list(data) {
        return request({
            url: this.config.list.url,
            method: this.config.list.method,
            data: data
        });
    }
}
