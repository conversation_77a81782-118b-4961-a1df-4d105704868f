<template>
  <div>
    <sn-crud :data="listData" :option="option" v-model:page="queryForm.page" v-model:search="queryForm.filter" @on-load="getPageList" @search-change="onChangeSearch" @search-reset="onResetSearch">
      <template #businessNumber="scope">
        <el-link type="primary" @click="onEditData(scope.row, 'view')">{{ scope.row.businessNumber }}</el-link>
      </template>
      <template #menuLeft="{ row, index, size }">
        <el-button type="primary" :size="size" icon="el-icon-plus" @click="onEditData(row)">新增</el-button>
        <!--      <el-button type="primary" :size="size" icon="el-icon-upload" @click="onImportData(row)">列表导入</el-button> -->
        <el-button type="primary" :size="size" icon="el-icon-download" @click="onExportData(row)">列表导出</el-button>
        <!--         <el-button type="primary" :size="size" icon="el-icon-download" @click="downloadTemplate(row)">列表模版 </el-button> -->
      </template>
      <template #menu="{ row, index, size }">
        <!--未提交-->
        <el-button type="primary" :size="size" v-if="row.examineStatus === '1'" icon="el-icon-edit" link @click="onEditData(row)"> 编辑 </el-button>
        <el-button type="primary" :size="size" v-if="row.examineStatus === '1'" icon="el-icon-download" link @click="onDownloadData(row)"> 下载 </el-button>
        <el-button type="danger" :size="size" v-if="row.examineStatus === '1' && row.procInstanceId" icon="el-icon-edit" link @click="onCancelData(row, 'stop')"> 作废 </el-button>
        <el-button type="danger" :size="size" v-if="row.examineStatus === '1' && !row.procInstanceId" icon="el-icon-delete" link @click="onDelData([row])"> 删除 </el-button>
        <!--已提交-->
        <el-button type="danger" :size="size" v-if="row.examineStatus === '2'" icon="el-icon-edit" link @click="onCancelData(row, 'revoke')"> 撤回 </el-button>
        <el-button type="primary" :size="size" v-if="row.examineStatus === '2'" icon="el-icon-download" link @click="onDownloadData(row)"> 下载 </el-button>
        <!--已驳回到上一步-->

        <el-button type="primary" :size="size" v-if="row.examineStatus === '3'" icon="el-icon-download" link @click="onDownloadData(row)"> 下载 </el-button>

        <!--审批中-->
        <el-button type="primary" :size="size" v-if="row.examineStatus === '4'" icon="el-icon-download" link @click="onDownloadData(row)"> 下载 </el-button>
        <!--已作废-->
        <el-button type="primary" :size="size" v-if="row.examineStatus === '5'" icon="el-icon-edit" link @click="onDownloadData(row)"> 下载 </el-button>
        <!--已审批-->
        <el-button type="primary" :size="size" v-if="row.examineStatus === '6'" icon="el-icon-download" link @click="onDownloadData(row)"> 下载 </el-button>
        <!--已驳回提交人-->
        <el-button type="primary" :size="size" v-if="row.examineStatus === '7'" icon="el-icon-edit" link @click="onEditData(row)"> 编辑 </el-button>
        <el-button type="primary" :size="size" v-if="row.examineStatus === '7'" icon="el-icon-download" link @click="onDownloadData(row)"> 下载 </el-button>
        <el-button type="danger" :size="size" v-if="row.examineStatus === '7' && row.procInstanceId" icon="el-icon-edit" link @click="onCancelData(row, 'stop')"> 作废 </el-button>
      </template>
      <template #submitDateSearch="{ row, size }">
        <el-date-picker v-model="queryForm.filter.submitDate" type="daterange" value-format="YYYY-MM-DD" range-separator="至" start-placeholder="开始" end-placeholder="结束" style="width: 100%" />
      </template>
    </sn-crud>
    <div ref="myRef"></div>
  </div>
</template>

<script>
import ProjBizConsPlanReportApi from '@/project/api/constructionManagement/planReport/ProjBizConsPlanReport.js'
import EditProjBizConsPlanReport from './components/EditProjBizConsPlanReport.vue'
import ProjBizConsStartWorkReportApi from '@/project/api/constructionManagement/sceneStartWorkReport/ProjBizConsStartWorkReport'

export const routerConfig = [
  {
    menuType: 'C',
    menuName: '施工方案报审'
  },
  {
    menuType: 'F',
    menuName: '查看',
    perms: 'planReport:show',
    api: [ProjBizConsPlanReportApi.config.pageList]
  },
  {
    menuType: 'F',
    menuName: '新增',
    perms: 'planReport:add',
    api: [ProjBizConsPlanReportApi.config.add]
  },
  {
    menuType: 'F',
    menuName: '修改',
    perms: 'planReport:update',
    api: [ProjBizConsPlanReportApi.config.update, ProjBizConsPlanReportApi.config.view]
  },
  {
    menuType: 'F',
    menuName: '删除',
    perms: 'planReport:del',
    api: [ProjBizConsPlanReportApi.config.remove]
  },
  {
    menuType: 'F',
    menuName: '上传',
    perms: 'planReport:upload',
    api: [ProjBizConsPlanReportApi.config.view, ProjBizConsStartWorkReportApi.config.upload]
  },
  {
    menuType: 'F',
    menuName: '下载',
    perms: 'planReport:download',
    api: [ProjBizConsPlanReportApi.config.view, ProjBizConsPlanReportApi.config.pageList, ProjBizConsStartWorkReportApi.config.download]
  },
  {
    menuType: 'F',
    menuName: '撤回',
    perms: 'planReport:revoke',
    api: [ProjBizConsPlanReportApi.config.view, ProjBizConsPlanReportApi.config.startFlow, ProjBizConsPlanReportApi.config.submitTask, ProjBizConsPlanReportApi.config.printTemplate]
  },
  {
    menuType: 'F',
    menuName: '作废',
    perms: 'planReport:stop',
    api: [ProjBizConsPlanReportApi.config.view, ProjBizConsPlanReportApi.config.startFlow, ProjBizConsPlanReportApi.config.submitTask, ProjBizConsPlanReportApi.config.printTemplate]
  }
]
</script>

<script setup>
import { ref, getCurrentInstance } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getCurrentFormattedTime } from '@/common/utils/datetime'
import importView from './components/import.vue'

const { proxy } = getCurrentInstance()

const route = useRoute()
const router = useRouter()

let option = ref({
  tip: false,
  dialogType: 'dialog',
  border: true,
  index: true,
  stripe: true,
  menu: true,
  header: true,
  height: 'auto',
  searchSpan: 6,
  searchIcon: true,
  searchMenuSpan: 6,
  searchIndex: 3,
  searchLabelWidth: 140,
  selection: true,
  showTree: false,
  // importBtn: true,
  // excelBtn: false,
  addBtn: false,
  delBtn: false,
  delBtns: false,
  editBtn: false,
  // delBtnsText: '批量删除',
  // addBtnText: '新增',
  // customEditBtnText: '编辑',
  // customDelBtnText: '删除',
  // customViewBtnText: '查看',
  // excelBtnText: '批量导出',
  column: [
    {
      label: '业务编号',
      prop: 'businessNumber',
      overHidden: true,
      search: true
    },
    {
      label: '施工方案',
      prop: 'constructionPlan',
      search: false,
      overHidden: true,
      hide: true
    },
    {
      label: '施工方案类型',
      prop: 'constructionPlanType',
      width: 140,
      search: true,
      type: 'select',
      overHidden: true,
      dicData: [
        { label: '全部', value: '' },
        { label: '一般施工方案', value: '1' },
        { label: '专项施工方案', value: '2' }
      ],
      html: true,
      formatter: (val, value) => {
        if (value === '1') {
          return '一般施工方案'
        }
        return '专项施工方案'
      }
    },
    {
      label: '是否危大及以上工程',
      prop: 'hugeFlag',
      width: 140,
      search: true,
      type: 'select',
      overHidden: true,
      dicData: [
        { label: '全部', value: '' },
        { label: '是', value: '1' },
        { label: '否', value: '0' }
      ],
      html: true,
      formatter: (val, value) => {
        if (value === 1) {
          return '是'
        }
        return '否'
      }
    },
    {
      label: '是否经过专家评估论证',
      prop: 'expertCheckFlag',
      width: 150,
      search: false,
      type: 'select',
      overHidden: true,
      dicData: [
        { label: '全部', value: '' },
        { label: '是', value: '1' },
        { label: '否', value: '0' }
      ],
      html: true,
      formatter: (val, value) => {
        if (value === 1) {
          return '是'
        }
        return '否'
      }
    },
    {
      label: '编制单位',
      prop: 'organizationUnit',
      search: true,
      overHidden: true
    },
    {
      label: '提交日期',
      prop: 'submitDate',
      search: true,
      type: 'date',
      valueFormat: 'YYYY-MM-DD',
      searchRange: true,

      overHidden: true
    },
    {
      label: '审批状态',
      prop: 'examineStatus',
      search: true,
      type: 'select',
      overHidden: true,
      dicData: [
        { label: '全部', value: '' },
        { label: '草稿', value: '1' },
        { label: '已提交', value: '2' },
        { label: '上级驳回', value: '3' },
        { label: '审批中', value: '4' },
        { label: '已作废', value: '5' },
        { label: '已审批', value: '6' },
        { label: '已驳回', value: '7' }
      ],
      html: true,
      formatter: (val, value) => {
        if (value === '1') {
          return `<span style="color:#4871C0">草稿</span>`
        } else if (value === '2') {
          return `<span style="color:#4871C0">已提交</span>`
        } else if (value === '4') {
          return `<span style="color:#4871C0">审批中</span>`
        } else if (value === '3') {
          return `<span style="color:red">上级驳回</span>`
        } else if (value === '5') {
          return `<span style="color:red">已作废</span>`
        } else if (value === '6') {
          return `<span style="color:#4871C0">已审批</span>`
        } else if (value === '7') {
          return `<span style="color:red">已驳回</span>`
        }
        return `<p></p>`
      }
    }
  ]
})
let listData = ref([])
const myRef = ref(null)
let queryForm = ref({
  page: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  filter: {
    createTime: null,
    submitDate: null
  }
})
let formRules = ref({})

function getPageList() {
  //查询分页列表
  // 查询前处理参数
  const params = handleQueryForm()
  ProjBizConsPlanReportApi.pageList(params).then((res) => {
    listData.value = res.data.dataList
    queryForm.value.page.total = res.data.totalCount
  })
}

function onResetSearch() {
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20
  }
  getPageList()
}

function handleQueryForm() {
  // 处理参数
  const { pageSize, pageNum } = queryForm.value.page
  const filter = {}
  for (let key in queryForm.value.filter) {
    if (queryForm.value.filter[key]?.length) {
      // sn-curd搜索select会生成$prop的变量
      if (key.startsWith('$')) continue
      filter[key] = queryForm.value.filter[key]
    }
  }
  delete filter.createTime
  if (Array.isArray(queryForm.value.filter.createTime) && queryForm.value.filter.createTime?.length === 2) {
    filter['beginCreateTime'] = queryForm.value.filter.createTime[0]
    filter['endCreateTime'] = queryForm.value.filter.createTime[1]
  }

  delete filter.submitDate
  if (Array.isArray(queryForm.value.filter.submitDate) && queryForm.value.filter.submitDate?.length === 2) {
    filter['submitDateStart'] = queryForm.value.filter.submitDate[0]
    filter['submitDateStartEnd'] = queryForm.value.filter.submitDate[1]
  }

  const searchParams = {
    page: {
      pageSize,
      pageNum
    },
    filter
  }
  return searchParams
}

function onChangeSearch(params, done) {
  //搜索按钮操作
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20
  }
  getPageList()
  done && done()
}

function generateButtons(editType, row) {
  if (editType !== 'view') {
    const hasProcId = row?.procInstanceId
    const status = row?.examineStatus

    const buttons = [
      { key: 'cancelDirect', text: '关闭', buttonType: '', icon: 'el-icon-close' }
      /*       { key: 'downloadForm', text: '下载表单', buttonType: 'primary', icon: 'el-icon-download' } */
    ]
    console.log(status, status == '3' || status == '7')
    if (status == '3' || status == '7') {
      buttons.push(
        {
          key: 'save',
          text: '保存',
          buttonType: 'primary',
          icon: 'el-icon-check'
        },
        {
          key: 'submitFlowTaskProcess',
          text: '提交',
          buttonType: 'primary',
          icon: 'sn-button-fasong'
        }
      )
    } else {
      buttons.push(
        {
          key: 'save',
          text: '保存',
          buttonType: 'primary',
          icon: 'el-icon-check'
        },
        {
          key: 'afterProcessIsInitiated',
          text: '发起流程',
          buttonType: 'primary',
          icon: 'sn-button-fasong'
        }
      )
    }
    return buttons
  }
  return row.examineStatus != '1'
    ? [{ key: 'cancelDirect', text: '关闭', buttonType: '', icon: 'el-icon-close' }]
    : [
        { key: 'cancelDirect', text: '关闭', buttonType: '', icon: 'el-icon-close' },
        { key: 'edit', text: '编辑', buttonType: 'primary', icon: 'el-icon-edit' }
      ]
}
function closeDialog(e) {
  getPageList()
  e.close()
}
async function onEditData(row, viewtype) {
  //编辑,新增按钮操作
  let editType = viewtype === 'view' ? 'view' : row ? 'edit' : 'add'
  let rowInfo = await (editType !== 'add' ? ProjBizConsPlanReportApi.view(row.id) : {})
  const formData = editType !== 'add' ? rowInfo.data : rowInfo
  delete formData.beginCreateTime
  // delete formData.createTime;
  delete formData.endCreateTime
  proxy.$DialogForm.show({
    title: '施工方案报审',
    type: 'page',
    el: myRef.value,
    width: '80%',
    content: EditProjBizConsPlanReport,
    data:
      editType !== 'add'
        ? {
            formData: rowInfo.data,
            isShowCloseBtn: false,
            approvalOption: ref({
              isShowApprovalList: true,
              isShowFlowDiagram: true,
              procInstId: rowInfo.data.procInstanceId
            }),
            type: editType,
            el: myRef.value,
            closeDialog: closeDialog
          }
        : {
            approvalOption: ref({
              isShowApprovalList: false,
              isShowFlowDiagram: false,
              procInstId: null
            }),
            type: editType,
            el: myRef.value,
            closeDialog: closeDialog
          },
    option: {
      submitBtn: false,
      emptyBtn: false,
      submitText: '保存',
      emptyText: '取消',
      extendButton: generateButtons(editType, row)
    },
    callback: (res) => {
      let vm = res.dialogRefs
      if (vm) {
        let formData = vm.getFormData()
        switch (res.type) {
          // 取消
          case 'cancelDirect':
            proxy.$modal
              .confirm('确认关闭？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              })
              .then(() => {
                res.close()
              })
            break
          // 保存
          case 'save':
            vm.getSaveFormData(formData).then((resp) => {
              proxy.$message.success('保存成功')
              getPageList()
              // res.close();
            })
            break
          // 发起流程
          case 'afterProcessIsInitiated':
            let startProcDto = {
              businessKey: null,
              clientId: null
            }
            if (formData.procInstanceId) {
              let taskInfo = vm.getTaskInfo()
              vm.handlerOpenConfirm(taskInfo, res)
              getPageList()
            } else {
              vm.getStartFlow(formData, startProcDto).then(() => {
                let taskInfo = vm.getTaskInfo()
                vm.handlerOpenConfirm(taskInfo, res)
                getPageList()
              })
            }
            break
          // 编辑
          case 'edit':
            res.close()
            onEditData(row)
            break
          //提交流程
          case 'submitFlowTaskProcess':
            vm.submitFlowTask()
            break
        }
      }
    }
  })
}

function onDelData(rows) {
  //删除行
  if (!rows || rows.length == 0) {
    proxy.$message.info('请勾选数据！')
    return false
  }
  let ids = rows.map((item) => {
    return item.id
  })
  proxy.$modal
    .confirm('确认删除数据项？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    .then(() => {
      ProjBizConsPlanReportApi.remove(ids).then((res) => {
        proxy.$message.success('已删除')
        getPageList()
      })
    })
    .catch(() => {})
}

function onImportData(row) {
  proxy.$DialogForm.show({
    title: '上传',
    type: 'dialog',
    width: '80%',
    content: importView,
    data: {},
    option: {
      submitBtn: true,
      emptyBtn: true,
      submitText: '确定',
      emptyText: '取消'
    },
    callback: (res) => {
      let vm = res.dialogRefs
      if (vm) {
        res.close()
      } else {
        res.close()
      }
    }
  })
}

//下载施工方案报审模板
function downloadTemplate() {
  proxy.download('/project/planReport/import/template', {}, '施工方案报审模板.xlsx')
}

function onExportData() {
  const params = handleQueryForm()
  let queryForm = JSON.parse(JSON.stringify(params))
  let timestamp = proxy.parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')
  proxy.download('/project/planReport/export', queryForm, '施工方案报审-' + timestamp + '.xlsx')
}

function toReportDetail(row) {
  router.push({
    path: '/lifecycleBiz/views/constructionManagement/technologyManagement/planReport/components/EditProjBizConsPlanReport',
    query: {
      businessNumber: row.businessNumber,
      businessKey: row.id
    }
  })
}

function onEditFlowData(row) {
  proxy.$modal
    .confirm('确认撤回数据项？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    .then(() => {
      ProjBizConsPlanReportApi.remove(row.id).then((res) => {
        proxy.$message.success('已删除')
        getPageList()
      })
    })
    .catch(() => {})
}

/*流程作废*/
function onCancelData(row, type) {
  //删除行
  let formDate = {
    id: row.id,
    actionType: type
  }
  let typeName
  if (type == 'revoke') {
    typeName = '撤回'
  } else {
    typeName = '作废'
  }
  proxy.$modal
    .confirm('确认' + typeName + '流程？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    .then(() => {
      ProjBizConsPlanReportApi.operateFlow(formDate).then((res) => {
        proxy.$message.success('已' + typeName)
        getPageList()
      })
    })
    .catch(() => {})
}

function onDownloadData(row) {
  let params = {
    id: row.id,
    type: 'planReport'
  }
  let timestamp = proxy.parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')
  proxy.download('/project/sceneStartWorkReport/export/attachments', params, '施工方案报审-' + row.businessNumber + '-' + timestamp + '.zip')
}
</script>

<style lang="scss" scoped></style>
