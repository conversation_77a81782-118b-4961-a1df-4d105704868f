<template>
  <div>
    <sn-crud :data="listData" :option="option" v-model:page="queryForm.page" v-model:search="queryForm.filter" @on-load="getPageList" @search-change="onChangeSearch" @search-reset="onResetSearch" @addBtnHandle="onEditData" @row-del="onDelData" @row-excel="onExportData">
      <template #menu="{ row, index, size }">
        <!-- 草稿状态显示的按钮 -->
        <template v-if="Number(row.publishStatus) !== 1">
          <el-button type="primary" :size="size" icon="el-icon-edit" link @click="onEditData(row)">编辑</el-button>
          <el-button type="danger" :size="size" icon="el-icon-delete" link @click="onDelData([row])">删除</el-button>
          <!-- <el-button type="primary" :size="size" icon="el-icon-circle-check" link @click="onPublish(row, index)">发布</el-button> -->
        </template>
        <!-- 已发布状态显示的按钮 -->
        <template v-else>
          <el-button type="danger" :size="size" icon="el-icon-refresh-left" link @click="onUnpublish(row, index)">撤回</el-button>
          <el-button type="primary" :size="size" icon="el-icon-download" link @click="onDownload(row)">下载</el-button>
        </template>
      </template>
      <!-- 设计服务文件编号列自定义渲染，点击跳转编辑 -->
      <template #fileId="{ row }">
        <el-link class="image-link" @click="onViewData(row)">{{ row.fileId }}</el-link>
      </template>
      <template #publishStatus="{row}">
        <span :style="{color : row.publishStatus && row.publishStatus===1 ? 'green' : 'red'}">
          {{row.publishStatus && row.publishStatus===1 ? '已发布': '草稿'}}
        </span>
      </template>
        <!--添加如果文本太长展示省略号-->
      <template #fileName="{ row }">
        <el-tooltip class="item" effect="dark" :content="row.fileName" placement="top">
          <div class="text-overflow">{{ row.fileName }}</div>
        </el-tooltip>
      </template>
      <template #fileType="{ row }">
        <el-tooltip class="item" effect="dark" :content="row.fileType" placement="top">
          <div class="text-overflow">{{ row.fileType }}</div>
        </el-tooltip>
      </template>
    </sn-crud>
    <div ref="myRef"></div>
  </div>
</template>

<script>
import ProjBizServiceFileMApi from '@/project/api/serviceFile/ProjBizServiceFileM.js'
  import EditProjBizServiceFileM from "./components/EditProjBizServiceFileM.vue";
  import { getToken } from "sn-base-utils";
  export const routerConfig = [{
    menuType: "C",
    menuName: "设计服务文件",
  }, {
    menuType: "F",
    menuName: "查看",
    perms: "show",
    api: [ProjBizServiceFileMApi.config.pageList],
  }, {
    menuType: "F",
    menuName: "新增",
    perms: "add",
    api: [ProjBizServiceFileMApi.config.add],
  }, {
    menuType: "F",
    menuName: "修改",
    perms: "update",
    api: [ProjBizServiceFileMApi.config.update, ProjBizServiceFileMApi.config.view],
  }, {
    menuType: "F",
    menuName: "删除",
    perms: "del",
    api: [ProjBizServiceFileMApi.config.remove],
  }, {
    menuType: "F",
    menuName: "下载",
    perms: "dowload",
    api: [ProjBizServiceFileMApi.config.downloadZip],
  }];
</script>

<script setup>
import {
  ref,
  getCurrentInstance
} from 'vue';
const {
  proxy
} = getCurrentInstance()
const myRef = ref(null);
let option = ref({
  tip: false,
  dialogType: "page",
  border: true,
  index: true,
  stripe: true,
  menu: true,
  header: true,
  height: "auto",
  searchSpan: 6,
  searchIcon: true,
  searchMenuSpan: 6,
  searchLabelWidth: 130,
  searchIndex: 3,
  selection: true,
  showTree: false,
  excelBtn: false,
  delBtn: false,
  delBtns: false,
  editBtn: false,
  delBtnsText: "批量删除",
  addBtnText: "新增",
  customEditBtnText: "编辑",
  customDelBtnText: "删除",
  customViewBtnText: "查看",
  column: [{
    label: "设计服务文件编号",
    prop: "fileId"
  }, {
    label: "设计服务文件名称",
    prop: "fileName",
    search: true
  }, {
    label: "文件类别",
    prop: "fileType",
    search: true,
    dictDatas: "service_file_type",
    type: "select",
    dicUrl: "/system/dict/data/type/service_file_type",
    dicMethod: "get",
    props: {
      label: "dictLabel",
      value: "dictValue"
    }
  }, {
    label: "设计单位",
    prop: "designOrg",
    search: true
  }, {
    label: "设计单位负责人",
    prop: "designLeader"
  }, {
    label: "录入人",
    prop: "inputBy"
  }, {
    label: "录入日期",
    prop: "inputDate"
  }, {
    label: "发布状态",
    prop: "publishStatus",
    width: 80
  }]
});

let listData = ref([]);
let queryForm = ref({
  page: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  filter: {
    createTime: null
  }
});
let formRules = ref({});

function getPageList() {
  //查询分页列表
  // 查询前处理参数
  const params = handleQueryForm();
  ProjBizServiceFileMApi.pageList(params).then((res) => {
    listData.value = res.data.dataList;
    queryForm.value.page.total = res.data.totalCount;
  });
}

function onResetSearch() {
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
}

function handleQueryForm() {
  // 处理参数
  const {
    pageSize,
    pageNum
  } = queryForm.value.page;
  const filter = {}
  for (let key in queryForm.value.filter) {
    if (queryForm.value.filter[key]?.length) {
      // sn-curd搜索select会生成$prop的变量
      if (key.startsWith("$")) continue;
      filter[key] = queryForm.value.filter[key];
    }
  }
  delete filter.createTime;
  if (Array.isArray(queryForm.value.filter.createTime) && queryForm.value.filter.createTime?.length === 2) {
    filter["beginCreateTime"] = queryForm.value.filter.createTime[0];
    filter["endCreateTime"] = queryForm.value.filter.createTime[1];
  }
  const searchParams = {
    page: {
      pageSize,
      pageNum,
    },
    filter,
  }
  return searchParams
}

function onChangeSearch(params, done) {
  //搜索按钮操作
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
  done && done();
}
async function onEditData(row) {
  //编辑,新增按钮操作
  let editType = row ? "edit" : "add";
  let rowInfo = await (editType !== "add" ? ProjBizServiceFileMApi.view(row.id) : {});
  const formData = editType !== "add" ? rowInfo.data : rowInfo;
  //编辑,新增按钮操作
  proxy.$DialogForm.show({
    title: editType == "edit" ? "编辑" : "新增",
    type: option.value.dialogType,
    width: "80%",
    el: myRef.value,
    content: EditProjBizServiceFileM,
    data: {
      formData: formData,
      type: editType,
    },
    option: {
      submitBtn: false,
      emptyBtn: false,
      emptyText:'取消',
      extendButton:[
        {
          key: 'save',
          text: '保存',
          icon: 'el-icon-plus',
          buttonType: 'primary',
        },
        {
          key: 'submit',
          text: '发布',
          icon: 'el-icon-check',
          buttonType: 'primary',
        },
        {
          key: 'close',
          text: '关闭',
          icon: 'el-icon-close',
          buttonType: '',
        },
      ],
    },
    callback: (res) => {
      if (res.type && res.type == 'save') {
        if (res.dialogRefs) {
          res.dialogRefs.submitData(res.type).then((flag) => {
            if (flag) {
              getPageList();
            }
          });
        }
      }else if (res.type && res.type == 'submit') {
        if (res.dialogRefs) {
          res.dialogRefs.submitData(res.type).then((flag) => {
            if (flag) {
              getPageList();
              res.close();
            }
          });
        }
      }
       else {
        if (editType === 'edit') {
          proxy.$confirm('确认关闭？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            res.close();
          }).catch(() => {
            // 用户点击取消，不关闭弹窗
          });
        } else {
          res.close();
        }
      }
    }
  });
}

async function onViewData(row) {
  let editType = "view";
  // 动态扩展按钮
  let extendButtons = [];
  if (row.publishStatus === 0) {
    extendButtons.push({
      key: 'edit',
      text: '编辑',
      icon: 'el-icon-edit',
      buttonType: 'primary',
      click: (res) => {
        res.close();
        onEditData(row);
      },
    });
  }
  extendButtons.push({
    key: 'close',
    text: '关闭',
    icon: 'el-icon-close',
    buttonType: '',
  });

  let rowInfo = await ProjBizServiceFileMApi.view(row.id);
  const formData = rowInfo.data;

  proxy.$DialogForm.show({
    title: "查看",
    type: option.value.dialogType,
    width: "80%",
    el: myRef.value,
    content: EditProjBizServiceFileM,
    data: {
      formData: formData,
      type: editType,
    },
    option: {
      submitBtn: false,
      emptyBtn: false,
      emptyText: '取消',
      extendButton: extendButtons
    },
    callback: (res) => {
      if (res.type === 'edit') {
        res.close();
        onEditData(row);
      } else if (res.type === 'close') {
        // 当点击关闭按钮且处于编辑模式时，弹出确认对话框
        if (editType === 'edit') {
          proxy.$confirm('确认关闭？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            res.close();
          }).catch(() => {
            // 用户点击取消，不关闭弹窗
          });
        } else {
          res.close();
        }
      } else if (res.type && res.type !== 'close') {
        if (res.dialogRefs) {
          res.dialogRefs.submitData(res.type).then((flag) => {
            if (flag) {
              getPageList();
              res.close();
            }
          });
        }
      } else {
        res.close();
      }
    }
  });
}

function onDelData(rows) {
  //删除行
  if (!rows || rows.length == 0) {
    proxy.$message.info("请勾选数据！");
    return false;
  }
  let ids = rows.map((item) => {
    return item.id;
  });
  proxy.$modal.confirm("确认删除数据项？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    ProjBizServiceFileMApi.remove(ids).then((res) => {
      proxy.$message.success("已删除");
      getPageList();
    });
  }).catch(() => {});
}

function onExportData() {
  const params = handleQueryForm();
  let queryForm = JSON.parse(JSON.stringify(params));
  proxy.download("/project/serviceFile/export", queryForm, '数据导出_1752568873829.xlsx');
}

function onPublish(row, index) {
  proxy.$modal.confirm("确认发布该记录？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    if (row.id) {
      ProjBizServiceFileMApi.update({
        ...row,
        publishStatus: 1
      }).then(() => {
        proxy.$message.success("发布成功");
        getPageList();
      }).catch(() => {
        proxy.$message.error("发布失败");
      });
    } else {
      proxy.$message.success("发布成功");
      getPageList();
    }
  }).catch(() => {});
}

function onUnpublish(row, index) {
  proxy.$modal.confirm("确认撤回该记录？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    if (row.id) {
      ProjBizServiceFileMApi.update({
        ...row,
        publishStatus: 0
      }).then(() => {
        proxy.$message.success("撤回成功");
        getPageList();
      }).catch(() => {
        proxy.$message.error("撤回失败");
      });
    } else {
      proxy.$message.success("撤回成功");
      getPageList();
    }
  }).catch(() => {});
}

/**
 * 下载文件
 * @param row
 */
 function onDownload(row) {
  proxy.download(ProjBizServiceFileMApi.config.downloadZip.url, [row.id], 'file_'+row.id+'.zip');
}

</script>

<style lang="scss" scoped>
.image-link {
  color: #1a5cff !important;
  cursor: pointer;
  text-decoration: none;
}
.image-link:hover {
  text-decoration: underline;
}

.text-overflow {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
