{"V2": "<template>\n  <el-row class=\"\" :gutter=\"16\">\n    <el-form :model=\"formData\" :rules=\"formRules\" ref=\"formRef\" label-width=\"100px\" label-position=\"right\">\n      <el-col :span='12'>\n        <el-form-item label=\"岗位\" prop=\"id\">\n          <el-input v-model=\"formData.id\" type=\"text\" placeholder=\"请输入\" clearable>\n          </el-input>\n        </el-form-item>\n      </el-col>\n      <el-col :span='12'>\n        <el-form-item label=\"姓名\" prop=\"name\">\n          <el-input v-model=\"formData.name\" type=\"text\" placeholder=\"请输入\" clearable>\n          </el-input>\n        </el-form-item>\n      </el-col>\n      <el-col :span='12'>\n        <el-form-item label=\"年龄\" prop=\"age\">\n          <el-input v-model=\"formData.age\" type=\"text\" placeholder=\"请输入\" clearable>\n          </el-input>\n        </el-form-item>\n      </el-col>\n      <el-col :span='12'>\n        <el-form-item label=\"邮箱\" prop=\"email\">\n          <el-input v-model=\"formData.email\" type=\"text\" placeholder=\"请输入\" clearable>\n          </el-input>\n        </el-form-item>\n      </el-col>\n      <el-col :span='12'>\n        <el-form-item label=\"版本\" prop=\"version\">\n          <el-input v-model=\"formData.version\" type=\"text\" placeholder=\"请输入\" clearable>\n          </el-input>\n        </el-form-item>\n      </el-col>\n      <el-col :span='12'>\n        <el-form-item label=\"创建者\" prop=\"createBy\">\n          <el-input v-model=\"formData.createBy\" type=\"text\" placeholder=\"请输入\" clearable>\n          </el-input>\n        </el-form-item>\n      </el-col>\n      <el-col :span='12'>\n        <el-form-item label=\"创建者姓名\" prop=\"createName\">\n          <el-input v-model=\"formData.createName\" type=\"text\" placeholder=\"请输入\" clearable>\n          </el-input>\n        </el-form-item>\n      </el-col>\n      <el-col :span='12'>\n        <el-form-item label=\"创建时间\" prop=\"createTime\">\n          <el-input v-model=\"formData.createTime\" type=\"text\" placeholder=\"请输入\" clearable>\n          </el-input>\n        </el-form-item>\n      </el-col>\n      <el-col :span='12'>\n        <el-form-item label=\"更新组织\" prop=\"updateOrg\">\n          <el-input v-model=\"formData.updateOrg\" type=\"text\" placeholder=\"请输入\" clearable>\n          </el-input>\n        </el-form-item>\n      </el-col>\n      <el-col :span='12'>\n        <el-form-item label=\"更新者\" prop=\"updateBy\">\n          <el-input v-model=\"formData.updateBy\" type=\"text\" placeholder=\"请输入\" clearable>\n          </el-input>\n        </el-form-item>\n      </el-col>\n      <el-col :span='12'>\n        <el-form-item label=\"更新者姓名\" prop=\"updateName\">\n          <el-input v-model=\"formData.updateName\" type=\"text\" placeholder=\"请输入\" clearable>\n          </el-input>\n        </el-form-item>\n      </el-col>\n      <el-col :span='12'>\n        <el-form-item label=\"更新时间\" prop=\"updateTime\">\n          <el-input v-model=\"formData.updateTime\" type=\"text\" placeholder=\"请输入\" clearable>\n          </el-input>\n        </el-form-item>\n      </el-col>\n      <el-col :span='12'>\n        <el-form-item label=\"删除标志\" prop=\"delFlag\">\n          <el-input v-model=\"formData.delFlag\" type=\"text\" placeholder=\"请输入\" clearable>\n          </el-input>\n        </el-form-item>\n      </el-col>\n    </el-form>\n  </el-row>\n</template>\r\n\r\n<script>\r\n//TODO 引入所需JS\nexport default {\n  data() {\n    return {\n      formData: {\n        id: \"\",\n        name: \"\",\n        age: \"\",\n        email: \"\",\n        version: \"\",\n        createBy: \"\",\n        createName: \"\",\n        createTime: \"\",\n        updateOrg: \"\",\n        updateBy: \"\",\n        updateName: \"\",\n        updateTime: \"\",\n        delFlag: \"\"\n      },\n      formRules: {\n        id: [],\n        name: [],\n        age: [],\n        email: [],\n        version: [],\n        createBy: [],\n        createName: [],\n        createTime: [],\n        updateOrg: [],\n        updateBy: [],\n        updateName: [],\n        updateTime: [],\n        delFlag: []\n      }\n    }\n  },\n  methods: {\n    getFormData() {\n      return this.formData;\n    },\n    async submitData() {\n      return new Promise((resolve) => {\n        this.$refs.formRef.validate((flag) => {\n          if (!flag) {\n            return false;\n          } else {\n            if (this.type === \"add\") {\n              resolve(this.saveData());\n            } else {\n              resolve(this.editData());\n            }\n          }\n        });\n      });\n    },\n    async saveData() {\n      //新增操作\n      const formData = this.getFormData();\n      return __classNameVariable__Api.add(formData).then(() => {\n        this.$message.success(\"新增成功\");\n        return true;\n      });\n    },\n    async editData() {\n      //编辑操作\n      const formData = this.getFormData();\n      return __classNameVariable__Api.update(formData).then(() => {\n        this.$message.success(\"修改成功\");\n        return true;\n      });\n    },\n  },\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\r\n</style>", "V3": "<template>\n  <el-row :gutter=\"16\">\n    <el-form :model=\"formData\" :rules=\"formRules\" ref=\"formRef\" label-width=\"100px\" label-position=\"right\">\n      <el-col :span='12'>\n        <el-form-item label=\"岗位\" prop=\"id\">\n          <el-input v-model=\"formData.id\" type=\"text\" placeholder=\"请输入\" clearable>\n          </el-input>\n        </el-form-item>\n      </el-col>\n      <el-col :span='12'>\n        <el-form-item label=\"姓名\" prop=\"name\">\n          <el-input v-model=\"formData.name\" type=\"text\" placeholder=\"请输入\" clearable>\n          </el-input>\n        </el-form-item>\n      </el-col>\n      <el-col :span='12'>\n        <el-form-item label=\"年龄\" prop=\"age\">\n          <el-input v-model=\"formData.age\" type=\"text\" placeholder=\"请输入\" clearable>\n          </el-input>\n        </el-form-item>\n      </el-col>\n      <el-col :span='12'>\n        <el-form-item label=\"邮箱\" prop=\"email\">\n          <el-input v-model=\"formData.email\" type=\"text\" placeholder=\"请输入\" clearable>\n          </el-input>\n        </el-form-item>\n      </el-col>\n      <el-col :span='12'>\n        <el-form-item label=\"版本\" prop=\"version\">\n          <el-input v-model=\"formData.version\" type=\"text\" placeholder=\"请输入\" clearable>\n          </el-input>\n        </el-form-item>\n      </el-col>\n      <el-col :span='12'>\n        <el-form-item label=\"创建者\" prop=\"createBy\">\n          <el-input v-model=\"formData.createBy\" type=\"text\" placeholder=\"请输入\" clearable>\n          </el-input>\n        </el-form-item>\n      </el-col>\n      <el-col :span='12'>\n        <el-form-item label=\"创建者姓名\" prop=\"createName\">\n          <el-input v-model=\"formData.createName\" type=\"text\" placeholder=\"请输入\" clearable>\n          </el-input>\n        </el-form-item>\n      </el-col>\n      <el-col :span='12'>\n        <el-form-item label=\"创建时间\" prop=\"createTime\">\n          <el-input v-model=\"formData.createTime\" type=\"text\" placeholder=\"请输入\" clearable>\n          </el-input>\n        </el-form-item>\n      </el-col>\n      <el-col :span='12'>\n        <el-form-item label=\"更新组织\" prop=\"updateOrg\">\n          <el-input v-model=\"formData.updateOrg\" type=\"text\" placeholder=\"请输入\" clearable>\n          </el-input>\n        </el-form-item>\n      </el-col>\n      <el-col :span='12'>\n        <el-form-item label=\"更新者\" prop=\"updateBy\">\n          <el-input v-model=\"formData.updateBy\" type=\"text\" placeholder=\"请输入\" clearable>\n          </el-input>\n        </el-form-item>\n      </el-col>\n      <el-col :span='12'>\n        <el-form-item label=\"更新者姓名\" prop=\"updateName\">\n          <el-input v-model=\"formData.updateName\" type=\"text\" placeholder=\"请输入\" clearable>\n          </el-input>\n        </el-form-item>\n      </el-col>\n      <el-col :span='12'>\n        <el-form-item label=\"更新时间\" prop=\"updateTime\">\n          <el-input v-model=\"formData.updateTime\" type=\"text\" placeholder=\"请输入\" clearable>\n          </el-input>\n        </el-form-item>\n      </el-col>\n      <el-col :span='12'>\n        <el-form-item label=\"删除标志\" prop=\"delFlag\">\n          <el-input v-model=\"formData.delFlag\" type=\"text\" placeholder=\"请输入\" clearable>\n          </el-input>\n        </el-form-item>\n      </el-col>\n    </el-form>\n  </el-row>\n</template>\r\n\r\n<script setup>\r\n//TODO 引入所需JS \nimport {\n  ref,\n  toRef,\n  defineProps,\n  defineExpose,\n  getCurrentInstance\n} from 'vue';\nconst {\n  proxy\n} = getCurrentInstance()\nconst formRef = ref()\nlet formData = ref({\n  id: \"\",\n  name: \"\",\n  age: \"\",\n  email: \"\",\n  version: \"\",\n  createBy: \"\",\n  createName: \"\",\n  createTime: \"\",\n  updateOrg: \"\",\n  updateBy: \"\",\n  updateName: \"\",\n  updateTime: \"\",\n  delFlag: \"\"\n});\nlet formRules = ref({\n  id: [],\n  name: [],\n  age: [],\n  email: [],\n  version: [],\n  createBy: [],\n  createName: [],\n  createTime: [],\n  updateOrg: [],\n  updateBy: [],\n  updateName: [],\n  updateTime: [],\n  delFlag: []\n});\nconst props = defineProps({\n  data: Object,\n});\nformData = toRef(Object.keys(props.data.formData || {}).length > 0 ? {\n  ...props.data.formData\n} : toRef(formData.value));\nconst type = toRef(props.data.type);\n\nfunction getFormData() {\n  return formData.value\n};\n\nfunction submitData() {\n  return new Promise((resolve) => {\n    formRef.value.validate((flag) => {\n      if (!flag) {\n        return false;\n      } else {\n        if (type.value === \"add\") {\n          resolve(saveData());\n        } else {\n          resolve(editData());\n        }\n      }\n    });\n  });\n}\n\nfunction saveData() {\n  //新增操作\n  const formData = getFormData();\n  return __classNameVariable__Api.add(formData).then(() => {\n    proxy.$message.success(\"新增成功\");\n    return true;\n  });\n}\n\nfunction editData() {\n  //编辑操作\n  const formData = getFormData();\n  return __classNameVariable__Api.update(formData).then(() => {\n    proxy.$message.success(\"修改成功\");\n    return true;\n  });\n}\ndefineExpose({\n  submitData,\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\r\n</style>"}