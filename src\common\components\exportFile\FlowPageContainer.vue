<template>
  <div class="flex flex1 bgW" style="width: 100% !important">
    <div class="flow-task" style="position: relative; overflow: hidden">
      <div
        class="task-title"
        v-if="closeBtn"
        :style="{
          borderBottomWidth: '2px',
          borderBottomStyle: 'solid',
          borderBottomColor: $BPMN.theme(),
        }"
      >
        <div
          v-if="
            urlParams.taskId || urlParams.procInstId || urlParams.procDefKey
          "
        >
          <span class="text">{{ urlParams.businessTitle }}</span>
          <el-tag
            type="success"
            v-if="urlParams.taskFlag == 1 && taskInfo.taskName"
          >
            {{ "当前节点：" + taskInfo.taskName }}
          </el-tag>
          <el-tag v-if="urlParams.procInstInitiatorName" type="info">
            {{ "发起人:" + urlParams.procInstInitiatorName }}
          </el-tag>
        </div>
      </div>
      <el-row
        type="flex"
        justify="space-between"
        style="margin-bottom: 5px; margin-top: 10px"
        v-if="
          urlParams.taskId ||
          urlParams.procInstId ||
          urlParams.procDefKey ||
          isShowFlowDiagram ||
          isShowApprovalList
        "
      >
        <el-col :span="12">
          <el-radio-group
            v-model="currentPage"
            style="min-width: 400px; margin-left: 15px"
          >
            <el-radio-button label="formLabel">表单信息</el-radio-button>
            <el-radio-button label="diagramLabel" v-if="isShowFlowDiagram"
              >流程图</el-radio-button
            >
            <el-radio-button label="commentLabel" v-if="isShowApprovalList"
              >审批记录</el-radio-button
            >
          </el-radio-group>
        </el-col>
        <el-col :span="12">
          <el-row
            type="flex"
            justify="end"
            style="width: 100%; margin-right: 15px"
          >
            <template v-if="urlParams.taskFlag === 3">
              <el-button type="primary" @click="handlerReadAction()">
                已阅
              </el-button>
            </template>
            <template v-else>
              <el-button
                v-for="(action, index) in taskInfo.taskActionList"
                :key="index"
                :plain="action.plain || false"
                :type="getButtonType(action.type) || 'primary'"
                @click="handlerAction(action)"
              >
                {{ action.label }}
              </el-button>
            </template>
            <template v-if="$slots.extActions">
              <slot name="extActions" />
            </template>
          </el-row>
        </el-col>
      </el-row>
      <div
        class="flex flex1"
        style="margin: 10px; padding: 10px; margin-top: 0px; overflow: auto"
      >
        <!-- 表单信息 -->
        <el-row
          v-show="currentPage === 'formLabel'"
          type="flex"
          style="width: 100%"
        >
          <slot />
        </el-row>
        <!-- 流程图 -->
        <el-row
          v-if="currentPage === 'diagramLabel'"
          style="width: 100%; height: 100%; display: flex"
          :style="!this.$route ? { height: '500px' } : {}"
        >
          <flow-chart
            :procDefKey="urlParams.procDefKey"
            :procInstId="urlParams.procInstId"
            :urlParams="urlParams"
            :styles="!this.$route ? { height: '100% !important' } : {}"
          />
        </el-row>
        <!-- 审批记录 -->
        <el-row
          v-if="currentPage === 'commentLabel'"
          style="width: 100%; height: 100%; display: flex"
        >
          <el-col :span="24">
            <task-comment
              :key="urlParams.procInstId"
              :procInstId="urlParams.procInstId"
              :urlParams="urlParams"
            ></task-comment>
          </el-col>
        </el-row>
      </div>
      <label
        class="page-close-box"
        :style="{ background: $BPMN.theme() }"
        v-if="closeBtn"
      >
        <el-button type="text" @click="handlerClose()" icon="el-icon-close" />
      </label>
    </div>
    <taskCommit
      ref="taskCommitRef"
      :operation="operation"
      @handleActionCallback="handleActionCallback"
    >
      <template #customFields="slotProps">
        <slot name="customFields" v-bind="slotProps"></slot>
      </template>
    </taskCommit>
  </div>
</template>

<script>
import TaskCommit from "./TaskCommit.vue";
import TaskComment from "./TaskComment.vue";
import {HandelTaskApi} from "sn-bpm-v3";
import SelectPrintTemplate from "./SelectPrintTemplate.vue";
export default {
  name: "FlowPageContainer",

  components: { TaskComment, TaskCommit },
  props: {
    closeBtn: {
      type: Boolean,
      default: true,
    },
    approvalOption: {
      default: {},
    },
    approvalOptionCallback: {
      default: () => {},
    },
  },
  data() {
    return {
      currentPage: "formLabel",
      isInAlert: false,
      operation: {},
      urlParams: {
        procInstId: "",
        procDefKey: "",
        taskId: "",
        taskFlag: 0,
        businessTitle: "",
        businessKey: "",
        procInstInitiatorName: "",
        copyMsgId: "",
      },
      taskInfo: {
        procDefId: "",
        procDefKey: "",
        procDefName: "",
        taskKey: "",
        taskName: "",
        formConfig: {},
        taskActionList: [],
        variableList: [],
        firstAct: true,
      },
      isShowApprovalList: false,
      isShowFlowDiagram: false,
      approvalOptionData: {},
      open: false,
    };
  },
  mounted() {
    if (
      process.env.NODE_ENV == "development" &&
      window.location.host == "localhost:5172"
    ) {
      this.urlParams = this.parseUrlParams();
      this.initTaskInfo();
    }

    this.approvalOptionFn(this.approvalOption)

    //this.urlParams.procInstId = '138718435641278464'
    if (!this.$route) {
      //  console.log('this.$route.query', this.$route.query)
      // if (JSON.stringify(this.$route.query) == "{}") {
      this.isInAlert = true;
      this.$emit("approvalOptionCallback", this.approvalOptionFn);
      //}
    }
  },
  watch: {
    "$route.query": {
      handler(newVal) {
        // console.log(newVal);
        // this.urlParams = {
        //   taskId: "197295080134942722",
        //   procInstId: "197295079744872448",
        //   procInstInitiatorName: "",
        //   businessTitle: "snpit发起的mubanlist:2025-06-28",
        //   taskFlag: 1,
        //   businessKey: "1938784466687414273",
        // };
        //  this.initTaskInfo();
        if (newVal && JSON.stringify(newVal) !== "{}") {
          this.urlParams = newVal;
          this.initTaskInfo();
          if (!newVal?.taskId && !newVal?.procInstId && !newVal?.procDefKey) {
            this.currentPage = "formLabel";
          }
        } else {
          this.currentPage = "formLabel";
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    parseUrlParams() {
      const params = {};
      const queryString = window.location.search.substring(1);
      const pairs = queryString.split("&");

      for (const pair of pairs) {
        if (!pair) continue;
        const eqIndex = pair.indexOf("=");
        const key = eqIndex > -1 ? pair.substr(0, eqIndex) : pair;
        const value = eqIndex > -1 ? pair.substr(eqIndex + 1) : "";
        const decodedKey = decodeURIComponent(key.replace(/\+/g, " "));
        const decodedValue = decodeURIComponent(value.replace(/\+/g, " "));

        if (decodedKey in params) {
          Array.isArray(params[decodedKey])
            ? params[decodedKey].push(decodedValue)
            : (params[decodedKey] = [params[decodedKey], decodedValue]);
        } else {
          params[decodedKey] = decodedValue;
        }
      }
      return params;
    },
    approvalOptionFn(data) {
      console.log(data);
      this.approvalOptionData = data;
      this.urlParams.procInstId = this.approvalOptionData?.procInstId;
      if (this.approvalOptionData?.procInstId) {
        if (this.approvalOptionData?.isShowApprovalList) {
          //是否展示审批记录页面
          this.isShowApprovalList = true;
        }
        if (this.approvalOptionData?.isShowFlowDiagram) {
          //是否展示流程图
          this.isShowFlowDiagram = true;
        }
      } else {
        this.isShowFlowDiagram = false;
        this.isShowApprovalList = false;
      }
    },
    /**
     * 获取任务相关信息
     */
    async initTaskInfo() {
      let res = { data: {} },
        expoRes = { data: {} };
      if (this.urlParams) {
        if (
          this.urlParams?.taskFlag == 1 &&
          (this.urlParams?.taskId || this.urlParams?.procInstId)
        ) {
          //待办  获取流程运行时指定任务的信息
          res = await HandelTaskApi.viewRuTaskInfo({
            taskId: this.urlParams?.taskId,
            procInstId: this.urlParams?.procInstId,
          });
        }
        if (this.urlParams?.procInstId && this.urlParams?.taskFlag != 1) {
          if (this.urlParams?.taskFlag == 5) {
            res = await HandelTaskApi.viewArchHiTaskInfo({
              procInstId: this.urlParams.procInstId,
              taskId: this.urlParams.taskId,
              year: this.urlParams.year,
            });
          } else {
            //已办 历史 待阅 已阅 我的申请 。。。 获取流程历史任务的信息
            res = await HandelTaskApi.viewHiTaskInfo({
              procInstId: this.urlParams.procInstId,
              taskId: this.urlParams.taskId,
            });
          }
        }

        if (!this.urlParams.procInstId && this?.urlParams?.procDefKey) {
          //判断首环节 获取首环节任务信息
          res = await HandelTaskApi.viewInitialTaskInfo({
            procDefKey: this.urlParams.procDefKey,
          });
        }
        try {
          // 获取节点扩展信息, 在代办,已办, 历史任务, 传阅的详情/办理中调用
          expoRes = await HandelTaskApi.viewActDefExtInfo({
            procDefId: res?.data?.procDefId,
            actKey: res?.data?.taskKey,
          });
        } catch (error) {}
      }

      if (res?.data) {
        let taskInfo = { ...res?.data };
        if (expoRes?.data) {
          this.taskInfo = {
            ...taskInfo,
            ...expoRes?.data,
          };
        } else {
          this.taskInfo = {
            ...taskInfo,
          };
        }
      }

      //流程任务参数回传

      if (!this.$attrs.onInitData) {
        this.$message.error("业务页面未实现initData方法，请确认！");
      } else {
        this.$emit(
          "initData",
          this.urlParams,
          this.taskInfo,
          this.handlerClose
        );
      }
    },
    getButtonType(type) {
      switch (type) {
        case this.FlowActionType.START:
        case this.FlowActionType.AGREE:
        case this.FlowActionType.TURN:
        case this.FlowActionType.DELEGATE:
        case this.FlowActionType.COPYTO:
        case this.FlowActionType.MULYSIGN:
        case this.FlowActionType.MULTSIGNAGREE:
        case this.FlowActionType.MULTISIGNADD:
        case this.FlowActionType.MULITSIGNSUB:
          return "primary";
        case this.FlowActionType.SAVE:
        case this.FlowActionType.SAVESTART:
          return "success";
        case this.FlowActionType.REFUSE:
        case this.FlowActionType.MULTI_SIGN_REFUSE:
        case this.FlowActionType.MULTSIGNABSTAIN:
          return "warning";
        case this.FlowActionType.REJECT:
        case this.FlowActionType.REJECTTOPREV:
        case this.FlowActionType.REJECTTOSTART:
        case this.FlowActionType.REJECTTOTASK:
        case this.FlowActionType.REVOKE:
        case this.FlowActionType.RECALL:
          return "danger";
        default:
          return "default";
      }
    },
    /**
     * 执行流程操作
     */
    handlerAction(action) {
      let preHandlerActionParams = {
        ...action,
        procDefId: this.taskInfo.procDefId,
        procDefKey: this.taskInfo.procDefKey,
        taskKey: this.taskInfo.taskKey,
        taskId: this.urlParams.taskId,
        procInstId: this.urlParams.procInstId,
        approvalOption: this.approvalOptionData,
      };
      console.log("actionactionaction===>", action);
      this.$emit("getHandlerActionType", action.type);
      console.log("preHandlerActionParams===>", preHandlerActionParams);
      this.operation = { ...preHandlerActionParams };
      // 打开办理弹窗
      // console.log("this.$refs.taskCommitRef===>",this.$refs.taskCommitRef);
      this.$refs.taskCommitRef.openModal(true);
    },
    handlerActionSubmit(params,assigneeType,hugeFlag) {
      console.log("handlerActionSubmit走了这个")
      let preHandlerActionParams = {
        type: "agree",
        label: "通过",
        procDefId: params.procDefId,
        procDefKey: params.procDefKey,
        taskKey: params.taskKey,
        taskId: params.taskId,
        procInstId: params.procInstId,
        approvalOption: this.approvalOptionData,
        appointNextAssigneeType: assigneeType,
        hugeFlag
      };
      this.$emit("getHandlerActionType", "agree");
      this.operation = { ...preHandlerActionParams };
      // 打开办理弹窗
      this.$refs.taskCommitRef.openModal(true);
    },
    handleActionCallback(res) {
      let action = this.operation;
      console.log("handleActionCallback===>", res);
      if (res) {
        //  打印按钮处理
        if (action.type == this.FlowActionType.PRINT) {
          if (!this.$attrs.onHandlerPrint) {
            this.$message.error("当前流程并未实现此功能，请联系管理员！");
          } else {
            HandelTaskApi.getPrintTemplateList({
              procInstId: this.urlParams.procInstId,
              taskKey: this.taskInfo.taskKey,
            }).then((res) => {
              if (!res.data || res.data?.length == 0) {
                this.$message.error("当前流程没有打印模版，请联系管理员！");
                return;
              }
              if (res.data.length > 1) {
                res.data.forEach((item, index) => {
                  item.index = index + 1;
                });
                this.$DialogForm.show({
                  content: SelectPrintTemplate,
                  title: "选择打印模版",
                  data: { flowPrintTemplateTabelData: res.data },
                  width: "60%",
                  callback: (r) => {
                    if (r.type) {
                      if (r.dialogRefs) {
                        this.$emit("handlerPrint", {
                          ...r,
                          templateId: r.dialogRefs.templateId,
                        });
                      }
                    } else {
                      r.close();
                    }
                  },
                });
              } else {
                this.$emit("handlerPrint", { templateId: res.data[0].id });
              }
            });
          }
          return;
        }
        if (!this.$attrs.onHandlerAction) {
          this.$message.error("当前流程并未实现此功能，请联系管理员！");
        } else {
          this.$emit(
            "handlerAction",
            action,
            res,
            this.handlerClose,
            this.operation
          );
        }
      } else {
        res.close();
      }
    },
    handlerReadAction() {
      HandelTaskApi.submitReadComment({
        copyMsgId: this.urlParams.copyMsgId,
        comment: "已阅",
      }).then(() => {
        this.$modal.msgSuccess("已阅!");
      });
    },
    // 关闭流程处理
    handlerClose() {
      this.$router.go(-1);
    },
  },
};
</script>
<style scoped>
.task-title {
  display: flex;
  justify-content: space-between;
  padding-bottom: 5px;
  margin-top: 5px;
  margin-bottom: 10px;
  min-height: 30px;
}

.task-title .text {
  height: 28px;
  line-height: 28px;
  font-weight: 600;
  font-size: 16px;
  color: #383838;
  margin-left: 10px;
}

.task-title .el-tag {
  margin-left: 10px;
}

.flow-task {
  overflow: hidden;
  display: flex;
  flex: 1;
  flex-direction: column;
}

.page-close-box {
  position: absolute;
  transform: rotate(45deg);
  height: 50px;
  width: 50px;
  right: -25px;
  top: -25px;
  text-align: center;
}

.page-close-box .el-button {
  transform: rotate(-45deg);
  color: white;
  margin-top: 20px;
}

.bgW {
  background: #fff;
}

.bgG {
  background-color: #f1f2f6;
}

::v-deep .sp-dialog .el-dialog,
.sp-dialog-full .el-dialog {
  min-height: 80vh;
}
</style>
