<template>
  <project-document-storage-ui-table
      ref="childRef"
      :type="type"
      :relevantId="relevantId"
      :isPageSearch="false"
      :isDeleteMinio = "isDeleteMinio"
      :isHasAi = "isHasAi"
      @on-add-data="onAddData"
      @on-ai-review="onAiReview"
      @on-preview="onPreview"
      @on-delete="onDelete"
      :file-serial-number-builder="fileSerialNumberBuilder"
      :preview-config="previewConfig"
      :isShowAddBtn="isShowAddBtn"
      :isShowDelBtn="isShowDelBtn"
      :isShowPreviewBtn="isShowPreviewBtn"
      :isShowDownloadBtn="isShowDownloadBtn"
      :isShowLinkBtn="isShowLinkBtn"
      :isShowDownloadBatchBtn="isShowDownloadBatchBtn"
      :isBatchUpload="isBatchUpload"
      :fileAccept="fileAccept"
      :fileMaxSize="fileMaxSize"
  ></project-document-storage-ui-table>
  <el-button @click="getListData">获取数据</el-button>
  <el-button @click="onListen">监听</el-button>
  <el-button @click="onListenAddBtn">监听添加按钮显示</el-button>
  <el-button @click="getRel">getRel</el-button>
</template>

<script setup>
import IndexComponent from './index.vue'
import {
  ref,
  getCurrentInstance
} from 'vue';
const {
  proxy
} = getCurrentInstance()
const type = ref("inspection_and_rectification")
const relevantId = ref("2222")
const isDeleteMinio = ref(true)
const isHasAi = ref(true)
const column = ref([])
const queryForm = ref({
  page: {
    pageSize: 2,
    pageNum: 1,
    total: 0
  },
  filter: {

  }
})

const childRef = ref(null);
const previewConfig = ref({
  // 是否外置预览,必传
  isExternalPreview: false,
  // 内置预览返回的替换字符,akey代表要替换的字符串，avalue代码替换的字符串
  substitute: {
      "akey":"avalue"
  },
  // 内置预览服务地址
  previewServerUrl: "http://127.0.0.1:8012/onlinePreview",
})
const isShowAddBtn = ref(true)
const isShowDelBtn = ref(true)
const isShowPreviewBtn = ref(true)
const isShowDownloadBtn = ref(true)
const isShowLinkBtn = ref(true)
const isShowDownloadBatchBtn = ref(true)
const isBatchUpload = ref(true)
const fileAccept = ref(null) // 上传的文件类型,传null为全部例子,格式请参考平台组的sn-upload的accept属性
const fileMaxSize = ref(200000000000)

function onAddData(list) {
  console.log(list,"onAddData-sssss")
}

function onAiReview(row) {
  console.log(row,"onAiReview-sssss")
}

function onPreview(row) {
  console.log(row,"onPreview-sssss")
}

function onDelete(list) {
  console.log(list,"onDelete-sssss")
}

function getListData() {
  if(childRef.value){
    let list = childRef.value.getListData()
    console.log(list,"组件数据。。。。。。。。。")
  }
}

function onListen() {
  relevantId.value = "222"
}

function onListenAddBtn() {
  isShowAddBtn.value = !isShowAddBtn.value
}

function getRel() {
  console.log(relevantId.value)
}

// 文件序列号生成函数
function fileSerialNumberBuilder() {
  return "TEST" + Math.floor(Math.random()*10000)
}




</script>

<style scoped lang="scss">

</style>
