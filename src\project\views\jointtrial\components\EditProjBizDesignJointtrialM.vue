<template>
  <el-form :model="formData" :rules="formRules" ref="formRef" label-width="100px" label-position="right" :disabled="type == 'view'">
    <el-row :gutter="16" :span="24">
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>基本信息
          </legend>
          <el-row :gutter="16" :span="24">
            <el-col :span='12'>
              <el-form-item label="会审记录编号" prop="jointtrialNumber" style="width: 100%; white-space: nowrap;">
<!--                <el-input v-model="formData.jointtrialNumber" type="text" :disabled="dialogdisabled" placeholder="请输入会审记录编号" @blur="checkjointtrialNumber()"  clearable>-->
                  <el-input v-model="formData.jointtrialNumber" type="text" :disabled="dialogdisabled" placeholder="请输入会审记录编号" :maxlength="20" clearable>
                  </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='12'>
              <el-form-item label="会审时间" prop="jointtrialTime">
                <el-date-picker type="date" v-model="formData.jointtrialTime" format="YYYY-MM-DD" value-format="YYYY-MM-DD" style="width: 100%;"  placeholder="请选择" :disabled="dialogdisabled"clearable></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="16" :span="24">
            <el-col :span='24'>
              <el-form-item label="备注" prop="remark" style="margin-bottom: 18px;">
                <el-input type="textarea" v-model="formData.remark" rows="3" placeholder="请输入备注" :disabled="dialogdisabled"clearable></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </fieldset>
      </el-card>

      <el-card class="box-card" style="width: 100%;">
        <!--        <fieldset class="fieldset2">-->
        <legend>
          <span class="el-button--primary"></span>卷册信息
        </legend>
        <el-row :gutter="16" :span="24">
          <sn-crud :data="formData.projBizDesignJointtrialVolumeSDtoList" :option="optionj" v-model:search="queryForm.filter" @on-load="getPageList" @search-change="onChangeSearch" @search-reset="onResetSearch" @addBtnHandle="onEditDataj" @row-del="(row, index) => {subDelRow(row, index, 'projBizDesignJointtrialVolumeSDtoList');}">
            <template #menu="{ row, index, size }">
              <!--                <el-button type="primary" :size="size" icon="el-icon-edit" link @click="onEditData(row)">编辑</el-button>-->
<!--              <el-button type="danger" :size="size" icon="el-icon-delete" link @click="onDelDataj([row])" :disabled="dialogdisabled">删除</el-button>-->
<!--              <el-button type="danger" :size="size" icon="el-icon-delete" link @click="subDelRow(row, index, 'projBizDesignJointtrialVolumeSDtoList')" :disabled="dialogdisabled">删除</el-button>-->
            </template>
            <template #empty>
              <div>无数据</div>
            </template>
          </sn-crud>
        </el-row>
        <!--        </fieldset>-->
      </el-card>

      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>会审意见
          </legend>
          <el-row :gutter="16" :span="24">
            <sn-crud :data="formData.projBizDesignJointtrialIdeaSDtoList" :option="projBizDesignJointtrialIdeaSOption" @row-save="subRowSave" @row-update="subRowUpdate" @row-del="(row, index) => {subDelRow(row, index, 'projBizDesignJointtrialIdeaSDtoList');}">
              <template #empty>
                <div>无数据</div>
              </template>
            </sn-crud>
            <el-button @click="subAddRow('projBizDesignJointtrialIdeaSDtoList')" type="primary" plain style="display: block; width: 100%; margin-top: 10px" :disabled="dialogdisabled">新增一行数据</el-button>
          </el-row>
        </fieldset>
      </el-card>


      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>附件
          </legend>
          <project-document-storage-ui-table
              ref="childRef"
              :type="fileType"
              :relevantId="relevantId"
              :isPageSearch="false"
              :isDeleteMinio = "isDeleteMinio"
              :isHasAi = "isHasAi"
              @on-add-data="onAddfileData"
              @on-ai-review="onfileAiReview"
              @on-preview="onfilePreview"
              @on-delete="onfileDelete"
              :file-serial-number-builder="fileSerialNumberBuilder"
              :preview-config="previewConfig"
              :isShowAddBtn="type !== 'show'"
              :isShowDelBtn="type !== 'show'"
              :isShowPreviewBtn="isShowPreviewBtn"
              :isShowDownloadBtn="type !== 'show'"
              :isShowLinkBtn="false"
              :isShowDownloadBatchBtn="type !== 'show'"
          ></project-document-storage-ui-table>
        </fieldset>
      </el-card>

      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>单据信息
          </legend>
          <el-row :gutter="16" :span="24">
            <el-col :span='8'>
              <el-form-item label="创建人" prop="createName">
                <el-input v-model="formData.createName" type="text" placeholder="" :disabled="true" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="创建时间" prop="createTime">
                <el-input v-model="formData.createTime" type="text" placeholder="" :disabled="true" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <!--              <el-form-item label="单据状态" prop="docStatus">-->
              <!--                <el-input v-model="formData.docStatus" type="text" placeholder="" :disabled="true" clearable>-->
              <!--                </el-input>-->
              <!--              </el-form-item>-->
              <el-form-item label="单据状态" prop="docStatus">
                <!--                <el-input v-model="formData.docStatus" type="text" placeholder="请输入" :disabled="true" clearable>-->
                <!--                </el-input>-->
                <el-select v-model="formData.docStatus" disabled placeholder="" clearable>
                  <el-option v-for="(item,index) in statusOption" :key="index" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="最近修改人" prop="updateName">
                <el-input v-model="formData.updateName" type="text" placeholder="" :disabled="true" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="最近修改时间" prop="updateTime">
                <el-input v-model="formData.updateTime" type="text" placeholder="" :disabled="true" clearable>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </fieldset>
      </el-card>
    </el-row>
  </el-form>
</template>

<script setup>
import ProjBizDesignJointtrialMApi from '@/project/api/jointtrial/ProjBizDesignJointtrialM.js'
import EditProjBizDesignIntentionsvolume from "@/project/views/design/components/EditProjBizDesignIntentionsvolume";
import { kkFileViewUrl } from "@/config";
import {
  nextTick,
  onMounted,
  ref,
  reactive,
  toRef,
  defineProps,
  defineExpose,
  getCurrentInstance
} from 'vue';
import {
  useRoute,
  useRouter
} from 'vue-router'
import store from "@/store";
import {getCurrentFormattedTime} from "@/common/utils/datetime";
import ProjBizDesignIntentionMApi from "@/project/api/design/ProjBizDesignIntentionM";
const {
  proxy
} = getCurrentInstance()
const route = useRoute();
const router = useRouter();
const props = defineProps({
  data: Object,
});
const formRef = ref()
const type = toRef(props.data?.type);
let formData = ref({
  jointtrialNumber: "",
  jointtrialTime: "",
  remark: "",
  projBizDesignJointtrialIdeaSDtoList: [],
  projBizDesignJointtrialVolumeSDtoList: [],
  createName: "",
  createTime: "",
  docStatus: "0",
  projectId: sessionStorage.getItem('projectId'),
  updateName: "",
  volumeNum: "", // 用于存储卷册号的字符串
  major: "", // 用于存储专业的字符串
  volumeName: "", // 用于存储卷册名称的字符串
  fileList: [], // 👈 补上文件信息
  updateTime: ""
});
let formRules = ref({
  jointtrialNumber: [
    { required: true, message: '会审记录编号不能为空', trigger: 'blur' }
  ],
  jointtrialTime: [
    { required: true, message: '会审时间不能为空', trigger: 'blur' }
  ],
  remark:[
    { required: true, message: '备注内容不能为空', trigger: 'blur' }
  ],
  createName: [],
  createTime: [],
  docStatus: [],
  updateName: [],
  updateTime: []
});
//处理附件上传相关方法---------begin
const fileType = ref("designJointtrial")//文件类型记得在后台配置-表名
const relevantId = toRef(props.data?.id)

const previewConfig = ref({
  // 是否外置预览,必传
  isExternalPreview: false,
  // 内置预览返回的替换字符,akey代表要替换的字符串，avalue代码替换的字符串
  substitute: {
    "akey":"avalue"
  },
  // 内置预览服务地址
  // previewServerUrl: "http://10.191.64.191:8012/onlinePreview",
  // 内置预览服务地址
  previewServerUrl: kkFileViewUrl,
})
function onAddfileData(list) {
  console.log(list,"onAddData-sssss")
  console.log(childRef.value.getListData(),"onAddData-ccccccc")
}

function onAifileReview(row) {
  console.log(row,"onAiReview-sssss")
}

function onfilePreview(row) {
  console.log(row,"onPreview-sssss")
}

function onfileDelete(list) {
  console.log(list,"onDelete-sssss")
}
const childRef = ref(null);
function getfileListData() {
  if(childRef.value){
    let list = childRef.value.getListData()
    console.log(list,"组件数据。。。。。。。。。")
  }
}
// 文件序列号生成函数
function fileSerialNumberBuilder() {
  return "file" + Math.floor(Math.random()*10000)
}
const getAttachmentList = () => {
  // 假设子组件有 getListData 方法
  return childRef.value?.getListData() || [];
};
//处理附件上传相关方法---------end

let optionj = ref({
  tip: false,
  dialogType: "dialog",
  border: true,
  index: true,
  stripe: true,
  menu: true,
  header: true,
  height: "auto",
  searchSpan: 6,
  searchIcon: true,
  searchMenuSpan: 6,
  searchIndex: 3,
  selection: true,
  showTree: false,
  excelBtn: false,
  delBtn: true,//管理卷册表格增加删除按钮
  delBtns: false,            // 👈 显式隐藏批量删除按钮
  editBtn: false,
  delBtnText: '删除',//管理卷册表格增加删除按钮提示
  delBtnsText: "批量删除",
  addBtnText: "关联卷册",
  customEditBtnText: "编辑",
  customDelBtnText: "删除",
  customViewBtnText: "查看",
  column: [{
    label: "专业",
    prop: "major",
    columnSlot: false,
    searchSlot: false
  }, {
    label: "卷册号",
    prop: "volumeNum",
    columnSlot: false,
    searchSlot: false
  }, {
    label: "卷册名称",
    prop: "volumeName",
    columnSlot: false,
    searchSlot: false
  }]
});
//输入框是否禁止输入
let dialogdisabled = ref(false)
let listvolumeData = ref([]);//存储选中的卷册信息
let listData = ref([]);
let queryForm = ref({
  page: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  filter: {
    createTime: null
  }
});
//单据的发布未发布状态
let statusOption = ref([{
  label: "草稿",
  value: "0"
}, {
  label: "已发布",
  value: "1"
}]);
let delRowData = ref({});
//会审意见表单数据
let projBizDesignJointtrialIdeaSOption = ref({
  tip: false,
  border: true,
  index: true,
  stripe: true,
  menu: true,
  header: false,
  menuType: "text",
  addBtn: true,
  addBtnText: "新增",
  editBtn: true,
  editBtnText: "编辑",
  delBtn: true,
  delBtnText: "删除",
  cellBtn: true,
  maxHeight: "200px",
  column: [{
    label: "图号",
    prop: "figureNumber",
    type: "input",
    cell: true
  }, {
    label: "会审意见",
    prop: "jointHearingIdea",
    type: "input",
    cell: true
  }, {
    label: "处理意见",
    prop: "disposeIdea",
    type: "input",
    cell: true
  }]
});

function getPageList() {
  //查询分页列表
  // 查询前处理参数
  const params = handleQueryForm();
  ProjBizDesignJointtrialMApi.pageList(params).then((res) => {
    listData.value = res.data.dataList;
    queryForm.value.page.total = res.data.totalCount;
  });
  //判断是否为查看进来，是则禁用输入框及按钮
  if(type.value==='show'){
    dialogdisabled = true
    optionj.value.addBtn = false//如果是查看进来则隐藏这个按钮 关联卷册
    projBizDesignJointtrialIdeaSOption.value.delBtn = false
    projBizDesignJointtrialIdeaSOption.value.editBtn = false
  }
}

function onResetSearch() {
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
}

function handleQueryForm() {
  // 处理参数
  const {
    pageSize,
    pageNum
  } = queryForm.value.page;
  const filter = {}
  for (let key in queryForm.value.filter) {
    if (queryForm.value.filter[key]?.length) {
      // sn-curd搜索select会生成$prop的变量
      if (key.startsWith("$")) continue;
      filter[key] = queryForm.value.filter[key];
    }
  }
  delete filter.createTime;
  if (Array.isArray(queryForm.value.filter.createTime) && queryForm.value.filter.createTime?.length === 2) {
    filter["beginCreateTime"] = queryForm.value.filter.createTime[0];
    filter["endCreateTime"] = queryForm.value.filter.createTime[1];
  }
  const searchParams = {
    page: {
      pageSize,
      pageNum,
    },
    filter,
  }
  return searchParams
}

function onChangeSearch(params, done) {
  //搜索按钮操作
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
  done && done();
}
//关联卷册按钮操作
//定义一个数组用于接收关联卷册返回的数据
let existingData = []
async function onEditDataj(row) {
  //编辑,新增按钮操作
  let editType = row ? "edit" : "add";
  let rowInfo = await (editType !== "add" ? ProjBizDesignJointtrialMApi.view(row.id) : {});
  const formData = editType !== "add" ? rowInfo.data : rowInfo;
  existingData =  getFormData().projBizDesignJointtrialVolumeSDtoList
  //编辑,新增按钮操作
  proxy.$DialogForm.show({
    title: editType == "edit" ? "编辑" : "关联卷册",
    type: optionj.value.dialogType,
    width: "80%",
    content: EditProjBizDesignIntentionsvolume,
    data: {
      formData: formData,
      type: editType,
      existingData: existingData // 当前已关联的卷册数据
    },
    callback: (res) => {
      if (res.type && res.type !== 'close') {
        res.dialogRefs.submitData();
        // listvolumeData.value = res.dialogRefs.sData;
        // //获取formdata
        // const formData = getFormData();
        // formData.projBizDesignJointtrialVolumeSDtoList = listvolumeData.value

        //获取formdata
        const formData = getFormData();
        if(type.value==='add'){
          listvolumeData.value = res.dialogRefs.sData;
          formData.projBizDesignJointtrialVolumeSDtoList=listvolumeData.value
        }else{
          const oldTeamIds = new Set(formData.projBizDesignJointtrialVolumeSDtoList.map((item) =>item.volumeNum));
          res.dialogRefs.sData.value.forEach(item => {
            if (!oldTeamIds.has(item.volumeNum)) {
              formData.projBizDesignJointtrialVolumeSDtoList.push({
                major: item.major,
                volumeNum: item.volumeNum,
                volumeName: item.volumeName
              })
            }
          })
          listvolumeData.value = formData.projBizDesignJointtrialVolumeSDtoList;
        }
        // 获取已关联的卷册数据
        existingData = formData.projBizDesignJointtrialVolumeSDtoList
        res.close();
      } else {
        // formData.projBizDesignJointtrialVolumeSDtoList = existingData
        res.close();
      }
    }
  });
}

async function onEditData(row) {
  //编辑,新增按钮操作
  let editType = row ? "edit" : "add";
  let rowInfo = await (editType !== "add" ? ProjBizDesignJointtrialMApi.view(row.id) : {});
  const formData = editType !== "add" ? rowInfo.data : rowInfo;
  //编辑,新增按钮操作
  proxy.$DialogForm.show({
    title: editType == "edit" ? "编辑" : "新增",
    type: option.value.dialogType,
    width: "80%",
    content: EditProjBizDesignJointtrialM,
    data: {
      formData: formData,
      type: editType,
    },
    callback: (res) => {
      if (res.type && res.type !== 'close') {
        if (res.dialogRefs) {
          res.dialogRefs.submitData().then((flag) => {
            if (flag) {
              getPageList();
              res.close();
            }
          });
        }
      } else {
        res.close();
      }
    }
  });
}

function onDelData(rows) {
  //删除行
  if (!rows || rows.length == 0) {
    proxy.$message.info("请勾选数据！");
    return false;
  }
  let ids = rows.map((item) => {
    return item.id;
  });
  proxy.$modal.confirm("确认删除数据项？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    ProjBizDesignJointtrialMApi.remove(ids).then((res) => {
      proxy.$message.success("已删除");
      getPageList();
    });
  }).catch(() => {});
}

function subRowSave(form, done) {
  //编辑行
  done();
}

function subAddRow(name) {
  //新增一行
  if (formData.value[name]) {
    formData.value[name].push({
      $cellEdit: true,
    });
  } else {
    let arr = new Array();
    arr.push({
      $cellEdit: true
    });
    formData.value[name] = arr
  }
}

function subRowUpdate(form, index, done, loading) {
  //编辑行
  done();
}
//子表的逻辑删除方法
function subDelRow(row, index, name) {
  //删除行
  if (row[0].id) {
    let data = JSON.parse(JSON.stringify(row[0]));
    if (delRowData.value[name]) {
      delRowData.value[name].push(Object.assign(data, {
        delFlag: 1,
      }));
    } else {
      delRowData.value[name] = [
        Object.assign(data, {
          delFlag: 1,
        }),
      ]
    }
  }
  formData.value[name].splice(index, 1);
}

function getFormData() {
  //获取formData数据
  for (let item in delRowData.value) {
    formData.value[item] = formData.value[item].concat(delRowData.value[item]);
  }
  formData.value.fileList = getAttachmentList();
  return formData.value;
}
formData = toRef(Object.keys(props.data?.formData || {}).length > 0 ? {
  ...props.data?.formData
} : toRef(formData.value));

//按钮的类型定义全局方便获取
let buttonType = ref();
function submitData(buttontype) {
  return new Promise((resolve) => {
    formRef.value.validate((flag) => {
      if (!flag) {
        return false;
      } else {
        buttonType.value = buttontype
        if (type.value === "add") {
          resolve(saveData());
        } else {
          resolve(editData());
        }
      }
    });
  });
}
//校验记录编号是否重复   目前后端校验了
async function checkjointtrialNumber() {
  const jointtrialNumber = formData.value.jointtrialNumber;
  if(type.value==='add') {
    // 调用后端接口，查询编号是否存在
    try {
      const params = {
        filter: {
          jointtrialNumber: jointtrialNumber
        },
        page: {
          pageSize: 1,
          pageNum: 1
        }
      };
      const res = await ProjBizDesignJointtrialMApi.pageList(params); // 确保等待异步请求完成
      console.log('res.data.dataList;==', res.data.dataList);
      if (res.data.dataList && res.data.dataList.length > 0) {
        // 如果编号已存在，提示用户
        proxy.$message.error("会审记录编号已存在，请重新输入！");
        formData.value.jointtrialNumber = "";
      }

    } catch (error) {
      console.error("校验会审记录编号时出错：", error);
      proxy.$message.error("校验会审记录编号时出错，请稍后再试！");
    }
  } else{
    console.log('编辑的',formData.value.id)
    try {
      const viewResult = await ProjBizDesignJointtrialMApi.view(formData.value.id); // 确保等待异步请求完成
      const viewData = viewResult.data;
      const existingNumber = viewData ? viewData.jointtrialNumber : null;
      console.log('existingNumber==', existingNumber);
      console.log('编辑判断结果',existingNumber!==jointtrialNumber)
      if(existingNumber!==jointtrialNumber){
        const params = {
          filter: {
            jointtrialNumber: jointtrialNumber
          },
          page: {
            pageSize: 1,
            pageNum: 1
          }
        };
        const res = await ProjBizDesignJointtrialMApi.pageList(params); // 确保等待异步请求完成
        if (res.data.dataList && res.data.dataList.length > 0) {
          // 如果编号已存在，提示用户
          proxy.$message.error("会审记录编号已存在，请重新输入！");
          formData.value.jointtrialNumber = "";
        }
      }
    } catch (error) {
      console.error("校验会审记录编号时出错：", error);
      proxy.$message.error("校验会审记录编号时出错，请稍后再试！");
    }
  }
}
function saveData() {
  //新增操作
  const formData1 = getFormData();
  if(listvolumeData.value.length !== 0){
    formData1.volumeNum = convertListToString(listvolumeData.value, 'volumeNum'); // 假设你想用卷册号
    formData1.major = convertListToString(listvolumeData.value, 'major'); // 假设你想用专业
    formData1.volumeName = convertListToString(listvolumeData.value, 'volumeName'); // 假设你想用卷册名称
    formData1.projBizDesignJointtrialVolumeSDtoList = listvolumeData.value;
  }
  if(buttonType.value==='submit'){
    formData1.docStatus = '1'
  }
  return ProjBizDesignJointtrialMApi.add(formData1).then((resp) => {
    if(resp.data){
      proxy.$message.success("保存成功");
      formData.value.id = resp.data.id;
      relevantId.value = resp.data.id;
      if(resp.data.projBizDesignJointtrialIdeaSDtoList===null||projBizDesignJointtrialVolumeSDtoList===null){
      }else{
        formData.projBizDesignJointtrialIdeaSDtoList = resp.data.projBizDesignJointtrialIdeaSDtoList
        formData.projBizDesignJointtrialVolumeSDtoList = resp.data.projBizDesignJointtrialVolumeSDtoList
      }
      type.value = 'edit'
      return true;
    }
  });
    return true;
}
//如果为选中为多个则中间中,号分割方便列表展示
function convertListToString(list, field) {
  if(type.value==='add'){
    return list.value.map(item => item[field]).join(',');
  }else{
    return list.map(item => item[field]).join(',');
  }
}
function editData() {
  //编辑操作
  const formData = getFormData();
  if(buttonType.value==='submit'){
    formData.docStatus = '1'
  }
  if(buttonType.value==='revoke'){
    formData.docStatus = '0'
  }
  if(listvolumeData.value.length !== 0){
    formData.volumeNum = convertListToString(existingData, 'volumeNum'); // 假设你想用卷册号
    formData.major = convertListToString(existingData, 'major'); // 假设你想用专业
    formData.volumeName = convertListToString(existingData, 'volumeName'); // 假设你想用卷册名称
    // 如果有选中的卷册数据，合并到formData中
    // 合并数据
    // formData.projBizDesignJointtrialVolumeSDtoList = formData.projBizDesignJointtrialVolumeSDtoList.concat(listvolumeData.value);
    return ProjBizDesignJointtrialMApi.update(formData).then((resp) => {
      if(buttonType.value==='save'){
        proxy.$message.success("保存成功");
        formData.projBizDesignJointtrialIdeaSDtoList = resp.data.projBizDesignJointtrialIdeaSDtoList
        formData.projBizDesignJointtrialVolumeSDtoList = resp.data.projBizDesignJointtrialVolumeSDtoList
        delRowData = ref({});
      }else{
        proxy.$message.success("发布成功");
      }
      return true;
    });
  }else{
    // 如果没有选中的卷册数据，使用formData中已有的数据，但需要过滤掉delFlag为1的项
    formData.volumeNum = formData.projBizDesignJointtrialVolumeSDtoList
        .filter(item => item.delFlag !== 1) // 过滤掉delFlag为1的项
        .map(item => item.volumeNum)
        .join(',');
    formData.major = formData.projBizDesignJointtrialVolumeSDtoList
        .filter(item => item.delFlag !== 1) // 过滤掉delFlag为1的项
        .map(item => item.major)
        .join(',');
    formData.volumeName = formData.projBizDesignJointtrialVolumeSDtoList
        .filter(item => item.delFlag !== 1) // 过滤掉delFlag为1的项
        .map(item => item.volumeName)
        .join(',');
    console.log('formdata==',formData)
    return ProjBizDesignJointtrialMApi.update(formData).then((resp) => {
      if(buttonType.value==='save'){
        proxy.$message.success("保存成功");
        formData.projBizDesignJointtrialIdeaSDtoList = resp.data.projBizDesignJointtrialIdeaSDtoList
        formData.projBizDesignJointtrialVolumeSDtoList = resp.data.projBizDesignJointtrialVolumeSDtoList
        delRowData = ref({});
      }else{
        proxy.$message.success("发布成功");
      }
      return true;
    });
  }

}
//卷册信息删除按钮
function onDelDataj(rows) {
  // 删除行
  // 确认删除操作
  proxy.$modal.confirm("确认删除数据项？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    // 从 formData 中移除选中的行
    formData.value.projBizDesignJointtrialVolumeSDtoList = formData.value.projBizDesignJointtrialVolumeSDtoList.filter(
        item => !rows.some(row => row.id === item.id)
    );

    proxy.$message.success("已删除");
  }).catch(() => {
    proxy.$message.info("取消删除");
  });
}
//初始化表单填充数据
function initializer(){
  if(type.value==='add'){
    formData.value.briefingPerson =  store.state.user && store.state.user.userInfo.userName ? store.state.user.userInfo.userName : null;//获取到登陆人
    formData.value.createName =  store.state.user && store.state.user.userInfo.userName ? store.state.user.userInfo.userName : null;//获取到登陆人
    formData.value.createTime = getCurrentFormattedTime();//获取到登录时间
  }
  //判断是否为查看进来，是则禁用输入框及按钮
  // console.log('getPageList.type==',type.value)
  // if(type.value==='show'){
  //   dialogdisabled = true
  // }
}
defineExpose({
  getFormData,
  submitData,
});
//初始化
onMounted(() => {
  initializer()
});
</script>

<style lang="scss" scoped>

</style>
