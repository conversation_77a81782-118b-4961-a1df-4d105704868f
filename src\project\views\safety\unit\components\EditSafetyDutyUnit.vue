<template>
  <el-form :model="formData" :rules="rules" ref="snForm" label-width="100px" label-position="right" :disabled="type == 'view'">
    <el-row :gutter="16" :span="24">
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <el-row :gutter="16" :span="24">
            <el-col :span='8'>
              <el-form-item label="业务编码" prop="businessCode">
                <el-input v-model="formData.businessCode" type="text" placeholder="请输入业务编码" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="目标年份" prop="projectYear">
                <el-input v-model="formData.projectYear" type="text" placeholder="请输入目标年份" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="项目主体" prop="projectBody">
                <el-select :disabled="type == 'view'" v-model="formData.projectBody" placeholder="请选择" clearable filterable >
                  <el-option v-for="(item, index) in orgData" :key="index" :label="item.name" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </fieldset>
      </el-card>
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>安全生产目标
          </legend>
          <el-row :gutter="16" :span="24">
            <sn-crud :data="formData.safetyProductionTargetDtoList" :option="safetyProductionTargetOption" @row-save="subRowSave" @row-update="subRowUpdate" @row-del="(row, index) => {subDelRow(row, index, 'safetyProductionTargetDtoList');}">
              <template #empty>
                <div>无数据</div>
              </template>
            </sn-crud>
            <el-button @click="subAddRow('safetyProductionTargetDtoList')" type="primary" plain style="display: block; width: 100%; margin-top: 10px">添 加</el-button>
          </el-row>
        </fieldset>
      </el-card>
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>附件信息
          </legend>
          <el-row :gutter="16" :span="24">
            <project-document-storage-ui-table
                ref="childRef"
                :type="fileType"
                :relevantId="relevantId"
                :isPageSearch="false"
                :isDeleteMinio = "isDeleteMinio"
                :isHasAi = "isHasAi"
                @on-add-data="onAddfileData"
                @on-ai-review="onfileAiReview"
                @on-preview="onfilePreview"
                @on-delete="onfileDelete"
                :file-serial-number-builder="fileSerialNumberBuilder"
                :preview-config="previewConfig"
                :isShowAddBtn="type !== 'view'"
                :isShowDelBtn="type !== 'view'"
                :isShowPreviewBtn="isShowPreviewBtn"
                :isShowDownloadBtn="type !== 'view'"
                :isShowLinkBtn="false"
                :isShowDownloadBatchBtn="type !== 'view'"
            ></project-document-storage-ui-table>
          </el-row>
        </fieldset>
      </el-card>
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>单据信息
          </legend>
          <el-row :gutter="16" :span="24">
            <el-col :span='8'>
              <el-form-item label="创建人" prop="createName">
                <el-input v-model="formData.createName" disabled type="text" placeholder="" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="创建时间" prop="createTime">
                <el-date-picker type="dateTime" style="width: 100%;" v-model="formData.createTime" disabled format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" clearable></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="单据状态" prop="status">
                <el-select v-model="formData.status" disabled placeholder="请选择单据状态" clearable>
                  <el-option v-for="(item,index) in statusOption" :key="index" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="最近修改人" prop="updateName">
                <el-input v-model="formData.updateName" type="text" disabled placeholder="" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="最近修改时间" prop="updateTime">
                <el-date-picker type="dateTime" v-model="formData.updateTime" style="width: 100%;" disabled format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" clearable></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
        </fieldset>
      </el-card>
    </el-row>
  </el-form>
</template>
<script setup>
import SafetyDutyUnitApi from '@/project/api/safety/SafetyDutyUnit.js'
import ProjBizProjectTeamOrgApi from '@/project/api/projectTeam/ProjBizProjectTeamOrg.js'
import {
  nextTick,
  onMounted,
  ref,
  reactive,
  toRef,
  defineProps,
  defineExpose,
  getCurrentInstance
} from 'vue';
import {
  useRoute,
  useRouter
} from 'vue-router'
import store from "@/store";
import {getCurrentFormattedTime} from "@/common/utils/datetime";
import FileApi from "sn-base-layout-vue3/packLayout/api/File";
import * as Base64 from "js-base64";
const {
  proxy
} = getCurrentInstance()
const route = useRoute();
const router = useRouter();
const props = defineProps({
  data: Object,
  // 文件序列号生成器,必传
  fileSerialNumberBuilder: {
    type: Function,
    required: true
  },
});
const snForm = ref()
const buttonType = ref()
const type = toRef(props.data?.type);
let formData = ref({
  id: null,
  businessCode: "",
  projectId: sessionStorage.getItem('projectId'),
  projectName: "",
  projectDept: "",
  projectYear: "",
  projectBody: "",
  safetyProductionTargetDtoList: [],
  fileDtoList: [],
  createName: store.state.user && store.state.user.userInfo.userName ? store.state.user.userInfo.userName : null,
  createTime: getCurrentFormattedTime(),
  status: "0",
  updateName: "",
  updateTime: ""
});
let rules = ref({
  businessCode: [{
    required: true,
    message: "业务编码不能为空"
  }],
  projectYear: [{
    required: true,
    message: "目标年份不能为空"
  }, {
    pattern: /(19|20)\d{2}$/,
    trigger: ["blur", "change"],
    message: "请输入正确的项目年份"
  }],
  projectBody: [{
    required: true,
    message: "项目主体不能为空"
  }],
  createName: [],
  createTime: [],
  status: [],
  updateName: [],
  updateTime: []
});
let delRowData = ref({});
let safetyProductionTargetOption = ref({
  tip: false,
  border: true,
  index: true,
  stripe: true,
  menu: type.value == 'view'? false:true,
  header: false,
  menuType: "text",
  addBtn: true,
  addBtnText: "新增",
  editBtn: true,
  editBtnText: "编辑",
  delBtn: true,
  delBtnText: "删除",
  cellBtn: true,
  maxHeight: "200px",
  column: [{
    label: "生产目标名称",
    prop: "targetName",
    type: "input",
    cell: true
  }]
});
let statusOption = ref([{
  label: "草稿",
  value: "0"
}, {
  label: "已发布",
  value: "1"
}]);
let Option = ref({
  tip: false,
  border: true,
  index: true,
  stripe: true,
  menu: true,
  header: false,
  menuType: "text",
  addBtn: false,
  addBtnText: "新增",
  editBtn: false,
  editBtnText: "预览",
  delBtn: false,
  delBtnText: "下载",
  cellBtn: false,
  maxHeight: "200px",
  column: [{
    label: "文件名称",
    prop: "fileName",
    type: "input",
    columnSlot: true,
    searchSlot: false,
    cell: true
  }, {
    label: "文件大小",
    prop: "fileSizeFormat",
    type: "input",
    columnSlot: true,
    searchSlot: false,
    cell: true
  }, {
    label: "上传人",
    prop: "createName",
    type: "input",
    columnSlot: false,
    searchSlot: false,
    cell: true
  }, {
    label: "上传时间",
    prop: "createTime",
    type: "date",
    columnSlot: false,
    searchSlot: false,
    cell: true
  }/*, {
    label: "AI检查结果",
    prop: "AIResult",
    type: "input",
    columnSlot: false,
    searchSlot: false,
    cell: true
  }*/]
});

//组织数据
let orgData = ref([]);

ProjBizProjectTeamOrgApi.queryOrgCompanyList({projectId : sessionStorage.getItem('projectId')}).then((res) => {
  orgData.value = res.data;
})

function subRowSave(form, done) {
  //编辑行
  done();
}

function subAddRow(name) {
  //新增一行
  if (formData.value[name]) {
    formData.value[name].push({
      $cellEdit: true,
    });
  } else {
    let arr = new Array();
    arr.push({
      $cellEdit: true
    });
    formData.value[name] = arr
  }
}

function subRowUpdate(form, index, done, loading) {
  //编辑行
  done();
}

function subDelRow(row, index, name) {
  proxy.$confirm('确认删除？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    //删除行
    if (row[0].id) {
      let data = JSON.parse(JSON.stringify(row[0]));
      if (delRowData.value[name]) {
        delRowData.value[name].push(Object.assign(data, {
          delFlag: 1,
        }));
      } else {
        delRowData.value[name] = [
          Object.assign(data, {
            delFlag: 1,
          }),
        ]
      }
    }
    formData.value[name].splice(index, 1);
  }).catch(() => {
    // 用户点击取消，不关闭弹窗
  });
}

function getFormData() {
  //获取formData数据
  for (let item in delRowData.value) {
    formData.value[item] = formData.value[item].concat(delRowData.value[item]);
  }
  return formData.value;
}
formData = toRef(Object.keys(props.data?.formData || {}).length > 0 ? {
  ...props.data?.formData
} : toRef(formData.value));

function submitData(btnType) {
  return new Promise((resolve) => {
    snForm.value.validate((flag) => {
      if (!flag) {
        return false;
      } else {
        buttonType.value = btnType
        if (type.value === "add") {
          if (childRef.value) {
            childRef.value.getListData().forEach((item) => {
              formData.value['fileDtoList'].push({
                fileId:item.fileId,
                fileName: item.fileName,
                fileSize: item.fileSize,
                createTime: item.createBy,
                fileSerialNumber: item.fileSerialNumber,
                fileSizeFormat: item.fileSizeFormat
              })
            })
          }
          resolve(saveData());
        } else {
          resolve(editData());
        }
      }
    });
  });
}

function saveData() {
  //新增操作
  const formData1 = getFormData();
  if (buttonType.value == 'submit') {
    formData1.status = '1'
  }
  return SafetyDutyUnitApi.add(formData1).then((res) => {
    if(res.data){
      proxy.$message.success("保存成功");
      formData.value.id = res.data.id;
      relevantId.value = res.data.id;
      formData.value.safetyProductionTargetDtoList = res.data.safetyProductionTargetDtoList
      type.value = "edit"
      return true;
    }
  });
}

function editData() {
  //编辑操作
  const formData1 = getFormData();
  if (buttonType.value == 'submit') {
    formData1.status = '1'
  }
  return SafetyDutyUnitApi.update(formData1).then((res) => {
    if(res.data){
      formData.value.safetyProductionTargetDtoList = res.data.safetyProductionTargetDtoList
      delRowData = ref({});
      proxy.$message.success("修改成功");
      return true;
    }
  });
}

function compileData(row,index, name) {
  formData.value[name][index].$cellEdit=true
}

const previewConfig = ref({
  // 是否外置预览,必传
  isExternalPreview: false,
  // 内置预览返回的替换字符,akey代表要替换的字符串，avalue代码替换的字符串
  substitute: {
    "akey":"avalue"
  },
  // 内置预览服务地址
  previewServerUrl: "http://10.191.64.191:8012/onlinePreview",
})

const fileType = ref("safety_duty_unit")
let relevantId = toRef(props.data?.id)

//处理附件上传相关方法---------begin
let listfileData = ref([]);
function onAddfileData(list) {
}

function onAifileReview(row) {
}

function onfilePreview(row) {
}

function onfileDelete(list) {
}
const childRef = ref(null);
function getfileListData() {
  if(childRef.value){
    let list = childRef.value.getListData()
  }
}
// 文件序列号生成函数
function fileSerialNumberBuilder() {
  return "file" + Math.floor(Math.random()*10000)
}
//处理附件上传相关方法---------end

defineExpose({
  getFormData,
  submitData,
});
</script>

<style lang="scss" scoped>

</style>
