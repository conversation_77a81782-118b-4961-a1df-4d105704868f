/*
 * @Author: 王云飞
 * @Date: 2024-12-20 09:57:02
 * @LastEditTime: 2025-05-30 16:37:21
 * @LastEditors: 王云飞
 * @Description:
 *
 */

import './public-path'
import { createApp } from 'vue'
import { createI18n } from 'vue-i18n'
import { registerComponents } from '@/common/components'
import App from '@/App.vue'
import LayoutView from '@/common/layoutView/LayoutIndex'
import router, { constantRoutes } from '@/router'
import store from '@/store'
import ElementPlus from 'element-plus'
import zhCn from 'element-plus/dist/locale/zh-cn'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import plugins from '@common/plugins'
import { loadView, readLocalSrcFiles } from '@/common/utils'
import { setLocalThemeMode, getLocalThemeMode } from '@/common/utils/chageTheme'
import { startQiankun, actionsQiankun } from '@/common/utils/qiankun'
import messages from './locale.js'
import {
  useStorePlugin,
  getToken,
  setAppName,
  parseTime,
  hasPermi,
  selectDictLabel,
  restPage,
  download,
  changeElTheme3,
  handleTree,
  getAppInfo,
  encodeBase64
} from 'sn-base-utils'
import SnBaseLayout from 'sn-base-layout'
import SnpitPlus from 'snpit-plus'
import 'snpit-plus/lib/snpit-plus.css'
import '@common/styles/element-plus-dark.css'
import 'element-plus/theme-chalk/src/index.scss'
import '@common/styles/index.scss'
import Bpmn from "sn-bpm-v3";
import 'sn-bpm-v3/lib/style.css'
import config, { isCloud } from '@/config.js'
import ProjectDocumentStorageUi from 'project_document_storage_ui'
// 自定义角色筛选
import FitterRole from '@common/components/fitterRole'
import permi from  "@common/components/exportFile/permi.js"

import '@common/assets/icons'
import '@/permission'
let routers = null
let app = null
let addRouter = false

app = createApp(App)
app.use(store)
app.use(router)
app.use(ProjectDocumentStorageUi)

app.use(
  SnBaseLayout,
  {
    routerStore: {
      router,
      constantRoutes,
      loadView
    },
    qiankunStore: {
      startQiankun: startQiankun.bind(null, app),
      actionsQiankun
    },
    envStore: {
      NODE_ENV: process.env.NODE_ENV,
      VUE_APP_APPLATION_NAME: process.env.VUE_APP_APPLATION_NAME,
      VUE_APP_BASE_API: process.env.VUE_APP_BASE_API
    },
    viewStore: {
      LayoutView
    }
  },
  readLocalSrcFiles
)
if(config.themeMode === 'DARK' && getLocalThemeMode() !== 'light') {
  setLocalThemeMode('dark')
}
// 处理暗黑主题 theme
const appInfo = getAppInfo()
if (appInfo.theme && appInfo.theme === 'DARK') {
  setLocalThemeMode('dark')
}
app.use(useStorePlugin, {
  config, // 可选, 项目基础配置
  noPermission: () => store.getters.superAdmin || false, // 可选, 项目基础配置
  axiosRequestBefore: (axiosConfig) => {
    // 处理租户
    const tenantId = localStorage.getItem('tenantId') || store.state.user.tenantId
    if (typeof tenantId === 'string') {
      axiosConfig.headers['X-Tenant-Id'] = tenantId
    }
    const appInfo = getAppInfo()
    if (appInfo && appInfo.appId) {
      axiosConfig.headers['X-Enos-AppId'] = appInfo.appId
      axiosConfig.headers['X-Enos-UserId'] = appInfo.id ? encodeBase64(appInfo.id) : null
    }
    // 处理 项目空间 Header 头
    const projectId = sessionStorage.getItem('projectId')
    if (projectId) {
      axiosConfig.headers['X-ProjectId'] = projectId
    }

  }
})
// 创建i18n实例
const i18n = createI18n({
  locale: 'en', // 默认语言
  fallbackLocale: 'en', // 回退语言
  messages // 语言包
})

function render(props = {}) {
  const { container } = props
  if (!app) {
    app = createApp(App)
    app.use(store)
    app.use(router)

    app.use(
      SnBaseLayout,
      {
        routerStore: {
          router,
          constantRoutes,
          loadView
        },
        qiankunStore: {
          startQiankun: startQiankun.bind(null, app),
          actionsQiankun
        },
        envStore: {
          NODE_ENV: process.env.NODE_ENV,
          VUE_APP_APPLATION_NAME: process.env.VUE_APP_APPLATION_NAME,
          VUE_APP_BASE_API: process.env.VUE_APP_BASE_API
        },
        viewStore: {
          LayoutView
        }
      },
      readLocalSrcFiles
    )
  }
  // 全局注册element-plus的图标
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
  }
  app.directive('permi2', permi)
  registerComponents(app)
  app.use(ElementPlus, {
    namespace: 'ep',
    locale: zhCn
  })

  // 全局覆盖主题色
  app.use(i18n)
  app.use(plugins)
  app.use(SnpitPlus, {
    prefix: isCloud
      ? `${process.env.VUE_APP_BASE_API}/${process.env.VUE_APP_APPLATION_NAME}`
      : process.env.VUE_APP_BASE_API,
    getToken: getToken,
    Instance: app,
    calcHeight: 0
  })
  app.use(Bpmn, {
    getToken,
    prefix: isCloud
      ? `${process.env.VUE_APP_BASE_API}/bpm`
      : process.env.VUE_APP_BASE_API,
    theme: () => store.getters.theme,
    Instance: app,
  });

  // 全局组件挂载
  app.component('FitterRole', FitterRole)

  // 项目中所有拥有 size 属性的组件的默认尺寸均为 'small'，弹框的初始 z-index 为 3000
  app.config.globalProperties.hasPermi = hasPermi
  app.config.globalProperties.selectDictLabel = selectDictLabel
  app.config.globalProperties.download = download
  app.config.globalProperties.parseTime = parseTime
  app.config.globalProperties.restPage = restPage
  app.config.globalProperties.handleTree = handleTree

  routers = router
  app.mount(container ? container.querySelector('#app-container') : '#app-container')
}

// 独立运行时
if (!window.__POWERED_BY_QIANKUN__) {
  if (isCloud) {
    setAppName(process.env.VUE_APP_APPLATION_NAME)
    store.dispatch('app/changeIsFullscreen', true)
  }
  render()
} else {
  store.dispatch('app/changeIsFullscreen', false)
}

export async function bootstrap(props) {
  console.log('props', props)
}
export async function mount(props) {
  console.log('[vue] props from main framework', props.remoteRouters)
  if (props.remoteRouters && !addRouter) {
    addRouter = true
    store.dispatch('GenerateRoutes', { routerArr: props.remoteRouters }).then((accessRoutes) => {
      for (let x of accessRoutes) {
        if (x.isFrame == 1) {
          router.addRoute(x)
        }
      }
    })
  }
  props.onGlobalStateChange((state) => {
    // state: 变更后的状态; prev 变更前的状态
    if (state.theme && state.theme != store.getters.theme) {
      changeElTheme3(state.theme)
    }
  })
  render(props)
}
export async function unmount() {
  app.unmount()
  app._container.innerHTML = ''
  app = null
  routers = null
}
