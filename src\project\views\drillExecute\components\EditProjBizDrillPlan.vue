<template>
  <el-form :model="formData" :rules="rules" ref="snForm" label-width="100px" label-position="right" :disabled="type == 'view'">
    <el-row :gutter="16" :span="24">
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>
          </legend>
          <el-row :gutter="16" :span="24">
            <el-col :span='8'>
              <el-form-item label="编码" prop="id">
                <el-input v-model="formData.id" type="text" disabled placeholder="请输入" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="演练日期" prop="drillTime">
                <el-date-picker type="date" v-model="formData.drillTime" style="width: 100%;" format="YYYY-MM-DD" value-format="YYYY-MM-DD" clearable></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="演练名称" prop="drillRealName">
                <el-input v-model="formData.drillRealName" type="text" placeholder="请输入演练名称" clearable>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </fieldset>
      </el-card>
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>附件信息
          </legend>
          <el-row :gutter="16" :span="24">
            <project-document-storage-ui-table
                ref="childRef"
                :type="fileType"
                :relevantId="relevantId"
                :isPageSearch="false"
                :isDeleteMinio = "isDeleteMinio"
                :isHasAi = "isHasAi"
                @on-add-data="onAddfileData"
                @on-ai-review="onfileAiReview"
                @on-preview="onfilePreview"
                @on-delete="onfileDelete"
                :file-serial-number-builder="fileSerialNumberBuilder"
                :preview-config="previewConfig"
                :isShowAddBtn="type !== 'view'"
                :isShowDelBtn="type !== 'view'"
                :isShowPreviewBtn="isShowPreviewBtn"
                :isShowDownloadBtn="type !== 'view'"
                :isShowLinkBtn="false"
                :isShowDownloadBatchBtn="type !== 'view'"
            ></project-document-storage-ui-table>
          </el-row>
        </fieldset>
      </el-card>
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>单据信息
          </legend>
          <el-row :gutter="16" :span="24">
            <el-col :span='8'>
              <el-form-item label="创建人" prop="createName">
                <el-input v-model="formData.createName" :disabled="true" type="text" placeholder="请输入创建人" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="创建时间" prop="createTime">
                <el-date-picker type="dateTime" v-model="formData.createTime" style="width: 100%;" disabled format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" clearable></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="最近修改人" prop="updateName">
                <el-input v-model="formData.updateName" :disabled="true" type="text" placeholder="请输入最近修改人" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="最近修改时间" prop="updateTime">
                <el-date-picker type="dateTime" v-model="formData.updateTime" style="width: 100%;" disabled format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" clearable></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
        </fieldset>
      </el-card>
    </el-row>
  </el-form>
</template>

<script setup>
import ProjBizDrillPlanApi from '@/project/api/drillExecute/ProjBizDrillPlan.js' 
import {
  nextTick,
  onMounted,
  ref,
  reactive,
  toRef,
  defineProps,
  defineExpose,
  getCurrentInstance
} from 'vue';
import {
  useRoute,
  useRouter
} from 'vue-router'
const {
  proxy
} = getCurrentInstance()
const route = useRoute();
const router = useRouter();
const props = defineProps({
  data: Object,
});
const snForm = ref()
const type = toRef(props.data?.type);
const fileType = ref("safety_drill_execute")
const relevantId = toRef(props.data?.id)
const buttonType = ref()
const previewConfig = ref({
  // 是否外置预览,必传
  isExternalPreview: false,
  // 内置预览返回的替换字符,akey代表要替换的字符串，avalue代码替换的字符串
  substitute: {
    "akey":"avalue"
  },
  // 内置预览服务地址
  previewServerUrl: "http://*************:8012/onlinePreview",
})
let formData = ref({
  id: "",
  drillTime: "",
  drillRealName: "",
  createName: "",
  createTime: "",
  updateName: "",
  updateTime: "",
  state:'0'
});
let rules = ref({
  id: [],
  drillTime: [],
  drillRealName: [],
  createName: [],
  createTime: [],
  updateName: [],
  updateTime: []
});
formData = toRef(Object.keys(props.data?.formData || {}).length > 0 ? {
  ...props.data?.formData
} : toRef(formData.value));

function getFormData() {
  formData.value.state='0'
  return formData.value
};

function submitData(btnType) {
  return new Promise((resolve) => {
    snForm.value.validate((flag) => {
      if (!flag) {
        return false;
      } else {
        buttonType.value = btnType
        if (type.value === "add") {
          if (childRef.value) {
            childRef.value.getListData().forEach((item) => {
              formData.value['fileDtoList'].push({
                fileId:item.fileId,
                fileName: item.fileName,
                fileSize: item.fileSize,
                createTime: item.createBy,
                fileSerialNumber: item.fileSerialNumber,
                fileSizeFormat: item.fileSizeFormat
              })
            })
          }
          resolve(saveData());
        } else {
          resolve(editData());
        }
      }
    });
  });
}

function saveData() {
  //新增操作
  const formData1 = getFormData();
  if (buttonType.value == 'submit') {
    formData1.state = '1'
  }
  return ProjBizDrillPlanApi.add(formData1).then(() => {
    proxy.$message.success("新增成功");
    return true;
  });
}

function editData() {
  //编辑操作
  const formData1 = getFormData();
  if (buttonType.value == 'submit') {
    formData1.state = '1'
  }
  return ProjBizDrillPlanApi.update(formData1).then(() => {
    proxy.$message.success("修改成功");
    return true;
  });
}
//处理附件上传相关方法---------begin
let listfileData = ref([]);
function onAddfileData(list) {
}

function onAifileReview(row) {
}

function onfilePreview(row) {
}

function onfileDelete(list) {
}
const childRef = ref(null);
function getfileListData() {
  if(childRef.value){
    let list = childRef.value.getListData()
  }
}
// 文件序列号生成函数
function fileSerialNumberBuilder() {
  return "file" + Math.floor(Math.random()*10000)
}
//处理附件上传相关方法---------end
defineExpose({
  getFormData,
  submitData,
});
</script>

<style lang="scss" scoped>

</style>
