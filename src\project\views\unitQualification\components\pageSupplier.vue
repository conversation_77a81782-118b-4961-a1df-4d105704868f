<template>
  <div>
    <sn-crud :data="listData" :option="option" v-model:page="queryForm.page" v-model:search="queryForm.filter"
      @on-load="getPageList" @search-change="onChangeSearch" @search-reset="onResetSearch" @select="xuanzhong" 
   >
      <template #unifiedSocialCreditCode="{ row }">
        <el-tooltip class="item" effect="dark" :content="row.unifiedSocialCreditCode" placement="top">
          <div class="text-overflow">{{ row.unifiedSocialCreditCode }}</div>
        </el-tooltip>
      </template>
            <template #orgFullName="{ row }">
        <el-tooltip class="item" effect="dark" :content="row.orgFullName" placement="top">
          <div class="text-overflow">{{ row.orgFullName }}</div>
        </el-tooltip>
      </template>

    </sn-crud>
  </div>
</template>

<script setup>
import {

  ref

} from 'vue';
import ProjBizUnitQualificationApi from '@/project/api/unitQualification/ProjBizUnitQualification.js'

let list = ref({})
let option = ref({
  tip: false,
  // dialogType: "page",
  dialogType: "page",
  border: true,
  index: true,
  searchLabelWidth: 100,
  stripe: true,
  menu: false,
  header: false,
  height: "auto",
  searchSpan: 6,
  searchIcon: true,
  searchMenuSpan: 6,
  searchIndex: 3,
    headerAlign: true,
  selection: true,
  menuWidth:300,
  showTree: false,
  excelBtn: false,
  headerAlign: true,
  delBtn: false,
  editBtn: false,
  delBtnsText: "批量删除",
  addBtnText: "新增",
  customEditBtnText: "编辑",
  customDelBtnText: "删除",
  customViewBtnText: "查看",
  column: [{
    label: "供应商编号",
    
    prop: "unifiedSocialCreditCode",
    columnSlot: false,
    searchSlot: false,
         search: true,
  }, {
    label: "承包商名称",
    prop: "orgFullName",
    search: true,

    queryType: "LIKE"
  }, {
    label: "供应商简称",
      
    prop: "orgName"
  }]
});
let listData = ref([]);
let queryForm = ref({
  page: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  filter: {
    createTime: null
  }
});
function xuanzhong(selection,row){
 
 list.value   = selection 

 

}
function onResetSearch() {
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
}
function onChangeSearch(params, done) {
  //搜索按钮操作
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
  done && done();
}
function handleQueryForm() {
  // 处理参数
  const {
    pageSize,
    pageNum
  } = queryForm.value.page;
  const filter = {}
  for (let key in queryForm.value.filter) {
    if (queryForm.value.filter[key]?.length) {
      // sn-curd搜索select会生成$prop的变量
      if (key.startsWith("$")) continue;
      filter[key] = queryForm.value.filter[key];
    }
  }
  delete filter.createTime;

  const searchParams = {
    page: {
      pageSize,
      pageNum,
    },
    filter,
  }
  return searchParams
}

function getPageList() {
  //查询分页列表
  // 查询前处理参数
  const params = handleQueryForm();
  ProjBizUnitQualificationApi.pageSupplier(params).then((res) => {
    listData.value = res.data.dataList;
    queryForm.value.page.total = res.data.totalCount;
  });
}
defineExpose({
list

})
</script>
<style lang="scss" scoped>
//文本过长省略号
.text-overflow {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
