const isWindowAvailable = typeof window !== 'undefined'
const isCloud = process.env.VUE_APP_IS_CLOUD == 'false' ? false : process.env.VUE_APP_IS_CLOUD
module.exports = {
  title: '天易开发平台', // 平台标题
  themeMode: 'LIGHT', // 主题模式 DARK/LIGHT
  theme: '#4871C0', // 默认主题色
  isCloud: isCloud, //是否是微服务
  publicPath: '/', // 静态资源路径
  isShowLayoutSetting: true, // 是否显示布局设置
  layoutType: 'side', // 默认布局类型 side/top/edge
  isShowTagsSetting: true, // 是否显示页签样式设置
  tagsStyle: 'line', // 默认标签风格 line/tab
  iamLogin: false, //启用集团4A认证登录
  appName: process.env.VUE_APP_APPLATION_NAME,
  baseUrl: process.env.VUE_APP_BASE_API,
  bpmCode: 'bpm',
  systemCode: 'system',
  URI: isWindowAvailable && window.BASE_URL ? window.BASE_URL : `http://gateway.dev.snpit.com:${isCloud ? 9999 : 8920}`, // 接口地址
  projectManagerServiceCode: 'project-management',
  screenServiceCode: process.env.VUE_APP_APPLATION_NAME === 'guohe-project' ? 'guohe-screen' : 'zzh-bigscreen',
  huizhiServiceCode: process.env.VUE_APP_APPLATION_NAME === 'guohe-project' ? 'huizhi-project' : 'huizhi-base', // 汇智服务编码
  lifecycleBizServiceCode: process.env.VUE_APP_APPLATION_NAME === 'guohe-project' ? 'guohe-project' : 'project',
  kkFileViewUrl: process.env.VUE_APP_APPLATION_NAME === 'guohe-project' ? 'http://10.212.36.143:8012/onlinePreview' : 'http://10.191.64.191:8012/onlinePreview',
}
