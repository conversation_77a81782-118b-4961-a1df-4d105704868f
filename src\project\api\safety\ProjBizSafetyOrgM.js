import { request, replaceUrl } from "sn-base-utils";

export default class ProjBizSafetyOrgMApi {
    static config = {
        add: {
            url: '/project/safetyOrg/add',
            method: 'POST'
        },
        remove: {
            url: '/project/safetyOrg/delete',
            method: 'DELETE'
        },
        update: {
            url: '/project/safetyOrg/update',
            method: 'PUT'
        },
        view: {
            url: '/project/safetyOrg/get/{id}',
            method: 'GET'
        },
        pageList: {
            url: '/project/safetyOrg/page',
            method: "POST"
        },
        list: {
            url: '/project/safetyOrg/list',
            method: "POST"
        },
        downloadZip: {
            url: '/project/safetyOrg/downloadZip',
            method: "POST"
        },
    };

    /**
     * 新增安全组织架构
     * @param data
     * @returns {*}
     */
    static add(data) {
        return request({
            url: this.config.add.url,
            method: this.config.add.method,
            data: data
        });
    }

    /**
     * 删除安全组织架构
     * @param data
     * @returns {*}
     */
    static remove(data) {
        return request({
            url: this.config.remove.url,
            method: this.config.remove.method,
            data: data
        });
    }

    /**
     * 更新安全组织架构
     * @param data
     * @returns {*}
     */
    static update(data) {
        return request({
            url: this.config.update.url,
            method: this.config.update.method,
            data: data
        });
    }

    /**
     * 查询安全组织架构详细
     * @param id
     * @returns {*}
     */
    static view(id) {
        return request({
            url: replaceUrl(this.config.view.url, { id }),
            method: this.config.view.method
        });
    }

    /**
     * 分页查询安全组织架构列表
     * @param data
     * @returns {*}
     */
    static pageList(data) {
        return request({
            url: this.config.pageList.url,
            method: this.config.pageList.method,
            data: data
        });
    }

    /**
     * 全部安全组织架构列表
     * @returns {*}
     */
    static list(data) {
        return request({
            url: this.config.list.url,
            method: this.config.list.method,
            data: data
        });
    }
}
