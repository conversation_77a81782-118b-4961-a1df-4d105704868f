import TaskCommit from "./TaskCommit.vue";
/**
 * 预处理工作流操作
 */
export default function preHandlerAction(operation, proxy, fn) {
  if (operation == null) {
    fn(false);
    return;
  }
  if( operation.type == this.FlowActionType.PRINT){
    fn(true);
    return;
  }
  // 撤销操作不弹出选择窗口
  const showCommitDig =
    operation.type !== proxy.FlowActionType.SAVE &&
    operation.type !== proxy.FlowActionType.SAVESTART &&
    operation.type !== proxy.FlowActionType.SIGN &&
    operation.type !== proxy.FlowActionType.REVOKE &&
    operation.type !== proxy.FlowActionType.RECALL;
  if (showCommitDig) {
    proxy.$DialogForm.show({
      content: TaskCommit,
      // type:'drawer',
      title: "办理",
      data: { operation,fullScreen:true,open:true },
      top:'10vh',
      width: "40%",
      callback: (res) => {
        fn(res);
      },
    });
  } else {
    fn(true);
  }
}
