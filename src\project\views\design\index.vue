<template>
  <div>
  <sn-crud :data="listData" :option="option" v-model:page="queryForm.page" v-model:search="queryForm.filter" @on-load="getPageList" @search-change="onChangeSearch" @search-reset="onResetSearch" @addBtnHandle="onEditData" @row-del="onDelData">
    <template #menu="{ row, index, size }">
      <!-- 未发布：编辑 + 删除 -->
      <template v-if="row.docStatus === '0' || !row.docStatus">
        <el-button type="primary" :size="size" icon="el-icon-edit" link @click="onEditData(row)">编辑</el-button>
        <el-button type="danger" :size="size" icon="el-icon-delete" link @click="onDelData([row])">删除</el-button>
      </template>

      <!-- 已发布：撤回 + 下载 -->
      <template v-else>
        <el-button type="warning" :size="size" icon="el-icon-refresh-left" link @click="onRevoke(row)">撤回</el-button>
        <el-button type="primary" :size="size" icon="el-icon-download" link @click="onDownload(row)">下载</el-button>
      </template>
    </template>
    <template #docStatus="{row}">
      <span :style="{color : row.docStatus && row.docStatus==='1' ? 'green' : 'red'}">
          {{row.docStatus && row.docStatus==='1' ? '已发布': '草稿'}}
        </span>
    </template>
    <template #brirecordNumber="{ row }">
      <el-button type="text" @click="onDetailClick(row)">{{ row.brirecordNumber }}</el-button>
    </template>
    <template #header>
        <el-button type="primary" icon="el-icon-plus" style="margin-left: 10px;" @click="onEditData(row)">新增</el-button>
<!--        <el-button type="primary" icon="el-icon-document" @click="onListTemplate">列表模板</el-button>-->
<!--        <el-button type="primary" icon="el-icon-upload" @click="onListImport">列表导入</el-button>-->
        <el-button type="primary" icon="el-icon-download" @click="onExportData">列表导出</el-button>
    </template>
    <!--      添加如果文本太长展示省略号-->
    <template #major="{ row }">
      <el-tooltip class="item" effect="dark" :content="row.major" placement="top">
        <div class="text-overflow">{{ row.major }}</div>
      </el-tooltip>
    </template>
    <!--      添加如果文本太长展示省略号-->
    <template #volumeName="{ row }">
      <el-tooltip class="item" effect="dark" :content="row.volumeName" placement="top">
        <div class="text-overflow">{{ row.volumeName }}</div>
      </el-tooltip>
    </template>
<!--    <template #brirecordNumber="{ row }">-->
<!--      <el-tooltip class="item" effect="dark" :content="row.brirecordNumber" placement="top">-->
<!--        <div class="text-overflow">{{ row.brirecordNumber }}</div>-->
<!--      </el-tooltip>-->
<!--    </template>-->
    <template #volumeNum="{ row }">
      <el-tooltip class="item" effect="dark" :content="row.volumeNum" placement="top">
        <div class="text-overflow">{{ row.volumeNum }}</div>
      </el-tooltip>
    </template>
  </sn-crud>
    <div ref="myRef"></div>
  </div>
</template>
<script>
import ProjBizDesignIntentionMApi from '@/project/api/design/ProjBizDesignIntentionM.js'
  import EditProjBizDesignIntentionM from "./components/EditProjBizDesignIntentionM.vue";
  import { getToken } from "sn-base-utils";
  export const routerConfig = [{
    menuType: "C",
    menuName: "施工图设计交底",
  }, {
    menuType: "F",
    menuName: "查看",
    perms: "show",
    api: [ProjBizDesignIntentionMApi.config.pageList],
  }, {
    menuType: "F",
    menuName: "新增",
    perms: "add",
    api: [ProjBizDesignIntentionMApi.config.add],
  }, {
    menuType: "F",
    menuName: "修改",
    perms: "update",
    api: [ProjBizDesignIntentionMApi.config.update, ProjBizDesignIntentionMApi.config.view],
  }, {
    menuType: "F",
    menuName: "删除",
    perms: "del",
    api: [ProjBizDesignIntentionMApi.config.remove],
  }, {
      menuType: "F",
      menuName: "下载",
      perms: "exportZip",
      api: [ProjBizDesignIntentionMApi.config.downloadZip],
    }];
</script>

<script setup>
import {
  useDicts
} from "@/common/hooks/useDicts";
import {
  ref,
  getCurrentInstance
} from 'vue';
const {
  proxy
} = getCurrentInstance()

const {} = useDicts([])
const myRef = ref(null);
let formData = ref({
  specialty: "",
  volumeNumber: "",
  volumeName: ""
});
let formRules = ref({
  specialty: [],
  volumeNumber: [],
  volumeName: []
});
let option = ref({
  tip: false,
  dialogType: "page",//page为弹出一个页面   dialog为正常弹窗
  border: true,
  index: true,
  stripe: true,
  menu: true,
  header: false,//自定义头不使用其组件，这样新增按钮就没了 需要自己定义
  height: "auto",
  searchSpan: 6,
  searchIcon: true,
  searchMenuSpan: 6,
  searchIndex: 3,
  selection: true,
  headerAlign: "center",//表格列居中
  // selection: false,// ✅ 关键：关闭多选，隐藏批量删除按钮
  delBtns: false,            // 👈 显式隐藏批量删除按钮
  showTree: false,
  excelBtn: false,
  delBtn: false,
  editBtn: false,
  importBtn: true,//表格导入按钮
  importBtnText:"列表导入",//表格导入按钮文案
  importBtnIcon:"el-icon-upload",//表格导入按钮图标
  delBtnsText: "批量删除",
  addBtnText: "新增",
  customEditBtnText: "编辑",
  customDelBtnText: "删除",
  customViewBtnText: "查看",

  column: [{
    label: "交底记录编号",
    prop: "brirecordNumber",
    columnSlot: false,
    searchSlot: false,
    width: 150,
    // sortable: true//添加排序按钮

  }, {
    label: "专业",
    prop: "major",
    search: true,
    queryType: "LIKE",
    columnSlot: false,
    searchSlot: false,
    dictDatas: "major_type",
    type: "select",
    dicUrl: "/system/dict/data/type/major_type",
    dicMethod: "get",
    props: {
      label: "dictLabel",
      value: "dictValue"
    }
  }, {
    label: "卷册号",
    prop: "volumeNum",
    search: true,
    queryType: "LIKE",
    columnSlot: false,
    searchSlot: false,
    // sortable: true//添加排序按钮
  }, {
    label: "卷册名称",
    prop: "volumeName",
    search: true,
    queryType: "LIKE",
    columnSlot: false,
    searchSlot: false,
    // sortable: true,//添加排序按钮
  }, {
    label: "交底人",
    prop: "briefingPerson",
    columnSlot: false,
    searchSlot: false,
    // sortable: true,//添加排序按钮
  }, {
    label: "交底时间",
    prop: "briefingTime",
    columnSlot: false,
    searchSlot: false,
    // sortable: true//添加排序按钮
  }, {
    label: "发布状态",
    prop: "docStatus",
    columnSlot: false,
    searchSlot: false,
    // sortable: true,//添加排序按钮
  }]
});
let listData = ref([]);
let queryForm = ref({
  page: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  filter: {
    createTime: null,
    projectId: sessionStorage.getItem('projectId')
  }
});
// let formRules = ref({});

function getPageList() {
  //查询分页列表
  // 查询前处理参数
  const params = handleQueryForm();
  ProjBizDesignIntentionMApi.pageList(params).then((res) => {
    listData.value = res.data.dataList;
    queryForm.value.page.total = res.data.totalCount;
  });
}

function onResetSearch() {
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
}

function handleQueryForm() {
  // 处理参数
  const {
    pageSize,
    pageNum
  } = queryForm.value.page;
  const filter = {}
  for (let key in queryForm.value.filter) {
    if (queryForm.value.filter[key]?.length) {
      // sn-curd搜索select会生成$prop的变量
      if (key.startsWith("$")) continue;
      filter[key] = queryForm.value.filter[key];
    }
  }
  filter.projectId = sessionStorage.getItem('projectId')
  delete filter.createTime;
  if (Array.isArray(queryForm.value.filter.createTime) && queryForm.value.filter.createTime?.length === 2) {
    filter["beginCreateTime"] = queryForm.value.filter.createTime[0];
    filter["endCreateTime"] = queryForm.value.filter.createTime[1];
  }
  const searchParams = {
    page: {
      pageSize,
      pageNum,
    },
    filter,
  }
  return searchParams
}

function onChangeSearch(params, done) {
  //搜索按钮操作
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
  done && done();
}
async function onEditData(row) {
  //编辑,新增按钮操作
  let editType = row ? "edit" : "add";
  let rowInfo = await (editType !== "add" ? ProjBizDesignIntentionMApi.view(row.id) : {});
  const formData = editType !== "add" ? rowInfo.data : rowInfo;
  //编辑,新增按钮操作
  proxy.$DialogForm.show({
    title: editType == "edit" ? "编辑" : "新增",
    type: option.value.dialogType,
    el: myRef.value,
    width: "80%",
    content: EditProjBizDesignIntentionM,
    data: {
      formData: formData,
      type: editType,
      id:row ? row.id:null,
    },
    option: {
      submitBtn: false,
      emptyBtn: false,
      emptyText:'取消',
      extendButton:[
        {
          key: 'save',
          text: '保存',
          icon: 'el-icon-plus',
          buttonType: 'primary',
        },
        {
          key: 'submit',
          text: '发布',
          icon: 'el-icon-check',
          buttonType: 'primary',
        },
        {
          key: 'close',
          text: '关闭',
          icon: 'el-icon-close',
          buttonType: '',
        },
      ],
    },
    callback: (res) => {
      if (res.type === 'save') {
        if (res.dialogRefs) {
          res.dialogRefs.submitData(res.type).then((flag) => {
            if (flag) {
              getPageList();
            }
          });
        }
      } else if (res.type === 'close' ) {
        // 当点击关闭按钮且处于编辑模式时，弹出确认对话框
        proxy.$confirm('确认关闭？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          res.close();
        }).catch(() => {
          // 用户点击取消，不关闭弹窗
        });
      }else if(res.type === 'submit'){
        if (res.dialogRefs) {
          res.dialogRefs.submitData(res.type).then((flag) => {
            if (flag) {
              getPageList();
              res.close();
            }
          });
        }
      }else {
        res.close();
      }
    }
  });
}

function onDelData(rows) {
  //删除行
  if (!rows || rows.length == 0) {
    proxy.$message.info("请勾选数据！");
    return false;
  }
  let ids = rows.map((item) => {
    return item.id;
  });
  proxy.$modal.confirm("确认删除数据项？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    ProjBizDesignIntentionMApi.remove(ids).then((res) => {
      proxy.$message.success("已删除");
      getPageList();
    });
  }).catch(() => {});
}
function getFormData() {
  return formData.value
};
function onRevoke(row) {
  proxy.$modal.confirm("确认撤回该条数据项？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    //编辑操作
    // const formData = getFormData();
    // getPageList();
    row.docStatus = '0'
    ProjBizDesignIntentionMApi.update(row).then((res) => {
      proxy.$message.success("已撤回");
      getPageList();
    });
  }).catch(() => {});
}

async function onDetailClick(row) {
  //编辑,新增按钮操作
  let editType = "show";
  let rowInfo = await (editType !== "add" ? ProjBizDesignIntentionMApi.view(row.id) : {});
  const formData = editType !== "add" ? rowInfo.data : rowInfo;
  // 根据文档状态动态添加编辑按钮
  let extendButtons = []
  // 首先检查是否需要添加“编辑”按钮
  if (row.docStatus === '0') {
    extendButtons.push({
      key: 'edit',
      text: '编辑',
      icon: 'el-icon-edit',
      buttonType: 'primary',
      click: () => {
        res.close();
        onEditData(row);
      },
    });
  }
// 然后添加“关闭”按钮
  extendButtons.push({
    key: 'close',
    text: '关闭',
    icon: 'el-icon-close',
    buttonType: '',
  });
  //编辑,新增按钮操作
  proxy.$DialogForm.show({
    title: editType == "show" ? "查看" : "编辑",
    type: option.value.dialogType,
    el: myRef.value,
    width: "80%",
    content: EditProjBizDesignIntentionM,
    data: {
      formData: formData,
      type: editType,
      id:row ? row.id:null,
    },
    option: {
      submitBtn: false,
      emptyBtn: false,
      emptyText:'取消',
      extendButton: extendButtons,
    },
    callback: (res) => {
      if (res.type === 'edit') {
        // 当文档状态为草稿时，显示编辑按钮
        onEditData(row);
        res.close();
      } else if (res.type && res.type !== 'close') {
        if (res.dialogRefs) {
          res.dialogRefs.submitData(res.type).then((flag) => {
            if (flag) {
              getPageList();
              res.close();
            }
          });
        }
      } else {
        res.close();
      }
    }
  });
  // 动态添加编辑按钮
  if (row.docStatus === '0') {
    proxy.$DialogForm.extendButton.push({
      key: 'edit',
      text: '编辑',
      icon: 'el-icon-edit',
      buttonType: 'primary',
      click: () => {
        onEditData(row);
      },
    });
  }
}
/**
 * 列表导出
 * @param row
 */
function onExportData() {
  // const params = handleQueryForm();
  const params = queryForm.value.filter
   // queryForm = JSON.parse(JSON.stringify(params));
  proxy.download("/project/design/export", params, '施工图设计交底数据导出.xlsx');
  // proxy.download("/project/design/export", queryForm, '施工图设计交底数据导出.xlsx');
}

/**
 * 下载文件
 * @param row
 */
function onDownload(row) {
  proxy.download(ProjBizDesignIntentionMApi.config.downloadZip.url, [row.id], '施工图设计交底附件'+row.id+'.zip');
}

</script>

<style lang="scss" scoped>
//文本过长省略号
.text-overflow {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>

