/*
 * @Author: 王云飞
 * @Date: 2024-12-20 09:57:02
 * @LastEditTime: 2025-01-03 10:51:24
 * @LastEditors: 王云飞
 * @Description:
 *
 */
import { createStore } from 'vuex'
import {
  app,
  user,
  meta,
  commonGetters,
  tagsView,
  permission,
  settings,
  dict
  // Store
} from 'sn-base-layout'
const store = createStore({
  modules: {
    app: app(),
    settings: settings(),
    permission: permission(),
    tagsView: tagsView(),
    user: user(),
    meta: meta(),
    dict: dict()
  },
  getters: { ...commonGetters }
})
if (!window['__TY_Store']) {
  window['__TY_Store'] = store
  window['store'] = () => store
}

export default store
