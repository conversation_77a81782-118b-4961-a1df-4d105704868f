<template>
  <div>
    <sn-crud :data="listData" :option="option" v-model:page="queryForm.page" v-model:search="queryForm.filter" @on-load="getPageList" @search-change="onChangeSearch" @search-reset="onResetSearch" @addBtnHandle="onEditData" @row-del="onDelData" @row-excel="onExportData">
      <template #menu="{ row, index, size }">
        <el-button type="primary" :size="size" icon="el-icon-edit" link @click="onEditData(row)" v-if="hasPermi('show') && row.status !== 'draft'">查看</el-button>
        <el-button type="primary" :size="size" icon="el-icon-edit" link @click="onEditData(row)" v-if="hasPermi('update') && row.status === 'draft'">编辑</el-button>
        <el-button type="danger" :size="size" icon="el-icon-edit" link @click="onRevokeData(row)" v-if="hasPermi('del') && row.status !== 'draft'">撤回</el-button>
        <el-button type="danger" :size="size" icon="el-icon-delete" link @click="onDelData([row])" v-if="hasPermi('del') && row.status === 'draft'">删除</el-button>
        <!--      <el-button type="primary" :size="size" icon="el-icon-edit" link @click="onPublishData(row)" v-if="hasPermi('publish') && row.status === 'draft'">发布</el-button>-->
      </template>
      <template #inspectionDateSearch = "{ row, size }">
        <el-date-picker
            v-model="queryForm.filter.inspectionDate"
            type="daterange"
            value-format="YYYY-MM-DD"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
        </el-date-picker>
      </template>
      <template #name="{ row, size }">
        <span class="jump-title" @click="onEditData(row)" >{{row.name}}</span>
      </template>
      <template #reformStatus="{ row, size }">
        <span v-if="row.status === 'draft'">未整改</span>
        <span v-else-if="row.reformStatus === 'in_progress'" class="color-danger">整改中</span>
        <span v-else class="color-success">已完成</span>
      </template>
      <template #progress ="{ row, size }">
        <el-progress style="width: 100%" :stroke-width="10" :text-inside="false" :percentage="row.progress" disabled=""></el-progress>
      </template>
      <template #isOverdue ="{ row, size }">
        <span v-if="row.isOverdue" class="color-danger">是</span>
        <span v-else class="color-success">否</span>
      </template>
      <template #status ="{ row, size }">
        <span v-if="row.status === 'published'" class="color-success">已发布</span>
        <span v-else class="color-danger">未发布</span>
      </template>
    </sn-crud>
    <div ref="myRef"></div>
  </div>
</template>

<script>
import ProjBizIrRecordMApi from '@/project/api/constructionManagement/InspectionAndRectification/ProjBizIrRecordM.js'
import {calculateIntegerPercentage} from '@/common/utils/math'
import {isDateInFuture} from '@/common/utils/datetime'
  import EditProjBizIrRecordM from "./components/EditProjBizIrRecordM.vue";
import IrFlow from "../../InspectionAndRectification/irFlow/index.vue"
import EditProjBizIrPrM from "../../InspectionAndRectification/irFlow/components/EditProjBizIrPrM.vue"
  import { getToken } from "sn-base-utils";
  export const routerConfig = [{
    menuType: "C",
    menuName: "检查与整改",
  }, {
    menuType: "F",
    menuName: "查看",
    perms: "show",
    api: [ProjBizIrRecordMApi.config.pageList],
  }, {
    menuType: "F",
    menuName: "新增",
    perms: "add",
    api: [ProjBizIrRecordMApi.config.add],
  }, {
    menuType: "F",
    menuName: "修改",
    perms: "update",
    api: [ProjBizIrRecordMApi.config.update,
      ProjBizIrRecordMApi.config.view,
      ProjBizIrRecordMApi.config.questionsUpdate,
      ProjBizIrRecordMApi.config.listQuestionByIdList,
    ],
  }, {
    menuType: "F",
    menuName: "删除",
    perms: "del",
    api: [ProjBizIrRecordMApi.config.remove],
  }, {
    menuType: "F",
    menuName: "发布",
    perms: "publish",
    api: [ProjBizIrRecordMApi.config.publish],
  }];
</script>

<script setup>
import {formatBytes} from '@/common/utils/Bytes'
import {
  ref,
  getCurrentInstance,
    reactive,
  provide
} from 'vue';
import {useDicts} from "@/common/hooks/useDicts";
import {requestImgUrl} from "@/common/api/MinioFile";
import {hasPermi} from "sn-base-utils";
import store from "@/store";
import {getCurrentFormattedTime} from "@/common/utils/datetime";
const {
  proxy
} = getCurrentInstance()
const {
  inspection_reform_status
} = useDicts(['inspection_reform_status'])
let option = ref({
  tip: false,
  dialogType: "page",
  border: true,
  index: true,
  stripe: true,
  menu: true,
  menuWidth: 300,
  header: true,
  height: "auto",
  searchSpan: 5,
  searchIcon: true,
  searchLabelWidth: 95,
  searchLabelPosition:"right",
  searchMenuSpan: 6,
  searchIndex: 3,
  selection: true,
  selectable: selectable,
  selectionFixed: true,
  showTree: false,
  excelBtn: true,
  delBtn: false,
  delBtns: false,
  editBtn: false,
  delBtnsText: "批量删除",
  addBtnText: "新增",
  customEditBtnText: "编辑",
  customDelBtnText: "删除",
  customViewBtnText: "查看",
  excelBtnText: "列表导出",
  column: [{
    label: "检查名称",
    prop: "name",
    search: true,
    columnSlot: true,
    overHidden: true,
    queryType: "LIKE"
  }, {
    label: "检查单位",
    prop: "inspectionUnitName",
    search: true,
    overHidden: true,
    queryType: "LIKE"
  }, {
    label: "被检查单位",
    prop: "inspectedUnitName",
    search: true,
    overHidden: true,
    queryType: "LIKE"
  }, {
    label: "检查时间",
    prop: "inspectionDate",
    search: true,
    searchSlot: true,
    queryType: "BETWEEN"
  }, {
    label: "发现问题数",
    prop: "questionsNum",
    search: false
  }, {
    label: "整改期限",
    prop: "reformDeadline"
  }, {
    label: "整改状态",
    prop: "reformStatus",
    type: "select",
    search: true,
    columnSlot: true,
    queryType: "EQ",
    dicUrl: "/system/dict/data/type/inspection_reform_status",
    dicMethod: "get",
    props: {
      label: "dictLabel",
      value: "dictValue",
    },
  }, {
    label: "整改进度",
    prop: "progress",
    search: false,
    columnSlot: true
  }, {
    label: "是否超期",
    prop: "isOverdue",
    type: "select",
    search: true,
    columnSlot: true,
    searchSlot: false,
    dicUrl: "/system/dict/data/type/inspection_is_overdue",
    dicMethod: "get",
    props: {
      label: "dictLabel",
      value: "dictValue",
    },
  }, {
    label: "发布状态",
    prop: "status",
    search: false,
    columnSlot: true
  }]
});
let listData = ref([]);
let queryForm = ref({
  page: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  filter: {
    createTime: null
  }
});
let formRules = ref({});
const myRef = ref(null)


/**
 * 某一行是否可选
 * 如果是草稿才能删除
 * @param row
 */
function selectable(row) {
  return row.status === 'draft';
}

function getPageList() {
  //查询分页列表
  // 查询前处理参数
  const params = handleQueryForm();
  ProjBizIrRecordMApi.pageList(params).then((res) => {
    listData.value = res.data.dataList;
    listData.value.forEach((v) => {
      v.progress = calculateIntegerPercentage(v.questionsCompleted,v.questionsNum)
      v.isOverdue = Number(v.progress) < 100 && isDateInFuture(v.reformDeadline)
    })
    queryForm.value.page.total = res.data.totalCount;
  });
}

function onResetSearch() {
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
}

function handleQueryForm() {
  // 处理参数
  const {
    pageSize,
    pageNum
  } = queryForm.value.page;
  const filter = {}
  for (let key in queryForm.value.filter) {
    if (queryForm.value.filter[key]?.length) {
      // sn-curd搜索select会生成$prop的变量
      if (key.startsWith("$")) continue;
      filter[key] = queryForm.value.filter[key];
    }
  }
  delete filter.inspectionDate;
  if (Array.isArray(queryForm.value.filter.inspectionDate) && queryForm.value.filter.inspectionDate?.length === 2) {
    filter["beginInspectionDate"] = queryForm.value.filter.inspectionDate[0];
    filter["endInspectionDate"] = queryForm.value.filter.inspectionDate[1];
  }
  const searchParams = {
    page: {
      pageSize,
      pageNum,
    },
    filter,
  }
  return searchParams
}

function onChangeSearch(params, done) {
  //搜索按钮操作
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
  done && done();
}
async function onEditData(row) {
  //编辑,新增按钮操作
  let editType = row ? "edit" : "add";
  let rowInfo = await (editType !== "add" ? ProjBizIrRecordMApi.view(row.id) : {});
  let formData = editType !== "add" ? rowInfo.data : rowInfo;
  // 处理相关数据
  let isDisabled = false;
  if(formData.id){
    formData.isOverdue = isDateInFuture(formData.reformDeadline)
    formData.isOverdueStr = formData.isOverdue ? '是' : "否";
    if(formData.reformStatus === 'in_progress'){
      formData.reformStatusStr = '整改中'
    }else if(formData.reformStatus === 'not_rectified'){
      formData.reformStatusStr = '未整改'
    }else{
      formData.reformStatusStr = '已完成'
    }

    formData.progress = calculateIntegerPercentage(formData.questionsCompleted, formData.questionsNum)
    formData.statusStr = formData.status === 'draft' ? '草稿' : "已发布"
    isDisabled = formData.status !== 'draft'
    // 处理附件数据
    if (formData.projBizDmStgMDtoList) {
      formData.projBizDmStgMDtoList.forEach((v) => {
        v.fileSizeFormat = formatBytes(v.fileSize)
      })
    }else {
      formData.projBizDmStgMDtoList = []
    }
    // 修改父组件的图片处理逻辑
    if (formData.projBizIrQuestionSDtoList) {
      // 使用 Promise.all 处理异步请求
      const promises = formData.projBizIrQuestionSDtoList.map(async (v) => {
        if (v.reformImage && v.reformImageInfo?.id) {
          try {
            // 确保 requestImgUrl 返回 Promise
            v.reformImagePreviewUrl = await requestImgUrl(v.reformImageInfo.id);
            v.reformImagePreviewUrlList = [v.reformImagePreviewUrl];
          } catch (e) {
            console.error("图片加载失败", e);
            v.reformImagePreviewUrl = "/fallback-image.jpg"; // 添加备用图片
            v.reformImagePreviewUrlList = [];
          }
        }
        return v;
      });
      // 等待所有图片加载完成
      await Promise.all(promises);
      // 使用响应式方式复制数组
      formData.projBizIrQuestionSDtoListFilter = [...formData.projBizIrQuestionSDtoList];
    }
  }else{
    formData = { // 初始化数据
      id: "",
      projectId: "",
      projectName: "",
      projectUnit: "",
      inspectionUnitId: "",
      inspectionUnitName: JSON.parse(store?.state?.user?.orgName)?.find(
          (item) => item.id === store.state.user.defaultOrg
      )?.orgName,
      inspectedUnitId: "",
      inspectedUnitName: "",
      inspectionDate: "",
      name: "",
      reformDeadline: "",
      inspectionType: "",
      reformStatus: "not_rectified",
      reformStatusStr: "未整改",
      questionsNum: "0",
      questionsCompleted: "0",
      projBizIrQuestionSDtoList: [],
      projBizIrQuestionSDtoListFilter: [],
      projBizDmStgMDtoList: [],
      isOverdue: "否", // 是否超期
      progress: 0, // 进度
      reformFile: "", // 检查文件ID
      reformFileInfo: {}, // 检查文件信息
      businessCode: "",
      createName: store.state.user?.userName || store.state.user?.userInfo?.userName || '',
      createTime: getCurrentFormattedTime(),
      statusStr: "草稿",
    }
  }
  // console.log("form数据",formData,isDisabled)
  // console.log("缓存数据",store.state?.user)
  //编辑,新增按钮操作
  proxy.$DialogForm.show({
    title: "检查与整改",
    type: option.value.dialogType,
    width: "80%",
    content: EditProjBizIrRecordM,
    el: myRef.value,
    data: reactive({
      formData: formData,
      type: editType,
      isDisabled: isDisabled,
      onLink:linkData
    }),
    option: {
      submitBtn: false,
      emptyBtn: false,
      submitText: '保存',
      emptyText: '关闭',
      extendButton: [{
        key: 'cancel',
        text: '关闭',
        buttonType: '',
        // icon: 'el-icon-close',
      }]
    },
    callback: (res) => {
      let vm = res.dialogRefs
      console.log("打印res",res)
      if (vm) {
        let formData = vm.getFormData();
        switch (res.type) {
            // 关闭
          case 'cancel':
            proxy.$modal.confirm("确认关闭？", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }).then(() => {
              res.close();
              getPageList()
            }).catch(() => {});
            break;
          //   // 保存
          // case 'save':
          //   vm.submitData().then((resp) => {
          //     res.close();
          //     getPageList()
          //   })
          //   break;
          //   // 流程记录
          // case 'processRecord':
          //   recordDialog(row)
          //   break;
          // case 'submit':
          //   let questionIdList= vm.getCheckQuestionIdList()
          //   if (questionIdList && questionIdList.length) {
          //     vm.getNewSubmitQuestions().then((resp) => {
          //       let submitQuestionsError = []
          //       let errorMsg = ""
          //       resp.data.forEach((v) => {
          //         if (v.submitStatus === 'yes') {
          //           submitQuestionsError.push(v.serialNumber)
          //         }
          //       })
          //       if (submitQuestionsError.length > 0) {
          //         errorMsg = '问题编号:[' + submitQuestionsError.join(",") + "]已提审,不可重复提审"
          //         proxy.$message.success(errorMsg);
          //         return
          //       }
          //       submitTaskDialog(resp.data)
          //     })
          //   }else {
          //     proxy.$message.success("请选择要提交审批的问题");
          //   }
          //   break;
        }
      }
    },
  });

}

/**
 * 点击发布后关闭弹窗,刷新列表
 * @param e
 */
function linkData(e) {
  e.close()
  getPageList()
}

/**
 * 撤回
 * @param row
 */
function onRevokeData(row){
  proxy.$modal.confirm("确认撤回数据项？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    ProjBizIrRecordMApi.revoke(row.id).then((res) => {
      proxy.$message.success("撤回成功");
      getPageList();
    });
  }).catch(() => {});
}

function onDelData(rows) {
  //删除行
  if (!rows || rows.length == 0) {
    proxy.$message.info("请勾选数据！");
    return false;
  }
  let ids = rows.map((item) => {
    return item.id;
  });
  proxy.$modal.confirm("确认删除数据项？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    ProjBizIrRecordMApi.remove(ids).then((res) => {
      proxy.$message.success("已删除");
      getPageList();
    });
  }).catch(() => {});
}

/**
 * 发布检查
 * @param row
 */
function onPublishData(row) {
  proxy.$modal.confirm("确认要发布数据项？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    ProjBizIrRecordMApi.publish(row.id).then((res) => {
      proxy.$message.success("发布成功");
      getPageList();
    });
  }).catch(() => {});
}

function onExportData() {
  const params = handleQueryForm();
  let queryForm = JSON.parse(JSON.stringify(params));
  proxy.download("/project/irRecord/export", queryForm, '检查与整改.xlsx');
}


</script>

<style lang="scss" scoped>
.jump-title{
  text-decoration: underline;
  cursor: pointer;
}
.jump-title:hover{
  color: #7F9CD3
}
.color-success{
  color: #67C23A;
}
.color-in{
  color: #E6A23C;
}
.color-danger{
  color: #F56C6C;
}

</style>

