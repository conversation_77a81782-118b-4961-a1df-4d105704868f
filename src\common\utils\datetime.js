/**
 * 获取当前日期
 * @returns {string}
 */
export function getCurrentDate() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1，并补零
  const day = String(now.getDate()).padStart(2, '0'); // 补零

  return `${year}-${month}-${day}`;
}

/**
 * 获取当前时间
 * @returns {string}
 */
export function getCurrentFormattedTime() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}


/**
 * 判断是否超期
 * @param dateString
 * @returns {boolean}
 */
export function isDateInFuture(dateString) {
  // 获取当前日期并格式化为 YYYY-MM-DD
  const currentDate = new Date();
  // 将输入的日期字符串转换为 Date 对象
  const inputDate = new Date(dateString);
  // 比较两个日期
  return currentDate > inputDate;
}

