@use 'sass:color';

@function to-dark-color($color, $type: 'default') {
  @if $type == 'primary' {
    // 主题色保持原样或轻微调整
    @return $color;
  } @else if $type == 'background' {
    // 背景色变暗
    @return color.mix(#000, $color, 85%);
  } @else if $type == 'text' {
    // 文本色变亮
    @return color.mix(#fff, $color, 85%);
  } @else {
    // 默认暗色转换
    @return color.mix(#222, $color, 30%);
  }
}

$bg-fff: #ffffff;
$color-333: #333333;
$color-303133: #303133;
$color-606266: #606266;
$bg-f1f1f1: #f1f1f1;
$bg-f1f2f6: #f1f2f6;
$color-666: #666666;
$color-e7eaec: #e7eaec;
$color-dddddd: #dddddd;
$bg-eee: #eeeeee;

:root {
  --bg-fff: #{$bg-fff};
  --bg-eee: #{$bg-eee};
  --color-333: #{$color-333};
  --color-303133: #{$color-303133};
  --color-606266: #{$color-606266};
  --color-666: #{$color-666};
  --bg-f1f1f1: #{$bg-f1f1f1};
  --bg-f1f2f6: #{$bg-f1f2f6};
  --tree-model-border-color: #f1f1f1;
  --label-tree-width: 219px;
  --label-tree-border-width: 10px;
  --bg-fff-dark-t: #ffffff;
  --empty-img-opacity: 1;
  --color-e7eaec: #{$color-e7eaec};
  --color-dddddd: #{$color-dddddd};
}

html.dark {
  --bg-fff: #{to-dark-color($bg-fff, 'background')};
  --bg-eee: #365773;
  --color-333: #{to-dark-color($color-333, 'text')};
  --color-303133: #{to-dark-color($color-303133, 'text')};
  --color-606266: #{to-dark-color($color-606266, 'text')};
  --color-666: #{to-dark-color($color-666, 'text')};
  --bg-f1f1f1: #{to-dark-color($bg-f1f1f1, 'background')};
  --bg-f1f2f6: transparent;
  --bg-fff-dark-t: transparent;
  --tree-model-border-color: transparent;
  --label-tree-width: 221px;
  --label-tree-border-width: 0;
  --empty-img-opacity: 0.75;
  --color-e7eaec: transparent;
  --color-dddddd: var(--ep-border-color);
  // 处理编辑器暗黑模式
  // textarea - css vars
  --w-e-textarea-bg-color: #06404e;
  --w-e-textarea-color: #fff;
  --w-e-textarea-border-color: #{to-dark-color(#ccc, 'text')};
  --w-e-textarea-slight-border-color: #{to-dark-color(#e8e8e8, 'text')};
  --w-e-textarea-slight-color: #{to-dark-color(#d4d4d4, 'text')};
  --w-e-textarea-slight-bg-color: #{to-dark-color(#f5f2f0, 'background')};
  --w-e-textarea-selected-border-color: #{to-dark-color(#b4d5ff, 'text')};
  --w-e-textarea-handler-bg-color: #{to-dark-color(#4290f7, 'background')};

  // toolbar - css vars
  --w-e-toolbar-color: #{to-dark-color(#595959, 'text')};
  --w-e-toolbar-bg-color: #06404e;
  --w-e-toolbar-active-color: #{to-dark-color(#333, 'text')};
  --w-e-toolbar-active-bg-color: #{to-dark-color(#f1f1f1, 'background')};
  --w-e-toolbar-disabled-color: #{to-dark-color(#999, 'text')};
  --w-e-toolbar-border-color: #{to-dark-color(#e8e8e8, 'text')};

  // modal - css vars
  --w-e-modal-button-bg-color: #{to-dark-color(#fafafa, 'background')};
  --w-e-modal-button-border-color: #{to-dark-color(#d9d9d9, 'text')};
}
