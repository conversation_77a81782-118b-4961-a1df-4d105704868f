import { request, replaceUrl } from 'sn-base-utils'

export default class ProjBizConsStartWorkReportApi {
  static config = {
    add: {
      url: '/project/sceneStartWorkReport/add',
      method: 'POST'
    },
    remove: {
      url: '/project/sceneStartWorkReport/delete',
      method: 'DELETE'
    },
    update: {
      url: '/project/sceneStartWorkReport/update',
      method: 'PUT'
    },
    view: {
      url: '/project/sceneStartWorkReport/get/{id}',
      method: 'GET'
    },
    pageList: {
      url: '/project/sceneStartWorkReport/page',
      method: 'POST'
    },
    list: {
      url: '/project/sceneStartWorkReport/list',
      method: 'POST'
    },
    startFlow: {
      url: '/project/sceneStartWorkReport/saveAndSubmitProc',
      method: 'POST'
    },
    operateFlow: {
      url: '/project/sceneStartWorkReport/operateFlow',
      method: 'POST'
    },
    submitTask: {
      url: '/project/sceneStartWorkReport/saveAndSubmitTask',
      method: 'POST'
    },
    printTemplate: {
      url: `/project/sceneStartWorkReport/printTemplate`,
      method: 'POST'
    },
    upload: {
      url: `/file/basics/uploadFile`,
      method: 'POST'
    },
    download: {
      url: `/file/basics/fileDownload`,
      method: 'POST'
    },
    importDetail: {
      url: '/project/sceneStartWorkReport/import/excel',
      method: 'POST'
    },
    fillTemplateAndDownload: {
      url: `/project/sceneStartWorkReport/fillTemplateAndDownload/{id}`,
      method: 'POST'
    },
    queryorgCompanyList: {
      url: `/project/projectTeam/queryOrgCompanyList`,
      method: 'POST'
    },
    downloadFormFile: {
      url: '/project/sceneStartWorkReport/get/{id}',
      method: 'GET'
    }
  }

  /**
   * 新增现场开工报审
   * @param data
   * @returns {*}
   */
  static add(data) {
    return request({
      url: this.config.add.url,
      method: this.config.add.method,
      data: data
    })
  }

  /**
   * 删除现场开工报审
   * @param data
   * @returns {*}
   */
  static remove(data) {
    return request({
      url: this.config.remove.url,
      method: this.config.remove.method,
      data: data
    })
  }

  /**
   * 更新现场开工报审
   * @param data
   * @returns {*}
   */
  static update(data) {
    return request({
      url: this.config.update.url,
      method: this.config.update.method,
      data: data
    })
  }

  /**
   * 查询现场开工报审详细
   * @param id
   * @returns {*}
   */
  static view(id) {
    return request({
      url: replaceUrl(this.config.view.url, { id }),
      method: this.config.view.method
    })
  }

  /**
   * 分页查询现场开工报审列表
   * @param data
   * @returns {*}
   */
  static pageList(data) {
    return request({
      url: this.config.pageList.url,
      method: this.config.pageList.method,
      data: data
    })
  }

  /**
   * 导入施工图出图计划列表
   * @returns {*}
   */
  static importDetail(data) {
    return request({
      url: this.config.importDetail.url,
      method: this.config.importDetail.method,
      data: data,
      Headers: { 'Content-Type': 'multipart/form-data' }
    })
  }

  /**
   * 全部现场开工报审列表
   * @returns {*}
   */
  static list(data) {
    return request({
      url: this.config.list.url,
      method: this.config.list.method,
      data: data
    })
  }

  /**
   * 工作流-启动流程
   * @returns {*}
   */
  static startFlow(data) {
    return request({
      url: this.config.startFlow.url,
      method: this.config.startFlow.method,
      data: data
    })
  }

  /**
   * 工作流-完成任务
   * @returns {*}
   */
  static submitTask(data) {
    return request({
      url: this.config.submitTask.url,
      method: this.config.submitTask.method,
      data: data
    })
  }

  /**
   * 更新变更申请单
   * @param data
   * @returns {*}
   */
  static operateFlow(data) {
    return request({
      url: this.config.operateFlow.url,
      method: this.config.operateFlow.method,
      data: data
    })
  }

  /**
   * 工作流-打印模板
   */
  static printTemplate(data) {
    return request({
      url: this.config.printTemplate.url,
      method: this.config.printTemplate.method,
      data: data
    })
  }

  /**
   * 填充模版并导出所有附件
   * @param id
   * @returns {*}
   */
  static fillTemplateAndDownload(id) {
    return request({
      url: replaceUrl(this.config.fillTemplateAndDownload.url, { id }),
      method: this.config.fillTemplateAndDownload.method
    })
  }

  /**
   * 获取项目团队
   * @param data
   * @returns {*}
   */
  static queryorgCompanyList(data) {
    return request({
      url: this.config.queryorgCompanyList.url,
      method: this.config.queryorgCompanyList.method,
      data: data
    })
  }

  /**
   * 下载表单文件
   * @param id
   * @returns {*}
   */
  static downloadFormFile(id) {
    return request({
      url: replaceUrl(this.config.downloadFormFile.url, { id }),
      method: this.config.downloadFormFile.method
    })
  }
}
