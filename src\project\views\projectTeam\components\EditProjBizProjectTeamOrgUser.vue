<template>
  <el-form :model="formData" :rules="rules" ref="snForm" label-width="100px" label-position="right"
    :disabled="type == 'view'" style="overflow-x: hidden;">
    <el-row :gutter="16" :span="24">
      <el-col :span='24'>
        <el-form-item label="姓名" prop="userName">
          <el-input v-model="formData.userName" type="text" placeholder="请选择人员">
            <template #append>
              <el-button :icon="Search" @click="selectUserDialog"></el-button>
            </template>
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span='24'>
        <el-form-item label="角色" prop="roleName">
          <el-input v-model="formData.roleName" type="text" placeholder="请选择角色" :disabled="true">
            <template #append>
              <el-button :icon="Search" @click="selectRoleDialog"></el-button>
            </template>
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span='24'>
        <el-form-item label="工号" prop="workNo">
          <el-input v-model="formData.workNo" type="text" placeholder="">
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span='24'>
        <el-form-item label="岗位工种" prop="workType">
          <el-select v-model="formData.workType">
            <el-option v-for="(item, index) in positionType" :key="index" :label="item.label"
              :value="item.value"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span='24'>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="formData.phone" type="text" placeholder="">
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span='24'>
        <el-form-item label="身份证号" prop="idCard">
          <el-input v-model="formData.idCard" type="text" placeholder="">
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span='24'>
        <el-form-item label="籍贯" prop="nativePlace">
          <el-input v-model="formData.nativePlace" type="text" placeholder="">
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span='24'>
        <el-form-item label="政治面貌" prop="politicalStatus">
          <el-select v-model="formData.politicalStatus">
            <el-option v-for="(item, index) in project_team_politicalStatus" :key="index" :label="item.label"
              :value="item.value"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span='24'>
        <el-form-item label="民族" prop="nationality">
          <el-select v-model="formData.nationality">
            <el-option v-for="(item, index) in project_team_nationality" :key="index" :label="item.label"
              :value="item.value"></el-option>
          </el-select>
        </el-form-item>
      </el-col>

      <el-col :span='24'>
        <el-form-item label="住址" prop="address">
          <el-input v-model="formData.address" type="text" placeholder="">
          </el-input>
        </el-form-item>
      </el-col>

    </el-row>
  </el-form>
</template>

<script setup>
import ProjBizProjectTeamOrgApi from '@/project/api/projectTeam/ProjBizProjectTeamOrg.js'
import { watch, ref, toRef, defineProps, defineExpose, getCurrentInstance } from 'vue';
import { useDicts } from '@/common/hooks/useDicts'
const { proxy } = getCurrentInstance()
const props = defineProps({
  data: Object,
});

const { project_team_nationality, project_team_politicalStatus, positionType } = useDicts(["project_team_nationality", "project_team_politicalStatus", "positionType"])

const snForm = ref()
const type = toRef(props.data?.type);
let formData = ref({});
let rules = ref({

});

watch(props.data?.rowId, (value) => {
  if (props.data?.type == "edit") {
    getDataById(props.data?.rowId);
  } else {
    formData.value = { orgId: props.data?.orgId }
  }
}, { immediate: true })


function getDataById(id) {
  ProjBizProjectTeamOrgApi.viewDetail(id).then((resp) => {
    formData.value = resp.data;
  });
}

function getFormData() {
  return { ...formData.value, projectId: formData.value.projectId ? formData.value.projectId : sessionStorage.getItem('projectId') };
}

function selectUserDialog() {
  proxy.$SelectUser({
    multiple: false,
    onSuccess(res) {
      console.log(res.data);
      if (Array.isArray(res.data) && res.data.length) {
        const orgInfo = res.data[0];
        if (orgInfo.userStatus == 1) {
          proxy.$message.success("选择失败，当前人账号已停用");
        } else {
          formData.value = {
            ...formData.value,
            workNo: orgInfo.userCode,
            userName: orgInfo.userName,
            phone: orgInfo.phonenumber,
            userId: orgInfo.userId

          }
        }
      }
    }
  })

}


function selectRoleDialog() {
  proxy.$SelectRole({
    multiple: false,
    onSuccess(res) {
      console.log(res.data);
      if (Array.isArray(res.data) && res.data.length) {
        const roleInfo = res.data[0];
        formData.value["roleId"] = roleInfo.id,
          formData.value["roleName"] = roleInfo.roleName

      }
    }
  })
}

function submitData() {
  return new Promise((resolve) => {
    snForm.value.validate((flag) => {
      if (!flag) {
        return false;
      } else {
        if (type.value === "add") {
          resolve(saveData());
        } else {
          resolve(editData());
        }
      }
    });
  });
}

 //新增操作
function saveData() {
  const formData = getFormData();
  return ProjBizProjectTeamOrgApi.addUser(formData).then(() => {
    return true;
  });
}

function editData() {
  //编辑操作
  const formData = getFormData();
  return ProjBizProjectTeamOrgApi.addUser(formData).then(() => {
    return true;
  });
}
defineExpose({
  getFormData,
  submitData,
});
</script>

<style lang="scss" scoped></style>
