<template>
  <flow-page-container ref="flowPageContainerRef" @handlerAction="handlerAction" @initData="initData"
                       @handlerPrint="handlerPrint" :closeBtn="isShowCloseBtn" :approvalOption="approvalOption" @approvalOptionCallback="getApprovalOption">
    <div style="width: 100%">
      <el-form :model="formData" :rules="rules" ref="formRef" label-width="100px" label-position="left" :disabled="type == 'view'">
        <el-card class="box-card" style="width: 100%;height:auto">
          <fieldset class="fieldset2">
            <legend>
              <span class="el-button--primary"></span>通知单预览
            </legend>
            <el-row :gutter="16" :span="24">
              <el-col :span='16'>
              </el-col>
              <div class="mainContent">
                <div class="qualityDom">
                  <h3>通知单</h3>
                  <div class="titletop">
                    <el-row :gutter="16" :span="24">
                      <el-col :span='12'>
                        <el-form-item label="工程名称：" prop="projectName">
                          <el-input v-model="formData.projectName" type="text" placeholder="请输入" disabled></el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span='12'>
                        <el-form-item label="编号：" prop="noticeNum">
                          <el-input v-model="formData.noticeNum" type="text" placeholder="请输入" clear required :disabled="type == 'view' || (type !== 'edit' && taskKey)"></el-input>
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <div class="form-content">
                      <div class="contents padding-20">致：<span>
                        <el-form-item prop="unitId" label="" label-width="0" style="width:100%;">
<!--                          <org-dialog v-model="formData.unitId" type="allOrg" style="width:100%;" :multiple="false" :disabled="type == 'view' || (type !== 'edit' && taskKey)" @change="changeOrg"></org-dialog>-->
                        <el-select v-model="formData.unitId" clearable placeholder="请选择" @click="showMain" type="primary">
                          <el-option v-for="(item, index) in mainUnitOption" :key="index" :label="item.label" :value="item.value"></el-option>
                        </el-select>
                        </el-form-item>
                        </span></div>
                      <div class="contents textindent padding-auto">
                        <el-col :span='24'>
                          <el-form-item label="主题：" prop="title">
                            <el-input type="textarea" v-model="formData.title" placeholder="请输入" rows="2"
                                      clearable></el-input>
                          </el-form-item>
                        </el-col>
                        <el-col :span='24'>
                          <el-form-item label="内容：" prop="content">
                            <el-input type="textarea" v-model="formData.content" placeholder="请输入" rows="5"
                                      clearable></el-input>
                          </el-form-item>
                        </el-col>
                        <div v-if="formData.isReply == 'Y'">
                          请于&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;年&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;月&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;日前回复本通知
                        </div>
                      </div>
                      <div class="contents textindent padding-auto" v-if="formData.isReply == 'Y'">
                        <div style="padding-left: 20px">
                          <el-form-item label="" prop="replyDate" label-width="0">
                            <el-date-picker type="date" v-model="formData.replyDate" :disabled="type == 'view' || (type !== 'edit' && taskKey)"
                                            format="YYYY-MM-DD" value-format="YYYY-MM-DD" clearable></el-date-picker>
                          </el-form-item>
                        </div>
                      </div>
                      <div class="contents">
                        <div class="right-content">
                          <div><span class="label">盖章：</span></div>
                          <div><span class="label">负责人：</span></div>
                          <div><span class="label">提出人：</span><span class="sp-inline"><el-input v-model="formData.createName" :disabled="true"></el-input></span></div>
                          <div><span class="label">日期：</span><span class="sp-inline">
                            <el-form-item label="" prop="startDate" label-width="0">
                            <el-date-picker type="date" v-model="formData.startDate" :disabled="type == 'view' || (type !== 'edit' && taskKey)"
                                            format="YYYY-MM-DD" value-format="YYYY-MM-DD" clearable></el-date-picker>
                          </el-form-item>
                          </span></div>
                        </div>
                      </div>
                      <div class="contents padding-20 textline">
                        注：根据工程联系单的内容，所涉及的单位可增加或减少。
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-row>
          </fieldset>
        </el-card>
        <el-card class="box-card" style="width: 100%;">
          <fieldset class="fieldset2">
            <el-row :gutter="16" :span="24">
              <el-col :span='12'>
                <el-form-item label="是否需提交通知回复单" prop="isReply" label-width="200">
                  <el-radio-group v-model="formData.isReply" @change="changeIsReply" prop="isReply" :disabled="formData.procInstanceId !== ''">
                    <el-radio v-for="(item, index) in sys_yn" :key="index" :label="item.value">{{item.label}}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
          </fieldset>
        </el-card>
      </el-form>
      <el-card class="box-card" style="width: 100%;" v-if="isShowReply">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>回复信息及附件
          </legend>
          <el-row :gutter="16" :span="24">
            <el-col :span='24'>
              <el-form-item label="主送单位回复信息" prop="replyContent" label-width="160">
                <el-input type="textarea" v-model="formData.replyContent" placeholder="请输入" rows="5" clearable :disabled="contentFlag"></el-input>
              </el-form-item>
              <project-document-storage-ui-table
                ref="replyRef"
                :type="`commReply`"
                :relevantId="formData.id"
                :isPageSearch="false"
                :isDeleteMinio="false"
                :isHasAi="false"
                :file-serial-number-builder="replySerialNumberBuilder"
                :preview-config="previewConfig"
                :isShowAddBtn="isShowAddBtn"
                :isShowDelBtn="isShowDelBtn"
                :isShowPreviewBtn="true"
                :isShowDownloadBtn="true"
                :isShowLinkBtn="false"
                :isShowDownloadBatchBtn="true"
              ></project-document-storage-ui-table>
            </el-col>
          </el-row>
        </fieldset>
      </el-card>
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>附件信息
          </legend>
          <project-document-storage-ui-table
            ref="commref"
            :type="`commNotice`"
            :relevantId="formData.id"
            :isPageSearch="false"
            :isDeleteMinio = "type !== 'view'"
            :isHasAi = "isHasAi"
            :file-serial-number-builder="fileSerialNumberBuilder"
            :preview-config="previewConfig"
            :isShowAddBtn="type !== 'view'"
            :isShowDelBtn="type !== 'view'"
            :isShowLinkBtn="false"
          ></project-document-storage-ui-table>
        </fieldset>
      </el-card>
    </div>
  </flow-page-container>
</template>

<script setup>
import { generateWordDocument, download } from '@/project/components/downloadWord/word.js';
import ProjBizConsStartWorkReportApi from '@/project/api/constructionManagement/sceneStartWorkReport/ProjBizConsStartWorkReport.js'
import ProjInfoMApi from '@/project/api/projectInfo/ProjInfoM'
import __classNameVariable__Api from '@/project/api/comprehensiveManagement/communication/notice/ProjBizCommunicationNoticeM.js'
import store from '@/store'
import { kkFileViewUrl } from "@/config";
//TODO 引入所需JS
import {
  nextTick,
  onMounted,
  ref,
  reactive,
  toRef,
  defineProps,
  defineExpose,
  getCurrentInstance
} from 'vue';
import {
  useRoute,
  useRouter
} from 'vue-router'
import { useDicts } from '@/common/hooks/useDicts'
import dayjs from 'dayjs'
import BpmTaskApi from '@/project/api/bpm/bpmTask'
import FlowPageContainer from '@/common/components/exportFile/FlowPageContainer.vue'
import { btnHandle } from '@/project/components/hooks/buttonChangeName'
const {
  sys_yn
} = useDicts(["sys_yn"])
let auth = new Map();
const {
  proxy
} = getCurrentInstance()
const route = useRoute();
const router = useRouter();
const props = defineProps({
  data: Object,
});
const formRef = ref()
const taskInfo = ref()
const type = toRef(props.data?.type);
let isShowReply = ref(false)
let contentFlag = ref(false)
let isShowAddBtn = ref(true)
let isShowDelBtn = ref(true)
const commref = ref(null);
const replyRef = ref(null);
const isShowCloseBtn = ref(false);
const FlowActionType = ref(proxy.FlowActionType);
const res=ref()
const flowPageContainerRef = ref();
let actKey = ref("")
let mainUnitOption = ref([{
  label: "请选择",
  value: ''
}]);
let mainUnitCheck = ref([]);
const state = reactive({
  flowData: {
    businessKey: "",
    procInstId: "",
    procDefKey: "",
    taskId: "",
    formConfig: [],
    variableList: [],
    customPropertyConfigList: []
  },
  formAuth: {},
  taskFlag: 1,
  firstAct: false,
  handlerClose: null,
  rootFields: {},
  fullFieldNameMap: {},
  formAuthList: {}
})
let approvalOption = props.data?.approvalOption ? props.data?.approvalOption : route.query?.procInstId ? ref({
  isShowApprovalList: true,
  isShowFlowDiagram: true,
  procInstId: route.query?.procInstId
}) : ref({
  isShowApprovalList: false,
  isShowFlowDiagram: false,
  procInstId: ''
});
let formData = ref({
  projectName: '',
  noticeNum: '',
  unitId: '',
  unitName: '',
  parentNames: '',
  projectId: '',
  fileId: '',
  isReply: '',
  replyFileId: '',
  replyContent: '',
  title: '',
  content: '',
  createName: '',
  replyDate: '',
  submittingUnit: '',
  docState: '1',
  startDate: '',
  procInstanceId: ''
});
let rules = ref({
  startDate:  [{
    required: false,
    message: "请选择"
  }],
  replyContent:  [{
    required: true,
    message: "请选择"
  }],
  unitId:  [{
    required: true,
    message: "请选择"
  }],
  noticeNum:  [{
    required: true,
    message: "请输入"
  }],
  title: [{
    required: true,
    message: "请输入"
  }],
  createName: [{
    required: false,
    message: ""
  }],
  content: [{
    required: true,
    message: "请输入"
  }],
  isReply: [{
    required: true,
    message: "请选择"
  }],
  replyDate: [{
    required: true,
    message: "请选择"
  }]});
const optionList = ref([])
//导出word
const generatedFile = ref(null);
const startGeneration = async () => {
  try {
    let form = {
      ...formData.value
    }
    let fileType = ''
    if(form.isReply === 'Y') {
      fileType = 'noticeYes'
      if(form.replyDate) {
        const formattedDate = dayjs(form.replyDate);
        form.year = formattedDate.year();   // 年
        form.month = formattedDate.month() + 1; // 月
        form.day = formattedDate.date();
      } else {
        form.year = '';   // 年
        form.month = ''; // 月
        form.day = '';
      }
    } else {
      fileType = 'noticeNo'
    }
    if(form.startDate) {
      const formattedDate = dayjs(form.startDate);
      form.startYear = formattedDate.year();   // 年
      form.startMonth = formattedDate.month() + 1; // 月
      form.startDay = formattedDate.date();
    } else {
      form.startYear = '';   // 年
      form.startMonth = ''; // 月
      form.startDay = '';
    }
    generatedFile.value = await generateWordDocument(form, fileType);
    if (generatedFile.value) {
      download(generatedFile.value, '通知单.docx');
      proxy.$message.success("word下载成功！");
    } else {
    }
  } catch (error) {
  }
};
const startEmpty = async () => {
  try {
    generatedFile.value = await generateWordDocument(null, 'noticeEmpty');
    if (generatedFile.value) {
      download(generatedFile.value, '通知单模板.docx');
      proxy.$message.success("word下载成功！");
    } else {
    }
  } catch (error) {
  }
};
formData = toRef(Object.keys(props.data?.formData || {}).length > 0 ? {
  ...props.data?.formData
} : toRef(formData.value));
if(!formData.value.submittingUnit){
  formData.value.submittingUnit = JSON.parse(store.state.user.orgName)?.find((item) => item.id === store.state.user.defaultOrg)?.orgName
}
if(!formData.value.createName){
  formData.value.createName = store.state.user.userInfo.userName
}
if(!formData.value.startDate) {
  formData.value.startDate = dayjs().format('YYYY-MM-DD HH:mm:ss')
}
if(formData.value.unitId) {
  mainUnitOption.value.push({
    label: formData.value.unitName,
    value: formData.value.unitId
  })
  mainUnitCheck.value.push({
    orgName: formData.value.unitName,
    id: formData.value.unitId,
    parentNames: formData.value.parentNames,
  })
}
if(formData.value.isReply === 'Y' && type.value === 'view') {
  contentFlag = true
  isShowReply = true
  isShowAddBtn = false
  isShowDelBtn = false
}
// 获取项目名称
function getProjectInfo() {
  let id = sessionStorage.getItem('projectId')
  formData.value.projectId = sessionStorage.getItem('projectId')
  ProjInfoMApi.view(id).then((resp) => {
    if (!!resp.data) {
      formData.value.projectName = resp.data.projName
    }
  })
}
function changeIsReply(e) {
  if(e === 'Y'){
    formData.value.replyDate = dayjs().format('YYYY-MM-DD HH:mm:ss')
  } else {
    formData.value.replyDate = null
  }
}
  // 定义组件参数
const fileType = ref("commNotice"); // 业务类型标识
const relevantId = ref(props.data?.formData?.id ?? null) // 关联业务ID，新增业务的时候传null（⽰例值）
const isHasAi = ref(false); // AI审查功能开关


/**
 * 预览配置对象
 * - isExternalPreview: 是否使⽤⾃定义预览（false=内置kkfileview）
 * - substitute: 替换Minio返回URL中的字符串（⽰例：{"原始字符串":"替换值"}）
 * - previewServerUrl: 内置预览服务地址
 */
const previewConfig = ref({
  isExternalPreview: false,
  substitute: { "akey": "avalue" },
  previewServerUrl: kkFileViewUrl,
});
function showMain() {
  if(type.value !== 'view') {
    proxy.$SelectOrg({
      multiple: false,
      checkData: mainUnitCheck,
      onSuccess(res) {
        mainUnitCheck.value = []
        res.data.forEach(item => {
          mainUnitOption.value.push({
            label: item.orgName,
            value: item.id
          })
          mainUnitCheck.value.push({
            orgName: item.orgName,
            id: item.id,
            parentNames: item.parentNames,
          })
          formData.value.unitName = item.orgName
          formData.value.parentNames = item.parentNames
        })
        formData.value.unitId = res.ids[0] ? res.ids[0] : res.ids[1]
      }
    })
  }
}
function getTaskInfo() {
  return taskInfo.value
}
// 文件序列号生成函数
function fileSerialNumberBuilder() {
  return 'COMMNOTICE' + Math.floor(Math.random() * 10000)
}
function replySerialNumberBuilder() {
  return "COMMNOTICE_REPLY" + Math.floor(Math.random()*10000)
}
function getListData() {
  if (commref.value) {
    let list = commref.value.getListData()
    if(list) {
      list = list.map(item => ({
        ...item,
        createName: store.state.user.userInfo.userName
      }));
      formData.value.fileId = list.map(item => item.fileId).join(',')
      formData.value.projBizDmStgMDtoList = list
    }
  }
}
function getReplyData() {
  if (replyRef.value) {
    let list = replyRef.value.getListData()
    if(list) {
      formData.value.replyFileId =  list.map(item => item.fileId).join(',')
    }
  }
}
function getFormData() {
  return formData.value
};

function submitData() {
  return new Promise((resolve) => {
    formRef.value.validate((flag) => {
      if (!flag) {
        return false;
      } else {
        if (type.value === "add") {
          resolve(saveData());
        } else {
          resolve(editData());
        }
      }
    });
  });
}

function saveData() {
  //新增操作
  const formData = getFormData();
  formData.replyDate = formData.replyDate
    ? dayjs(formData.replyDate).format('YYYY-MM-DDTHH:mm:ss') // 转为 ISO 8601
    : null
  getListData()
  return __classNameVariable__Api.add(formData).then(() => {
    proxy.$message.success("新增成功");
    return true;
  });
}
function getSaveFormData(formData) {
  return new Promise((resolve) => {
    formRef.value.validate((flag) => {
      if (!flag) {
        return false;
      } else {
        getListData()
        let form = {
          ...formData
        }
        form.replyDate = form.replyDate
          ? dayjs(form.replyDate).format('YYYY-MM-DDTHH:mm:ss') // 转为 ISO 8601
          : null
        form.startDate = form.startDate
          ? dayjs(form.startDate).format('YYYY-MM-DDTHH:mm:ss') // 转为 ISO 8601
          : null
        if (formData.id) {
          resolve(__classNameVariable__Api.update(form))
        } else {
          resolve(__classNameVariable__Api.add(form).then((resp) => {
            if(resp.data) {
              formData.id = resp.data.id;
            }
          }))
        }
      }
    });
  });
};
function editData() {
  //编辑操作
  const formData = getFormData();
  formData.replyDate = formData.replyDate
    ? dayjs(formData.replyDate).format('YYYY-MM-DDTHH:mm:ss') // 转为 ISO 8601
    : null
  getListData()
  return __classNameVariable__Api.update(formData).then(() => {
    proxy.$message.success("修改成功");
    return true;
  });
}

function submitFlowTask(resp) {
  res.value=resp;
  return proxy.$confirm('确定提交当前单据?', '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
    const formDatas = getFormData();
    // formDatas.value.createTime = null
    // formDatas.value.updateTime = formDatas.value.updateTime = dayjs().format('YYYY-MM-DDTHH:mm:ss')
    return BpmTaskApi.listRuTaskByProcInstId({
      procInstId: formDatas.procInstanceId
    }).then((params) => {
      flowPageContainerRef.value.handlerActionSubmit(params.data[0],1);
    })
  }).catch(() => {
    return true;
  })
}
function getSubmitTask(taskActionDto) {
  getReplyData()
  if(isShowReply.value && taskActionDto.actionType === 'agree' && (!formData.value.replyFileId || !formData.value.replyContent)) {
    proxy.$message.error("请回复信息并上传附件");
    return false
  }
  formData.value.startDate = formData.value.startDate
    ? dayjs(formData.value.startDate).format('YYYY-MM-DDTHH:mm:ss') // 转为 ISO 8601
    : null
  /**
   * 工作流提交任务功能
   */
  return __classNameVariable__Api.submitTask({
    busiDto: formData.value,
    taskActionDto: taskActionDto
  });
};

function getStartFlow(formData, startProcDto) {
  return new Promise((resolve) => {
    formRef.value.validate((flag) => {
      if (!flag) {
        return false;
      } else {
        formData.replyDate = formData.replyDate
          ? dayjs(formData.replyDate).format('YYYY-MM-DDTHH:mm:ss') // 转为 ISO 8601
          : null
        formData.startDate = formData.startDate
          ? dayjs(formData.startDate).format('YYYY-MM-DDTHH:mm:ss') // 转为 ISO 8601
          : null
        /**
         * 工作流启动流程功能
         */
        resolve(__classNameVariable__Api.startFlow({
          busiData: formData,
          startProcDto: startProcDto
        }).then(respon => {
          state.flowData.procDefKey = respon.data[0].procDefKey
          state.flowData.procInstId = respon.data[0].procInstId
          state.flowData.businessKey = respon.data[0].businessKey
          state.flowData.taskId = respon.data[0].taskId
          taskInfo.value = respon.data[0];
          nextTick(() => {
            btnHandle(props.data.el.lastChild)
          })
        }))
      }
    })
  })
};
//打开确认框
function handlerOpenConfirm(taskInfo,resp) {
  __classNameVariable__Api.view(taskInfo.businessKey).then((resp) => {
    if (resp.data) {
      formData.value = resp.data;
      relevantId.value = resp.data.id;
      isShowAddBtn.value = true;
      isShowDelBtn.value = true;
    }
  })
  res.value=resp;
  flowPageContainerRef.value.handlerActionSubmit(taskInfo,1);
}
function handlerAction(operation, taskComment, handlerClose) {
  /**
   * 工作流操作功能
   */
  // 启动并保存草稿后再次提交
  if (
    (operation.type == FlowActionType.value.SAVESTART ||
      operation.type == FlowActionType.value.SAVE ||
      operation.type == FlowActionType.value.START) &&
    !formData.value.taskId
  ) {
    let startProcDto = {
      procDefKey: formData.value.procDefKey,
      businessKey: null,
      clientId: null,
      ...taskComment?.dialogRefs?.getFormData()
    }
    let httpCall = null
    if (operation.type == FlowActionType.value.SAVESTART) {
      httpCall = getStartFlow(formData, startProcDto)
    } else if (operation.type == FlowActionType.value.SAVE) {
      httpCall = getSaveFormData(formData)
    }
    httpCall.then(() => {
      proxy.$modal.msgSuccess('提交成功')
      taskComment.close && taskComment.close()
      handlerClose()
    })
  } else {
    operation.type == FlowActionType.value.START || operation.type == FlowActionType.value.SAVESTART
      ? (operation.type = FlowActionType.value.AGREE)
      : operation.type
    let taskActionDto = {
      taskId: state.flowData.taskId,
      procInstId: state.flowData.procInstId,
      actionType: operation.type,
      ...taskComment?.dialogRefs?.getFormData(),
      taskAssignees:taskComment?.dialogRefs?.getFormData()?.taskAssignees ? taskComment.dialogRefs.getFormData().taskAssignees.join(',') : ''
    }
    getSubmitTask(taskActionDto)
      .then(() => {
        proxy.$modal.msgSuccess('任务办理成功')
        taskComment.close && taskComment.close()
        let businessKey = route.query.businessKey
        if(res){
          if (businessKey) {
            handlerClose()
          } else {
            props.data.closeDialog(proxy)
          }
        }else {
          handlerClose()
        }
      })
      .catch(() => {
        taskComment.close && taskComment.close()
      })
  }
}

function getFieldForm() {
  let rootFields = {
    ...state.rootFields
  }
  let fieldList = []
  let keys = []
  for (let key in rootFields) {
    keys.push(key)
    fieldList.push(rootFields[key])
  }
  const result = processAuthFieldForm ? processAuthFieldForm(fieldList) : fieldList;
  const message = {
    type: "getTemplateRoot",
    pathname: window.location.pathname,
    actKey: getQueryParams("actKey"),
    content: result
  };
  window.parent.postMessage(JSON.parse(JSON.stringify(message)), "*");
};
function getQueryParams(key) {
  let url = window.location.href;
  // 使用正则表达式解析URL中的查询字符串
  var queryString = url.split('?')[1];
  if (!queryString) {
    return {};
  }
  var params = {};
  // 分割查询字符串成单个参数
  var vars = queryString.split("&");
  for (var i = 0; i < vars.length; i++) {
    var pair = vars[i].split("=");
    params[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);
  }
  return params[key];
};
const taskKey = ref(null);
function initData(urlParams, taskInfo, handlerClose) {
  // 如果用户从流程设计点击启动, 如果是在线表单, 则阻止执行initData, 防止在线表单报错
  if (urlParams.menuId && urlParams.openInStart) {
    return
  }
  type.value = 'view'
  if(taskInfo.actKey === 'Activity_4046e3e' || taskInfo.actKey === 'Activity_40b455c') {
    type.value = 'edit'
  } else if (taskInfo.actKey === 'Activity_4fe35f5') {
    isShowReply.value = true
    contentFlag.value = false
  }
  if(taskInfo.actKey === 'Activity_7d98078' || taskInfo.actKey === 'Activity_088cd14') {
    isShowReply.value = true
    contentFlag.value = true
    isShowDelBtn.value = false
    isShowAddBtn.value = false
  }
  actKey = taskInfo.actKey
  state.flowData.procDefKey = taskInfo.procDefKey;
  state.flowData.procInstId = urlParams.procInstId;
  state.flowData.businessKey = urlParams.businessKey;
  state.flowData.taskId = urlParams.taskId;
  state.flowData.fiedPermission = taskInfo.fiedPermission;
  state.flowData.variableList = taskInfo.variableList;
  state.taskFlag = urlParams.taskFlag;
  state.firstAct = taskInfo.firstAct;
  state.handlerClose = handlerClose;
  state.flowData.customPropertyConfigList = taskInfo.customPropertyConfigList
  let fieldPerList = taskInfo.formConfig?.formFieldConfig?.fieldPerList || []
  taskKey.value = taskInfo.taskKey;
  handleFormAuth(fieldPerList);
  initBusiForm();
};

function initBusiForm() {
  /**
   * 工作流初始化表单功能
   */
  let that = proxy
  let routerQueryParams = route.query;
  auth = new Map(Object.entries(state.formAuth));
  if (routerQueryParams) {
    if (routerQueryParams.busiData) {
      formData.value = routerQueryParams.busiData;
    } else {
      if (routerQueryParams.businessKey) {
        formData.id = routerQueryParams.businessKey
        let businessKey = route.query.businessKey;
        __classNameVariable__Api.view(businessKey).then(resp => {
          formData.value = resp.data;
          if(formData.value.unitId) {
            mainUnitOption.value.push({
              label: formData.value.unitName,
              value: formData.value.unitId
            })
            mainUnitCheck.value.push({
              orgName: formData.value.unitName,
              id: formData.value.unitId,
              parentNames: formData.value.parentNames,
            })
          }
          relevantId.value = resp.data.id;
          optionList.value = JSON.parse(formData.value.optionList) || [];
        });
      } else {
        that.$message.error("初始化失败,因为工作流未将流程信息传入！")
      }
    }
  }
};
function handleFormAuth(data) {
  let formAuth = {};
  for (let item of data) {
    let permi = 1;
    if (item.readonly) {
      permi = 2;
    }
    if (item.hidden) {
      permi = 3;
    }
    if (!isCamelCase(item.fieldModelId)) {
      item.fieldModelId = toCamelCase(item.fieldModelId)
    }
    formAuth = {
      ...formAuth,
      [`${item.fieldModelId}_${item.field}`]: permi,
    };
  }
  state.formAuth = formAuth;
  state.formAuthList = JSON.parse(JSON.stringify(formAuth))
};
function handlerPrint(taskComment) {
  /**
   * 工作流打印模板功能
   */
  ProjBizConsStartWorkReportApi.printTemplate({
    id: formData.value.id,
    templateId: taskComment.templateId,
    formData: formData.value
  }).then(
    (res) => {
      router.push({
        name: "PrintDoc",
        query: {
          fileId: res.data,
        },
      });
      taskComment.close && taskComment.close();
    });
};
function processAuthFieldForm(fieldList) {
  /**
   * 工作流表单权限功能：在扫描完成之前，业务可对表单字段进行修改。
   */
  //TODO 这里可以对权限表单的字段进行任意的修改,如果不满足业务需求，可以在这里手动添加你想要的字段
  return fieldList
};
function getApprovalOption(fun) {
  /**
   * 工作流表单权限功能(重要)：应对弹窗无法及时相应的问题，特别增加该函数修复该bug
   */
  nextTick(() => {
    fun(approvalOption.value)
  })
};
onMounted(() => {
  getFieldForm()
  if (!formData.value.id) {
    getProjectInfo()
  }
})
defineExpose({
  getFormData,
  submitData,
  startGeneration,
  startEmpty,
  getSaveFormData,
  getStartFlow,
  submitFlowTask,
  handlerOpenConfirm,
  getTaskInfo
});
</script>

<style lang="scss" scoped>
.mainContent {
  width: 100%;

  .qualityDom {
    width: 56%;
    margin: 0 auto;
  }

  h3 {
    width: 100%;
    text-align: center;
    line-height: 40px;
  }
}

.form-content {
  border: 1px solid #333;
}

.padding-20 {
  padding: 20px;
}

.padding-auto {
  padding: 0 20px;
}

.pad-right-20 {
  padding-left: 20px;
}

.contents {
  span {
    display: inline-block;
    width: 230px;
    padding: 0 5px;
    text-indent: 0ch;
  }
}

.textindent {
  text-indent: 2ch;
}

.leftcon {
  display: inline-block;
  vertical-align: top;
  text-indent: 2ch;
  padding-top: 20px;

  p {
    padding-bottom: 10px;
  }
}

.sp-inline {
  display: inline-block;
}

.right-content {
  // padding-left: 50%;
  padding-left: calc(100% - 460px);
  padding-top: 10px;

  div {
    margin-top: 10px;
  }
  .label {
    text-align: right;
  }
}

.textline {
  margin-top: 20px;
  border-top: 1px solid #333;
}

::v-deep .ep-textarea__inner {
  box-shadow: none;
  outline: none;
}
.preparation {
  padding: 10px 20px;
  ::v-deep .ep-checkbox {
    display: block;
  }
}
.bgW {
  height: 100%;
}
</style>