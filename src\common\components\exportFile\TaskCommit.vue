<template>
  <div>
    <el-dialog
      v-if="!fullScreen"
      title="办理"
      v-model="open"
      :modal="true"
      :append-to-body="true"
      :fullscreen="full"
      z-index="1000"
    >
      <taskCommitForm
        ref="taskCommitFormRef"
        :operation="operation"
        @handleActionCallback="handleActionCallback"
        @handleClose="handleClose"
      >
        <template #customFields="slotProps">
          <slot name="customFields" v-bind="slotProps"></slot>
        </template>
      </taskCommitForm>
      <template #footer>
        <el-button @click="open = false" size="mini">取 消</el-button>
        <el-button type="primary" @click="preHandlerAction" size="mini"
          >确 定</el-button
        >
      </template>
    </el-dialog>
    <taskCommitForm
      v-else
      ref="taskCommitFormRef"
      :operation="operation"
      @handleActionCallback="handleActionCallback"
      @handleClose="handleClose"
    >
      <template #customFields="slotProps">
        <slot name="customFields" v-bind="slotProps"></slot>
      </template>
    </taskCommitForm>
  </div>
</template>
<script>
import{HandelTaskApi as Handel<PERSON>lowTaskApi }   from "sn-bpm-v3";
import taskCommitForm from "./taskCommitForm.vue";
export default {
  props: {
    operation: {
      default: {},
    },
    fullScreen: {
      default: false,
    },
  },

  data() {
    return {
      procDefKey: "",
      commentItemList: [],
      targetTasArr: [],
      copyUserIds: "",
      delTaskAssignees: "",
      open: false,
      formData: {
        comment: "",
        targetTaskKey: "",
        taskAssignees: "",
        copyDataList: [],
      },
      transactSuggestionRules: null,
      identityConfigList: [],

      identityConfigListOptions: {
        menuWidth: 180,
        column: [
          {
            label: "参与者类型",
            prop: "identityType",
            dicData: [
              {
                label: "指定人员",
                value: "user",
              },
              {
                label: "指定角色",
                value: "role",
              },
              {
                label: "指定部门",
                value: "org",
              },
              {
                label: "指定负责人",
                value: "leader",
              },
              {
                label: "指定岗位",
                value: "post",
              },
              {
                label: "指定标签",
                value: "label",
              },
              {
                label: "变量获取",
                value: "variable",
              },
              {
                label: "人员脚本",
                value: "script",
              },
            ],
          },
        ],
      },
    };
  },
  components: { taskCommitForm },
  mounted() {},
  watch: {},
  methods: {
    handleActionCallback(data) {
      this.$emit("handleActionCallback", data);
    },
    handleClose() {
      this.open = false;
    },
    openModal(status) {
      console.log("open", status);
      this.open = status;
    },
    getFormData() {
      return this.$refs.taskCommitFormRef.getFormData();
    },
    preHandlerAction() {
      let handleActionCallbackRes = {
        dialogRefs: {
          getFormData: this.getFormData,
        },
        close: () => {
          this.open = false;
        },
      };
      this.$refs.taskCommitFormRef.preHandlerAction(handleActionCallbackRes);
    },

    delIdentityConfig(item, index) {
      this.identityConfigList.splice(index, 1);
    },
   
    handleClickComment(item) {
      this.formData = {
        ...this.formData,
        comment: item,
      };
    },
    // 获取常用语列表
    getCommentItemList() {
      HandelFlowTaskApi.commentItemList({ procDefKey: this.procDefKey }).then(
        (res) => {
          this.commentItemList = res.data;
        }
      );
    },
    // 获取加签列表
    listRuTaskUserByProcInstId() {
      console.log("listRuTaskUserByProcInstId operation", this.operation);
      HandelFlowTaskApi.listRuTaskUserByProcInstId({
        procInstId: this.operation.procInstId,
        taskId: this.operation.taskId,
      }).then((res) => {
        this.$refs.multiSignRef.setList(res.data);
      });
    },
  },
};
</script>
<style scoped>
.form-single-fragment ::v-deep .el-button {
  margin-left: 0 !important;
  margin-right: 10px !important;
  margin-bottom: 10px;
  white-space: break-spaces;
  height: auto;
  min-height: 32px;
  line-height: 20px;
  text-align: left;
}
</style>
