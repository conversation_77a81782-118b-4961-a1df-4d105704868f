import { request, replaceUrl } from "sn-base-utils";

export default class ProjBizDmStgMApi {
    static config = {
        add: {
            url: '/project/dmStg/add',
            method: 'POST'
        },
        addBatch: {
            url: '/project/dmStg/addBatch',
            method: 'POST'
        },
        view: {
            url: '/project/dmStg/get/{id}',
            method: 'GET'
        },
        pageList: {
            url: '/project/dmStg/page',
            method: "POST"
        },
        pageListHasSize: {
            url: '/project/dmStg/pageHasSize',
            method: "POST"
        },
        remove: {
            url: '/project/dmStg/delete',
            method: 'DELETE'
        },
        list: {
            url: '/project/dmStg/list',
            method: "POST"
        }
    };

    /**
     * 新增文档管理-文档目录
     * @param data
     * @returns {*}
     */
    static add(data) {
        return request({
            url: this.config.add.url,
            method: this.config.add.method,
            data: data
        });
    }

    /**
     * 新增文档管理-文档目录
     * @param data
     * @returns {*}
     */
    static addBatch(data) {
        return request({
            url: this.config.addBatch.url,
            method: this.config.addBatch.method,
            data: data
        });
    }

    /**
     * 查询文档管理-文档目录详细
     * @param id
     * @returns {*}
     */
    static view(id) {
        return request({
            url: replaceUrl(this.config.view.url, { id }),
            method: this.config.view.method
        });
    }

    /**
     * 分页查询文档管理-文档目录列表
     * @param data
     * @returns {*}
     */
    static pageList(data) {
        return request({
            url: this.config.pageList.url,
            method: this.config.pageList.method,
            data: data
        });
    }

    /**
     * 分页查询文档管理-文档目录列表
     * @param data
     * @returns {*}
     */
    static pageListHasSize(data) {
        return request({
            url: this.config.pageListHasSize.url,
            method: this.config.pageListHasSize.method,
            data: data
        });
    }

    /**
     * 删除文档管理-文档目录
     * @param data
     * @returns {*}
     */
    static remove(data) {
        return request({
            url: this.config.remove.url,
            method: this.config.remove.method,
            data: data
        });
    }

    /**
     * 全部文档管理-文档目录列表
     * @returns {*}
     */
    static list(data) {
        return request({
            url: this.config.list.url,
            method: this.config.list.method,
            data: data
        });
    }
}
