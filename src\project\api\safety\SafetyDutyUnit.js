import { request, replaceUrl } from "sn-base-utils";

export default class SafetyDutyUnitApi {
    static config = {
        add: {
            url: '/project/safety/unit/add',
            method: 'POST'
        },
        remove: {
            url: '/project/safety/unit/delete',
            method: 'DELETE'
        },
        update: {
            url: '/project/safety/unit/update',
            method: 'PUT'
        },
        view: {
            url: '/project/safety/unit/get/{id}',
            method: 'GET'
        },
        pageList: {
            url: '/project/safety/unit/page',
            method: "POST"
        },
        list: {
            url: '/project/safety/unit/list',
            method: "POST"
        },
        downloadZip: {
            url: '/project/safety/unit/downloadZip',
            method: "POST"
        },
        downloadFile: {
            url: '/project/safety/unit/downloadFile',
            method: "POST"
        }
    };

    /**
     * 新增安全责任书
     * @param data
     * @returns {*}
     */
    static add(data) {
        return request({
            url: this.config.add.url,
            method: this.config.add.method,
            data: data
        });
    }

    /**
     * 删除安全责任书
     * @param data
     * @returns {*}
     */
    static remove(data) {
        return request({
            url: this.config.remove.url,
            method: this.config.remove.method,
            data: data
        });
    }

    /**
     * 更新安全责任书
     * @param data
     * @returns {*}
     */
    static update(data) {
        return request({
            url: this.config.update.url,
            method: this.config.update.method,
            data: data
        });
    }

    /**
     * 查询安全责任书详细
     * @param id
     * @returns {*}
     */
    static view(id) {
        return request({
            url: replaceUrl(this.config.view.url, { id }),
            method: this.config.view.method
        });
    }

    /**
     * 分页查询安全责任书列表
     * @param data
     * @returns {*}
     */
    static pageList(data) {
        return request({
            url: this.config.pageList.url,
            method: this.config.pageList.method,
            data: data
        });
    }

    /**
     * 全部安全责任书列表
     * @returns {*}
     */
    static list(data) {
        return request({
            url: this.config.list.url,
            method: this.config.list.method,
            data: data
        });
    }
}
