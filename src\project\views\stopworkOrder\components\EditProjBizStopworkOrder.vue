<template>
  <el-form :model="formData" :rules="formRules" ref="formRef" label-width="150px" label-position="right" :disabled="type == 'view'">
    <el-row :gutter="16" :span="24">
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>停工令
          </legend>
          <el-row :gutter="16" :span="24">
            <el-col :span='8'>
              <el-form-item label="关联停工申请" prop="applyNo">
                <el-input v-model="formData.applyNo" type="text" placeholder="自动填写" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="业务编号" prop="projectName">
                <el-input v-model="formData.projectName" type="text" placeholder="必填" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="停工令编号" prop="stopworkOrderNo">
                <el-input v-model="formData.stopworkOrderNo" type="text" placeholder="必填" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="停工原因" prop="stopworkReason">
                <el-input v-model="formData.stopworkReason" type="text" placeholder="例：xxx部位的xxx原因" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="停工部位（工序）" prop="stopworkPart">
                <el-input v-model="formData.stopworkPart" type="text" placeholder="必填" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="停工要求" prop="stopworkRequirement">
                <el-input v-model="formData.stopworkRequirement" type="text" placeholder="请输入" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="施工单位" prop="constructionUnit">
                <el-select v-model="formData.constructionUnit" clearable placeholder=" 选择施工单位">
                  <el-option v-for="(item, index) in construction_unit" :key="index" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="停工日期" prop="stopworkTime">
                <el-date-picker type="date" placeholder="必填" v-model="formData.stopworkTime" format="YYYY-MM-DD" value-format="YYYY-MM-DD" style="width: 100%;" clearable></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
        </fieldset>
      </el-card>
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>附件信息（请上传停工令及相关材料）
          </legend>
          <el-row :gutter="16" :span="24"></el-row>
            <project-document-storage-ui-table
                ref="childRef"
                :type="stopworkOrder"
                :relevantId="formData.id"
                :isPageSearch="false"
                :isDeleteMinio="false"
                :isHasAi="false"
                @on-add-data="onAddData"
                :file-serial-number-builder="fileSerialNumberBuilder"
                :preview-config="previewConfig"
                :isShowAddBtn="type !== 'view'"
                :isShowDelBtn="type !== 'view'"
                :isShowPreviewBtn="true"
                :isShowDownloadBtn="type !== 'view'"
                :isShowLinkBtn="type !== 'view'"
                :isShowDownloadBatchBtn="type !== 'view'"
            ></project-document-storage-ui-table>
        </fieldset>
      </el-card>
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>单据信息
          </legend>
          <el-row :gutter="16" :span="24">
            <el-col :span='8'>
              <el-form-item label="创建人姓名" prop="createName">
                <el-input v-model="formData.createName" type="text" placeholder="" clearable disabled>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="创建时间" prop="createTime">
                <el-date-picker type="datetime" v-model="formData.createTime" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" clearable disabled style="width: 100%;"></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="单据状态" prop="publishStatus">
                <el-select v-model="formData.publishStatus" clearable placeholder="" disabled>
                  <el-option v-for="(item, index) in publishStatusOption" :key="index" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="最近修改人" prop="updateName">
                <el-input v-model="formData.updateName" type="text" placeholder="" clearable disabled>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="最近修改时间" prop="updateTime">
                <el-date-picker type="datetime" v-model="formData.updateTime" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" clearable disabled style="width: 100%;"></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
        </fieldset>
      </el-card>
    </el-row>
  </el-form>
</template>

<script setup>
import store from "@/store";
import {getCurrentFormattedTime} from "@/common/utils/datetime";
import ProjBizStopworkOrderApi from '@/project/api/stopworkOrder/ProjBizStopworkOrder.js' 
import {
  nextTick,
  onMounted,
  ref,
  reactive,
  toRef,
  defineProps,
  defineExpose,
  getCurrentInstance
} from 'vue';
import {
  useRoute,
  useRouter
} from 'vue-router'
const setting = require('../../../../config.js')
const {
  proxy
} = getCurrentInstance()
const route = useRoute();
const router = useRouter();
const props = defineProps({
  data: Object,
});
const formRef = ref()
const type = toRef(props.data?.type);
const stopworkOrder = ref("stopworkOrder");
const childRef = ref(null);
import {
  useDicts
} from "@/common/hooks/useDicts";
const {
  construction_unit
} = useDicts(["construction_unit"])
let formData = ref({
  applyNo: "",
  projectName: "",
  stopworkOrderNo: "",
  stopworkReason: "",
  stopworkPart: "",
  stopworkRequirement: "",
  constructionUnit: "",
  createName: store.state.user && store.state.user.userInfo.userName ? store.state.user.userInfo.userName : null,
  createTime: getCurrentFormattedTime(),
  publishStatus: 0,
  updateName: "",
  updateTime:"",
  projBizDmStgMDtoList: [],
  stopworkTime: "",
});
let formRules = ref({
  applyNo: [],
  projectName: [{
    required: true,
    message: ""
  }],
  stopworkOrderNo: [{
    required: true,
    message: ""
  }],
  stopworkReason: [{
    required: true,
    message: ""
  }],
  stopworkPart: [{
    required: true,
    message: ""
  }],
  stopworkRequirement: [{
    required: true,
    message: ""
  }],
  constructionUnit: [{
    required: true,
    message: ""
  }],
  createName: [],
  createTime: [],
  publishStatus: [],
  updateName: [],
  updateTime: [],
  projBizDmStgMDtoList: [],
  stopworkTime: [{
    required: true,
    message: ""
  }]
});
let option = ref({
  tip: false,
  dialogType: "dialog",
  border: true,
  index: true,
  stripe: true,
  menu: true,
  header: true,
  height: "auto",
  searchSpan: 6,
  searchIcon: true,
  searchMenuSpan: 6,
  searchIndex: 3,
  selection: true,
  showTree: false,
  excelBtn: false,
  delBtn: false,
  editBtn: false,
  delBtnsText: "批量删除",
  addBtnText: "新增",
  customEditBtnText: "编辑",
  customDelBtnText: "删除",
  customViewBtnText: "查看",
  column: [{
    label: "文件名称",
    prop: "file_name",
    columnSlot: false,
    searchSlot: false
  }, {
    label: "文件大小",
    prop: "file_size",
    columnSlot: false,
    searchSlot: false
  }, {
    label: "上传人",
    prop: "upload_person",
    columnSlot: false,
    searchSlot: false
  }, {
    label: "上传时间",
    prop: "upload_time",
    columnSlot: false,
    searchSlot: false
  }, {
    label: "AI检查结果",
    prop: "",
    columnSlot: false,
    searchSlot: false
  }]
});
let listData = ref([]);
let queryForm = ref({
  page: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  filter: {
    createTime: null
  }
});
const publishStatusOption = [
    { label: '草稿', value: 0 },
    { label: '已发布', value: 1 }
  ];

function getPageList() {
  //查询分页列表
  // 查询前处理参数
  const params = handleQueryForm();
  ProjBizStopworkOrderApi.pageList(params).then((res) => {
    listData.value = res.data.dataList;
    queryForm.value.page.total = res.data.totalCount;
  });
}

function onResetSearch() {
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
}

function handleQueryForm() {
  // 处理参数
  const {
    pageSize,
    pageNum
  } = queryForm.value.page;
  const filter = {}
  for (let key in queryForm.value.filter) {
    if (queryForm.value.filter[key]?.length) {
      // sn-curd搜索select会生成$prop的变量
      if (key.startsWith("$")) continue;
      filter[key] = queryForm.value.filter[key];
    }
  }
  delete filter.createTime;
  if (Array.isArray(queryForm.value.filter.createTime) && queryForm.value.filter.createTime?.length === 2) {
    filter["beginCreateTime"] = queryForm.value.filter.createTime[0];
    filter["endCreateTime"] = queryForm.value.filter.createTime[1];
  }
  const searchParams = {
    page: {
      pageSize,
      pageNum,
    },
    filter,
  }
  return searchParams
}

function onChangeSearch(params, done) {
  //搜索按钮操作
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
  done && done();
}
async function onEditData(row) {
  //编辑,新增按钮操作
  let editType = row ? "edit" : "add";
  let rowInfo = await (editType !== "add" ? ProjBizStopworkOrderApi.view(row.id) : {});
  const formData = editType !== "add" ? rowInfo.data : rowInfo;
  //编辑,新增按钮操作
  proxy.$DialogForm.show({
    title: editType == "edit" ? "编辑" : "新增",
    type: option.value.dialogType,
    width: "80%",
    content: EditProjBizStopworkOrder,
    data: {
      formData: formData,
      type: editType,
    },
    callback: (res) => {
      if (res.type && res.type !== 'close') {
        if (res.dialogRefs) {
          res.dialogRefs.submitData().then((flag) => {
            if (flag) {
              getPageList();
              res.close();
            }
          });
        }
      } else {
        res.close();
      }
    }
  });
}

function onDelData(rows) {
  //删除行
  if (!rows || rows.length == 0) {
    proxy.$message.info("请勾选数据！");
    return false;
  }
  let ids = rows.map((item) => {
    return item.id;
  });
  proxy.$modal.confirm("确认删除数据项？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    ProjBizStopworkOrderApi.remove(ids).then((res) => {
      proxy.$message.success("已删除");
      getPageList();
    });
  }).catch(() => {});
}

const getAttachmentList = () => {
  // 假设子组件有 getListData 方法
  return childRef.value?.getListData() || [];
};

formData = toRef(Object.keys(props.data?.formData || {}).length > 0 ? {
  ...props.data?.formData
} : toRef(formData.value));

function getFormData() {

  // 获取附件列表并赋值
  formData.value.projBizDmStgMDtoList = getAttachmentList();
  return formData.value
};

function submitData(buttonType) {
  return new Promise((resolve) => {
    formRef.value.validate((flag) => {
      if (!flag) {
        return false;
      } else {
        if (type.value === "add") {
          resolve(saveData(buttonType));
        } else {
          resolve(editData(buttonType));
        }
      }
    });
  });
}

function saveData(buttonType) {
  //新增操作
  const formData = getFormData();
  if(buttonType==='submit'){
    formData.publishStatus = 1
  }
  if(formData.id===null || formData.id === undefined){
    return ProjBizStopworkOrderApi.add(formData).then((resp) => {
      if(resp.data){
        proxy.$message.success("保存成功");
        formData.id = resp.data.id;
        return true;
      }
    });
  }else{
    return ProjBizStopworkOrderApi.update(formData).then(() => {
      proxy.$message.success("保存成功");
      return true;
    });
  }
}

function editData(buttonType) {
  //编辑操作
  const formData = getFormData();
  if(buttonType==='submit'){
    formData.publishStatus = 1
  }
  return ProjBizStopworkOrderApi.update(formData).then(() => {
    proxy.$message.success("修改成功");
    return true;
  });
}

// 文件序列号生成函数
function fileSerialNumberBuilder() {
  return "TEST" + Math.floor(Math.random()*10000)
}

const previewConfig = ref({
  // 内置预览返回的替换字符,akey代表要替换的字符串，avalue代码替换的字符串
  substitute: {
    "akey": "avalue"
  },
  // 内置预览服务地址
  previewServerUrl: setting.kkFileViewUrl,
})

defineExpose({
  getFormData,
  submitData,
});
</script>

<style lang="scss" scoped>

</style>
