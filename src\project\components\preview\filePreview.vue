<template>
<!--  <div v-loading="loading" style="width: 100%;height:100%">-->
<!--    <iframe :src="previewUrl" v-if="isshow && !isVideo" :style="{width: width,height:height}" id="ifra" ></iframe>-->
<!--    <video :src="videoUrl" v-if="isshow && isVideo" :style="{width: width,height:height}" id="ifra" controls ></video>-->
<!--  </div>-->
</template>

<script>
import { getToken } from "sn-base-utils";
//import * as Base64 from "js-base64";
import FileApi from "sn-base-layout-vue3/packLayout/api/File";

export default {
  name: "FilePreview",
  props: {
    //预览服务器地址
    serverUrl: {
      type: String,
      default: window.KK_FILE_VIEW_URL + "/onlinePreview",
    },
    //文件持久化接口地址
    portUrl: {
      type: String,
      default: '',
    },
    // 预览方式 iframe 内嵌  windows 打开新的窗口
    previewType: {
      type: String,
      default: 'iframe',
    },
    // 宽度
    width: {
      type: [Number, String],
      default: '100%'
    },
    // 高度
    height: {
      type: [Number, String],
      default: '100%'
    },
    //材料id
    materialId: {
      type: String,
      default: ''
    },
    progress: {
      type: [Number, String],
      default: 1
    }
  },
  data() {
    return {
      loading:false,
      isshow: false,
      fileId: '',//文件id
      previewUrl: '',//文件预览地址
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      isVideo: false,
      videoUrl: ""
    };
  },
  methods: {
    // //文件预览 fileId文件id  fileUrl文件地址
    // preview(fileId) {
    //   this.loading = true;
    //   this.isshow = false;
    //   this.fileId = fileId;
    //   const _this = this;
    //   if (this.previewType === 'iframe') {
    //     this.isshow = true;
    //   } else {
    //     window.open(this.previewUrl);
    //   }
    //   this.addEvent()
    //   /*let url =  "http://smartsite.sdepci.com:8090/api/bhScreen/file/basics/fileDownload/" + fileId;
    //   let base64Url = encodeURIComponent(Base64.encode(url))
    //   this.previewUrl = this.serverUrl + "?url=" + base64Url;*/
    //   //查询文件名
    //   FileApi.getFileNameAndUrlById(this.fileId).then(res => {
    //     this.isVideo = false
    //     this.setPageFileType(res.data);
    //
    //     let url = res.data.filePreviewUrl;
    //     let base64Url = encodeURIComponent(Base64.encode(url))
    //     this.previewUrl = this.serverUrl + "?url=" + base64Url;
    //   })
    //
    // },
    // setPageFileType(data) {
    //   let fileArray = data.fileName.split(".");
    //   let fileSuffix = fileArray[fileArray.length - 1];
    //   let typeArray = ["mp4", "flv", "f4v", "webm", "m4v", "mov", "3gp", "3g2", "rm", "rmvb", "wmv", "avi", "asf", "mpg", "mpeg", "mpe", "ts", "div", "dv", "divx", "vob", "dat", "mkv", "lavf", "cpk", "dirac", "ram", "qt", "fli", "flc", "mod"];
    //   for (let i = 0; i < typeArray.length; i++) {
    //     const thisType = typeArray[i];
    //     if (thisType == fileSuffix) {
    //       this.isVideo = true;
    //       this.videoUrl = data.filePreviewUrl;
    //       this.loading = false;
    //     }
    //
    //   }
    //   this.loading=false
    // },
    // addEvent() {
    //   const self = this
    //   this.$nextTick(() =>{
    //     const iframe = document.querySelector('#ifra')
    //     if (iframe.attachEvent) {
    //       iframe.attachEvent("onload", function() {
    //         self.loading = false;
    //       });
    //     } else {
    //       iframe.onload = function() {
    //         self.loading = false;
    //       };
    //     }
    //   })
    // }
  }
};
</script>

<style scoped lang="scss">
</style>
