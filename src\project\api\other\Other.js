import { request, replaceUrl } from "sn-base-utils";

export default class OtherApi {
    static config = {
        getSecondaryUnit: {
            url: '/other/getSecondaryUnit',
            method: 'GET'
        },
    };

    /**
     * 获取所有二级单位
     * @returns {*}
     */
    static getSecondaryUnit() {
        return request({
            url: this.config.getSecondaryUnit.url,
            method: this.config.getSecondaryUnit.method
        });
    }

}
