<template>
    <div style="height: calc(100vh - 88px)">
        <!-- <div>1111111</div> -->
        <iframe :src="src" width="100%" height="100%" frameborder="0" scrolling="yes">
        </iframe>

        <div style="position: absolute; right: 10px; top:10px; color: #fff;" @click="openView">
            <el-icon><FullScreen /></el-icon>
        </div>
    </div>
</template>

<script setup>
import { openBlock, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { getToken } from 'sn-base-utils'
const src = ref();
const route = useRoute();
watch(() => route, (route) => {
    console.log(route.query)
    if(route.query.src ){
        src.value = route.query.src + "?access_token=" + getToken();
    }
}, { immediate: true }); 


function openView(){

     window.open(src.value,"_blank")   


}


</script>

<style lang="scss" scoped></style>
