import { request, replaceUrl } from "sn-base-utils";

export default class ProjBizPersonQualificationApi {
    static config = {
        add: {
            url: '/project/personQualification/add',
            method: 'POST'
        },
        remove: {
            url: '/project/personQualification/delete',
            method: 'DELETE'
        },
        update: {
            url: '/project/personQualification/update',
            method: 'PUT'
        },
        view: {
            url: '/project/personQualification/get/{id}',
            method: 'GET'
        },
        pageList: {
            url: '/project/personQualification/page',
            method: "POST"
        },
        list: {
            url: '/project/personQualification/list',
            method: "POST"
        },
        startFlow: {
            url: '/project/personQualification/saveAndSubmitProc',
            method: "POST"
        },
         operateFlow: {
            url: `/project/personQualification/operateFlow`,
            method: "POST"
        },
        submitTask: {
            url: '/project/personQualification/saveAndSubmitTask',
            method: "POST"
        },
        printTemplate: {
            url: `/project/personQualification/printTemplate`,
            method: "POST"
        }
    };

    /**
     * 新增人员资质报审
     * @param data
     * @returns {*}
     */
    static add(data) {
        return request({
            url: this.config.add.url,
            method: this.config.add.method,
            data: data
        });
    }

    /**
     * 删除人员资质报审
     * @param data
     * @returns {*}
     */
    static remove(data) {
        return request({
            url: this.config.remove.url,
            method: this.config.remove.method,
            data: data
        });
    }

    /**
     * 更新人员资质报审
     * @param data
     * @returns {*}
     */
    static update(data) {
        return request({
            url: this.config.update.url,
            method: this.config.update.method,
            data: data
        });
    }

    /**
     * 查询人员资质报审详细
     * @param id
     * @returns {*}
     */
    static view(id) {
        return request({
            url: replaceUrl(this.config.view.url, { id }),
            method: this.config.view.method
        });
    }

       /**
     * 人员资质的流程状态变更
     * @param data
     * @returns {*}
     */
    static operateFlow(data) {
        return request({
            url: this.config.operateFlow.url,
            method: this.config.operateFlow.method,
            data: data
        });
    }

    /**
     * 分页查询人员资质报审列表
     * @param data
     * @returns {*}
     */
    static pageList(data) {
        return request({
            url: this.config.pageList.url,
            method: this.config.pageList.method,
            data: data
        });
    }


    /**
     * 全部人员资质报审列表
     * @returns {*}
     */
    static list(data) {
        return request({
            url: this.config.list.url,
            method: this.config.list.method,
            data: data
        });
    }

    /**
     * 工作流-启动流程
     * @returns {*}
     */
    static startFlow(data) {
        return request({
            url: this.config.startFlow.url,
            method: this.config.startFlow.method,
            data: data
        });
    }

    /**
     * 工作流-完成任务
     * @returns {*}
     */
    static submitTask(data) {
        return request({
            url: this.config.submitTask.url,
            method: this.config.submitTask.method,
            data: data
        });
    }

    /**
     * 工作流-打印模板
     */
    static printTemplate(data) {
        return request({
            url: this.config.printTemplate.url,
            method: this.config.printTemplate.method,
            data: data
        });
    }
}
