<template>
  <div ref="crud" style="position: relative; width: 100%; height: 100%">
    <sn-crud
      :data="flowPrintTemplateTabelData"
      :option="flowPrintTemplateOption"
    >
    <template slot="radio" slot-scope="{row}">
      <el-radio v-model="templateId"
        :label="row.id"><span></span></el-radio>
    </template>
  </sn-crud>
  </div>
</template>

<script>

export default {
  name: "PrintTemplate",
  data() {
    return {
      flowPrintTemplateTabelData: [],
      templateId:'',
      flowPrintTemplateOption: {
        search: false,
        tip: false,
        dialogType: "",
        border: true,
        index: false,
        stripe: true,
        menu: false,
        header: true,
        searchSpan: 6,
        searchIcon: true,
        searchMenuSpan: 6,
        searchIndex: 3,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        delBtns:false,
        height: "400",
        menuWidth: 100,
        page: false,
        column: [
          {   label: '',
              prop: 'radio',
              width: 60,
              hide: false
            },
            {
            label: "序号",
            prop: "index",
            search: false,
          },
          {
            label: "模板名称",
            prop: "templateName",
            search: false,
          },
        ],
      },
      flowPrintTemplateQueryForm: {
        procInstId:'',
        taskId:'',
      },
      rules: {},
    };
  },
  methods: {
    flowPrintTemplateHandleOption(row){
      this.selectRow = row;
    }
  },
};
</script>

<style lang="scss" scoped></style>
