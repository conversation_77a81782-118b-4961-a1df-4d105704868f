//引入工具
import <PERSON>z<PERSON><PERSON> from 'pizzip';
import Docxtemplater from 'docxtemplater';
// import JSZipUtils from 'jszip-utils';
import { saveAs } from 'file-saver';
import fileTypes from './fileType'
//文件模板转换的base64数据
// 转换 Base64 到 ArrayBuffer
const base64ToArrayBuffer = (base64) => {
    try {
        const binaryString = atob(base64);
        const len = binaryString.length;
        const bytes = new Uint8Array(len);

        for (let i = 0; i < len; i++) {
            bytes[i] = binaryString.charCodeAt(i);
        }

        return bytes.buffer;
    } catch (error) {
        statusMessage.value = `Base64转换错误: ${error.message}`;
        statusClass.value = 'error';
        return null;
    }
};

// 加载 .docx 模板文件
// function loadFile(url, callback) {
//     JSZipUtils.getBinaryContent(url, callback);
// }

// 下载生成的文档
export function download(file, name) {
    saveAs(file, name);
}

// 生成并下载 Word 文档(templatePath是word文档模版地址,data是对应的数据)
export function generateWordDocument(data, fileType) {
    console.log('fileTypes', fileTypes)
    return new Promise((resolve, reject) => {
        try {
            const templateContent = base64ToArrayBuffer(fileTypes[fileType]);
            console.log(templateContent)
            if (!templateContent) {
                throw new Error('无效的模板内容');
            }
            // 加载模板文件内容到 PizZip
            const zip = new PizZip(templateContent);
            const doc = new Docxtemplater(zip, {
                paragraphLoop: true,
                linebreaks: true,
            });
            console.log(doc)
            // 设置模板中的占位符数据
            doc.setData(data);

            // 渲染文档
            doc.render();

            // 生成最终的文档 Blob
            const fileWord = doc.getZip().generate({
                type: 'blob',
                mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            });

            // 返回生成的文档 Blob
            resolve(fileWord);
        } catch {
            console.error('Error rendering document:', error);
            reject(new Error(`Error rendering document: ${error.message}`));
        }

    });
}
