import { request, replaceUrl } from "sn-base-utils";
import { lifecycleBizServiceCode } from "@/config";
export default class ProjBizDesignAsBuiltdrawOutPlanMApi {
    static config = {
        add: {
            url: '/project/asBuiltdrawManager/add',
            method: 'POST'
        },
        remove: {
            url: '/project/asBuiltdrawManager/delete',
            method: 'DELETE'
        },

        deleteDetail: {
            url: '/project/asBuiltdrawManager/deleteDetail',
            method: 'DELETE'
        },
        update: {
            url: '/project/asBuiltdrawManager/update',
            method: 'PUT'
        },
        view: {
            url: '/project/asBuiltdrawManager/get/{id}',
            method: 'GET'
        },
        getOne: {
            url: '/project/asBuiltdrawManager/getOne',
            method: 'GET'
        },
        pageList: {
            url: '/project/asBuiltdrawManager/page',
            method: "POST"
        },

        detailPageList: {
            url: '/project/asBuiltdrawManager/detailPage',
            method: "POST"
        },

        list: {
            url: '/project/asBuiltdrawManager/list',
            method: "POST"
        },

        importDetail: {
            url: '/project/asBuiltdrawManager/importDetail',
            method: "POST"
        },

        importDetailFile: {
            url: '/project/asBuiltdrawManager/importDetailFile',
            method: "POST"
        },

        startFlow: {
            url: '/project/asBuiltdrawManager/saveAndSubmitProc',
            method: "POST"
        },
        submitTask: {
            url: '/project/asBuiltdrawManager/saveAndSubmitTask',
            method: "POST"
        },
        printTemplate: {
            url: `/project/asBuiltdrawManager/printTemplate`,
            method: "POST"
        },

        recallTask: {
            url: '/project/asBuiltdrawManager/recallTask',
            method: 'GET'
        },
    };

    /**
     * 新增竣工图出图计划
     * @param data
     * @returns {*}
     */
    static add(data) {
        return request({
            url: this.config.add.url,
            method: this.config.add.method,
            data: data,
            requestPrefix: lifecycleBizServiceCode
        });
    }

    /**
     * 删除竣工图出图计划
     * @param data
     * @returns {*}
     */
    static remove(data) {
        return request({
            url: this.config.remove.url,
            method: this.config.remove.method,
            data: data,
            requestPrefix: lifecycleBizServiceCode
        });
    }

    /**
   * 删除竣工图出图计划详细
   * @param data
   * @returns {*}
   */
    static deleteDetail(data) {
        return request({
            url: this.config.deleteDetail.url,
            method: this.config.deleteDetail.method,
            data: data,
            requestPrefix: lifecycleBizServiceCode
        });
    }



    /**
     * 更新竣工图出图计划
     * @param data
     * @returns {*}
     */
    static update(data) {
        return request({
            url: this.config.update.url,
            method: this.config.update.method,
            data: data,
            requestPrefix: lifecycleBizServiceCode
        });
    }

    /**
     * 查询竣工图出图计划详细
     * @param id
     * @returns {*}
     */
    static view(id) {
        return request({
            url: replaceUrl(this.config.view.url, { id }),
            method: this.config.view.method,
            requestPrefix: lifecycleBizServiceCode
        });
    }

    /**
     * 查询竣工图出图计划详细
     * @returns {*}
     */
    static getOne(id) {
        return request({
            url: replaceUrl(this.config.getOne.url, {}),
            method: this.config.getOne.method,
            requestPrefix: lifecycleBizServiceCode
        });
    }
    /**
     * 分页查询竣工图出图计划列表
     * @param data
     * @returns {*}
     */
    static pageList(data) {
        return request({
            url: this.config.pageList.url,
            method: this.config.pageList.method,
            data: data,
            requestPrefix: lifecycleBizServiceCode
        });
    }

    /**
     * 分页查询竣工图出图计划详细列表
     * @param data
     * @returns {*}
     */
    static detailPageList(data) {
        return request({
            url: this.config.detailPageList.url,
            method: this.config.detailPageList.method,
            data: data,
            requestPrefix: lifecycleBizServiceCode
        });
    }



    /**
     * 全部竣工图出图计划列表
     * @returns {*}
     */
    static list(data) {
        return request({
            url: this.config.list.url,
            method: this.config.list.method,
            data: data,
        });
    }

    /**
     * 导入竣工图出图计划列表
     * @returns {*}
     */
    static importDetail(data) {
        return request({
            url: this.config.importDetail.url,
            method: this.config.importDetail.method,
            data: data,
            Headers: { "Content-Type": "multipart/form-data" },
            requestPrefix: lifecycleBizServiceCode
        });
    }


    /**
    * 导入竣工图出图文件
    * @returns {*}
    */
    static importDetailFile(data) {
        return request({
            url: this.config.importDetailFile.url,
            method: this.config.importDetailFile.method,
            data: data,
            Headers: { "Content-Type": "multipart/form-data" },
            requestPrefix: lifecycleBizServiceCode
        });
    }




    /**
     * 工作流-启动流程
     * @returns {*}
     */
    static startFlow(data) {
        return request({
            url: this.config.startFlow.url,
            method: this.config.startFlow.method,
            data: data
        });
    }

    /**
     * 工作流-完成任务
     * @returns {*}
     */
    static submitTask(data) {
        return request({
            url: this.config.submitTask.url,
            method: this.config.submitTask.method,
            data: data
        });
    }

    /**
     * 工作流-打印模板
     */
    static printTemplate(data) {
        return request({
            url: this.config.printTemplate.url,
            method: this.config.printTemplate.method,
            data: data
        });
    }

    /**
     * 撤回竣工图出图计划
     * @param data
     * @returns {*}
     */
    static recallTask(data) {
        return request({
            url: this.config.recallTask.url,
            method: this.config.recallTask.method,
            params: data,
            requestPrefix: lifecycleBizServiceCode
        });
    }
}
