import { request, replaceUrl } from "sn-base-utils";

export default class ProjBizDesignChangeAcceptanceCheckMApi {
    static config = {
        add: {
            url: '/project/designChangeAcceptanceCheck/add',
            method: 'POST'
        },
        remove: {
            url: '/project/designChangeAcceptanceCheck/delete',
            method: 'DELETE'
        },
        update: {
            url: '/project/designChangeAcceptanceCheck/update',
            method: 'PUT'
        },
        view: {
            url: '/project/designChangeAcceptanceCheck/get/{id}',
            method: 'GET'
        },
        pageList: {
            url: '/project/designChangeAcceptanceCheck/page',
            method: "POST"
        },
        list: {
            url: '/project/designChangeAcceptanceCheck/list',
            method: "POST"
        },
        startFlow: {
            url: '/project/designChangeAcceptanceCheck/saveAndSubmitProc',
            method: "POST"
        },
        submitTask: {
            url: '/project/designChangeAcceptanceCheck/saveAndSubmitTask',
            method: "POST"
        },
        printTemplate: {
            url: `/project/designChangeAcceptanceCheck/printTemplate`,
            method: "POST"
        },
        operateFlow: {
            url: `/project/designChangeAcceptanceCheck/operateFlow`,
            method: "POST"
        }
    };

    /**
     * 新增变更验收单
     * @param data
     * @returns {*}
     */
    static add(data) {
        return request({
            url: this.config.add.url,
            method: this.config.add.method,
            data: data
        });
    }

    /**
     * 删除变更验收单
     * @param data
     * @returns {*}
     */
    static remove(data) {
        return request({
            url: this.config.remove.url,
            method: this.config.remove.method,
            data: data
        });
    }

    /**
     * 更新变更验收单
     * @param data
     * @returns {*}
     */
    static update(data) {
        return request({
            url: this.config.update.url,
            method: this.config.update.method,
            data: data
        });
    }

    /**
     * 更新变更申请单
     * @param data
     * @returns {*}
     */
    static operateFlow(data) {
        return request({
            url: this.config.operateFlow.url,
            method: this.config.operateFlow.method,
            data: data
        });
    }

    /**
     * 查询变更验收单详细
     * @param id
     * @returns {*}
     */
    static view(id) {
        return request({
            url: replaceUrl(this.config.view.url, { id }),
            method: this.config.view.method
        });
    }

    /**
     * 分页查询变更验收单列表
     * @param data
     * @returns {*}
     */
    static pageList(data) {
        return request({
            url: this.config.pageList.url,
            method: this.config.pageList.method,
            data: data
        });
    }


    /**
     * 全部变更验收单列表
     * @returns {*}
     */
    static list(data) {
        return request({
            url: this.config.list.url,
            method: this.config.list.method,
            data: data
        });
    }

    /**
     * 工作流-启动流程
     * @returns {*}
     */
    static startFlow(data) {
        return request({
            url: this.config.startFlow.url,
            method: this.config.startFlow.method,
            data: data
        });
    }

    /**
     * 工作流-完成任务
     * @returns {*}
     */
    static submitTask(data) {
        return request({
            url: this.config.submitTask.url,
            method: this.config.submitTask.method,
            data: data
        });
    }

    /**
     * 工作流-打印模板
     */
    static printTemplate(data) {
        return request({
            url: this.config.printTemplate.url,
            method: this.config.printTemplate.method,
            data: data
        });
    }
}
