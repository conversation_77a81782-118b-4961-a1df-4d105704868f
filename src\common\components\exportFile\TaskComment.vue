<template>
  <sn-crud
    :table-loading="loading"
    :data="dataList"
    :option="options"
    @on-load="getPageList"
  >
    <template slot="actionTypeName" slot-scope="scope">
      <el-tag
        type="danger"
        v-if="
          scope.row.actionType == FlowActionType.REFUSE ||
          scope.row.actionType == FlowActionType.STOP ||
          scope.row.actionType == FlowActionType.REJECT ||
          scope.row.actionType == FlowActionType.REJECTTOPREV ||
          scope.row.actionType == FlowActionType.REJECTTOSTART ||
          scope.row.actionType == FlowActionType.REJECTTOTASK
        "
      >
        {{ scope.label }}
      </el-tag>
      <el-tag v-else-if="scope.row.actionType" type="primary">{{
        scope.label
      }}</el-tag>
      <el-tag v-else type="success">{{ "待审批" }}</el-tag>
    </template>
  </sn-crud>
</template>

<script>
import{HandelTaskA<PERSON>  }   from "sn-bpm-v3";
export default {
  name: "TaskComment",
  props: {
    procInstId: {
      type: String,
      default: "",
    },
    urlParams:{
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      loading: false,
      dataList: [],
      options: {
        tip: false,
        border: true,
        index: true,
        stripe: false,
        menu: false,
        header: false,
        menuType: "text",
        searchSpan: 6,
        searchIcon: false,
        searchMenuSpan: 6,
        searchIndex: 4,
        selection: false,
        delBtns: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        viewBtn: false,
        excelBtn: false,
        addBtnText: "新增",
        height: "auto",
        calcHeight: 198,
        menuWidth: 0,
        column: [
          {
            label: "流程环节",
            prop: "taskName",
            search: false,
            overHidden: true,
          },
          {
            label: "候选人",
            prop: "hxPeople",
            search: false,
            overHidden: true,
          },
          {
            label: "操作",
            prop: "actionTypeName",
            type: "select",
            search: false,
            overHidden: true,
            // dicData: this.FlowActionType.getList(),
          },
          {
            label: "执行人",
            prop: "approverName",
            search: false,
            overHidden: true,
          },
          {
            label: "审批意见",
            prop: "taskComment",
            search: false,
          },
          {
            label: "处理时间",
            prop: "finishTime",
            search: false,
            overHidden: true,
          },
        ],
      },
    };
  },
  methods: {
    // 查询
    getPageList() {
      console.log('taskComment listTaskCommentByInstId urlParams',this.urlParams)
      if (this.procInstId) {
        HandelTaskApi.listTaskCommentByInstId({
          procInstId: this.procInstId,
        },this.urlParams).then((res) => {
          res.data.forEach(item=>{
            item.hxPeople=item.actionType ? item.identitylinkInfo : item.identityUserInfo
          })
          this.dataList = res.data;
        });
      }
    },
  },
};
</script>
