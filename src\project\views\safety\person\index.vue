<template>
  <div>
    <sn-crud :data="listData" :option="option" v-model:page="queryForm.page" v-model:search="queryForm.filter" @on-load="getPageList" @search-change="onChangeSearch" @search-reset="onResetSearch" @addBtnHandle="onEditData" @row-del="onDelData">
      <template #menu="{ row, index, size }">
        <el-button v-if="!row.status || row.status == '0'" type="primary" :size="size" icon="el-icon-edit" link @click="onEditData(row)">编辑</el-button>
        <el-button v-if="!row.status || row.status == '0'" type="danger" :size="size" icon="el-icon-delete" link @click="onDelData([row])">删除</el-button>
        <el-button v-if="row.status && row.status == '1'" type="warning" :size="size" icon="el-icon-refresh-left" link @click="onRefresh(row)">撤回</el-button>
        <el-button v-if="row.status && row.status == '1'" type="primary" :size="size" icon="el-icon-download" link @click="onDownload(row)">下载</el-button>
      </template>
      <template #header="{row ,index ,size }">
        <el-button type="primary" :size="size" icon="el-icon-plus" @click="onEditData()">新增</el-button>
        <el-button type="primary" :size="size" icon="el-icon-plus" @click="onListExport()">列表导出</el-button>
      </template>
      <template #status="{row}">
      <span :style="{color : row.status && row.status==='1' ? 'green' : 'red'}">
          {{row.status && row.status==='1' ? '已发布': '草稿'}}
        </span>
      </template>
      <template #code="{row}">
        <el-button type="text" @click="onRowClick(row)">{{row.code}}</el-button>
      </template>
    </sn-crud>
    <div ref="myRef"></div>
  </div>
</template>

<script>
import SafetyDutyPersonApi from '@/project/api/safety/SafetyDutyPerson.js'
  import EditSafetyDutyPerson from "./components/EditSafetyDutyPerson.vue";
  import { getToken } from "sn-base-utils";
import ProjBizProjectTeamOrgApi from "@/project/api/projectTeam/ProjBizProjectTeamOrg";
  export const routerConfig = [{
    menuType: "C",
    menuName: "安全责任书（个人）",
  }, {
    menuType: "F",
    menuName: "列表",
    perms: "show",
    api: [SafetyDutyPersonApi.config.pageList],
  }, {
    menuType: "F",
    menuName: "新增",
    perms: "add",
    api: [SafetyDutyPersonApi.config.add, ProjBizProjectTeamOrgApi.config.list],
  }, {
    menuType: "F",
    menuName: "修改",
    perms: "update",
    api: [SafetyDutyPersonApi.config.update, SafetyDutyPersonApi.config.view, ProjBizProjectTeamOrgApi.config.list],
  }, {
    menuType: "F",
    menuName: "删除",
    perms: "del",
    api: [SafetyDutyPersonApi.config.remove],
  }];
</script>

<script setup>
import {
  ref,
  getCurrentInstance
} from 'vue';
import { dayjs } from 'element-plus';
const {
  proxy
} = getCurrentInstance()
let option = ref({
  tip: false,
  dialogType: "page",
  border: true,
  index: true,
  stripe: true,
  menu: true,
  header: false,
  height: "auto",
  searchSpan: 6,
  searchLabelWidth: 100,
  searchIcon: true,
  searchMenuSpan: 6,
  searchIndex: 3,
  selection: true,
  showTree: false,
  excelBtn: false,
  delBtn: false,
  editBtn: false,
  delBtnsText: "批量删除",
  addBtnText: "新增",
  customEditBtnText: "编辑",
  customDelBtnText: "删除",
  customViewBtnText: "查看",
  column: [{
    label: "业务编码",
    prop: "code"
  }, {
    label: "甲方单位",
    prop: "clientUnit",
    search: true
  }, {
    label: "甲方签字人",
    prop: "clientSignatory",
    search: true
  }, {
    label: "创建人",
    prop: "createName",
    columnSlot: false,
    searchSlot: false
  }, {
    label: "创建时间",
    prop: "createTime",
    columnSlot: false,
    searchSlot: false,
    formatter:(row, column, value) => {
      // 使用dayjs进行时间格式化，确保value存在
      if (!value) return '';
      // 默认格式为YYYY-MM-DD HH:mm:ss，您可以根据需要修改
      return dayjs(value).format('YYYY-MM-DD');
    }
  }, {
    label: "单据状态",
    prop: "status",
    columnSlot: false,
    searchSlot: false,
    dictDatas: "gh_document_status",
    type: "select",
    dicUrl: "/system/dict/data/type/gh_document_status",
    dicMethod: "get",
    props: {
      label: "dictLabel",
      value: "dictValue"
    }
  }]
});
let listData = ref([]);
let queryForm = ref({
  page: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  filter: {
    createTime: null,
    projectId: sessionStorage.getItem('projectId')
  }
});
let formRules = ref({});

function getPageList() {
  //查询分页列表
  // 查询前处理参数
  const params = handleQueryForm();
  SafetyDutyPersonApi.pageList(params).then((res) => {
    listData.value = res.data.dataList;
    queryForm.value.page.total = res.data.totalCount;
  });
}

function onResetSearch() {
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
}

function handleQueryForm() {
  // 处理参数
  const {
    pageSize,
    pageNum
  } = queryForm.value.page;
  const filter = {}
  for (let key in queryForm.value.filter) {
    if (queryForm.value.filter[key]?.length) {
      // sn-curd搜索select会生成$prop的变量
      if (key.startsWith("$")) continue;
      filter[key] = queryForm.value.filter[key];
    }
  }
  delete filter.createTime;
  if (Array.isArray(queryForm.value.filter.createTime) && queryForm.value.filter.createTime?.length === 2) {
    filter["beginCreateTime"] = queryForm.value.filter.createTime[0];
    filter["endCreateTime"] = queryForm.value.filter.createTime[1];
  }
  const searchParams = {
    page: {
      pageSize,
      pageNum,
    },
    filter,
  }
  return searchParams
}

function onChangeSearch(params, done) {
  //搜索按钮操作
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
  done && done();
}
const myRef = ref(null)
async function onEditData(row) {
  //编辑,新增按钮操作
  let editType = row ? "edit" : "add";
  let rowInfo = await (editType !== "add" ? SafetyDutyPersonApi.view(row.id) : {});
  const formData = editType !== "add" ? rowInfo.data : rowInfo;
  //编辑,新增按钮操作
  proxy.$DialogForm.show({
    title: "年度安全生产责任书签订（个人）",
    type: option.value.dialogType,
    el: myRef.value,
    width: "80%",
    content: EditSafetyDutyPerson,
    option: {
      submitBtn: false,
      emptyBtn: false,
      emptyText:'取消',
      extendButton:[
        {
          key: 'save',
          text: '保存',
          icon: 'el-icon-plus',
          buttonType: 'primary',
        },
        {
          key: 'submit',
          text: '发布',
          icon: 'el-icon-check',
          buttonType: 'primary',
        },
        {
          key: 'close',
          text: '关闭',
          icon: 'el-icon-close',
          buttonType: '',
        },
      ],
    },
    data: {
      formData: formData,
      type: editType,
    },
    callback: (res) => {
      if (res.type && res.type == 'submit') {
        if (res.dialogRefs) {
          res.dialogRefs.submitData(res.type).then((flag) => {
            if (flag) {
              getPageList();
              res.close();
            }
          });
        }
      } else if (res.type && res.type == 'save') {
        if (res.dialogRefs) {
          res.dialogRefs.submitData(res.type).then((flag) => {
            if (flag) {
              getPageList();
            }
          });
        }
      } else {
        // 当点击关闭按钮且处于编辑模式时，弹出确认对话框
        proxy.$confirm('确认关闭？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          res.close();
        }).catch(() => {
          // 用户点击取消，不关闭弹窗
        });
      }
    }
  });
}

function onDelData(rows) {
  //删除行
  if (!rows || rows.length == 0) {
    proxy.$message.info("请勾选数据！");
    return false;
  }
  let ids = rows.map((item) => {
    return item.id;
  });
  proxy.$modal.confirm("确认删除数据项？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    SafetyDutyPersonApi.remove(ids).then((res) => {
      proxy.$message.success("已删除");
      getPageList();
    });
  }).catch(() => {});
}

//列表导入
function onListExport() {
  const params = queryForm.value.filter
  proxy.download("/project/safety/person/export", params, '个人安全责任书数据导出.xlsx');
}

//撤回
function onRefresh(row) {
  proxy.$modal.confirm("确认撤回该条数据项？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    //编辑操作
    // const formData = getFormData();
    // getPageList();
    row.status = '0'
    SafetyDutyPersonApi.update(row).then(res =>{
      proxy.$message.success("修改成功");
      getPageList();
    })
  }).catch(() => {});
}
async  function onRowClick(row) {
  //编辑,新增按钮操作
  let editType = "view";
  let rowInfo = await (editType !== "add" ? SafetyDutyPersonApi.view(row.id) : {});
  const formData = editType !== "add" ? rowInfo.data : rowInfo;

  let extendButtons = []
  if (row.status == '0') {
    extendButtons = [{
      key: 'edit',
      text: '编辑',
      icon: 'el-icon-edit',
      buttonType: 'primary',
      click: () => {
        res.close();
        onEditData(row);
      },
    },{
      key: 'close',
      text: '关闭',
      icon: 'el-icon-close',
      buttonType: '',
    }]
  }else {
    extendButtons = [{
      key: 'close',
      text: '关闭',
      icon: 'el-icon-close',
      buttonType: '',
    }]
  }
  //编辑,新增按钮操作
  proxy.$DialogForm.show({
    title: "年度安全生产责任书签订（单位）",
    type: option.value.dialogType,
    el: myRef.value,
    width: "80%",
    content: EditSafetyDutyPerson,
    data: {
      formData: formData,
      type: editType,
    },
    option: {
      submitBtn: false,
      emptyBtn: false,
      emptyText:'取消',
      extendButton:extendButtons,
    },
    callback: (res) => {
      if (res.type && res.type !== 'close') {
        res.close();
        onEditData(row)
      } else {
        res.close();
      }
    }
  });
}

//下载
function onDownload(row) {

}
</script>

<style lang="scss" scoped>

</style>

