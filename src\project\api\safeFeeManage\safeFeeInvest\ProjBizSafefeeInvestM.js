import { request, replaceUrl } from "sn-base-utils";

export default class ProjBizSafefeeInvestMApi {
    static config = {
        add: {
            url: '/project/safeFeeInvest/add',
            method: 'POST'
        },
        remove: {
            url: '/project/safeFeeInvest/delete',
            method: 'DELETE'
        },
        update: {
            url: '/project/safeFeeInvest/update',
            method: 'PUT'
        },
        view: {
            url: '/project/safeFeeInvest/get/{id}',
            method: 'GET'
        },
        pageList: {
            url: '/project/safeFeeInvest/page',
            method: "POST"
        },
        list: {
            url: '/project/safeFeeInvest/list',
            method: "POST"
        }
    };

    /**
     * 新增安全费用投入申报
     * @param data
     * @returns {*}
     */
    static add(data) {
        return request({
            url: this.config.add.url,
            method: this.config.add.method,
            data: data
        });
    }

    /**
     * 删除安全费用投入申报
     * @param data
     * @returns {*}
     */
    static remove(data) {
        return request({
            url: this.config.remove.url,
            method: this.config.remove.method,
            data: data
        });
    }

    /**
     * 更新安全费用投入申报
     * @param data
     * @returns {*}
     */
    static update(data) {
        return request({
            url: this.config.update.url,
            method: this.config.update.method,
            data: data
        });
    }

    /**
     * 查询安全费用投入申报详细
     * @param id
     * @returns {*}
     */
    static view(id) {
        return request({
            url: replaceUrl(this.config.view.url, { id }),
            method: this.config.view.method
        });
    }

    /**
     * 分页查询安全费用投入申报列表
     * @param data
     * @returns {*}
     */
    static pageList(data) {
        return request({
            url: this.config.pageList.url,
            method: this.config.pageList.method,
            data: data
        });
    }

    /**
     * 全部安全费用投入申报列表
     * @returns {*}
     */
    static list(data) {
        return request({
            url: this.config.list.url,
            method: this.config.list.method,
            data: data
        });
    }
}
