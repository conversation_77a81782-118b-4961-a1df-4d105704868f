<template>
  <flow-page-container ref="flowPageContainerRef" @handlerAction="handlerAction" @initData="initData"
    @handlerPrint="handlerPrint" :closeBtn="isShowCloseBtn" :approvalOption="approvalOption"
    @approvalOptionCallback="getApprovalOption">
    <div style="width: 100%">
      <!-- <button @click="startGeneration">生成文档</button>  -->
      <!-- <el-button @click="startGeneration" type="primary">打印文档</el-button> -->
      <el-form :model="formData" :rules="rules" ref="formRef" label-width="180px" label-position="right">
        <el-card class="box-card" style="width: 100%;height:auto">
          <fieldset class="fieldset2">
            <legend>
              <span class="el-button--primary"></span>工程表单预览
            </legend>
            <el-row :gutter="16" :span="24">
              <div class="mainContent">
                <div class="qualityDom">
                  <h3>工程开工报审表</h3>
                  <div class="titletop">
                    <el-row :gutter="16" :span="24">
                      <el-col :span='12'>
                        <el-form-item label="工程名称：" prop="projName" label-width="100px">
                          <el-input v-model="formData.projName" type="text" placeholder="请输入" disabled></el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span='12'>
                        <el-form-item label="编号：" prop="receiptNumber" label-width="100px">
                          <el-input v-model="formData.receiptNumber" type="text" placeholder="请输入" clear required
                            :disabled="type == 'view' || (type !== 'edit' && taskKey && taskKey !== 'Activity_0465d87')"></el-input>
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <div class="form-content">
                      <div class="contents padding-20">致：<span><el-input style="width:100%;"
                            v-model="formData.supervisorCompany" type="text" placeholder="请输入"
                            disabled></el-input></span></div>
                      <div class="contents textindent padding-auto">
                        我方承担的<span><el-form-item class="form-item-inline" label-width="0" label=""
                            prop="projectName"><el-input style="width:100%;" v-model="formData.projectName" type="text"
                              placeholder="请输入"
                              :disabled="type == 'view' || (type !== 'edit' && taskKey && taskKey !== 'Activity_0465d87')"
                              clear required></el-input></el-form-item></span>工程已完成了开工前的各项准备工作，特申请于 <span><el-form-item
                          class="form-item-inline" label-width="0" label="" prop="startDate"><el-date-picker type="date"
                            v-model="formData.startDate"
                            :disabled="type == 'view' || (type !== 'edit' && taskKey && taskKey !== 'Activity_0465d87')"
                            format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                            clearable></el-date-picker></el-form-item></span>开工，请审查。

                      </div>
                      <!-- <div class="contents">
                        
                      </div> -->
                      <div class="contents padding-20">
                        <el-checkbox-group v-model="optionList" class="preparation"
                          :disabled="type == 'view' || (type !== 'edit' && taskKey && taskKey !== 'Activity_0465d87')">
                          <el-checkbox v-for="(item, index) in checkBoxOptions" :key="index" :label="item"
                            :value="item" />
                        </el-checkbox-group>
                        <div class="right-content">
                          <el-form-item class="form-item-inline" label="施工项目部（章）"></el-form-item>
                          <el-form-item
                                class="form-item-inline" label="项目经理（签字）：" prop="projectManagerSign"><el-input
                                  v-model="formData.projectManagerSign"
                                  :disabled="type == 'view' || (type !== 'edit' && taskKey && taskKey !== 'Activity_0465d87')"></el-input></el-form-item>
                          
                          <el-form-item
                                class="form-item-inline" label="日期："
                                prop="projectManagerSignDate"><el-date-picker type="date"
                                  v-model="formData.projectManagerSignDate"
                                  :disabled="type == 'view' || (type !== 'edit' && taskKey && taskKey !== 'Activity_0465d87')"
                                  format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                                  clearable></el-date-picker></el-form-item>
                        </div>
                      </div>
                      <div class="contents padding-20 textline">
                        <div>
                          项目监理机构审查意见：
                          <p></p>
                          <el-input type="textarea" v-model="formData.supervisorEngineerFeedback" placeholder="请输入"
                            rows="3" :disabled="!(taskKey === 'Activity_97bf274')" clearable></el-input>
                        </div>
                        <div class="right-content">
                          <el-form-item class="form-item-inline" label="项目监理机构（盖章）"></el-form-item>
                          <el-form-item class="form-item-inline" label="总监理工程师（签字）："><el-input
                                v-model="formData.supervisorEngineerSign"
                                :disabled="!(taskKey === 'Activity_97bf274')"></el-input></el-form-item>
                          <el-form-item class="form-item-inline" label="日期："><el-date-picker type="date"
                                v-model="formData.supervisorEngineerSignDate"
                                :disabled="!(taskKey === 'Activity_97bf274')" format="YYYY-MM-DD"
                                value-format="YYYY-MM-DD" clearable></el-date-picker></el-form-item>
                        </div>
                      </div>
                      <div class="contents padding-20 textline">
                        <div>
                          建设单位审批意见：
                          <p></p>
                          <el-input type="textarea" v-model="formData.constructionCompanyFeedback" placeholder="请输入"
                            rows="3" :disabled="!(taskKey === 'Activity_c3a43b1')" clearable></el-input>
                        </div>
                        <div class="right-content">
                          <el-form-item class="form-item-inline" label="建设单位（盖章）"></el-form-item>
                          <el-form-item class="form-item-inline" label="项目代表（签字）：">
                            <el-input
                                v-model="formData.constructionCompanySign"
                                :disabled="!(taskKey === 'Activity_c3a43b1')"></el-input>
                          </el-form-item>
                          <el-form-item class="form-item-inline" label="日期：">
                            <el-date-picker type="date"
                                v-model="formData.constructionCompanySignDate"
                                :disabled="!(taskKey === 'Activity_c3a43b1')" format="YYYY-MM-DD"
                                value-format="YYYY-MM-DD" clearable></el-date-picker>
                          </el-form-item>
                        </div>
                      </div>
                      <div class="contents padding-20 textline">
                        注：本表一式三份，由施工项目部填报，建设单位、项目监理机构、施工项目部各一份。
                      </div>

                    </div>
                  </div>
                </div>
              </div>
            </el-row>
          </fieldset>
        </el-card>
      </el-form>
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>附件信息
          </legend>
          <!-- <project-document-storage-ui-table ref="childRef" :type="fileType" :relevantId="relevantId"
            :isPageSearch="false"
            :isShowLinkBtn="false"
            :isDeleteMinio="isDeleteMinio"
            :isShowDelBtn="isShowDelBtn"
            :isHasAi="isHasAi" :file-serial-number-builder="fileSerialNumberBuilder" :preview-config="previewConfig"
            :isShowAddBtn="isShowAddBtn"></project-document-storage-ui-table> -->
          <project-document-storage-ui-table ref="childRef" :type="fileType" :relevantId="relevantId"
            :isPageSearch="false"
            :isShowLinkBtn="false"
            :isDeleteMinio="isDeleteMinio"
            :isShowDelBtn="!(type === 'view' || (type !== 'edit' && taskKey && taskKey !== 'Activity_0465d87'))"
            :isHasAi="isHasAi" :file-serial-number-builder="fileSerialNumberBuilder" :preview-config="previewConfig"
            :isShowAddBtn="!(type === 'view' || (type !== 'edit' && taskKey && taskKey !== 'Activity_0465d87'))"></project-document-storage-ui-table>
        </fieldset>
      </el-card>
    </div>
  </flow-page-container>
</template>

<script setup>
import { generateWordDocument, download, printDocument } from '@/project/components/downloadWord/word.js';
import ProjBizConsStartWorkReportApi from '@/project/api/constructionManagement/sceneStartWorkReport/ProjBizConsStartWorkReport.js'
import ProjInfoMApi from '@/project/api/projectInfo/ProjInfoM'
import store from '@/store'
import { kkFileViewUrl } from "@/config";
import BpmTaskApi from '@/project/api/bpm/bpmTask'
import FlowPageContainer from '@/common/components/exportFile/FlowPageContainer.vue'
import { btnHandle } from '@/project/components/hooks/buttonChangeName'
//TODO 引入所需JS 
import {
  nextTick,
  onMounted,
  ref,
  reactive,
  toRef,
  defineProps,
  defineExpose,
  getCurrentInstance
} from 'vue';
import {
  useRoute,
  useRouter
} from 'vue-router'
let auth = new Map();
const {
  proxy
} = getCurrentInstance()
const route = useRoute();
const router = useRouter();
const props = defineProps({
  data: Object,
});
const formRef = ref()
const taskInfo = ref(null)
const type = toRef(props.data?.type);
const isShowCloseBtn = ref(false);
const FlowActionType = ref(proxy.FlowActionType);
const state = reactive({
  flowData: {
    businessKey: "",
    procInstId: "",
    procDefKey: "",
    taskId: "",
    formConfig: [],
    variableList: [],
    customPropertyConfigList: []
  },
  formAuth: {},
  taskFlag: 1,
  firstAct: false,
  handlerClose: null,
  rootFields: {},
  fullFieldNameMap: {},
  formAuthList: {}
})
const res = ref(null)
const flowPageContainerRef = ref(null);
let approvalOption = props.data?.approvalOption ? props.data?.approvalOption : route.query?.procInstId ? ref({
  isShowApprovalList: true,
  isShowFlowDiagram: true,
  procInstId: route.query?.procInstId
}) : ref({
  isShowApprovalList: false,
  isShowFlowDiagram: false,
  procInstId: ''
});
let formData = ref({
  projName: '',
  projectName: '',
  receiptNumber: '',
  startDate: '',
  supervisorCompany: '', // 监理单位
  projectManagerDate: '', // 项目经理签字日期
  superSupervisorDate: '', // 总监理工程师签字日期
  constructionCompanyDate: '', // 建设单位代表签字日期
  optionList: `["施工组织设计己审批。","施工单位安全生产许可资质已审查。","各项施工管理制度和相应的施工方案己制定并审查合格。","施工图已会审。","技术交底已进行。","质量验收及评定项目划分表已报审。","工程控制网测量/线路复测资料已审核。","特种作业人员满足工程需要。","施工人员和船舶、机械设备已进场。","物资、材料准备满足连续施工的需要。","计量器具、仪表经法定单位检验合格。","分包单位资格审查文件已报审。","试验(检测)单位资质审查文件已报审。","与本工程相关的其他工作已完成。"]`,
  receiptStatus: '1',
  applicantOrg: JSON.parse(store.state.user.orgName)?.find(
  (item) => item.id === store.state.user.defaultOrg
)?.orgName, // 申请单位
  applicant: store.state.user && store.state.user.userInfo.userName
    ? store.state.user.userInfo.userName
    : '-', // 申请人
  projectManagerSign: store.state.user && store.state.user.userInfo.userName
    ? store.state.user.userInfo.userName
    : '-', // 项目经理签字
  projectManagerSignDate: proxy.parseTime(new Date()).slice(0, 10), // 项目经理签字日期
  supervisorEngineerFeedback: '', // 总监理工程师审批意见
  supervisorEngineerSign: '', // 总监理工程师签字
  supervisorEngineerSignDate: '', // 总监理工程师签字日期
  constructionCompanyFeedback: '', // 建设单位审批意见
  constructionCompanySign: '', // 建设单位签字
  constructionCompanySignDate: '', // 建设单位签字日期
});

let rules = ref({
  projectName: { required: true, message: '请输入工程名称', trigger: 'blur' },
  receiptNumber: { required: true, message: '请输入编号', trigger: 'blur' },
  startDate: { required: true, message: '请选择开工日期', trigger: 'blur' },
  projectManagerSign: { required: true, message: '请输入项目经理', trigger: 'blur' },
  projectManagerSignDate: { required: true, message: '请选择日期', trigger: 'blur' },
});
const optionList = ref([])
const checkBoxOptions = ref([
  '施工组织设计己审批。',
  '施工单位安全生产许可资质已审查。',
  '各项施工管理制度和相应的施工方案己制定并审查合格。',
  '施工图已会审。',
  '技术交底已进行。',
  '质量验收及评定项目划分表已报审。',
  '工程控制网测量/线路复测资料已审核。',
  '特种作业人员满足工程需要。',
  '施工人员和船舶、机械设备已进场。',
  '物资、材料准备满足连续施工的需要。',
  '计量器具、仪表经法定单位检验合格。',
  '分包单位资格审查文件已报审。',
  '试验(检测)单位资质审查文件已报审。',
  '与本工程相关的其他工作已完成。',
])
//导出word
const generatedFile = ref(null);
// 下载表单（前端生成word）
const startGeneration = async () => {
  try {
    let isCheckedbList = []
    checkBoxOptions.value.forEach(item => {
      if (optionList.value.includes(item)) {
        isCheckedbList.push({
          label: item,
          isChecked: true
        })
      } else {
        isCheckedbList.push({
          label: item,
          isChecked: false
        })

      }
    })
    let form = {
      ...formData.value,
      isCheckedbList: isCheckedbList
    }
    generatedFile.value = await generateWordDocument(form, 'projBizConsStartWorkReport');
    if (generatedFile.value) {
      download(generatedFile.value, '工程开工报审表.docx');
      proxy.$message.success("word下载成功！");
    } else {
      console.error('No file generated');
    }
  } catch (error) {
    console.error('Error generating document:', error);
  }
};

// 下载表单(后端)
const downloadWord =  () => {
  if (formData.value.id) {
    let timestamp = proxy.parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')
    proxy.download(`/project/sceneStartWorkReport/fillTemplateOnlyAndDownload/${formData.value.id}`, {}, '现场开工报审' + '-' + timestamp + '.docx')
  } else {
    proxy.$message.error("请选择先保存数据！");
    return;
  }
}
formData = toRef(Object.keys(props.data?.formData || {}).length > 0 ? {
  ...props.data?.formData
} : toRef(formData.value));
optionList.value = JSON.parse(formData.value.optionList) || [];
// 获取项目名称
function getProjectInfo() {
  let id = sessionStorage.getItem('projectId')
  // let id = '11'
  ProjInfoMApi.view(id).then((resp) => {
    if (!!resp.data) {
      formData.value.projName = resp.data.projName
      // projectData.value = resp.data;
      // position.value = (resp.data.country ? resp.data.country : '--') + '/' + (resp.data.country ? resp.data.country : '--') + '/' + (resp.data.city ? resp.data.city : '--');
    }
  })
}
function getTeamData() {
  let params = {
    projectId: sessionStorage.getItem('projectId'),
    orgNature: 'supervisor'
  }
  ProjBizConsStartWorkReportApi.queryorgCompanyList(params).then((resp) => {
    if (!!resp.data) {
      let orgData = resp.data[0]
      formData.value.supervisorCompany = orgData.name
    }
  })
}


/**
 * 附件上传相关
 */

 // 定义组件参数
const fileType = ref("sceneStartWorkReport"); // 业务类型标识
const relevantId = ref(props.data?.formData?.id ?? null) // 关联业务ID，新增业务的时候传null（⽰例值）
const isDeleteMinio = ref(true); // 删除Minio⽂件开关
const isHasAi = ref(false); // AI审查功能开关
const childRef = ref(null); // 组件实例引⽤
const isShowAddBtn = ref(false); //上传
const isShowDelBtn = ref(false); //下载


/**
* 预览配置对象
* - isExternalPreview: 是否使⽤⾃定义预览（false=内置kkfileview）
* - substitute: 替换Minio返回URL中的字符串（⽰例：{"原始字符串":"替换值"}）
* - previewServerUrl: 内置预览服务地址
*/
const previewConfig = ref({
  isExternalPreview: false,
  substitute: { "akey": "avalue" },
  previewServerUrl: kkFileViewUrl,
});
// 获取文件上传列表
function getListData() {
  if (childRef.value) {
    const list = childRef.value.getListData();
    return list
  }
}
// 文件序列号生成函数
function fileSerialNumberBuilder() {
  return 'TEST' + Math.floor(Math.random() * 10000)
}


function getFormData() {
  return formData.value
};

// 获取任务信息
function getTaskInfo() {
  return taskInfo.value
}

function submitData() {
  return new Promise((resolve) => {
    formRef.value.validate((flag) => {
      if (!flag) {
        return false;
      } else {
        if (type.value === "add") {
          resolve(saveData());
        } else {
          resolve(editData());
        }
      }
    });
  });
}

function saveData() {
  //新增操作
  const formData = getFormData();
  return __classNameVariable__Api.add(formData).then(() => {
    proxy.$message.success("新增成功");
    return true;
  });
}
function getSaveFormData(formData) {
  return new Promise((resolve) => {
    formRef.value.validate((flag) => {
      if (!flag) {
        return false;
      } else {
        let form = {
          ...formData,
          optionList: JSON.stringify(optionList.value),
        }
        form.fileList = getListData()
        form.fileList.map(item=>{
          item.type = fileType.value
        })
        if (formData.id) {
          resolve(ProjBizConsStartWorkReportApi.update(form))
        } else {
          
          resolve(ProjBizConsStartWorkReportApi.add(form).then(resp => {
            if (resp.data) {
              formData.id = resp.data.id
            }
          }))
        }
      }
    });
  });
};
function editData() {
  //编辑操作
  const formData = getFormData();
  return __classNameVariable__Api.update(formData).then(() => {
    proxy.$message.success("修改成功");
    return true;
  });
}
function submitFlowTask() {
  return new Promise((resolve) => {
    formRef.value.validate((flag) => {
      if (!flag) {
        return false;
      } else {
        return proxy.$confirm('确定提交当前单据?', '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }).then(() => {
          const formDatas = getFormData();
          return BpmTaskApi.listRuTaskByProcInstId({
            procInstId: formDatas.procInstanceId
          }).then((resp) => {
            flowPageContainerRef.value.handlerActionSubmit(resp.data[0],1);
            // if (Array.isArray(resp.data) && resp.data.length > 0) {
            //   return  ProjBizConsStartWorkReportApi.submitTask({
            //     busiDto: formDatas,
            //     taskActionDto: {
            //       taskId: resp.data[0].taskId,
            //       procInstId: formDatas.procInstanceId,
            //       actionType: "agree",
            //     }
            //   })
            //     .then(() => {
            //     proxy.$message.success("提交成功");
            //     resolve();
            //   });
            // } else {
            //   proxy.$message.success("提交失败，未获取到当前任务id");
            //   resolve()
            // }
          })
        })
          .catch(() => {
            return true;
          })
      }
    })
  })
   

}
function getSubmitTask(taskActionDto) {
  /**
   * 工作流提交任务功能
   */

  return ProjBizConsStartWorkReportApi.submitTask({
    busiDto: formData.value,
    taskActionDto: taskActionDto
  });
};

function getStartFlow(formData, startProcDto) {
  return new Promise((resolve) => {
    formRef.value.validate((flag) => {
      if (!flag) {
        return false;
      } else {
        /**
         * 工作流启动流程功能
         */
        let form = {
          ...formData,
          optionList: JSON.stringify(optionList.value),
        }
        form.fileList = getListData()
        form.fileList.map(item=>{
          item.type = fileType.value
        })
        resolve(ProjBizConsStartWorkReportApi.startFlow({
          busiData: form,
          startProcDto: startProcDto
        }).then((respon) => {
            state.flowData.procDefKey = respon.data[0].procDefKey
            state.flowData.procInstId = respon.data[0].procInstId
            state.flowData.businessKey = respon.data[0].businessKey
            state.flowData.taskId = respon.data[0].taskId
            taskInfo.value = respon.data[0];
            nextTick(() => {
              btnHandle(props.data.el.lastChild)
            })
            // return respon
          })
        )
      }
    })
  })
  
};
//打开确认框
function handlerOpenConfirm(taskInfo,resp) {
  ProjBizConsStartWorkReportApi.view(taskInfo.businessKey).then((resp) => {
    if (resp.data) {
      formData.value = resp.data;
      relevantId.value = resp.data.id;
      // isShowAddBtn.value = true;
      // isShowDelBtn.value = true;
    }
  })
  res.value=resp;
  flowPageContainerRef.value.handlerActionSubmit(taskInfo,1);
}
/**
 * 工作流校验必填
 * @param {String} flowNode 流程节点
 */
function validFile(flowNode) {
  let data = formData.value
  if (flowNode === 'Activity_97bf274') {
    if (!data.supervisorEngineerSign) {
      return '总监理工程师签字不能为空'
    } else if (!data.supervisorEngineerSignDate) {
      return '总监理工程师签字日期不能为空'
    } else if (!data.supervisorEngineerFeedback) {
      return '项目监理机构审查意见不能为空'
    } else {
      return false
    }
  } else if (flowNode === 'Activity_c3a43b1') {
    if (!data.constructionCompanySign) {
      return '项目代表签字不能为空'
    } else if (!data.constructionCompanySignDate) {
      return '项目代表签字日期不能为空'
    } else if (!data.constructionCompanyFeedback) {
      return '建设单位审批意见不能为空'
    } else {
      return false
    }
  } else {
    return false
  }
}
function handlerAction(operation, taskComment, handlerClose) {
  console.log('taskComment', taskComment)
  debugger
  /**
   * 工作流操作功能
   */
  // 启动并保存草稿后再次提交
  if (
    (operation.type == FlowActionType.value.SAVESTART || operation.type == FlowActionType.value.SAVE || operation.type == FlowActionType.value.START) && !formData.value.taskId) {
    let startProcDto = {
      procDefKey: formData.value.procDefKey,
      businessKey: null,
      clientId: null,
      ...taskComment?.dialogRefs?.getFormData(),
    };
    let httpCall = null;
    if (operation.type == FlowActionType.value.SAVESTART) {
      httpCall = getStartFlow(formData, startProcDto);
    } else if (operation.type == FlowActionType.value.SAVE) {
      httpCall = getSaveFormData(formData);
    }
    httpCall.then(() => {
      proxy.$modal.msgSuccess("提交成功");
      taskComment.close && taskComment.close();
      handlerClose();
    });
  } else {
    if (operation.type === FlowActionType.value.AGREE && validFile(taskKey.value)) {
      proxy.$modal.msgError(validFile(taskKey.value));
    } else {
      operation.type == FlowActionType.value.START || operation.type == FlowActionType.value.SAVESTART ? (operation.type = FlowActionType.value.AGREE) : operation.type;
      let taskActionDto = {
        taskId: state.flowData.taskId,
        procInstId: state.flowData.procInstId,
        actionType: operation.type,
        ...taskComment?.dialogRefs?.getFormData(),
        taskAssignees:taskComment?.dialogRefs?.getFormData()?.taskAssignees ? taskComment.dialogRefs.getFormData().taskAssignees.join(',') : ''
      };
      getSubmitTask(taskActionDto).then(() => {
        proxy.$modal.msgSuccess("任务办理成功");
        taskComment.close && taskComment.close();
        let businessKey = route.query.businessKey
        if (res){
          if (businessKey) {
            handlerClose()
          } else {
            props.data.closeDialog(proxy)
          }
        } else {
          handlerClose()
        }
        // handlerClose();
      }).catch(() => {
        taskComment.close && taskComment.close();
      });
    }
  }
};

function getFieldForm() {
  let rootFields = {
    ...state.rootFields
  }
  let fieldList = []
  let keys = []
  for (let key in rootFields) {
    keys.push(key)
    fieldList.push(rootFields[key])
  }
  const result = processAuthFieldForm ? processAuthFieldForm(fieldList) : fieldList;
  const message = {
    type: "getTemplateRoot",
    pathname: window.location.pathname,
    actKey: getQueryParams("actKey"),
    content: result
  };
  window.parent.postMessage(JSON.parse(JSON.stringify(message)), "*");
};
function getQueryParams(key) {
  let url = window.location.href;
  // 使用正则表达式解析URL中的查询字符串
  var queryString = url.split('?')[1];
  if (!queryString) {
    return {};
  }
  var params = {};
  // 分割查询字符串成单个参数
  var vars = queryString.split("&");
  for (var i = 0; i < vars.length; i++) {
    var pair = vars[i].split("=");
    params[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);
  }
  return params[key];
};
const taskKey = ref(null);
function initData(urlParams, taskInfo, handlerClose) {
  // 如果用户从流程设计点击启动, 如果是在线表单, 则阻止执行initData, 防止在线表单报错
  if (urlParams.menuId && urlParams.openInStart) {
    return
  }
  state.flowData.procDefKey = taskInfo.procDefKey;
  state.flowData.procInstId = urlParams.procInstId;
  state.flowData.businessKey = urlParams.businessKey;
  state.flowData.taskId = urlParams.taskId;
  state.flowData.fiedPermission = taskInfo.fiedPermission;
  state.flowData.variableList = taskInfo.variableList;
  state.taskFlag = urlParams.taskFlag;
  state.firstAct = taskInfo.firstAct;
  state.handlerClose = handlerClose;
  state.flowData.customPropertyConfigList = taskInfo.customPropertyConfigList
  let fieldPerList = taskInfo.formConfig?.formFieldConfig?.fieldPerList || []
  taskKey.value = taskInfo.taskKey;
  handleFormAuth(fieldPerList);
  initBusiForm();
};

function initBusiForm() {
  /**
   * 工作流初始化表单功能
   */
  let that = proxy
  let routerQueryParams = route.query;
  auth = new Map(Object.entries(state.formAuth));
  if (routerQueryParams) {
    if (routerQueryParams.busiData) {
      formData.value = routerQueryParams.busiData;
    } else {
      if (routerQueryParams.businessKey) {
        let businessKey = route.query.businessKey;
        ProjBizConsStartWorkReportApi.view(businessKey).then(resp => {
          formData.value = resp.data;
          relevantId.value = resp.data.id;
          optionList.value = JSON.parse(formData.value.optionList) || [];
          if (taskKey && taskKey.value === 'Activity_97bf274') {
            formData.value.supervisorEngineerSign = store.state.user && store.state.user.userInfo.userName ? store.state.user.userInfo.userName : '-'
            formData.value.supervisorEngineerSignDate = proxy.parseTime(new Date()).slice(0, 10)
          } else if (taskKey && taskKey.value === 'Activity_c3a43b1') {
            formData.value.constructionCompanySign = store.state.user && store.state.user.userInfo.userName ? store.state.user.userInfo.userName : '-'
            formData.value.constructionCompanySignDate = proxy.parseTime(new Date()).slice(0, 10)
          }
        });
      } else {
        that.$message.error("初始化失败,因为工作流未将流程信息传入！")
      }
    }
    
  }
};
function handleFormAuth(data) {
  let formAuth = {};
  for (let item of data) {
    let permi = 1;
    if (item.readonly) {
      permi = 2;
    }
    if (item.hidden) {
      permi = 3;
    }
    if (!isCamelCase(item.fieldModelId)) {
      item.fieldModelId = toCamelCase(item.fieldModelId)
    }
    formAuth = {
      ...formAuth,
      [`${item.fieldModelId}_${item.field}`]: permi,
    };
  }
  state.formAuth = formAuth;
  state.formAuthList = JSON.parse(JSON.stringify(formAuth))
};
function customFormPermi(obj) {
  /**
   * 工作流表单权限功能：自定义表单权限处理函数，将对应的对象的属性还原成对应的权限值
   */
  let {
    field,
    fieldModelId
  } = obj
  let fullFieldName = `${fieldModelId}_${field}`
  //TODO 这里可以自定义处理，默认返回0即可
  return auth.get(fullFieldName) || 0
};
function handlerPrint(taskComment) {
  /**
   * 工作流打印模板功能
   */
  ProjBizConsStartWorkReportApi.printTemplate({
    id: formData.value.id,
    templateId: taskComment.templateId,
    formData: formData.value
  }).then(
    (res) => {
      router.push({
        name: "PrintDoc",
        query: {
          fileId: res.data,
        },
      });
      taskComment.close && taskComment.close();
    });
};
function processAuthFieldForm(fieldList) {
  /**
   * 工作流表单权限功能：在扫描完成之前，业务可对表单字段进行修改。
   */
  //TODO 这里可以对权限表单的字段进行任意的修改,如果不满足业务需求，可以在这里手动添加你想要的字段
  return fieldList
};
function getApprovalOption(fun) {
  /**
   * 工作流表单权限功能(重要)：应对弹窗无法及时相应的问题，特别增加该函数修复该bug
   */
  nextTick(() => {
    fun(approvalOption.value)
  })
};
onMounted(() => {
  getFieldForm()
  if (!formData.value.id) {
    getProjectInfo()
    getTeamData()
  }
}) 
defineExpose({
  getFormData,
  submitData,
  downloadWord,
  startGeneration,
  getSaveFormData,
  getStartFlow,
  submitFlowTask,
  handlerOpenConfirm,
  getTaskInfo,
});
</script>

<style lang="scss" scoped>
.normal-text {
  font-size: 14px !important;
  color: #000;
}
::v-deep label, p, div {
  @extend .normal-text;
}
.mainContent {
  width: 100%;

  .qualityDom {
    width: 56%;
    margin: 0 auto;
  }

  h3 {
    width: 100%;
    text-align: center;
    line-height: 40px;
  }
}

.form-content {
  border: 1px solid #333;
}

.padding-20 {
  padding: 20px;
}

.padding-auto {
  padding: 0 20px;
}

.pad-right-20 {
  padding-left: 20px;
}

.contents {
  span {
    display: inline-block;
    width: 230px;
    padding: 0 5px;
    text-indent: 0ch;
  }
}

.textindent {
  text-indent: 2ch;
}

.leftcon {
  display: inline-block;
  vertical-align: top;
  text-indent: 2ch;
  padding-top: 20px;

  p {
    padding-bottom: 10px;
  }
}

.sp-inline {
  display: inline-block;
}

.right-content {
  // padding-left: 50%;
  padding-left: calc(100% - 400px);
  padding-top: 10px;

  .label {
    text-align: right;
  }
}

.textline {
  margin-top: 20px;
  border-top: 1px solid #333;
}

::v-deep .ep-textarea__inner {
  box-shadow: none;
  outline: none;
}
.preparation {
  padding: 10px 20px;
  ::v-deep .ep-checkbox {
    display: block;
  }
}
.form-item-inline {
  display: inline-block;
  ::v-deep .ep-form-item__label {
    @extend .normal-text;
  }
  ::v-deep .ep-form-item__content {
    display: inline-block;
    width: 220px;
   
  }
}
.bgW {
  height: 100%;
}
</style>