
export default {
  data() {
    return {
      flowData: {
        businessKey:"",
        procInstId: "",
        procDefKey: "",
        taskId: "",
        formConfig: [],
        variableList: [],
        customPropertyConfigList:[]
      },
      formAuth: {},
      taskFlag: 1,
      firstAct: false,
      handlerClose: null,
      rootFields:{},
      fullFieldNameMap:{},
      formAuthList:{}
    };
  },
  mounted(){
    // let t=setTimeout(()=>{
      
    // },500)
  },
  methods: {
    getFieldForm() {
   
      let rootFields={...this.rootFields}
      console.log('mixin rootFields',this.rootFields,this.getQueryParams("actKey"))
      let fieldList=[]
      let keys=[]
      for(let key in rootFields){
        keys.push(key)
        fieldList.push(rootFields[key])
      }
      const result = this.processAuthFieldForm ? this.processAuthFieldForm(fieldList) : fieldList;
      const message = { 
        type: "getTemplateRoot",
        pathname:window.location.pathname,
        actKey:this.getQueryParams("actKey"),
        content:result
         };
        // console.log('准备postMessage!!!',this.getQueryParams("actKey"),keys,result)
         window.parent.postMessage(message, "*");
    },
    getQueryParams(key) {
      let url= window.location.href;
      // 使用正则表达式解析URL中的查询字符串
      var queryString = url.split('?')[1];
      if (!queryString) {
          return {};
      }
      var params = {};
      // 分割查询字符串成单个参数
      var vars = queryString.split("&");
      for (var i = 0; i < vars.length; i++) {
          var pair = vars[i].split("=");
          params[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);
      }
      return params[key];
  },
    initData(urlParams, taskInfo, handlerClose) {
      // 如果用户从流程设计点击启动, 如果是在线表单, 则阻止执行initData, 防止在线表单报错
      if(urlParams.menuId && urlParams.openInStart){
        return
      }
      this.flowData.procDefKey = taskInfo.procDefKey;
      this.flowData.procInstId = urlParams.procInstId;
      this.flowData.businessKey = urlParams.businessKey;
      this.flowData.taskId = urlParams.taskId;
      this.flowData.fiedPermission = taskInfo.fiedPermission;
      this.flowData.variableList = taskInfo.variableList;
      this.taskFlag = urlParams.taskFlag;
      this.firstAct = taskInfo.firstAct;
      this.handlerClose = handlerClose;
      this.flowData.customPropertyConfigList=taskInfo.customPropertyConfigList
      let fieldPerList=taskInfo.formConfig?.formFieldConfig?.fieldPerList || []
      // fieldPerList.forEach(item=>{
      //   item.fieldModelId=item.fieldModelId ? this.toCamelCase(item.fieldModelId) : ''
      // })
      console.log('template权限接收到的表单字段,并进行handleFormAuth',fieldPerList)
      this.handleFormAuth(fieldPerList);
      this.initBusiForm();
    },
    initBusiForm(){
    },
    customFormPermi() {
      if (this.taskFlag == 1) {
        if (this.firstAct) {
          return 1;
        } else {
          return 2;
        }
      } else {
        return 2;
      }
    },
    handleFormAuth(data) {
      let formAuth = {};
      for (let item of data) {
        let permi = 1;
        if (item.readonly) {
          permi = 2;
        }
        if (item.hidden) {
          permi = 3;
        }
        if(!this.isCamelCase(item.fieldModelId)){
          item.fieldModelId=this.toCamelCase(item.fieldModelId)
        }
        formAuth = {
          ...formAuth,
          [`${item.fieldModelId}_${item.field}`]: permi,
        };
      }
      console.log('formAuth',formAuth)
      this.formAuth = formAuth;
     
      this.formAuthList=JSON.parse(JSON.stringify(formAuth))
    },
    getVariableData(formData) {
      let variableData;
      if (Array.isArray(this.flowData.variableList) && this.flowData.variableList.length > 0) {
        this.flowData.variableList.forEach(element => {
          if(formData[element.fieldName] != undefined){
            variableData = {...variableData,[element.fieldName]:formData[element.fieldName]}
          }
        });
      }
      return variableData;
    },
      /**
       * 处理表单权限相关操作, 开发者在template的v-permi设置中必须使用此函数
       * @param {Object} obj - 包含字段名称的对象
       * @param {Function} [callback] - 可选的回调函数，接收 obj 作为参数
       * 如果传入对象的 fieldName 不在 rootFields 中，则将其添加到 rootFields 数组，然后执行回调函数（如果存在）
       */
      formPermi(obj,callback) {
        if(!this.rootFields[obj.fieldModelId+'__'+obj.field]){
     
          this.rootFields[obj.fieldModelId+'__'+obj.field]=obj
        }
      
     // let fullFieldName=this.getFullFieldNamePermi(obj)
     return callback && callback(obj)
    },
    getFullFieldNamePermi(obj){
      let {
        field,
        fieldModelId
      } = obj
      let fullFieldName=this.fullFieldNameMap[`${fieldModelId}_${field}`] || ''
      if(fullFieldName){
        return fullFieldName
      }else{
        if (fieldModelId && fieldModelId !== '') {
          const reg = /_(.)/g;
          const processedTableName = fieldModelId.replace(reg, (fullMatch, g1, index) => {
            if (index === 0) return g1;
            return g1.toUpperCase();
          });
          fullFieldName = `${processedTableName}_${field}`;
        } else {
          fullFieldName = ''
        }
        this.fullFieldNameMap[`${fieldModelId}_${field}`]=fullFieldName
        return fullFieldName
      }
      
    },
    toCamelCase(s) {
      return s.toLowerCase().replace(/_(.)/g, function (match, group1) {
        return group1.toUpperCase();
      });
    },
    isCamelCase(str){
      return /^[a-z][a-zA-Z0-9]*$/.test(str) 
    }
    
  },
};
