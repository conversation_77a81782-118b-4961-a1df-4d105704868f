<template>
  <el-upload
    ref="upload"
    :headers="headers"
    class="upload-demo"
    method="post"
    action="/api/project/project/sceneStartWorkReport/import/excel"
    :limit="1"
    :on-exceed="handleExceed"
    :auto-upload="false"
  >
    <template #trigger>
      <el-button type="primary">请选择文件</el-button>
    </template>
    <el-button class="ml-3" type="success" @click="submitUpload">上传</el-button>
    <template #tip>
      <div class="el-upload__tip text-red">限制1个文件，新文件将覆盖旧文件</div>
    </template>
  </el-upload>
</template>

<script setup>
import { ref, getCurrentInstance } from 'vue'

import { genFileId } from 'element-plus'

import { getToken } from 'sn-base-utils'

const { proxy } = getCurrentInstance()

const upload = ref()

const headers = ref({
  Authorization: getToken()
})

function handleExceed(files) {
  upload.value.clearFiles()
  const file = files[0]
  file.uid = genFileId()
  upload.value.handleStart(file)
}

function submitUpload() {
  upload.value.submit()
  proxy.$message.info('导入成功！')
}
</script>
