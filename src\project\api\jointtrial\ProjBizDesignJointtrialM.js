import { request, replaceUrl } from "sn-base-utils";

export default class ProjBizDesignJointtrialMApi {
    static config = {
        add: {
            url: '/project/jointtrial/add',
            method: 'POST'
        },
        remove: {
            url: '/project/jointtrial/delete',
            method: 'DELETE'
        },
        update: {
            url: '/project/jointtrial/update',
            method: 'PUT'
        },
        view: {
            url: '/project/jointtrial/get/{id}',
            method: 'GET'
        },
        pageList: {
            url: '/project/jointtrial/page',
            method: "POST"
        },
        list: {
            url: '/project/jointtrial/list',
            method: "POST"
        },downloadZip: {
            url: '/project/jointtrial/downloadZip',
            method: "POST"
        }
    };

    /**
     * 新增施工图会审管理
     * @param data
     * @returns {*}
     */
    static add(data) {
        return request({
            url: this.config.add.url,
            method: this.config.add.method,
            data: data
        });
    }

    /**
     * 删除施工图会审管理
     * @param data
     * @returns {*}
     */
    static remove(data) {
        return request({
            url: this.config.remove.url,
            method: this.config.remove.method,
            data: data
        });
    }

    /**
     * 更新施工图会审管理
     * @param data
     * @returns {*}
     */
    static update(data) {
        return request({
            url: this.config.update.url,
            method: this.config.update.method,
            data: data
        });
    }

    /**
     * 查询施工图会审管理详细
     * @param id
     * @returns {*}
     */
    static view(id) {
        return request({
            url: replaceUrl(this.config.view.url, { id }),
            method: this.config.view.method
        });
    }

    /**
     * 分页查询施工图会审管理列表
     * @param data
     * @returns {*}
     */
    static pageList(data) {
        return request({
            url: this.config.pageList.url,
            method: this.config.pageList.method,
            data: data
        });
    }

    /**
     * 全部施工图会审管理列表
     * @returns {*}
     */
    static list(data) {
        return request({
            url: this.config.list.url,
            method: this.config.list.method,
            data: data
        });
    }
}
