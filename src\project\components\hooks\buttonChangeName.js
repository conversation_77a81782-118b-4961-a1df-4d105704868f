export function btnHandle(domParams) {
  var firstChildElement = domParams.querySelector('.page_header').lastChild;
  const elements = firstChildElement.children;
  console.log(elements);
  Array.from(elements).forEach(item => {
    if (item.innerText == "发起流程") {
      const buttonChildrens = item.children;
      Array.from(buttonChildrens).forEach(bitem => {
        console.log(bitem);
        if (bitem.innerText == "发起流程") {
          bitem.innerText = "提交"
        }
      })
    }
  })
}
