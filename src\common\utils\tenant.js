/*
 * @Author: 王云飞
 * @Date: 2025-02-24 10:17:46
 * @LastEditTime: 2025-03-21 11:05:41
 * @LastEditors: 王云飞
 * @Description:
 * 这个在system和tenant的改造中废弃
 *
 */
import { config } from 'sn-base-utils'
// import baseConfig from "@/config";
const commonDictTypeUrl = '/system/dict/data/type/{dictType}'
const tenantDictTypeUrl = '/system/tenantDictData/type/{dictType}'
const tenantDict = ['pwd_rule_reg', 'interface_type', 'request_method']
export const getDictUrl = (dictType) => {
  if (config().tenant && tenantDict.includes(dictType)) {
    return tenantDictTypeUrl
  }
  return commonDictTypeUrl
}
export const getUrlByIsTenant = (url) => {
  if (config().tenant) {
    const dictType = url.split('/').pop()
    return '/system/tenantDictData/type/' + dictType
  }
  return url
}
