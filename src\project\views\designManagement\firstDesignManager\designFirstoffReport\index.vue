<template>
  <div class="contents">
    <div class="top">
      <el-card style="width: 100%;">
        <el-button type="primary" @click="saveOrUpdate">保存</el-button>
        <el-button v-if="formData.id" type="primary" @click="deleteData">删除</el-button>
      </el-card>

    </div>
    <div class="centent">
      <el-form :model="formData" :rules="formRules" ref="formRef" label-width="170px" label-position="right">
        <el-row :gutter="16" :span="24">

          <el-card class="box-card" style="width: 100%;">
            <fieldset class="fieldset2">
              <legend>
                <span class="el-button--primary"></span>基本信息
              </legend>
              <el-row :gutter="16" :span="24">
                <el-col :span='8'>
                  <el-form-item label="项目编号" prop="">
                    <el-input v-model="projectData.projCode" :disabled="true" type="text" placeholder="请输入" clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span='8'>
                  <el-form-item label="项目名称" prop="">
                    <el-input v-model="projectData.projName" :disabled="true" type="text" placeholder="请输入" clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span='8'>
                  <el-form-item label="项目单位" prop="">
                    <el-input v-model="projectData.projOrgName" :disabled="true" type="text" placeholder="请输入" clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span='8'>
                  <el-form-item label="项目建设规模" prop="">
                    <el-input v-model="projectData.projScale" :disabled="true" type="text" placeholder="请输入" clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span='16'>
                  <el-form-item label="地理位置" prop="">
                    <el-input v-model="position" :disabled="true" type="text" placeholder="请输入" clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
                <!-- <el-col :span='8'>
                  <el-form-item label="厂址地形选择" prop="">
                    <el-input v-model="projectData.input23077" :disabled="true" type="text" placeholder="请输入" clearable>
                    </el-input>
                  </el-form-item>
                </el-col> -->
                <el-col :span='8'>
                  <el-form-item label="初设编制单位" prop="company">
                    <el-input v-model="formData.company" type="text" placeholder="请输入初设编制单位" clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span='8'>
                  <el-form-item label="初设概算收口版(万元)" prop="compute">
                    <el-input-number v-model="formData.compute" :min="0" :max="999999999999" style="width: 100%;" />
                  </el-form-item>
                </el-col>
                <el-col :span='8'>
                  <el-form-item label="单位千瓦造价(元/kW)" prop="price">
                    <el-input-number v-model="formData.price" :min="0" :max="999999999999" style="width: 100%;" />
                  </el-form-item>
                </el-col>
                <el-col :span='8'>
                  <el-form-item label="全容量投产工期(月)2" prop="workMonth">
                    <el-input-number v-model="formData.workMonth" :min="0" :max="999999999999" style="width: 100%;" />
                  </el-form-item>
                </el-col>
                <el-col :span='8'>
                  <el-form-item label="等效可利用小时数(h)" prop="utilizeHour">
                    <el-input-number v-model="formData.utilizeHour" :min="0" :max="999999999999" style="width: 100%;" />
                  </el-form-item>
                </el-col>
                <el-col :span='8'>
                  <el-form-item label="综合场用电率(%)" prop="electricityRate">
                    <el-input-number v-model="formData.electricityRate" :min="0" :max="999999999999"
                      style="width: 100%;" />
                  </el-form-item>
                </el-col>
                <el-col :span='24'>
                  <el-form-item label="备注" prop="">
                    <el-input type="textarea" v-model="formData.remarks" placeholder="请输入" :rows="3"
                      clearable></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </fieldset>
          </el-card>

          <el-card class="box-card" style="width: 100%;">
            <fieldset class="fieldset2">
              <legend>
                <span class="el-button--primary"></span>初设报告
              </legend>
              <project-document-storage-ui-table ref="reportFileRef" type="reportFile" :relevantId="formData.id"
                :isPageSearch="false" :isDeleteMinio="isDeleteMinio" :isHasAi="isHasAi" @on-add-data="onAddData"
                @on-ai-review="onAiReview" @on-preview="onPreview" @on-delete="onDelete"
                :file-serial-number-builder="fileSerialNumberBuilder" :preview-config="previewConfig"
                                                 :isShowLinkBtn="false"
                :isShowAddBtn="isShowAddBtn" :isShowDelBtn="isShowDelBtn" :isShowPreviewBtn="isShowPreviewBtn"
                :isShowDownloadBtn="isShowDownloadBtn"></project-document-storage-ui-table>
            </fieldset>
          </el-card>
          <el-card class="box-card" style="width: 100%;">
            <fieldset class="fieldset2">
              <legend>
                <span class="el-button--primary"></span>初设概算表
              </legend>
              <project-document-storage-ui-table ref="financialEstimatesFileRef" type="financialEstimatesFile"
                :relevantId="formData.id" :isPageSearch="false" :isDeleteMinio="isDeleteMinio" :isHasAi="isHasAi"
                @on-add-data="onAddData" @on-ai-review="onAiReview" @on-preview="onPreview" @on-delete="onDelete"
                :file-serial-number-builder="fileSerialNumberBuilder" :preview-config="previewConfig"
                                                 :isShowLinkBtn="false"
                :isShowAddBtn="isShowAddBtn" :isShowDelBtn="isShowDelBtn" :isShowPreviewBtn="isShowPreviewBtn"
                :isShowDownloadBtn="isShowDownloadBtn"></project-document-storage-ui-table>
            </fieldset>
          </el-card>
          <el-card class="box-card" style="width: 100%;">
            <fieldset class="fieldset2">
              <legend>
                <span class="el-button--primary"></span>外部条件
              </legend>
              <project-document-storage-ui-table ref="outConditionFileRef" type="outConditionFile"
                :relevantId="formData.id" :isPageSearch="false" :isDeleteMinio="isDeleteMinio" :isHasAi="isHasAi"
                @on-add-data="onAddData" @on-ai-review="onAiReview" @on-preview="onPreview" @on-delete="onDelete"
                :file-serial-number-builder="fileSerialNumberBuilder" :preview-config="previewConfig"
                                                 :isShowLinkBtn="false"
                :isShowAddBtn="isShowAddBtn" :isShowDelBtn="isShowDelBtn" :isShowPreviewBtn="isShowPreviewBtn"
                :isShowDownloadBtn="isShowDownloadBtn"></project-document-storage-ui-table>
            </fieldset>
          </el-card>
          <el-card class="box-card" style="width: 100%;">
            <fieldset class="fieldset2">
              <legend>
                <span class="el-button--primary"></span>单据信息
              </legend>
              <el-row :gutter="16" :span="24">
                <el-col :span='8'>
                  <el-form-item label="创建人" prop="createName">
                    <el-input v-model="formData.createName" :disabled="true" type="text" placeholder="" clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span='8'>
                  <el-form-item label="创建时间" prop="createTime">
                    <el-input v-model="formData.createTime" :disabled="true" type="text" placeholder="请输入创建时间" clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span='8'>
                  <el-form-item label="最近修改人" prop="updateName">
                    <el-input v-model="formData.updateName" :disabled="true" type="text" placeholder="" clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span='8'>
                  <el-form-item label="最近修改时间" prop="updateTime">
                    <el-input v-model="formData.updateTime" :disabled="true" type="text" placeholder="">
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </fieldset>
          </el-card>
        </el-row>
      </el-form>
    </div>

  </div>

</template>

<script>

export const routerConfig = [{
  menuType: "C",
  menuName: "初设收口报备",
}];
</script>



<script setup>
import ProjInfoMApi from '@/project/api/projectInfo/ProjInfoM.js'
import ProjBizDesignFirstoffReportMApi from '@/project/api/designManagement/firstDesignManager/ProjBizDesignFirstoffReportM.js'

import {
  nextTick,
  onMounted,
  ref,
  reactive,
  toRef,
  defineProps,
  defineExpose,
  getCurrentInstance
} from 'vue';
import store from "@/store";
import {getCurrentFormattedTime} from "@/common/utils/datetime";
import AreaInfApi from "@/project/api/areaInfo/AreaInfo";

const {
  proxy
} = getCurrentInstance()
const position = ref("")

const formRef = ref()
let formData = ref({
  projectId: "",
  company: "",
  compute: "",
  price: "",
  workMonth: "",
  utilizeHour: "",
  electricityRate: "",
  createName: store.state.user && store.state.user.userInfo.userName ? store.state.user.userInfo.userName : null,
  createTime: getCurrentFormattedTime(),
  updateName: "",
  updateTime: ""
});
let formRules = ref({
  company: [{
    required: true,
    message: "请输入初设编制单位"
  }],
  compute: [{
    required: true,
    message: "初设概算收口版(万元)"
  }],
  price: [{
    required: true,
    message: "请输入单位千瓦造价(元/kW)"
  }],
  workMonth: [{
    required: true,
    message: "请输入全容量投产工期(月)"
  }],
  utilizeHour: [{
    required: true,
    message: "请输入等效可利用小时数(h)"
  }],
  electricityRate: [{
    required: true,
    message: "请输入综合场用电率(%)"
  }],
  createName: [],
  createTime: [],
  updateName: [],
  updateTime: []
});


const reportFileRef = ref(null);
const outConditionFileRef = ref(null);
const financialEstimatesFileRef = ref(null);
const projectData = ref({
  projCode: "",
  projName: "",
  projOrgName: "",
  projScale: ""
});

//country  province  city projCode projName vbukr projScale
onMounted(() => {

  //获取项目信息
  getProjectInfo();


  //获取收口文件信息
  getDataView();

})





function getProjectInfo() {
  ProjInfoMApi.view(sessionStorage.getItem('projectId')).then((resp) => {
    console.log(resp);
    if (!!resp.data) {
      projectData.value = resp.data;
      let countryName = null
      let provinceName = null
      let cityName = null
      if (resp.data.country) {
        AreaInfApi.selectChildren({id:resp.data.country}).then(res => {
          if (!!res.data && res.data[0].fullName) {
            countryName = res.data[0].fullName;
          }
          if (resp.data.province) {
            AreaInfApi.selectChildren({parentId:resp.data.country,id:resp.data.province}).then(res => {
              if (!!res.data && res.data[0].fullName) {
                provinceName = res.data[0].fullName;
              }
              if (resp.data.city) {
                AreaInfApi.selectChildren({parentId: resp.data.province,id:resp.data.city}).then(res => {
                  console.log(res.data,"6")
                  if (!!res.data && res.data[0].fullName) {
                    cityName = res.data[0].fullName;
                  }
                  position.value = (countryName ? countryName : '--') + '/' + (provinceName ? provinceName : '--') + '/' + (cityName ? cityName : '--');
                })
              } else {
                position.value = (countryName ? countryName : '--') + '/' + (provinceName ? provinceName : '--') + '/' + (cityName ? cityName : '--');
              }
            })
          }else {
            position.value = (countryName ? countryName : '--') + '/' + (provinceName ? provinceName : '--') + '/' + (cityName ? cityName : '--');
          }
        })
      }else {
        position.value = (countryName ? countryName : '--') + '/' + (provinceName ? provinceName : '--') + '/' + (cityName ? cityName : '--');
      }
    }
  })
}

function getDataView() {
  ProjBizDesignFirstoffReportMApi.getOne().then((resp) => {
    if (!resp.data) {
      formData.value = { projectId: sessionStorage.getItem('projectId'),
        createName: store.state.user && store.state.user.userInfo.userName ? store.state.user.userInfo.userName : null,
        createTime: getCurrentFormattedTime(),
      };
    } else {
      formData.value = resp.data;
    }
    console.log(resp);
  })
}

function getFormData() {
  return {
    ...formData.value,
    reportFile: getListData(reportFileRef, "reportFile"),
    outConditionFile: getListData(outConditionFileRef, "outConditionFile"),
    financialEstimatesFile: getListData(financialEstimatesFileRef, "financialEstimatesFile"),
  }

};


function saveOrUpdate() {
  //新增操作
  const formDatas = getFormData();
  ProjBizDesignFirstoffReportMApi.add(formDatas).then(() => {
    proxy.$message.success("操作成功");
    getDataView();
  });
}

function deleteData() {
  proxy.$confirm('确定删除当前记录?', '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
      ProjBizDesignFirstoffReportMApi.remove([formData.value.id]).then(resp => {
        proxy.$modal.msgSuccess("操作成功");
        getDataView();
      });
    })
    .catch(() => { })

}


const previewConfig = ref({
  // 是否外置预览,必传
  isExternalPreview: false,
  // 内置预览返回的替换字符,akey代表要替换的字符串，avalue代码替换的字符串
  substitute: {
    "akey": "avalue"
  },
  // 内置预览服务地址
  previewServerUrl: "http://127.0.0.1:8012/onlinePreview",
})
const isDeleteMinio = ref(true)
const isHasAi = ref(true)
const isShowAddBtn = ref(true)
const isShowDelBtn = ref(true)
const isShowPreviewBtn = ref(true)
const isShowDownloadBtn = ref(true)

function onAddData(list) {
  console.log(list, "onAddData-sssss")
}

function onAiReview(row) {
  console.log(row, "onAiReview-sssss")
}

function onPreview(row) {
  console.log(row, "onPreview-sssss")
}

function onDelete(list) {
  console.log(list, "onDelete-sssss")
}

function getListData(dom, type) {
  if (dom.value) {
    let list = dom.value.getListData();
    list.forEach(item => item.type = type);
    return list;
  }
}

// 文件序列号生成函数
function fileSerialNumberBuilder() {
  return "TEST" + Math.floor(Math.random() * 10000)
}



</script>

<style lang="scss" scoped>
.contents {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: calc(100vh - 88px);
  overflow: hidden;
}

.top {
  height: 80px;
  width: 100%;
  flex: 0 1 auto;
  padding: 0 15px 0px 0px;
  box-sizing: border-box;
}

.centent {
  height: 100px;
  width: 100%;
  flex: auto;
  overflow-y: auto;
  overflow-x: hidden;
}
</style>
