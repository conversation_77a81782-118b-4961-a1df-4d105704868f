<template>
  <el-form :model="formData" :rules="rules" ref="snForm" label-width="100px" label-position="right" :disabled="type == 'view'">
    <el-row :gutter="16" :span="24">
      <el-tabs v-model="tabsType" class="switchingAngle" type="">
        <el-tab-pane label="业务" name="YeWu">
          <el-row :gutter="16" :span="24">
            <el-card class="box-card" style="width: 100%;">
              <fieldset class="fieldset2">
                <legend>
                  <span class="el-button--primary"></span>主表信息
                </legend>
                <el-row :gutter="16" :span="24">
                  <el-col :span='12'>
                    <el-form-item label="业务编号" prop="businessNumber">
                      <el-input v-model="formData.businessNumber" type="text" placeholder="请输入" clearable>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span='12'>
                    <el-form-item label="施工单位" prop="constructionCompany">
                      <el-input v-model="formData.constructionCompany" type="text" placeholder="请输入" clearable>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span='12'>
                    <el-form-item label="记录日期" prop="recordDate">
                      <el-date-picker v-model="formData.recordDate" type="date" value-format="YYYY-MM-DD" placeholder="请选择记录日期" clearable>
                      </el-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span='12'>
                    <el-form-item label="日志文件" prop="journalDoc">
                      <el-input v-model="formData.journalDoc" type="text" placeholder="请输入" clearable>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span='12'>
                    <el-form-item label="记录人" prop="recoder">
                      <el-input v-model="formData.recoder" type="text" placeholder="请输入" clearable>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span='12'>
                    <el-form-item label="发布状态" prop="publishStatus">
                      <el-select v-model="formData.publishStatus" placeholder="请输入" clearable>
                        <el-option label="未发布" value="1"></el-option>
                        <el-option label="已发布" value="2"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
              </fieldset>
            </el-card>
            <el-card class="box-card" style="width: 100%;">
              <fieldset class="fieldset2">
                <legend>
                  <span class="el-button--primary"></span>子表信息
                </legend>
                <el-row :gutter="16" :span="24">
                </el-row>
              </fieldset>
            </el-card>
          </el-row>
        </el-tab-pane>
        <el-tab-pane label="流程图" name="flow" v-if="isShowFlow">
          <el-row :gutter="16" :span="24">
            <iframe id="flowDraw" :src="getHtFlowDrawUrl" style="border: 0;width: 100%;height: 700px" title="流程图"></iframe>
          </el-row>
        </el-tab-pane>
        <el-tab-pane label="审批记录" name="approveList" v-if="isShowFlow">
          <el-row :gutter="16" :span="24">
            <el-table :data="approveList" style="width: 100%">
              <el-table-column type="index" width="50" />
              <el-table-column label="任务名称" prop="taskName" show-overflow-tooltip />
              <el-table-column label="审批人" prop="auditorName" show-overflow-tooltip />
              <el-table-column label="操作" prop="statusVal" show-overflow-tooltip />
              <el-table-column label="任务接收时间" prop="createTime" show-overflow-tooltip />
              <el-table-column label="审批时间" prop="completeTime" show-overflow-tooltip />
              <el-table-column label="审批意见" prop="opinion" show-overflow-tooltip />
            </el-table>
          </el-row>
        </el-tab-pane>
      </el-tabs>
    </el-row>
  </el-form>
</template>

<script setup>
import ProjBizConsJournalApi from '@/project/api/constructionManagement/buildJournal/ProjBizConsJournal.js'
import {
  nextTick,
  onMounted,
  ref,
  reactive,
  toRef,
  defineProps,
  defineExpose,
  getCurrentInstance
} from 'vue';
import {
  useRoute,
  useRouter
} from 'vue-router'
const {
  proxy
} = getCurrentInstance()
const route = useRoute();
const router = useRouter();
const props = defineProps({
  data: Object,
});
const snForm = ref()
const type = toRef(props.data?.type);
let tabsType = ref("YeWu");
let formData = ref({
  businessNumber: "",
  constructionCompany: "",
  recordDate: "",
  journalDoc: "",
  recoder: "",
  publishStatus: ""
});

let rules = ref({
  businessNumber: [
    {
      required: true,
      message: '请输入业务编号'
    },
    {
      pattern: /^[A-Za-z0-9//-]+$/,
      trigger: ['blur', 'change'],
      message: '请输入业务编号'
    }
  ],
  constructionCompany: [
    {
      required: true,
      message: '请输入施工单位'
    },
    {
      trigger: ['blur', 'change'],
      message: '请输入施工单位'
    }
  ],
  recordDate: [
    {
      required: true,
      message: '请选择记录日期'
    },
    {
      trigger: ['blur', 'change'],
      message: '请选择记录日期'
    }
  ],
  journalDoc: [
    {
      required: true,
      message: '请上传日志文件'
    },
    {
      trigger: ['blur', 'change'],
      message: '请上传日志文件'
    }
  ],
  recoder: [
    {
      required: true,
      message: '请输入记录人'
    },
    {
      trigger: ['blur', 'change'],
      message: '请输入记录人'
    }
  ],
  publishStatus: [
    {
      required: true,
      message: '请选择发布状态'
    },
    {
      trigger: ['blur', 'change'],
      message: '请选择发布状态'
    }
  ]
});

let getHtFlowDrawUrl = ref("");
let approveList = ref([]);
formData = toRef(Object.keys(props.data?.formData || {}).length > 0 ? {
  ...props.data?.formData
} : toRef(formData.value));

function getFormData() {
  return formData.value
};

function submitData() {
  return new Promise((resolve) => {
    snForm.value.validate((flag) => {
      if (!flag) {
        return false;
      } else {
        if (type.value === "add") {
          resolve(saveData());
        } else {
          resolve(editData());
        }
      }
    });
  });
}

function saveData() {
  //新增操作
  const formData = getFormData();
  return ProjBizConsJournalApi.add(formData).then(() => {
    proxy.$message.success("新增成功");
    return true;
  });
}

function editData() {
  //编辑操作
  const formData = getFormData();
  return ProjBizConsJournalApi.update(formData).then(() => {
    proxy.$message.success("修改成功");
    return true;
  });
}
defineExpose({
  getFormData,
  submitData,
});
</script>

<style lang="scss" scoped>

</style>
