import { request, replaceUrl } from "sn-base-utils";
import { lifecycleBizServiceCode } from "@/config";
export default class ProjBizDesignStudyoffFileMApi {
    static config = {
        add: {
            url: '/project/designManagement/add',
            method: 'POST'
        },
        remove: {
            url: '/project/designManagement/delete',
            method: 'DELETE'
        },
        update: {
            url: '/project/designManagement/update',
            method: 'PUT'
        },
        view: {
            url: '/project/designManagement/get/{id}',
            method: 'GET'
        },
        getOne: {
            url: '/project/designManagement/getOne',
            method: "GET"
        },
        pageList: {
            url: '/project/designManagement/page',
            method: "POST"
        },

        list: {
            url: '/project/designManagement/list',
            method: "POST"
        }
    };

    /**
     * 新增设计管理-可研收口文件
     * @param data
     * @returns {*}
     */
    static add(data) {
        return request({
            url: this.config.add.url,
            method: this.config.add.method,
            data: data,
            requestPrefix: lifecycleBizServiceCode
        });
    }

    /**
     * 删除设计管理-可研收口文件
     * @param data
     * @returns {*}
     */
    static remove(data) {
        return request({
            url: this.config.remove.url,
            method: this.config.remove.method,
            data: data,
            requestPrefix: lifecycleBizServiceCode
        });
    }

    /**
     * 更新设计管理-可研收口文件
     * @param data
     * @returns {*}
     */
    static update(data) {
        return request({
            url: this.config.update.url,
            method: this.config.update.method,
            data: data,
            requestPrefix: lifecycleBizServiceCode
        });
    }

    /**
     * 查询设计管理-可研收口文件详细
     * @param id
     * @returns {*}
     */
    static view(id) {
        return request({
            url: replaceUrl(this.config.view.url, { id }),
            method: this.config.view.method,
            requestPrefix: lifecycleBizServiceCode
        });
    }

    /**
    * 查询设计管理-可研收口文件详细
    * @param id
    * @returns {*}
    */
    static getOne() {
        return request({
            url: replaceUrl(this.config.getOne.url, {}),
            method: this.config.getOne.method,
            requestPrefix: lifecycleBizServiceCode
        });
    }

    /**
     * 分页查询设计管理-可研收口文件列表
     * @param data
     * @returns {*}
     */
    static pageList(data) {
        return request({
            url: this.config.pageList.url,
            method: this.config.pageList.method,
            data: data,
            requestPrefix: lifecycleBizServiceCode
        });
    }

    /**
     * 全部设计管理-可研收口文件列表
     * @returns {*}
     */
    static list(data) {
        return request({
            url: this.config.list.url,
            method: this.config.list.method,
            data: data,
            requestPrefix: lifecycleBizServiceCode
        });
    }
}
