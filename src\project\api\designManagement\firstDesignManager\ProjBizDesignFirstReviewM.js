import { request, replaceUrl } from "sn-base-utils";
import { lifecycleBizServiceCode } from "@/config";
export default class ProjBizDesignFirstReviewMApi {
    static config = {
        add: {
            url: '/project/firstDesignManager/add',
            method: 'POST'
        },
        remove: {
            url: '/project/firstDesignManager/delete',
            method: 'DELETE'
        },

        recallTask: {
            url: '/project/firstDesignManager/recallTask',
            method: 'GET'
        },

        update: {
            url: '/project/firstDesignManager/update',
            method: 'PUT'
        },
        view: {
            url: '/project/firstDesignManager/get/{id}',
            method: 'GET'
        },

        getOne: {
            url: '/project/firstDesignManager/getOne',
            method: 'GET'
        },


        pageList: {
            url: '/project/firstDesignManager/page',
            method: "POST"
        },
        list: {
            url: '/project/firstDesignManager/list',
            method: "POST"
        },
        startFlow: {
            url: '/project/firstDesignManager/saveAndSubmitProc',
            method: "POST"
        },
        submitTask: {
            url: '/project/firstDesignManager/saveAndSubmitTask',
            method: "POST"
        },



        printTemplate: {
            url: `/project/firstDesignManager/printTemplate`,
            method: "POST"
        }
    };

    /**
     * 新增初设审查
     * @param data
     * @returns {*}
     */
    static add(data) {
        return request({
            url: this.config.add.url,
            method: this.config.add.method,
            data: data,
            requestPrefix: lifecycleBizServiceCode
        });
    }

    /**
     * 删除初设审查
     * @param data
     * @returns {*}
     */
    static remove(data) {
        return request({
            url: this.config.remove.url,
            method: this.config.remove.method,
            data: data,
            requestPrefix: lifecycleBizServiceCode
        });
    }


    /**
   * 删除初设审查
   * @param data
   * @returns {*}
   */
    static recallTask(data) {
        return request({
            url: this.config.recallTask.url,
            method: this.config.recallTask.method,
            params: data,
            requestPrefix: lifecycleBizServiceCode
        });
    }






    /**
     * 更新初设审查
     * @param data
     * @returns {*}
     */
    static update(data) {
        return request({
            url: this.config.update.url,
            method: this.config.update.method,
            data: data,
            requestPrefix: lifecycleBizServiceCode
        });
    }

    /**
     * 查询初设审查详细
     * @param id
     * @returns {*}
     */
    static view(id) {
        return request({
            url: replaceUrl(this.config.view.url, { id }),
            method: this.config.view.method,
            requestPrefix: lifecycleBizServiceCode
        });
    }

    /**
  * 查询初设审查详细
  * @param id
  * @returns {*}
  */
    static getOne() {
        return request({
            url: replaceUrl(this.config.getOne.url, {}),
            method: this.config.getOne.method,
            requestPrefix: lifecycleBizServiceCode
        });
    }



    /**
     * 分页查询初设审查列表
     * @param data
     * @returns {*}
     */
    static pageList(data) {
        return request({
            url: this.config.pageList.url,
            method: this.config.pageList.method,
            data: data,
            requestPrefix: lifecycleBizServiceCode
        });
    }


    /**
     * 全部初设审查列表
     * @returns {*}
     */
    static list(data) {
        return request({
            url: this.config.list.url,
            method: this.config.list.method,
            data: data,
            requestPrefix: lifecycleBizServiceCode
        });
    }

    /**
     * 工作流-启动流程
     * @returns {*}
     */
    static startFlow(data) {
        return request({
            url: this.config.startFlow.url,
            method: this.config.startFlow.method,
            data: data,
            requestPrefix: lifecycleBizServiceCode
        });
    }

    /**
     * 工作流-完成任务
     * @returns {*}
     */
    static submitTask(data) {
        return request({
            url: this.config.submitTask.url,
            method: this.config.submitTask.method,
            data: data,
            requestPrefix: lifecycleBizServiceCode
        });
    }

    /**
     * 工作流-打印模板
     */
    static printTemplate(data) {
        return request({
            url: this.config.printTemplate.url,
            method: this.config.printTemplate.method,
            data: data,
            requestPrefix: lifecycleBizServiceCode
        });
    }
}
