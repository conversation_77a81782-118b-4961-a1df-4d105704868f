<template>
  <div class="wp-tableContent">
  <sn-input-table
    :multiple="true"
    :props="prop"
    :children="children"
    :formatter="formatter"
    :on-load="onLoadThrottle"
    v-model="checkedValue"
    :appendToBody="true"
    placeholder="请选择数据"
  ></sn-input-table>
</div>
</template>
<script>
import{HandelTaskApi as HandelFlowTask<PERSON>pi }   from "sn-bpm-v3";
import HandelUserListApi from './selectUser'
import { throttle } from "lodash-es";
export default {
  props: ["multiple", "params", "api"],
  watch: {
    checkedValue(newValue) {
      this.$emit("update:modelValue", newValue);
    },
  },
  data() {
    return {
      checkedValue: [],
      prop: {
        label: "userName",
        value: "userId",
      },
        children: {
          key: "userId",
          border: true,
          page: false,
          height: 500,
          searchSpan: 8,
          rowKey:"userId",
          searchMenuSpan: 8,
          column: [
            {
              label: "",
              prop: "radio",
              width: 60,
              hide: false,
            },
            
            {
              label: "姓名",
              search: true,
              prop: "userName",
            },
            {
              label: "组织",
              search: false,
              prop: "orgName",
            },
          ],
        }
    };
  },
  created(){
    this.onLoadThrottle=throttle(this.onLoad,1500)
  },
  methods: {
    async getNextIdentityConfig(){
      const res = await HandelUserListApi.getNextIdentityConfig({
        ...this.$props.params,
      })
      let roleId = res.data[0].identityRole.roleId
      const rps = await HandelUserListApi.getUserListByRoleId({
        projectId:sessionStorage.getItem('projectId'),
        roleId:roleId
      })
      return rps.data
    },
    
    async onLoad({ value, data }, callback) {
      let orgUserList = []
      const res = await HandelFlowTaskApi[this.$props.api]({
        ...this.$props.params,
      });
      const userList = res.data
      const roleList = await this.getNextIdentityConfig()
      orgUserList = userList.filter(item => {
          return roleList.some(val => item.userId === val.userId);
      });
      if (value && orgUserList?.length > 0) {
        const dataArr = [];
        for (let i in value) {
          for (let j in orgUserList) {
            if (value[i] == orgUserList[j].userId) {
              dataArr.push(orgUserList[j]);
            }
          }
        }
        callback(dataArr);
        return;
      }
      if (data && data.userName) {
        callback({
          data: orgUserList.filter(
            (item) => item.userName.indexOf(data.userName) >= 0
          ),
        });
        return;
      }
      callback({
        data: orgUserList,
      });
      
    },


    formatter(row) {
      if (Array.isArray(row)) {
        return row.map((ele) => ele.userName).join(",");
      } else {
        return row.userName;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.wp-tableContent ::v-deep .sp-form__item--right{
  display: flex !important;
  .el-form-item__content{
    margin-left: 0 !important;
  }
  
}
.wp-tableContent ::v-deep.sp-form__menu{
    width: 45% !important;
  }
</style>
