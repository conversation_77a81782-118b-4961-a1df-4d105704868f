import { request, replaceUrl } from "sn-base-utils";
import { lifecycleBizServiceCode } from "@/config";
export default class ProjBizDesignWorkdrawOutPlanMApi {
    static config = {
        add: {
            url: '/project/workdrawManager/add',
            method: 'POST'
        },
        remove: {
            url: '/project/workdrawManager/delete',
            method: 'DELETE'
        },

        deleteDetail: {
            url: '/project/workdrawManager/deleteDetail',
            method: 'DELETE'
        },
        update: {
            url: '/project/workdrawManager/update',
            method: 'PUT'
        },
        view: {
            url: '/project/workdrawManager/get/{id}',
            method: 'GET'
        },
        getOne: {
            url: '/project/workdrawManager/getOne',
            method: 'GET'
        },
        pageList: {
            url: '/project/workdrawManager/page',
            method: "POST"
        },

        detailPageList: {
            url: '/project/workdrawManager/detailPage',
            method: "POST"
        },

        list: {
            url: '/project/workdrawManager/list',
            method: "POST"
        },

        importDetail: {
            url: '/project/workdrawManager/importDetail',
            method: "POST"
        },

        importDetailFile: {
            url: '/project/workdrawManager/importDetailFile',
            method: "POST"
        },

        startFlow: {
            url: '/project/workdrawManager/saveAndSubmitProc',
            method: "POST"
        },
        submitTask: {
            url: '/project/workdrawManager/saveAndSubmitTask',
            method: "POST"
        },
        printTemplate: {
            url: `/project/workdrawManager/printTemplate`,
            method: "POST"
        },

        recallTask: {
            url: '/project/workdrawManager/recallTask',
            method: 'GET'
        },
    };

    /**
     * 新增施工图出图计划
     * @param data
     * @returns {*}
     */
    static add(data) {
        return request({
            url: this.config.add.url,
            method: this.config.add.method,
            data: data,
            requestPrefix: lifecycleBizServiceCode
        });
    }

    /**
     * 删除施工图出图计划
     * @param data
     * @returns {*}
     */
    static remove(data) {
        return request({
            url: this.config.remove.url,
            method: this.config.remove.method,
            data: data,
            requestPrefix: lifecycleBizServiceCode
        });
    }

    /**
   * 删除施工图出图计划详细
   * @param data
   * @returns {*}
   */
    static deleteDetail(data) {
        return request({
            url: this.config.deleteDetail.url,
            method: this.config.deleteDetail.method,
            data: data,
            requestPrefix: lifecycleBizServiceCode
        });
    }



    /**
     * 更新施工图出图计划
     * @param data
     * @returns {*}
     */
    static update(data) {
        return request({
            url: this.config.update.url,
            method: this.config.update.method,
            data: data,
            requestPrefix: lifecycleBizServiceCode
        });
    }

    /**
     * 查询施工图出图计划详细
     * @param id
     * @returns {*}
     */
    static view(id) {
        return request({
            url: replaceUrl(this.config.view.url, { id }),
            method: this.config.view.method,
            requestPrefix: lifecycleBizServiceCode
        });
    }

    /**
     * 查询施工图出图计划详细
     * @returns {*}
     */
    static getOne(id) {
        return request({
            url: replaceUrl(this.config.getOne.url, {}),
            method: this.config.getOne.method,
            requestPrefix: lifecycleBizServiceCode
        });
    }
    /**
     * 分页查询施工图出图计划列表
     * @param data
     * @returns {*}
     */
    static pageList(data) {
        return request({
            url: this.config.pageList.url,
            method: this.config.pageList.method,
            data: data,
            requestPrefix: lifecycleBizServiceCode
        });
    }

    /**
     * 分页查询施工图出图计划详细列表
     * @param data
     * @returns {*}
     */
    static detailPageList(data) {
        return request({
            url: this.config.detailPageList.url,
            method: this.config.detailPageList.method,
            data: data,
            requestPrefix: lifecycleBizServiceCode
        });
    }



    /**
     * 全部施工图出图计划列表
     * @returns {*}
     */
    static list(data) {
        return request({
            url: this.config.list.url,
            method: this.config.list.method,
            data: data,
        });
    }

    /**
     * 导入施工图出图计划列表
     * @returns {*}
     */
    static importDetail(data) {
        return request({
            url: this.config.importDetail.url,
            method: this.config.importDetail.method,
            data: data,
            Headers: { "Content-Type": "multipart/form-data" },
            requestPrefix: lifecycleBizServiceCode
        });
    }


    /**
    * 导入施工图出图文件
    * @returns {*}
    */
    static importDetailFile(data) {
        return request({
            url: this.config.importDetailFile.url,
            method: this.config.importDetailFile.method,
            data: data,
            Headers: { "Content-Type": "multipart/form-data" },
            requestPrefix: lifecycleBizServiceCode
        });
    }




    /**
     * 工作流-启动流程
     * @returns {*}
     */
    static startFlow(data) {
        return request({
            url: this.config.startFlow.url,
            method: this.config.startFlow.method,
            data: data
        });
    }

    /**
     * 工作流-完成任务
     * @returns {*}
     */
    static submitTask(data) {
        return request({
            url: this.config.submitTask.url,
            method: this.config.submitTask.method,
            data: data
        });
    }

    /**
     * 工作流-打印模板
     */
    static printTemplate(data) {
        return request({
            url: this.config.printTemplate.url,
            method: this.config.printTemplate.method,
            data: data
        });
    }

    /**
     * 撤回施工图出图计划
     * @param data
     * @returns {*}
     */
    static recallTask(data) {
        return request({
            url: this.config.recallTask.url,
            method: this.config.recallTask.method,
            params: data,
            requestPrefix: lifecycleBizServiceCode
        });
    }
}
