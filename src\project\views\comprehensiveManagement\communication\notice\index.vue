<template>
  <div>
  <sn-crud :data="listData" :option="option" v-model:page="queryForm.page" v-model:search="queryForm.filter" @on-load="getPageList" @search-change="onChangeSearch" @search-reset="onResetSearch" @addBtnHandle="onEditData" @row-del="onDelData">
    <template #menu="{ row, index, size }">
      <el-button type="primary" :size="size" icon="el-icon-edit" link @click="onEditData(row, 'edit')" v-if="(row.approvalStatus === 1 || row.approvalStatus === 7)">编辑</el-button>
      <el-button type="danger" :size="size" icon="el-icon-edit" link @click="operateFlow(row,'revoke')" v-if="row.approvalStatus === 2">撤回</el-button>
      <el-button type="primary" :size="size" icon="el-icon-download" link @click="onDownData(row)">下载</el-button>
      <el-button type="danger" :size="size" icon="el-icon-delete" link @click="onDelData([row])" v-if="row.approvalStatus === 1 && !row.procInstanceId">删除</el-button>
      <el-button type="danger" :size="size" icon="el-icon-edit" link @click="operateFlow(row,'stop')" v-if="(row.approvalStatus === 1 || row.approvalStatus === 7) && row.procInstanceId">作废</el-button>

    </template>
    <template #noticeNum="{ row }">
      <el-tooltip class="item" effect="dark" :content="row.noticeNum" placement="top">
        <el-link type="primary" @click="onEditData(row,'view')" style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 200px; display: block;">{{row.noticeNum}}</el-link>
      </el-tooltip>
    </template>
    <template #title="{ row }">
      <el-tooltip class="item" effect="dark" :content="row.title" placement="top">
        <div class="text-overflow">{{ row.title }}</div>
      </el-tooltip>
    </template>
    <template #unitName="{ row }">
      <el-tooltip class="item" effect="dark" :content="row.unitName" placement="top">
        <div class="text-overflow">{{ row.unitName }}</div>
      </el-tooltip>
    </template>
    <template #submittingUnit="{ row }">
      <el-tooltip class="item" effect="dark" :content="row.submittingUnit" placement="top">
        <div class="text-overflow">{{ row.submittingUnit }}</div>
      </el-tooltip>
    </template>
    <template #menuLeft="{ row, index, size }">
      <el-button type="primary" :size="size" icon="el-icon-plus" v-if="hasPermi('add')" @click="onEditData">新增</el-button>
      <el-button type="primary" :size="size" icon="el-icon-download" @click="downloadTemplate(row)">表单模版</el-button>
    </template>
  </sn-crud>
    <div ref="myRef"></div>
  </div>
</template>

<script>
//TODO 引入所需JS
import Edit__classNameVariable__ from "./components/EditProjBizCommunicationNoticeM.vue";
import __classNameVariable__Api from '@/project/api/comprehensiveManagement/communication/notice/ProjBizCommunicationNoticeM.js'
export const routerConfig = [{
  menuType: "C",
  menuName: "通知单",
  menuSort: 100, // 菜单排序, 初始化菜单使用
}, {
  menuType: "F",
  menuName: "查看",
  perms: "show",
  api: [__classNameVariable__Api.config.pageList],
}, {
  menuType: "F",
  menuName: "新增",
  perms: "add",
  api: [__classNameVariable__Api.config.add],
}, {
  menuType: "F",
  menuName: "修改",
  perms: "update",
  api: [__classNameVariable__Api.config.update, __classNameVariable__Api.config.view],
}, {
  menuType: "F",
  menuName: "删除",
  perms: "del",
  api: [__classNameVariable__Api.config.remove],
}];
</script>

<script setup>
import {
  ref,
  getCurrentInstance
} from 'vue';
import dayjs from 'dayjs'
import { hasPermi } from 'sn-base-utils'
import { download, generateWordDocument } from '@/project/components/downloadWord/word'
import { getCurrentFormattedTime } from '@/common/utils/datetime'
const {
  proxy
} = getCurrentInstance()
let option = ref({
  tip: false,
  dialogType: "page",
  border: true,
  index: true,
  stripe: true,
  menu: true,
  header: true,
  height: "auto",
  searchSpan: 6,
  searchIcon: true,
  searchMenuSpan: 6,
  searchIndex: 3,
  selection: true,
  showTree: false,
  excelBtn: false,
  delBtn: false,
  editBtn: false,
  delBtns: false,
  addBtn: false,
  delBtnsText: "批量删除",
  addBtnText: "新增",
  customEditBtnText: "编辑",
  customDelBtnText: "删除",
  customViewBtnText: "查看",
  column: [{
    label: "编号",
    prop: "noticeNum",
    width: 120
  }, {
    label: "主题",
    prop: "title",
    search: true,
    width: 200,
    queryType: "LIKE"
  },{
    label: "通知单位",
    prop: "unitName"
  },  {
    label: "提出单位",
    prop: "submittingUnit",
    search: true,
    columnSlot: false,
    searchSlot: false,
    queryType: "LIKE"
  }, {
    label: "提出日期",
    prop: "submissionDate",
    columnSlot: false,
    searchSlot: false,
    formatter: (row) => row.submissionDate ? dayjs(row.submissionDate).format('YYYY-MM-DD') : ''
  }, {
    label: "审批状态",
    prop: "approvalStatus",
    search: true,
    width: 100,
    align: "center", //对齐方式：居中
    type: 'select',
    dicUrl: "/system/dict/data/type/global_biz_flow_status",
    dicMethod: "get",
    props: {
      label: "dictLabel",
      value: "dictValue"
    },
    html: true,
    formatter: (val, value) => {
      value = value + ''
      if(value === '1'){
        return `<span style="color:#4871C0">草稿</span>`;
      } else if(value === '2'){
        return `<span style="color:#4871C0">已提交</span>`;
      } else if(value === '4'){
        return `<span style="color:#4871C0">审批中</span>`;
      } else if(value === '3'){
        return `<span style="color:red">上级驳回</span>`;
      } else if(value === '5'){
        return `<span style="color:red">已作废</span>`;
      } else if(value === '6'){
        return `<span style="color:dodgerblue">已审批</span>`;
      } else if(value === '7'){
        return `<span style="color:red">已驳回</span>`;
      }
      return `<p></p>`;
    }
  }, {
    label: "是否需回复",
    prop: "isReply",
    queryType: "EQ",
    width: 100,
    dictDatas: "sys_yn",
    type: "select",
    dicUrl: "/system/dict/data/type/sys_yn",
    dicMethod: "get",
    props: {
      label: "dictLabel",
      value: "dictValue"
    }
  },  {
    label: "回复状态",
    prop: "replyStatus",
    width: 100,
    queryType: "EQ",
    dictDatas: "replyStatus",
    type: "select",
    dicUrl: "/system/dict/data/type/replyStatus",
    dicMethod: "get",
    props: {
      label: "dictLabel",
      value: "dictValue"
    }
  }]
});
let listData = ref([]);
let queryForm = ref({
  page: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  filter: {
    createTime: null
  }
});
const myRef = ref(null);
let formRules = ref({});
//导出word
const generatedFile = ref(null);
function getPageList() {
  //查询分页列表
  // 查询前处理参数
  const params = handleQueryForm();
  __classNameVariable__Api.pageList(params).then((res) => {
    listData.value = res.data.dataList;
    queryForm.value.page.total = res.data.totalCount;
  });
}

function onResetSearch() {
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
}

function handleQueryForm() {
  // 处理参数
  const {
    pageSize,
    pageNum
  } = queryForm.value.page;
  const filter = {}
  for (let key in queryForm.value.filter) {
    if (queryForm.value.filter[key]?.length) {
      // sn-curd搜索select会生成$prop的变量
      if (key.startsWith("$")) continue;
      filter[key] = queryForm.value.filter[key];
    }
  }
  filter.projectId = sessionStorage.getItem('projectId')
  delete filter.createTime;
  if (Array.isArray(queryForm.value.filter.createTime) && queryForm.value.filter.createTime?.length === 2) {
    filter["beginCreateTime"] = queryForm.value.filter.createTime[0];
    filter["endCreateTime"] = queryForm.value.filter.createTime[1];
  }
  const searchParams = {
    page: {
      pageSize,
      pageNum,
    },
    filter,
  }
  return searchParams
}

function onChangeSearch(params, done) {
  //搜索按钮操作
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
  done && done();
}
async function onEditData(row, option) {
  //编辑,新增按钮操作
  let editType = row?.procInstanceId ? 'view' : (row?.id ? 'edit' : 'add');
  if (option) {
    editType = option;
  }
  let rowInfo = await (editType !== "add" ? __classNameVariable__Api.view(row.id) : {});
  //编辑,新增按钮操作
  proxy.$DialogForm.show({
    title: editType == "edit" ? "编辑" : (editType == "view" ? "查看" : "新增"),
    type: 'page',
    width: "100%",
    el: myRef.value,
    content: Edit__classNameVariable__,
    data: editType !== "add" ? {
      formData: rowInfo.data,
      isShowCloseBtn: false,
      approvalOption: ref({
        isShowApprovalList: true,
        isShowFlowDiagram: true,
        procInstId: rowInfo.data.procInstanceId
      }),
      type: editType,
      el: myRef.value,
      closeDialog: closeDialog,
    } : {
      approvalOption: ref({
        isShowApprovalList: false,
        isShowFlowDiagram: false,
        procInstId: null
      }),
      type: editType,
      el: myRef.value,
      closeDialog: closeDialog,
    },
    option: {
      submitBtn: false,
      emptyBtn: false,
      submitText: '保存',
      emptyText: '关闭',
      extendButton: generateButtons(editType, row)
    },
    callback: (res) => {
      let vm = res.dialogRefs
      if (vm) {
        let formData = vm.getFormData();
        switch (res.type) {
          case 'edit':
            res.close();
            onEditData(row)
            break;
          //直接取消
          // 取消
          case 'cancelDirect':
            getPageList();
            res.close();
            break;
          // 取消
          case 'cancel':
            proxy.$modal.confirm("确认关闭？", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }).then(() => {
              getPageList();
              res.close();
            }).catch(() => {});
            break;
          // 保存
          case 'save':
            if(!formData.approvalStatus){
              formData.approvalStatus = '1'
            }
            vm.getSaveFormData(formData).then(() => {
              proxy.$message.success("保存成功");
              getPageList();
              // res.close();
            })
            break;
          // 发起流程
          case 'afterProcessIsInitiated':
            let startProcDto = {
              businessKey: null,
              clientId: null
            };
            if(formData.procInstanceId){
              let taskInfo = vm.getTaskInfo();
              vm.handlerOpenConfirm(taskInfo,res);
              getPageList();
            }else {
              vm.getStartFlow(formData, startProcDto).then(() => {
                let taskInfo = vm.getTaskInfo();
                vm.handlerOpenConfirm(taskInfo,res);
                getPageList();
              })
            }
            break;
          case 'downloadEmptyForm':
            vm.startEmpty();
            break;
          case 'downloadForm':
            vm.startGeneration();
            break;
          //提交流程
          //提交流程
          case 'submitFlowTaskProcess':
            vm.submitFlowTask()
            break;
        }
      }
    }
  });
}
function closeDialog(e) {
  getPageList();
  e.close()
}
function generateButtons(editType, row) {
  if (editType !== "view") {
    const hasProcId = row?.procInstanceId;
    const buttons = [{ key: 'cancel', text: '关闭', buttonType: '', icon: 'el-icon-close' }];

    if (!hasProcId) {
      buttons.push({
        key: 'save',
        text: '保存',
        buttonType: 'primary',
        icon: 'el-icon-plus',
      },{
        key: 'afterProcessIsInitiated',
        text: '发起流程',
        buttonType: 'primary',
        icon: 'el-icon-check',
      },{
        key: 'downloadEmptyForm',
        text: '下载模板',
        buttonType: 'primary',
        icon: 'el-icon-download',
      },{
        key: 'downloadForm',
        text: '下载表单',
        buttonType: 'primary',
        icon: 'el-icon-download',
      });
    } else {
      buttons.push({
        key: 'save',
        text: '保存',
        buttonType: 'primary',
        icon: 'el-icon-plus',
      },{
        key: 'submitFlowTaskProcess',
        text: '提交',
        buttonType: 'primary',
        icon: 'el-icon-check'
      },{
        key: 'downloadEmptyForm',
        text: '下载模板',
        buttonType: 'primary',
        icon: 'el-icon-download',
      },{
        key: 'downloadForm',
        text: '下载表单',
        buttonType: 'primary',
        icon: 'el-icon-download',
      });
    }
    return buttons;
  }
  return row.approvalStatus != '1' ? [
    { key: 'cancelDirect', text: '关闭', buttonType: '', icon: 'el-icon-close' }
  ] : [
    { key: 'cancelDirect', text: '关闭', buttonType: '', icon: 'el-icon-close' },
    { key: 'edit', text: '编辑', buttonType: 'primary', icon: 'el-icon-edit' }
  ];
}
//下载模板
async function downloadTemplate() {
  try {
    generatedFile.value = await generateWordDocument(null, 'noticeEmpty');
    if (generatedFile.value) {
      download(generatedFile.value, '通知单模板.docx');
      proxy.$message.success("word下载成功！");
    } else {
    }
  } catch (error) {
  }
}
function onDelData(rows) {
  //删除行
  if (!rows || rows.length == 0) {
    proxy.$message.info("请勾选数据！");
    return false;
  }
  let ids = rows.map((item) => {
    return item.id;
  });
  proxy.$modal.confirm("确认删除数据项？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    __classNameVariable__Api.remove(ids).then((res) => {
      proxy.$message.success("已删除");
      getPageList();
    });
  }).catch(() => {
  });
}
function operateFlow(row, type) {
  let typeName;
  if(type === 'revoke'){
    typeName = '撤回';
  }else if(type === 'stop'){
    typeName = '作废';
  }
  proxy.$modal.confirm("确认要"+typeName+"流程？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    __classNameVariable__Api.operateFlow({
      id:row.id,
      operateFlowStatus:type,
      approvalStatus: row.approvalStatus
    }).then((res) => {
      proxy.$message.success("已"+typeName);
      // 重新请求接口刷新列表
      getPageList()
    });
  }).catch(() => {});
}
/**
 * 下载文件
 * @param row
 */
function onDownData(row){
  let params = {
    relevantId: row.id,
    type: 'commNotice'
  }
  let timestamp = getCurrentFormattedTime()
  proxy.download(`/communication/notice/fillTemplateAndDownload/${row.id}`, params, '通知单导出-' + row.noticeNum + '-' + timestamp + '.zip')
}
</script>

<style lang="scss" scoped>
//文本过长省略号
.text-overflow {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>