<template>
  <sn-crud :data="listData" :option="option" v-model:page="queryForm.page" v-model:search="queryForm.filter" @on-load="getPageList" @search-change="onChangeSearch" @search-reset="onResetSearch" @addBtnHandle="onEditData" @row-del="onDelData">
    <template #menu="{ row, index, size }">
      <template v-if="Number(row.publishStatus) !== 1">
        <el-button type="primary" :size="size" icon="el-icon-edit" link @click="onEditData(row)">编辑</el-button>
        <el-button type="danger" :size="size" icon="el-icon-delete" link @click="onDelData([row])">删除</el-button>
      </template>
      <template v-else>
        <el-button type="danger" :size="size" icon="el-icon-refresh-left" link @click="onUnpublish(row, index)">撤回</el-button>
      </template>
    </template>
    <template #stopworkOrderNo="{ row }">
      <el-link class="image-link" @click="onViewData(row)">{{ row.stopworkOrderNo }}</el-link>
    </template>
    <template #issueDateSearch = "{ row, size }">
        <el-date-picker
            v-model="queryForm.filter.issueDate"
            type="daterange"
            value-format="YYYY-MM-DD"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 100%;"
        />
    </template>
    <template #publishStatus="{row}">
      <span :style="{color : row.publishStatus && row.publishStatus===1 ? 'green' : 'red'}">
        {{row.publishStatus && row.publishStatus===1 ? '已发布': '草稿'}}
      </span>
    </template>
    <template #constructionUnit="{ row }">
      <el-tooltip class="item" effect="dark" :content="row.constructionUnit" placement="top">
        <div class="text-overflow">{{ row.constructionUnit }}</div>
      </el-tooltip>
    </template>
  </sn-crud>
  <div ref="myRef"></div>
</template>

<script>
import ProjBizStopworkOrderApi from '@/project/api/stopworkOrder/ProjBizStopworkOrder.js'
  import EditProjBizStopworkOrder from "./components/EditProjBizStopworkOrder.vue";
  import { getToken } from "sn-base-utils";
  export const routerConfig = [{
    menuType: "C",
    menuName: "停工令",
  }, {
    menuType: "F",
    menuName: "查看",
    perms: "show",
    api: [ProjBizStopworkOrderApi.config.pageList],
  }, {
    menuType: "F",
    menuName: "新增",
    perms: "add",
    api: [ProjBizStopworkOrderApi.config.add],
  }, {
    menuType: "F",
    menuName: "修改",
    perms: "update",
    api: [ProjBizStopworkOrderApi.config.update, ProjBizStopworkOrderApi.config.view],
  }, {
    menuType: "F",
    menuName: "删除",
    perms: "del",
    api: [ProjBizStopworkOrderApi.config.remove],
  }];
</script>

<script setup>
import {
  ref,
  getCurrentInstance
} from 'vue';
const {
  proxy
} = getCurrentInstance()
const myRef = ref(null);
let option = ref({
  tip: false,
  dialogType: "page",
  border: true,
  index: true,
  stripe: true,
  menu: true,
  header: true,
  height: "auto",
  searchSpan: 6,
  searchIcon: true,
  searchMenuSpan: 6,
  searchIndex: 3,
  selection: true,
  showTree: false,
  excelBtn: false,
  delBtn: false,
  editBtn: false,
  delBtns: false,
  delBtnsText: "批量删除",
  addBtnText: "新增",
  customEditBtnText: "编辑",
  customDelBtnText: "删除",
  customViewBtnText: "查看",
  column: [{
    label: "停工令号",
    prop: "stopworkOrderNo",
    search: true,
    width: 200
  }, {
    label: "施工单位",
    prop: "constructionUnit",
    search: true,
    width: 200
  }, {
    label: "停工日期",
    prop: "stopworkTime"
  }, {
    label: "发令人",
    prop: "issuer",
    search: true
  }, {
    label: "发令日期",
    prop: "issueDate",
    search: true,
    searchSlot: true,
    queryType: "BETWEEN"
  }, {
    label: "是否复工",
    prop: "isResumed",
    search: true,
    type: "select",
    dicData: [
      { label: "未复工", value: 0 },
      { label: "已复工", value: 1 }
    ]
  }, {
    label: "发布状态",
    prop: "publishStatus",
    search: true,
    type: "select",
    dicData: [
      { label: "草稿", value: 0},
      { label: "已发布", value: 1 }
    ]
  }]
});
let listData = ref([]);
let queryForm = ref({
  page: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  filter: {
    createTime: null
  }
});
let formRules = ref({});

function getPageList() {
  //查询分页列表
  // 查询前处理参数
  const params = handleQueryForm();
  ProjBizStopworkOrderApi.pageList(params).then((res) => {
    listData.value = res.data.dataList;
    queryForm.value.page.total = res.data.totalCount;
  });
}

function onResetSearch() {
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
}

function handleQueryForm() {
  // 处理参数
  const {
    pageSize,
    pageNum
  } = queryForm.value.page;
  const filter = {}
  for (let key in queryForm.value.filter) {
    if (
      queryForm.value.filter[key] !== undefined &&
      queryForm.value.filter[key] !== null &&
      queryForm.value.filter[key] !== ''
    ) {
      // sn-curd搜索select会生成$prop的变量
      if (key.startsWith("$")) continue;
      filter[key] = queryForm.value.filter[key];
    }
  }
  delete filter.createTime;
  if (Array.isArray(queryForm.value.filter.createTime) && queryForm.value.filter.createTime?.length === 2) {
    filter["beginCreateTime"] = queryForm.value.filter.createTime[0];
    filter["endCreateTime"] = queryForm.value.filter.createTime[1];
  }
  if (Array.isArray(queryForm.value.filter.issueDate) && queryForm.value.filter.issueDate?.length === 2) {
    filter["beginIssueDate"] = queryForm.value.filter.issueDate[0];
    filter["endIssueDate"] = queryForm.value.filter.issueDate[1];
    delete filter.issueDate;
  }
  const searchParams = {
    page: {
      pageSize,
      pageNum,
    },
    filter,
  }
  return searchParams
}

function onChangeSearch(params, done) {
  //搜索按钮操作
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
  done && done();
}
async function onEditData(row) {
  //编辑,新增按钮操作
  let editType = row ? "edit" : "add";
  let rowInfo = await (editType !== "add" ? ProjBizStopworkOrderApi.view(row.id) : {});
  const formData = editType !== "add" ? rowInfo.data : rowInfo;
  //编辑,新增按钮操作
  proxy.$DialogForm.show({
    title: editType == "edit" ? "编辑" : "新增",
    type: option.value.dialogType,
    width: "80%",
    el: myRef.value,
    content: EditProjBizStopworkOrder,
    data: {
      formData: formData,
      type: editType,
    },
    option: {
      submitBtn: false,
      emptyBtn: false,
      emptyText:'取消',
      extendButton:[
        {
          key: 'save',
          text: '保存',
          icon: 'el-icon-plus',
          buttonType: 'primary',
        },
        {
          key: 'submit',
          text: '发布',
          icon: 'el-icon-check',
          buttonType: 'primary',
        },
        {
          key: 'close',
          text: '关闭',
          icon: 'el-icon-close',
          buttonType: '',
        },
      ],
    },
    callback: (res) => {
      if (res.type && res.type == 'save') {
        if (res.dialogRefs) {
          res.dialogRefs.submitData(res.type).then((flag) => {
            if (flag) {
              getPageList();
            }
          });
        }
      }else if (res.type && res.type == 'submit') {
        if (res.dialogRefs) {
          res.dialogRefs.submitData(res.type).then((flag) => {
            if (flag) {
              getPageList();
              res.close();
            }
          });
        }
      }
       else {
        if (editType === 'edit') {
          proxy.$confirm('确认关闭？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            res.close();
          }).catch(() => {});
        } else {
          res.close();
        }
      }
    }
  });
}

async function onViewData(row) {
  //查看操作
  let editType = "view";
  let extendButtons = [];
  if (row.publishStatus === 0) {
    extendButtons.push({
      key: 'edit',
      text: '编辑',
      icon: 'el-icon-edit',
      buttonType: 'primary',
      click: (res) => {
        res.close();
        onEditData(row);
      },
    });
  }
  extendButtons.push({
    key: 'close',
    text: '关闭',
    icon: 'el-icon-close',
    buttonType: '',
  });
  let rowInfo =  await(ProjBizStopworkOrderApi.view(row.id));
  const formData = rowInfo.data;
  proxy.$DialogForm.show({
    title: "查看",
    type: option.value.dialogType,
    width: "80%",
    el: myRef.value,
    content: EditProjBizStopworkOrder,
    data: {
      formData: formData,
      type: editType,
    },
    option: {
      submitBtn: false,
      emptyBtn: false,
      emptyText:'取消',
      extendButton: extendButtons
    },
    callback: (res) => {
      if (res.type === 'edit') {
        res.close();
        onEditData(row);
      } else if (res.type === 'close') {
        if (editType === 'edit') {
          proxy.$confirm('确认关闭？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            res.close();
          }).catch(() => {});
        } else {
          res.close();
        }
      } else if (res.type && res.type !== 'close') {
        if (res.dialogRefs) {
          res.dialogRefs.submitData(res.type).then((flag) => {
            if (flag) {
              getPageList();
              res.close();
            }
          });
        }
      } else {
        res.close();
      }
    }
  });
}

function onDelData(rows) {
  //删除行
  if (!rows || rows.length == 0) {
    proxy.$message.info("请勾选数据！");
    return false;
  }
  let ids = rows.map((item) => {
    return item.id;
  });
  proxy.$modal.confirm("确认删除数据项？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    ProjBizStopworkOrderApi.remove(ids).then((res) => {
      proxy.$message.success("已删除");
      getPageList();
    });
  }).catch(() => {});
}

function onUnpublish(row, index) {
  proxy.$modal.confirm("确认撤回该记录？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    if (row.id) {
      ProjBizServiceFileMApi.update({
        ...row,
        publishStatus: 0
      }).then(() => {
        proxy.$message.success("撤回成功");
        getPageList();
      }).catch(() => {
        proxy.$message.error("撤回失败");
      });
    } else {
      proxy.$message.success("撤回成功");
      getPageList();
    }
  }).catch(() => {});
}
</script>

<style lang="scss" scoped>
.image-link {
  color: #1a5cff !important;
  cursor: pointer;
  text-decoration: none;
}
.image-link:hover {
  text-decoration: underline;
}
.text-overflow {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>

