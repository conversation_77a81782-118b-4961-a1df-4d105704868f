<template>
  <el-form :model="formData" :rules="formRules" ref="formRef" label-width="100px" label-position="right"
           :disabled="type === 'show'">
    <el-row :gutter="16" :span="24">
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>基本信息
          </legend>
        <el-row :gutter="16" :span="24">
          <el-col :span='12'>
            <el-form-item label="业务编号" prop="code">
              <el-input v-model="formData.code" type="text" placeholder="请输入" @blur="checkRepeatNumber()" clearable>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span='12'>
            <el-form-item label="培训负责人" prop="trainPeopleCharge">
              <el-input v-model="formData.trainPeopleCharge" type="text" placeholder="请输入" clearable>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span='12'>
            <el-form-item label="培训名称" prop="trainName">
              <el-input v-model="formData.trainName" type="text" placeholder="请输入" clearable>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span='12'>
            <el-form-item label="培训单位" prop="trainUnit">
              <el-input v-model="formData.trainUnit" type="text" placeholder="请输入" clearable>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span='12'>
            <el-form-item label="培训开始时间" prop="trainStartTime">
              <el-date-picker v-model="formData.trainStartTime" type="date" placeholder="选择时间" clearable style="width: 100%"></el-date-picker>

              <!--          <el-input v-model="formData.trainStartTime" type="text" placeholder="请输入" clearable>-->
              <!--          </el-input>-->
            </el-form-item>
          </el-col>
          <el-col :span='12'>
            <el-form-item label="培训结束时间" prop="trainEndTime">
              <el-date-picker v-model="formData.trainEndTime" type="date" placeholder="选择时间" clearable style="width: 100%"></el-date-picker>
              <!--          <el-input v-model="formData.trainEndTime" type="text" placeholder="请输入" clearable>-->
              <!--          </el-input>-->
            </el-form-item>
          </el-col>
          <el-col :span='12'>
            <el-form-item label="是否需要考试" prop="isExam">
              <el-select v-model="formData.isExam" placeholder="请选择" clearable>
                <el-option label="是" :value="1"></el-option>
                <el-option label="否" :value="0"></el-option>
              </el-select>
<!--              <el-input v-model="formData.isExam" type="text" placeholder="请输入" clearable>-->
<!--              </el-input>-->
            </el-form-item>
          </el-col>
          <el-col :span='24'>
            <el-form-item label="培训内容及要求" prop="trainContent">
              <el-input v-model="formData.trainContent" type="textarea" placeholder="请输入" clearable>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span='12'  style="display: none">
            <el-form-item label="附件id" prop="fileId">
              <el-input v-model="formData.fileId" type="text" placeholder="请输入" clearable>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span='12' style="display: none">
            <el-form-item label="项目编码" prop="projCode">
              <el-input v-model="formData.projCode" type="text" placeholder="" clearable disabled >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span='12' style="display: none">
            <el-form-item label="项目名称" prop="projName">
              <el-input v-model="formData.projName" type="text" placeholder="" disabled clearable>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span='12'>
            <el-form-item label="项目单位" prop="projUnit" style="display: none">
              <el-input v-model="formData.projUnit" type="text" placeholder="请输入"  clearable>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        </fieldset>
      </el-card>


      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>培训签到人员信息
          </legend>
          <el-row :gutter="16" :span="24">
            <sn-crud :data="formData.projBizTrainPersonSDtoList" :option="projBizTrainPersonSOption"
                     @row-save="subRowSave" @row-update="subRowUpdate"
                     @row-del="(row, index) => {subDelRow(row, index, 'projBizTrainPersonSDtoList');}">
              <template #empty>
                <div>无数据</div>
              </template>
            </sn-crud>
            <el-button @click="subAddRow('projBizTrainPersonSDtoList')" type="primary" plain
                       style="display: block; width: 100%; margin-top: 10px">新增一行数据
            </el-button>
          </el-row>
        </fieldset>
      </el-card>

      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>附件
          </legend>
          <el-row :gutter="16" :span="24">
            <project-document-storage-ui-table
                ref="childRef"
                :type="fileType"
                :relevantId="formData.id"
                :isPageSearch="false"
                :isDeleteMinio="false"
                :isHasAi="false"
                @on-add-data="onAddData"
                :file-serial-number-builder="fileSerialNumberBuilder"
                :preview-config="previewConfig"
                :isShowAddBtn="showUpload"
                :isShowDelBtn="true"
                :isShowPreviewBtn="true"
                :isShowDownloadBtn="true"
                :isShowLinkBtn="false"
                :isShowDownloadBatchBtn="true"
            ></project-document-storage-ui-table>
          </el-row>
        </fieldset>
      </el-card>

      <el-card class="box-card" style="width: 100%;">
        <!--        <fieldset class="fieldset2">-->
        <legend>
          <span class="el-button--primary"></span>单据信息
        </legend>
        <el-row :gutter="16" :span="24">
          <el-col :span='8'>
            <el-form-item label="创建者姓名" prop="createName">
              <el-input v-model="formData.createName" type="text" placeholder="" :disabled="true" clearable>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span='8'>
            <el-form-item label="创建时间" prop="createTime">
              <el-input v-model="formData.createTime" disabled type="text" placeholder="" clearable/>
              <!--              <el-date-picker type="date" v-model="formData.createTime" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD"-->
<!--                              :disabled="true" clearable></el-date-picker>-->
            </el-form-item>
          </el-col>
          <el-col :span='8'>
            <el-form-item label="单据状态" prop="docStatus">
              <el-select v-model="formData.docStatus" disabled placeholder="" clearable>
                <el-option v-for="(item,index) in statusOption" :key="index" :label="item.label"
                           :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span='8'>
            <el-form-item label="最近修改人" prop="lastModifyBy">
              <el-input v-model="formData.updateName" type="text" placeholder="" :disabled="true" clearable>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span='8'>
            <el-form-item label="最近修改时间" prop="lastModifyTime">
              <el-input v-model="formData.updateTime" disabled type="text" placeholder="" clearable/>
<!--              <el-date-picker type="date" v-model="formData.updateTime" format="YYYY-MM-DD HH:mm:ss"-->
<!--                              value-format="YYYY-MM-DD" :disabled="true" clearable></el-date-picker>-->
            </el-form-item>
          </el-col>
        </el-row>
        <!--        </fieldset>-->
      </el-card>
    </el-row>
  </el-form>
</template>

<script setup>
import ProjInfoMApi from '@/project/api/projectInfo/ProjInfoM.js'
import {getCurrentFormattedTime} from "@/common/utils/datetime";
import ProjBizSafeTrainingMApi from '@/project/api/safeTrain/ProjBizSafeTrainingM.js'
import {
  nextTick,
  onMounted,
  ref,
  reactive,
  toRef,
  defineProps,
  defineExpose,
  getCurrentInstance
} from 'vue';
import {
  useRoute,
  useRouter
} from 'vue-router'
import store from "@/store";
import dayjs from "dayjs";
import __classNameVariable__Api
  from "@/project/api/comprehensiveManagement/communication/engineeringindex/ProjBizCommunicationEngineeringM";
import ProjBizSafefeeplanMApi from "@/project/api/safeFeeManage/safeFeePlan/ProjBizSafefeeplanM";
const {
  proxy
} = getCurrentInstance()
//获取项目信息
const showUpload = ref(true)
const route = useRoute();
const router = useRouter();
const projectId = sessionStorage.getItem('projectId')
const props = defineProps({
  data: Object,
});
const formRef = ref()
const type = toRef(props.data?.type);
const fileType = toRef('safeTrain');
let formData = ref({
  id: "",
  trainName: "",
  trainStartTime: "",
  trainEndTime: "",
  trainUnit: "",
  trainPeopleNumber: "",
  projCode: "",
  projName: "",
  projUnit: "",
  code: "",
  trainPeopleCharge: "",
  isExam: "",
  fileId: "",
  projBizTrainPersonSDtoList: [],
  trainContent: "",
  docStatus: "0",
  createTime: getCurrentFormattedTime(),
  updateTime: "",
  createName: store.state.user && store.state.user.userInfo.userName ? store.state.user.userInfo.userName : null,
  updateName: "",
  projectId: sessionStorage.getItem('projectId')
});
let formRules = ref({
  trainName: [{ required: true, message: '请输入培训负责人', trigger: 'change' }],
  trainStartTime: [{ required: true, message: '请输入培训开始时间', trigger: 'change' }],
  trainEndTime: [{ required: true, message: '请输入培训结束时间', trigger: 'change' }],
  trainUnit: [],
  trainPeopleNumber: [],
  projCode: [{ required: true, message: '', trigger: 'change' }],
  projName: [{ required: true, message: '', trigger: 'change' }],
  // projUnit: [{ required: true, message: '', trigger: 'change' }],
  code: [{ required: true, message: '请输入业务编号', trigger: 'change' }],
  trainPeopleCharge: [],
  isExam: [{ required: true, message: '请输入是否需要考试', trigger: 'change' }],
  fileId: [],
  trainContent: [{ required: true, message: '请输入培训要求及内容', trigger: 'change' }]
});
let statusOption = ref([{
  label: "草稿",
  value: "0"
}, {
  label: "已发布",
  value: "1"
}]);
let delRowData = ref({});
let projBizTrainPersonSOption = ref({
  tip: false,
  border: true,
  index: true,
  stripe: true,
  menu: true,
  header: false,
  menuType: "text",
  addBtn: true,
  addBtnText: "新增",
  editBtn: true,
  editBtnText: "编辑",
  delBtn: true,
  delBtnText: "删除",
  cellBtn: true,
  maxHeight: "200px",
  column: [{
    label: "单位名称",
    prop: "unit",
    type: "input",
    columnSlot: true,
    cell: true
  }, {
    label: "姓名",
    prop: "personName",
    type: "input",
    columnSlot: true,
    cell: true
  }, {
    label: "岗位",
    prop: "position",
    type: "input",
    columnSlot: true,
    cell: true
  }, {
    label: "考试成绩",
    prop: "examScore",
    type: "input",
    columnSlot: true,
    cell: true
  }, {
    label: "是否合格",
    prop: "isQualify",
    type: "select",
    columnSlot: true,
    cell: true,
    dicData: [{
      label: "否",
      value: 0
    }, {
      label: "是",
      value: 1
    }]
  }]
});
const childRef = ref(null)
let buttonType = ref();
function fileSerialNumberBuilder() {
  return "safeTrain" + Math.floor(Math.random() * 10000);
}
function getListData() {
  if (childRef.value) {
    const list = childRef.value.getListData();
    formData.value.projBizDmStgMDtoList =[...list]
  }
}
const previewConfig = ref({
  // 是否外置预览,必传
  isExternalPreview: false,
  // 内置预览返回的替换字符,akey代表要替换的字符串，avalue代码替换的字符串
  substitute: {
    "akey": "avalue"
  },
  // 内置预览服务地址
  previewServerUrl: "http://10.191.64.191:8012/onlinePreview",
})

function subRowSave(form, done) {
  //编辑行
  done();
}

async function checkRepeatNumber() {
  const codeValid = formData.value.code;
  if (codeValid === null || codeValid === '') return
  if (type.value === 'add') {
    // 调用后端接口，查询编号是否存在
    try {
      const params = {
        filter: {
          code: codeValid
        },
        page: {
          pageSize: 1,
          pageNum: 1
        }
      };
      const res = await ProjBizSafeTrainingMApi.pageList(params); // 确保等待异步请求完成
      if (res.data.dataList && res.data.dataList.length > 0) {
        // 如果编号已存在，提示用户
        proxy.$message.error("业务编号已存在，请重新输入！");
        formData.value.code = "";
      }
    } catch (error) {
      console.error("校验业务编号时出错：", error);
      proxy.$message.error("校验业务编号时出错，请稍后再试！");
    }
  } else {
    try {
      const viewResult = await ProjBizSafeTrainingMApi.view(formData.value.id); // 确保等待异步请求完成
      const viewData = viewResult.data;
      const existingCode = viewData ? viewData.code : null;
      if (existingCode !== codeValid) {
        const params = {
          filter: {
            code: codeValid
          },
          page: {
            pageSize: 1,
            pageNum: 1
          }
        };
        const res = await ProjBizSafeTrainingMApi.pageList(params); // 确保等待异步请求完成
        if (res.data.dataList && res.data.dataList.length > 0) {
          // 如果编号已存在，提示用户
          proxy.$message.error("业务编号已存在，请重新输入！");
          formData.value.code = "";
        }
      }
    } catch (error) {
      console.error("校验业务编号时出错：", error);
      proxy.$message.error("校验业务编号时出错，请稍后再试！");
    }
  }
}

function onAddData(obj) {

  obj.forEach(item => {
    formData.fileId = item.id + ','
  })

}
onMounted(() => {
  if (type.value === 'show'){
    showUpload.value = false
  }
//获取项目信息
  getProjectInfo();
})
function getProjectInfo() {
  ProjInfoMApi.view(sessionStorage.getItem('projectId')).then((resp) => {
    formData.value.projCode = resp.data.projCode
    formData.value.projName = resp.data.projName
    formData.value.projUnit = resp.data.projOrg === null ? "projOrg" : resp.data.projOrg
  })
}

function subAddRow(name) {
  //新增一行
  if (formData.value[name]) {
    formData.value[name].push({
      $cellEdit: true,
    });
  } else {
    let arr = new Array();
    arr.push({
      $cellEdit: true
    });
    formData.value[name] = arr
  }
}

function subRowUpdate(form, index, done, loading) {
  //编辑行
  done();
}

function subDelRow(row, index, name) {
  //删除行
  if (row[0].id) {
    let data = JSON.parse(JSON.stringify(row[0]));
    if (delRowData.value[name]) {
      delRowData.value[name].push(Object.assign(data, {
        delFlag: 1,
      }));
    } else {
      delRowData.value[name] = [
        Object.assign(data, {
          delFlag: 1,
        }),
      ]
    }
  }
  formData.value[name].splice(index, 1);
}

function getFormData() {
  //获取formData数据
  for (let item in delRowData.value) {
    formData.value[item] = formData.value[item].concat(delRowData.value[item]);
  }
  return formData.value;
}

formData = toRef(Object.keys(props.data?.formData || {}).length > 0 ? {
  ...props.data?.formData
} : toRef(formData.value));

function submitData(buttontype) {
  return new Promise((resolve) => {
    formRef.value.validate((flag) => {
      if (!flag) {
        return false;
      } else {
        buttonType.value = buttontype
        if (type.value === "add") {
          resolve(saveData())
        } else {
          resolve(editData());
        }
      }
    });
  });
}

function saveData() {
  //新增操作
  const formData1 = getFormData();

  if(buttonType.value==='submit'){
    formData1.docStatus = '1'
  }
  // 携带文件信息
  getListData()
  // 如果当前是提交按钮
  if (buttonType.value === 'submit') {
    if (formData.id === null || formData.id === undefined || formData.id === '') {
      return ProjBizSafeTrainingMApi.add(formData1).then((resp) => {
        if (resp.data) {
          proxy.$message.success("发布成功");
          formData.id = resp.data.id;
          if (resp.data.projBizTrainPersonSDtoList){
            formData.projBizTrainPersonSDtoList = resp.data.projBizTrainPersonSDtoList
          }
          return true;
        }
      });
    } else {
      formData1.id = formData.id
      return ProjBizSafeTrainingMApi.update(formData1).then(() => {
        proxy.$message.success("发布成功");
        return true;
      });
    }
  } else {
    if (formData.id === null || formData.id === undefined || formData.id === '') {
      return ProjBizSafeTrainingMApi.add(formData1).then((resp) => {
        if (resp.data) {
          proxy.$message.success("保存成功");
          formData.id = resp.data.id;
          formData.projBizTrainPersonSDtoList = resp.data.projBizTrainPersonSDtoList
          return true;
        }
      });
    } else {
      formData1.id = formData.id
      return ProjBizSafeTrainingMApi.update(formData1).then(() => {
        proxy.$message.success("保存成功");
        return true;
      });
    }
  }
}

function editData() {
  //编辑操作
  const formData1 = getFormData();
  formData1.projBizTrainPersonSDtoList = formData.value.projBizTrainPersonSDtoList
  if(buttonType.value==='submit'){
    formData1.docStatus = '1'
    return ProjBizSafeTrainingMApi.update(formData1).then((res) => {
      formData.value.projBizTrainPersonSDtoList = res.data.projBizTrainPersonSDtoList
      proxy.$message.success("发布成功");
      return true;
    });
  }
  if(buttonType.value==='revoke'){
    formData1.docStatus = '0'
  }
  return ProjBizSafeTrainingMApi.update(formData1).then((res) => {
    formData.value.projBizTrainPersonSDtoList = res.data.projBizTrainPersonSDtoList
    proxy.$message.success("修改成功");
    return true;
  });
}

defineExpose({
  getFormData,
  submitData,
});
</script>

<style lang="scss" scoped>

</style>
