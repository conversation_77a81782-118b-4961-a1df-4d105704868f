<template >
            <!-- <el-col :span='8'> -->
                <el-form-item label="名称" >
       <el-input v-model="inputValue" ></el-input>
                </el-form-item>

       <!-- </el-col> -->
       
</template>

<script setup>
import {
  ref,
  toRef
} from 'vue';
const props = defineProps({
  data: Object,
});

const inputValue = ref('');
console.log("打印props",props.data.inputValue)
inputValue.value = props.data.inputValue;


defineExpose({

 inputValue
})




</script>
