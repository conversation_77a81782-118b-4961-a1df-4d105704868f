// import { request, replaceUrl } from "sn-base-utils";
import {default as request} from '@/project/utils/TestRequest'

export default class EncodingApi {
    static config = {
        add: {
            url: '/project/flow/add',
            method: 'POST'
        },
        update: {
            url: '/project/flow/update',
            method: 'PUT'
        },
        delete: {
            url: '/project/flow/delete',
            method: 'DELETE'
        },
        page: {
            url: '/project/flow/page',
            method: 'POST'
        },
        getById: {
            url: '/project/flow/page',
            method: 'GET'
        }
    };

    /**
     * 新增
     * @param data
     * @returns {*}
     */
    static add(data) {
        return request({
            url: this.config.add.url,
            method: this.config.add.method,
            data: data
        });
    }

    /**
     * 修改
     * @param data
     * @returns {*}
     */
    static update(data) {
        return request({
            url: this.config.update.url,
            method: this.config.update.method,
            data: data
        });
    }

    /**
     * 删除
     * @param data
     * @returns {*}
     */
    static delete(data) {
        return request({
            url: this.config.delete.url,
            method: this.config.delete.method,
            data: data
        });
    }

    /**
     * 分页查询
     * @param data
     * @returns {*}
     */
    static page(data) {
        return request({
            url: this.config.page.url,
            method: this.config.page.method,
            data: data
        });
    }

    /**
     * 根据ID查询
     * @param id
     * @returns {*}
     */
    static getById(id) {
        return request({
            url: `${this.config.getById.url}/${id}`,
            method: this.config.getById.method
        });
    }

}
