// import { request, replaceUrl } from "sn-base-utils";
import {default as request} from '@/project/utils/TestRequest'

export default class EncodingApi {
    static config = {
        page: {
            url: '/project/flow/page',
            method: 'POST'
        },
    };

    /**
     * 新增菜单
     * @param data
     * @returns {*}
     */
    static page(data) {
        return request({
            url: this.config.page.url,
            method: this.config.page.method,
            data: data
        });
    }

}
