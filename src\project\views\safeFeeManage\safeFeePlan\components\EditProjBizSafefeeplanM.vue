<template>
  <el-form :model="formData" :rules="rules" ref="snForm" label-width="100px" label-position="right"
           :disabled="type == 'show'">
    <el-row :gutter="16" :span="24">
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>基本信息
          </legend>
          <el-row :gutter="16" :span="24">
            <el-col :span='8'>
              <el-form-item label="业务编号" prop="code">
                <el-input v-model="formData.code" type="text" placeholder="请输入" @blur="checkRepeatNumber()" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="计划金额" prop="planAmount">
                <el-input v-model="formData.planAmount" type="text" placeholder="请输入" clearable disabled>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="版本" prop="planVersion">
                <el-input v-model="formData.planVersion" type="text" placeholder="请输入" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='12' style="display: none">
              <el-form-item label="项目编码" prop="projCode">
                <el-input v-model="formData.projCode" type="text" placeholder="请输入" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='12' style="display: none">
              <el-form-item label="项目名称" prop="projName">
                <el-input v-model="formData.projName" type="text" placeholder="请输入" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='12'>
              <el-form-item label="项目单位" prop="projUnit" style="display: none">
                <el-input v-model="formData.projUnit" type="text" placeholder="请输入" clearable>
                </el-input>
              </el-form-item>
            </el-col>


            <el-col :span='12' style="display: none">
              <el-form-item label="文件id" prop="fileId">
                <el-input v-model="formData.fileId" type="text" placeholder="请输入" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='12'>
              <el-form-item label="单据状态" prop="docStatus" style="display: none">
                <el-input v-model="formData.docStatus" type="text" placeholder="请输入" clearable>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </fieldset>
      </el-card>
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>安全措施费计划
          </legend>
          <el-row :gutter="16" :span="24">
            <sn-crud :data="formData.projBizSafefeeSDtoList" :option="projBizSafefeeSOption" @row-save="subRowSave"
                     @row-update="subRowUpdate"
                     @row-del="(row, index) => {subDelRow(row, index, 'projBizSafefeeSDtoList');}">
              <template #empty>
                <div>无数据</div>
              </template>
            </sn-crud>
            <el-button @click="subAddRow('projBizSafefeeSDtoList')" type="primary" plain
                       style="display: block; width: 100%; margin-top: 10px">新增一行数据
            </el-button>
          </el-row>
        </fieldset>
      </el-card>


      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>附件
          </legend>
          <el-row :gutter="16" :span="24">
            <project-document-storage-ui-table
                ref="childRef"
                :type="fileType"
                :relevantId="formData.id"
                :isPageSearch="false"
                :isDeleteMinio="false"
                :isHasAi="false"
                @on-add-data="onAddData"
                :file-serial-number-builder="fileSerialNumberBuilder"
                :preview-config="previewConfig"
                :isShowAddBtn="true"
                :isShowDelBtn="true"
                :isShowPreviewBtn="true"
                :isShowDownloadBtn="true"
                :isShowLinkBtn="false"
                :isShowDownloadBatchBtn="true"
            ></project-document-storage-ui-table>
          </el-row>
        </fieldset>
      </el-card>

      <el-card class="box-card" style="width: 100%;">
        <!--        <fieldset class="fieldset2">-->
        <legend>
          <span class="el-button--primary"></span>单据信息
        </legend>
        <el-row :gutter="16" :span="24">
          <el-col :span='8'>
            <el-form-item label="创建者姓名" prop="createName">
              <el-input v-model="formData.createName" type="text" placeholder="" :disabled="true" clearable>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span='8'>
            <el-form-item label="创建时间" prop="createTime">
              <el-input v-model="formData.createTime" disabled type="text" placeholder="" clearable/>
              <!--              <el-date-picker type="date" v-model="formData.createTime" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD"-->
<!--                              :disabled="true" clearable></el-date-picker>-->
            </el-form-item>
          </el-col>
          <el-col :span='8'>
            <el-form-item label="单据状态" prop="docStatus">
              <el-select v-model="formData.docStatus" disabled placeholder="" clearable>
                <el-option v-for="(item,index) in statusOption" :key="index" :label="item.label"
                           :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span='8'>
            <el-form-item label="最近修改人" prop="lastModifyBy">
              <el-input v-model="formData.updateName" type="text" placeholder="" :disabled="true" clearable>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span='8'>
            <el-form-item label="最近修改时间" prop="lastModifyTime">
              <el-input v-model="formData.updateTime" disabled type="text" placeholder="" clearable/>
              <!--              <el-date-picker type="date" v-model="formData.updateTime" format="YYYY-MM-DD HH:mm:ss"-->
<!--                              value-format="YYYY-MM-DD" :disabled="true" clearable></el-date-picker>-->
            </el-form-item>
          </el-col>
        </el-row>
        <!--        </fieldset>-->
      </el-card>
    </el-row>
  </el-form>
</template>

<script setup>
import {getCurrentFormattedTime} from "@/common/utils/datetime";
import store from "@/store";
import ProjInfoMApi from '@/project/api/projectInfo/ProjInfoM.js';
import ProjBizSafefeeplanMApi from '@/project/api/safeFeeManage/safeFeePlan/ProjBizSafefeeplanM.js';
import {
  nextTick,
  onMounted,
  ref,
  reactive,
  toRef,
  defineProps,
  defineExpose,
  getCurrentInstance
} from 'vue';
import {
  useRoute,
  useRouter
} from 'vue-router'
import ProjBizSafeTrainingMApi from "@/project/api/safeTrain/ProjBizSafeTrainingM";
import ProjBizSafefeeInvestMApi from "@/project/api/safeFeeManage/safeFeeInvest/ProjBizSafefeeInvestM";

const {
  proxy
} = getCurrentInstance()
const route = useRoute();
const router = useRouter();
const props = defineProps({
  data: Object,
});
let statusOption = ref([{
  label: "草稿",
  value: "0"
}, {
  label: "已发布",
  value: "1"
}]);
const showUpload = ref(true)
const snForm = ref()
const type = toRef(props.data?.type);
let formData = ref({
  projCode: "",
  projName: "",
  projUnit: "",
  planVersion: "",
  code: "",
  planAmount: 0,
  createName: store.state.user && store.state.user.userInfo.userName ? store.state.user.userInfo.userName : null,
  updateName: "",
  createTime: getCurrentFormattedTime(),
  updateTime: "",
  fileId: "",
  docStatus: "0",
  projectId: sessionStorage.getItem('projectId'),
  projBizSafefeeSDtoList: []
});
let rules = ref({
  projCode: [],
  projName: [],
  projUnit: [],
  code: [{ required: true, message: '请输入业务编号', trigger: 'change' }],
  planAmount: [{ required: true, message: '', trigger: 'change' }],
  planVersion: [{ required: true, message: '请输入版本号', trigger: 'change' }],
  fileId: [],
  docStatus: []
});
const childRef = ref(null)
let buttonFlag = ref();
const fileType = toRef('safeFee');
let delRowData = ref({});
let projBizSafefeeSOption = ref({
  tip: false,
  border: true,
  index: true,
  stripe: true,
  menu: true,
  header: false,
  menuType: "text",
  addBtn: true,
  addBtnText: "新增",
  editBtn: true,
  editBtnText: "编辑",
  delBtn: true,
  delBtnText: "删除",
  cellBtn: true,
  maxHeight: "200px",
  column: [{
    label: "类别",
    prop: "category",
    type: "input",
    columnSlot: true,
    cell: true
  }, {
    label: "主要内容",
    prop: "majorContent",
    type: "input",
    columnSlot: true,
    cell: true
  }, {
    label: "计划投入费用(万元)",
    prop: "planFee",
    type: "input",
    columnSlot: true,
    cell: true
  }]
});

function fileSerialNumberBuilder() {
  return "safeFee" + Math.floor(Math.random() * 10000);
}

function onAddData(obj) {
  obj.forEach(item => {
    formData.value.fileId += item.id + ','
  })
}

const previewConfig = ref({
  // 是否外置预览,必传
  isExternalPreview: false,
  // 内置预览返回的替换字符,akey代表要替换的字符串，avalue代码替换的字符串
  substitute: {
    "akey": "avalue"
  },
  // 内置预览服务地址
  previewServerUrl: "http://10.191.64.191:8012/onlinePreview",
})

function subRowSave(form, done) {
  let tmpData = 0;
  formData.value.projBizSafefeeSDtoList.forEach(item => {
    tmpData += parseInt(item.planFee, 10)
  })
  formData.value.planAmount = tmpData
  // if (form.planFee) {
  //   formData.value.planAmount += parseInt(form.planFee, 10)
  // }
  //编辑行
  done();
}

/**
 *
 获取表格数据 *
 通过组件引⽤调⽤内部⽅法 */
function getListData() {
  if (childRef.value) {
    const list = childRef.value.getListData();
    formData.value.projBizDmStgMDtoList = [...list]
  }
}
function getProjectInfo() {
  ProjInfoMApi.view(sessionStorage.getItem('projectId')).then((resp) => {
    formData.value.projCode = resp.data.projCode
    formData.value.projName = resp.data.projName
    formData.value.projUnit = resp.data.projOrg === null ? "projOrg" : resp.data.projOrg

  })
}
function subAddRow(name) {
  //新增一行
  if (formData.value[name]) {
    formData.value[name].push({
      $cellEdit: true,
    });
  } else {
    let arr = new Array();
    arr.push({
      $cellEdit: true
    });
    formData.value[name] = arr
  }
}

function subRowUpdate(form, index, done, loading) {
  let tmpData = 0;
  formData.value.projBizSafefeeSDtoList.forEach(item => {
    tmpData += parseInt(item.planFee, 10)
  })
  formData.value.planAmount = tmpData
  //编辑行
  done();
}

function subDelRow(row, index, name) {
  //删除行
  if (row[0].id) {
    let data = JSON.parse(JSON.stringify(row[0]));
    if (delRowData.value[name]) {
      delRowData.value[name].push(Object.assign(data, {
        delFlag: 1,
      }));
    } else {
      delRowData.value[name] = [
        Object.assign(data, {
          delFlag: 1,
        }),
      ]
    }
  }
  formData.value[name].splice(index, 1);
}

function getFormData() {
  //获取formData数据
  for (let item in delRowData.value) {
    formData.value[item] = formData.value[item].concat(delRowData.value[item]);
  }
  return formData.value;
}

formData = toRef(Object.keys(props.data?.formData || {}).length > 0 ? {
  ...props.data?.formData
} : toRef(formData.value));

function submitData(buttonType) {
  return new Promise((resolve) => {
    snForm.value.validate((flag) => {
      if (!flag) {
        return false;
      } else {
        buttonFlag.value = buttonType
        console.log(type.value, 888)
        if (type.value === "add") {
          resolve(saveData());
        } else {
          resolve(editData());
        }
      }
    });
  });
}

function saveData() {
  //新增操作
  const formData1 = getFormData();
  if (buttonFlag.value === 'submit') {
    formData1.docStatus = '1'
  }
  // 文件信息
  getListData()
  // 如果当前是提交按钮
  if (buttonFlag.value === 'submit') {
    if (formData.id === null || formData.id === undefined || formData.id === '') {
      return ProjBizSafefeeplanMApi.add(formData1).then((resp) => {
        if (resp.data) {
          proxy.$message.success("发布成功");
          formData.id = resp.data.id;
          formData.projBizSafefeeSDtoList = resp.data.projBizSafefeeSDtoList
          return true;
        }
      });
    } else {
      formData1.id = formData.id
      return ProjBizSafefeeplanMApi.update(formData1).then(() => {
        proxy.$message.success("发布成功");
        return true;
      });
    }
  } else {
    if (formData.id === null || formData.id === undefined || formData.id === '') {
      return ProjBizSafefeeplanMApi.add(formData1).then((resp) => {
        if (resp.data) {
          proxy.$message.success("保存成功");
          formData.id = resp.data.id;
          formData.projBizSafefeeSDtoList = resp.data.projBizSafefeeSDtoList
          return true;
        }
      });
    } else {
      console.log("到这儿了")
      formData1.id = formData.id
      return ProjBizSafefeeplanMApi.update(formData1).then(() => {
        proxy.$message.success("保存成功");
        return true;
      });
    }
  }
}
async function checkRepeatNumber() {
  const codeValid = formData.value.code;
  if (codeValid === null || codeValid === '') return
  if (type.value === 'add') {
    // 调用后端接口，查询编号是否存在
    try {
      const params = {
        filter: {
          code: codeValid
        },
        page: {
          pageSize: 1,
          pageNum: 1
        }
      };
      const res = await ProjBizSafefeeplanMApi.pageList(params); // 确保等待异步请求完成
      if (res.data.dataList && res.data.dataList.length > 0) {
        // 如果编号已存在，提示用户
        proxy.$message.error("业务编号已存在，请重新输入！");
        formData.value.code = "";
      }
    } catch (error) {
      proxy.$message.error("校验业务编号时出错，请稍后再试！");
    }
  } else {
    try {
      const viewResult = await ProjBizSafefeeplanMApi.view(formData.value.id); // 确保等待异步请求完成
      const viewData = viewResult.data;
      const existingCode = viewData ? viewData.code : null;
      if (existingCode !== codeValid) {
        const params = {
          filter: {
            code: codeValid
          },
          page: {
            pageSize: 1,
            pageNum: 1
          }
        };
        const res = await ProjBizSafefeeplanMApi.pageList(params); // 确保等待异步请求完成
        if (res.data.dataList && res.data.dataList.length > 0) {
          // 如果编号已存在，提示用户
          proxy.$message.error("业务编号已存在，请重新输入！");
          formData.value.code = "";
        }
      }
    } catch (error) {
      proxy.$message.error("校验业务编号时出错，请稍后再试！");
    }
  }
}
function editData() {
  //编辑操作
  const formData1 = getFormData();
  if (buttonFlag.value === 'submit') {
    formData1.docStatus = '1'
    return ProjBizSafefeeplanMApi.update(formData1).then((res) => {
      formData.value.projBizSafefeeSDtoList = res.data.projBizSafefeeSDtoList
      proxy.$message.success("发布成功");
      return true;
    });
  }
  if (buttonFlag.value === 'revoke') {
    formData1.docStatus = '0'
  }
  return ProjBizSafefeeplanMApi.update(formData1).then((res) => {
    formData.value.projBizSafefeeSDtoList = res.data.projBizSafefeeSDtoList
    proxy.$message.success("修改成功");
    return true;
  });
}
onMounted(() => {
  if (type.value === 'show'){
    showUpload.value = false
  }
//获取项目信息
  getProjectInfo();

})
defineExpose({
  getFormData,
  submitData,
});
</script>

<style lang="scss" scoped>

</style>
