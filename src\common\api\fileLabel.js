/*
 * @Author: 王云飞
 * @Date: 2025-01-09 15:58:43
 * @LastEditTime: 2025-02-08 14:30:20
 * @LastEditors: 王云飞
 * @Description:
 *
 */
import { request, replaceUrl } from 'sn-base-utils'
export default class FileLabelApi {
  static config = {
    listTree: {
      url: '/file/label/listTree',
      method: 'POST'
    },
    listMenuTree: {
      url: '/file/dir/listDir/{menuId}',
      method: 'GET'
    }
  }
  static listTree() {
    return request({
      ...this.config.listTree,
      data: {}
    })
  }
  static listMenuTree(menuId) {
    return request({
      ...this.config.listMenuTree,
      url: replaceUrl(this.config.listMenuTree.url, { menuId })
    })
  }
}
