<template>
    <div>
  <!-- <el-dialog title="办理" :visible.sync="open" :modal="true" :append-to-body="true" :fullscreen="full"> -->
    <div class="form-single-fragment third-party-dlg" style="position: relative">
      <el-form ref="formTaskCommit" :model="formData" class="full-width-input form-box" style="width: 100%"
        label-width="100px" label-position="top" @submit.native.prevent>
        <el-form-item label="下一环节：" v-if="operation.appointNextAct == 1">
          <el-radio-group v-model="formData.targetTaskKey">
            <el-radio v-for="item in targetTasArr" :key="item.actKey" :label="item.actKey">{{ item.actName }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="选择节点：" v-if="
          operation.type == FlowActionType.REJECTTOTASK ||
          operation.type == FlowActionType.FREE_JUMP
        " :rules="[
          {
            required: true,
            message: '请选择节点',
          },
        ]">
          <el-select v-model="formData.targetTaskKey" filterable>
            <el-option v-for="item in targetTasArr" :key="item.actKey" :label="item.actName"
              :value="item.actKey"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="用户选择：" v-if="operation.appointNextAssigneeType == 1">
          <select-task-user api="getNextUserInfoList" :multiple="true" :params="{
            procDefId: operation.procDefId,
            taskId: operation.taskId,
            targetTaskKey: operation.hugeFlag,
          }" v-model="formData.taskAssignees" />
        </el-form-item>
        <!-- 传阅没有用户选择 传顺说的, -->
        <el-form-item label="用户选择：" v-if="
          (operation.appointNextAssigneeType == 2 ||
            operation.type == FlowActionType.TURN) &&
          (operation.type != FlowActionType.MULITSIGNSUB && operation.type != FlowActionType.MULTISIGNADD)
        ">
          <!-- 如果是转办, 则需要改为流程的岗位/负责人等的比较全的选择弹窗 operation.type == FlowActionType.TURN -->
          <!-- <div v-if="operation.type == FlowActionType.TURN">
            <bpmn-crud @rowDel="(_row, index) =>
              delIdentityConfig(_row, index)
            " @addBtnHandle="editIdentityConfigList"
              @editBtnHandle="(row, index) => editIdentityConfigList(row, index)" :option="identityConfigListOptions"
              :data="identityConfigList">
            </bpmn-crud>
          </div>
          <input-user v-else v-model="formData.taskAssignees"></input-user> -->
          <input-user  v-model="formData.taskAssignees"></input-user>
        </el-form-item>
        <el-form-item label="传阅对象选择:" v-if="operation.type === FlowActionType.COPYTO" :rules="[
          {
            required: true,
            message: '请选择传阅对象',
          },
        ]">
          <input-user v-model="formData.taskAssignees"></input-user>
        </el-form-item>
        <el-form-item :label="operation.type == FlowActionType.MULTISIGNADD ? '加签列表：' : '减签列表：'" v-if="
          operation.type == FlowActionType.MULITSIGNSUB ||
          operation.type == FlowActionType.MULTISIGNADD
        ">
          <input-user ref="multiSignRef" v-model="formData.taskAssignees"
            :disableSelect="operation.type == FlowActionType.MULITSIGNSUB"></input-user>
        </el-form-item>
        <el-form-item label="办理意见：" prop="comment" :rules="transactSuggestionRules">
          <el-input v-model="formData.comment" rows="5" type="textarea" clearable placeholder="请输入办理意见" />
        </el-form-item>
        <el-form-item label="" prop="comment">
          <el-button v-for="item in commentItemList" :key="item" @click="handleClickComment(item)">{{ item }}
          </el-button>
        </el-form-item>
        <slot name="customFields" :formData="formData"></slot>
      </el-form>
    </div>
    <!-- <template #footer>
      <el-button @click="handleClose" size="mini">取 消</el-button>
      <el-button type="primary" @click="preHandlerAction" size="mini">确 定</el-button>
    </template> -->
</div>
  <!-- </el-dialog> -->

</template>
<script>
import{HandelTaskApi as HandelFlowTaskApi }   from "sn-bpm-v3";
import SelectTaskUser from "./SelectTaskUser";
// import BpmnCrud from "@/components/PropertySetting/components/common/BpmnCrud";
// import SelectParticipant from "@/components/PropertySetting/components/BpmnUserTask/components/SelectParticipant.vue";
export default {
  props: {
    operation: {
      default: {}
    },
     full: {
      default:false
     }
  },
  data() {
    return {
      procDefKey: "",
      commentItemList: [],
      targetTasArr: [],
      copyUserIds: "",
      delTaskAssignees: '',
      open: false,
      formData: {
        comment: "",
        targetTaskKey: "",
        taskAssignees: "",
        copyDataList: [],
      },
      transactSuggestionRules: null,
      identityConfigList: [],

      identityConfigListOptions: {
        menuWidth: 180,
        column: [
          {
            label: "参与者类型",
            prop: "identityType",
            dicData: [
              {
                label: "指定人员",
                value: "user",
              },
              {
                label: "指定角色",
                value: "role",
              },
              {
                label: "指定部门",
                value: "org",
              },
              {
                label: "指定负责人",
                value: "leader",
              },
              {
                label: "指定岗位",
                value: "post",
              },
              {
                label: "指定标签",
                value: "label",
              },
              {
                label: "变量获取",
                value: "variable",
              },
              {
                label: "人员脚本",
                value: "script",
              },
            ],
          }
        ],
      }
    };
  },
  components: { SelectTaskUser },
  mounted() {

  },
  watch: {
    operation: {
      handler(newVal) {
        console.log('opreation触发!!!!!!!!',newVal)
        this.procDefKey = newVal.procDefKey;
        if (newVal.appointNextAct == 1) {
          HandelFlowTaskApi.getNextActInfoList({
            procDefId: newVal.procDefId,
            taskKey: newVal.taskKey,
          }).then((res) => {
            this.targetTasArr = res.data;
            if (res.data.length == 1) {
              this.$set(this.formData, "targetTaskKey", res.data[0].actKey);
            }
          });
        }
        if (newVal.type == this.FlowActionType.REJECTTOTASK) {
          HandelFlowTaskApi.getRejectActInfoList({
            taskId: newVal.taskId,
          }).then((res) => {
            this.targetTasArr = res.data;
            if (res.data.length == 1) {
              this.$set(this.formData, "targetTaskKey", res.data[0].actKey);
            }
          });
        }
        if (newVal.type == this.FlowActionType.FREE_JUMP) {
          HandelFlowTaskApi.getAllActInfoList({
            procDefId: newVal.procDefId,
          }).then((res) => {
            this.targetTasArr = res.data;
            if (res.data.length == 1) {
              this.$set(this.formData, "targetTaskKey", res.data[0].actKey);
            }
          });
        }
        this.getCommentItemList();
        if (this.operation.type == this.FlowActionType.MULITSIGNSUB) {
          this.listRuTaskUserByProcInstId()
        }
        console.log('this.operation.approvalOption', this.operation)
        if (this.operation.approvalOption?.transactSuggestionRequired) {
          this.transactSuggestionRules = [
            {
              required: true,
              message: '请填写办理意见',
            }
          ]
        }

      },
      immediate: true,  // 初始化触发
    },
  },
  methods: {
    handleClose(){
        this.$emit('handleClose')
    },
    openModal(status) {
      console.log('openModal',status)
      this.open = status
    },
    preHandlerAction(obj) {
      if (this.operation == null) {
        this.open = false
        return;
      }
      let handleActionCallbackRes = obj
      if (this.operation.type == this.FlowActionType.PRINT) {
        this.$emit('handleActionCallback', handleActionCallbackRes)
        return;
      }
      // 撤销操作不弹出选择窗口
      const showCommitDig =
        this.operation.type !== this.FlowActionType.SAVE &&
        this.operation.type !== this.FlowActionType.SAVESTART &&
        this.operation.type !== this.FlowActionType.SIGN &&
        this.operation.type !== this.FlowActionType.REVOKE &&
        this.operation.type !== this.FlowActionType.RECALL;
      if (showCommitDig) {
        this.$emit('handleActionCallback', handleActionCallbackRes)
      }else{
        this.$emit('handleActionCallback', true)
      }
    },

    delIdentityConfig(item, index) {
      this.identityConfigList.splice(index, 1)
    },
    editIdentityConfigList(row, index) {
      let option = row ? "edit" : "add";

      //参与者配置

      const initData = {
        identityType: "user",
        identityUser: {
          type: 1,
          value: "",
          name: "",
          el: "",
          conditionOrRules: [],
        },
        identityRole: {
          linkOrgType: "",
          roleType: 0,
          el: "",
          conditionOrRules: [],
        },
        identityOrg: {
          el: "",
          conditionOrRules: [],
        },
        identityLeader: {
          el: "",
          searchType: 1,
          conditionOrRules: [],
        },
        identityPost: {
          postType: 0,
          el: "",
          conditionOrRules: [],
        },
        identityLabel: {
          el: "",
          conditionOrRules: [],
        },
        identityScript: {
          el: "",
          conditionOrRules: [],
        },
        identityVariable: {
          el: "",
          conditionOrRules: [],
        },

      };

      for (let i in row) {
        if (!row[i]) {
          delete row[i];
        }
      }

      let data = row ? { ...initData, ...row } : initData;
      // this.$DialogForm.show({
      //   content: SelectParticipant,
      //   customClass: "bpm-dialog",
      //   title: option == "edit" ? "编辑参与者配置" : "新增参与者配置",
      //   width: "800px",
      //   data: {
      //     formData: data,
      //     openInTurn: true
      //   },
      //   callback: (res) => {
      //     if (res.type) {
      //       let value = { identityType: res.dialogRefs.formData.identityType };
      //       if (res.dialogRefs.formData.identityType == "user") {
      //         value = {
      //           ...value,
      //           identityUser: res.dialogRefs.formData.identityUser,
      //         };
      //       }
      //       if (res.dialogRefs.formData.identityType == "role") {
      //         value = {
      //           ...value,
      //           identityRole: res.dialogRefs.formData.identityRole,
      //         };
      //       }
      //       if (res.dialogRefs.formData.identityType == "org") {
      //         value = {
      //           ...value,
      //           identityOrg: res.dialogRefs.formData.identityOrg,
      //         };
      //       }
      //       if (res.dialogRefs.formData.identityType == "leader") {
      //         value = {
      //           ...value,
      //           identityLeader: res.dialogRefs.formData.identityLeader,
      //         };
      //       }
      //       if (res.dialogRefs.formData.identityType == "post") {
      //         value = {
      //           ...value,
      //           identityPost: res.dialogRefs.formData.identityPost,
      //         };
      //       }
      //       if (res.dialogRefs.formData.identityType == "label") {
      //         value = {
      //           ...value,
      //           identityLabel: res.dialogRefs.formData.identityLabel,
      //         };
      //       }
      //       if (res.dialogRefs.formData.identityType == "variable") {
      //         value = {
      //           ...value,
      //           identityVariable: res.dialogRefs.formData.identityVariable,
      //         };
      //       }
      //       if (res.dialogRefs.formData.identityType == "script") {
      //         value = {
      //           ...value,
      //           identityScript: res.dialogRefs.formData.identityScript,
      //         };
      //       }
      //       res.dialogRefs.submitForm().then((r) => {
      //         if (r) {
      //           console.log('提交后参数', value, row, index)
      //           if (!isNaN(index)) {
      //             this.identityConfigList.splice(index, 1, value)
      //           }
      //           //没有index就是添加
      //           else {
      //             this.identityConfigList.push(value)
      //           }
      //           res.close();
      //         }
      //       });
      //     } else {
      //       res.close();
      //     }
      //   },
      // });
    },
    handleClickComment(item) {
      this.formData = {
        ...this.formData,
        comment: item,
      };
    },
    // 获取常用语列表
    getCommentItemList() {
      HandelFlowTaskApi.commentItemList({ procDefKey: this.procDefKey }).then(
        (res) => {
          this.commentItemList = res.data;
        }
      );
    },
    // 获取加签列表
    listRuTaskUserByProcInstId() {
      console.log('listRuTaskUserByProcInstId operation', this.operation)
      HandelFlowTaskApi.listRuTaskUserByProcInstId({
        procInstId: this.operation.procInstId,
        taskId: this.operation.taskId
      }).then(
        (res) => {
          this.$refs.multiSignRef.setList(res.data)
        }
      );
    },

    getFormData() {
      if (!this.formData.taskAssignees &&
        (this.operation.type == this.FlowActionType.MULITSIGNSUB ||
          this.operation.type == this.FlowActionType.MULTISIGNADD)) {
        this.$message({
          type: 'warning',
          message: `${this.operation.type == this.FlowActionType.MULTISIGNADD ? '加' : '减'}签` + '列表至少需要一人'
        });
        return 'warning'
      }
      if (!this.formData.targetTaskKey && (this.operation.type == this.FlowActionType.REJECTTOTASK ||
        this.operation.type == this.FlowActionType.FREE_JUMP)) {
        this.$message({
          type: 'warning',
          message: '请选择节点'
        });
        return 'warning'
      }
      console.log('this.formData:',this.formData, 'this.operation', this.operation, this.operation.approvalOption?.transactSuggestionRequired)
      if (!this.formData.comment && this.operation.approvalOption?.transactSuggestionRequired) {
        this.$message({
          type: 'warning',
          message: '请输入办理意见'
        });
        return 'warning'
      }
      if (!this.formData.taskAssignees && this.operation.type === this.FlowActionType.COPYTO) {
        this.$message({
          type: 'warning',
          message: '请选择传阅对象'
        });
        return 'warning'
      }
      if (this.copyUserIds) {
        this.formData.copyDataList = [
          { copyType: "user", value: this.copyUserIds },
        ];
      }
      let data = {
        ...JSON.parse(JSON.stringify(this.formData)),
      };
      if (this.operation.type == this.FlowActionType.MULITSIGNSUB) {
        data.taskAssignees = this.$refs.multiSignRef.getDelTaskAssignees()
      }
      data.identityConfigList = this.identityConfigList
      return data
    },
  },
};
</script>
<style scoped>
.form-single-fragment ::v-deep .el-button {
  margin-left: 0 !important;
  margin-right: 10px !important;
  margin-bottom: 10px;
  white-space: break-spaces;
  height: auto;
  min-height: 32px;
  line-height: 20px;
  text-align: left
}
</style>
