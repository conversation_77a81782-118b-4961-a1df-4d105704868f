import { request, replaceUrl } from "sn-base-utils";
import { lifecycleBizServiceCode } from '@/config'

export default class ProjBizDesignChangeRequestMApi {
    static config = {
        add: {
            url: '/project/designChangeRequest/add',
            method: 'POST'
        },
        remove: {
            url: '/project/designChangeRequest/delete',
            method: 'DELETE'
        },
        update: {
            url: '/project/designChangeRequest/update',
            method: 'PUT'
        },
        view: {
            url: '/project/designChangeRequest/get/{id}',
            method: 'GET'
        },
        pageList: {
            url: '/project/designChangeRequest/page',
            method: "POST"
        },
        list: {
            url: '/project/designChangeRequest/list',
            method: "POST"
        },
        startFlow: {
            url: '/project/designChangeRequest/saveAndSubmitProc',
            method: "POST"
        },
        submitTask: {
            url: '/project/designChangeRequest/saveAndSubmitTask',
            method: "POST"
        },
        printTemplate: {
            url: `/project/designChangeRequest/printTemplate`,
            method: "POST"
        },
        operateFlow: {
            url: `/project/designChangeRequest/operateFlow`,
            method: "POST"
        }
    };

    /**
     * 新增变更申请单
     * @param data
     * @returns {*}
     */
    static add(data) {
        return request({
            url: this.config.add.url,
            method: this.config.add.method,
            data: data
        });
    }

    /**
     * 删除变更申请单
     * @param data
     * @returns {*}
     */
    static remove(data) {
        return request({
            url: this.config.remove.url,
            method: this.config.remove.method,
            data: data
        });
    }

    /**
     * 更新变更申请单
     * @param data
     * @returns {*}
     */
    static update(data) {
        return request({
            url: this.config.update.url,
            method: this.config.update.method,
            data: data
        });
    }

    /**
     * 更新变更申请单
     * @param data
     * @returns {*}
     */
    static operateFlow(data) {
        return request({
            url: this.config.operateFlow.url,
            method: this.config.operateFlow.method,
            data: data
        });
    }

    /**
     * 查询变更申请单详细
     * @param id
     * @returns {*}
     */
    static view(id) {
        return request({
            url: replaceUrl(this.config.view.url, { id }),
            method: this.config.view.method
        });
    }

    /**
     * 分页查询变更申请单列表
     * @param data
     * @returns {*}
     */
    static pageList(data) {
        return request({
            url: this.config.pageList.url,
            method: this.config.pageList.method,
            data: data
        });
    }


    /**
     * 全部变更申请单列表
     * @returns {*}
     */
    static list(data) {
        return request({
            url: this.config.list.url,
            method: this.config.list.method,
            data: data
        });
    }

    /**
     * 工作流-启动流程
     * @returns {*}
     */
    static startFlow(data) {
        return request({
            url: this.config.startFlow.url,
            method: this.config.startFlow.method,
            data: data
        });
    }

    /**
     * 工作流-完成任务
     * @returns {*}
     */
    static submitTask(data) {
        return request({
            url: this.config.submitTask.url,
            method: this.config.submitTask.method,
            data: data
        });
    }

    /**
     * 工作流-打印模板
     */
    static printTemplate(data) {
        return request({
            url: this.config.printTemplate.url,
            method: this.config.printTemplate.method,
            data: data
        });
    }
}
