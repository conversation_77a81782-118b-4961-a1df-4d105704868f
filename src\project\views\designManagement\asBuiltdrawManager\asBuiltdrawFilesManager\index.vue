<template>
  <div class="contents">
    <input ref="importZip" type="file" @change="handleFileChange" accept=".zip" style="display: none" />
    <div class="centent">
      <el-form :model="formData" :rules="formRules" ref="formRef" label-width="100px" label-position="right">
        <el-row :gutter="16" :span="24">
          <el-card class="box-card" style="width: 100%;">
            <fieldset class="fieldset2">
              <legend>
                <span class="el-button--primary"></span>设计信息
              </legend>
              <el-row :gutter="16" :span="24">

                <el-col :span='8'>

                  <el-form-item label="设计单位" prop="company">
                    <el-input v-model="formData.company" type="text" placeholder="请输入" :disabled="true">
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span='8'>
                  <el-form-item label="设计负责人" prop="liabilityPerson">
                    <el-input v-model="formData.liabilityPerson" type="text" placeholder="请输入" :disabled="true">
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span='8'>
                  <el-form-item label="设计版本" prop="designVersion">
                    <el-input v-model="formData.designVersion" type="text" placeholder="请输入" :disabled="true">
                    </el-input>
                    <!-- <el-date-picker v-model="formData.designVersion" type="date" placeholder="请选择设计版本" /> -->
                  </el-form-item>
                </el-col>
              </el-row>
            </fieldset>
          </el-card>
          <el-card class="box-card" style="width: 100%; min-height: 400px;">
            <fieldset class="fieldset2">
              <legend>
                <span class="el-button--primary"></span>施工图信息
              </legend>
              <el-row :gutter="16" :span="24">
                <sn-crud :data="formData.projBizDesignAsBuiltdrawOutPlanSDtoList" v-model:page="queryForm.page"
                  @search-change="onChangeSearch" @search-reset="onResetSearch" v-model:search="queryForm.filter"
                  :option="projBizDesignAsBuiltdrawOutPlanSOption" style="width: 100%;">

                  <template #menuLeft="{ size }">
                    <el-button type="primary" :size="size" @click="downloadTemplate"><sn-icon icon="el-icon-download"
                        class="mr-6"></sn-icon>下载模板</el-button>
                    <el-button type="primary" :size="size" @click="uploadData"><sn-icon icon="el-icon-upload2"
                        class="mr-6"></sn-icon>导入</el-button>
                  </template>

                  <template #menu="{ row, index, size }">
                    <el-button type="primary" :size="size" icon="el-icon-edit" link
                      @click="editDetail(row)">编辑附件</el-button>
                  </template>
                  <template #empty>
                    <div>无数据</div>
                  </template>
                </sn-crud>
              </el-row>
            </fieldset>
          </el-card>
          <el-card class="box-card" style="width: 100%;">
            <fieldset class="fieldset2">
              <legend>
                <span class="el-button--primary"></span>单据信息
              </legend>
              <el-row :gutter="16" :span="24">
                <el-col :span='8'>
                  <el-form-item label="创建人" prop="createName">
                    <el-input v-model="formData.createName" :disabled="true" type="text" placeholder="" clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span='8'>
                  <el-form-item label="创建时间" prop="createTime">
                    <el-input v-model="formData.createTime" :disabled="true" type="text" placeholder="" clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span='8'>
                  <el-form-item label="最近修改人" prop="updateName">
                    <el-input v-model="formData.updateName" :disabled="true" type="text" placeholder="" clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span='8'>
                  <el-form-item label="最近修改时间" prop="updateTime">
                    <el-input v-model="formData.updateTime" :disabled="true" type="text" placeholder="">
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span='8'>
                  <el-form-item label="审批状态" prop="procStatus">
                    <el-select v-model="formData.procStatus" :disabled="true">
                      <el-option v-for="(item, index) in global_biz_flow_status" :key="index" :label="item.label"
                        :value="item.value"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </fieldset>
          </el-card>
        </el-row>
      </el-form>
    </div>
    <div ref="myRef"></div>
  </div>



</template>

<script>

export const routerConfig = [{
  menuType: "C",
  menuName: "竣工图文件管理",
}];
</script>

<script setup>
import ProjBizDesignAsBuiltdrawOutPlanMApi from '@/project/api/designManagement/asBuiltdrawManager/ProjBizDesignAsBuiltdrawOutPlanM.js'
import { useDicts } from '@/common/hooks/useDicts'
import { nextTick, onMounted, ref, reactive, toRef, defineProps, defineExpose, getCurrentInstance } from 'vue';


import EditFileView from '@/project/views/designManagement/asBuiltdrawManager/asBuiltdrawFilesManager/components/EditFileView.vue'


const { designManagement_major, global_biz_flow_status } = useDicts(["designManagement_major", "global_biz_flow_status"]);

const importZip = ref();

const { proxy } = getCurrentInstance()


const myRef = ref();
const queryForm = ref({
  page: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  filter: {
    major: "",
    volumeNum: "",
    volumeName: ""
  }
})



const formRef = ref()

let formData = ref({
  id: "",
  procInstanceId: "",
  procStatus: "",
  projectId: "",
  delFlag: "",
  version: "",
  createBy: "",
  createName: "",
  createTime: "",
  updateBy: "",
  updateName: "",
  updateTime: "",
  company: "",
  liabilityPerson: "",
  designVersion: "",
  projBizDesignAsBuiltdrawOutPlanSDtoList: []
});

let formRules = ref({
  company: [{
    required: true,
    message: "请输入设计单位"
  }],
  liabilityPerson: [{
    required: true,
    message: "请输入设计负责人"
  }],
  designVersion: [{
    required: true,
    message: "请输入版本"
  }]
});
let projBizDesignAsBuiltdrawOutPlanSOption = ref({
  tip: false,
  border: true,
  index: true,
  stripe: true,
  menu: true,
  menuWidth: '180px',
  menuHeaderAlign: "center",
  menuAlign: "center",
  header: true,
  height: 'auto',
  searchSpan: 6,
  dialogType: "page",
  searchIcon: true,
  searchMenuSpan: 6,
  searchIndex: 3,
  selection: false,
  menuType: "text",
  addBtn: false,
  addBtnText: "新增",
  editBtn: false,
  editBtnText: "编辑",
  delBtn: false,
  delBtnText: "删除",
  delBtns: false,
  cellBtn: false,
  maxHeight: "200px",
  column: [{
    label: "专业",
    prop: "major",
    align: "center",
    width: "150px",
    type: "select",
    search: true,
    dicData: designManagement_major,
    headerAlign: "center"
  }, {
    label: "卷册号",
    prop: "volumeNum",
    align: "center",
    width: "150px",
    type: "input",
    search: true,
    headerAlign: "center"
  }, {
    label: "卷册名称",
    prop: "volumeName",
    type: "input",
    search: true,
    headerAlign: "center"
  }, {
    label: "计划出图时间",
    width: "180px",
    align: "center",
    prop: "planOutDate",
    type: "date",
    headerAlign: "center"
  }, {
    label: "实际出图时间",
    width: "180px",
    align: "center",
    prop: "actualOutDate",
    type: "date",
    headerAlign: "center"
  }]
});



//获取业务数据
function getDataView() {
  ProjBizDesignAsBuiltdrawOutPlanMApi.getOne().then(resp => {
    if (!!resp.data) {
      resp.data["projBizDesignAsBuiltdrawOutPlanSDtoList"] = [];
      formData.value = resp.data;
      getDetailpage();
    }
  });

}

function onResetSearch() {
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getDetailpage();
}

//搜索按钮操作
function onChangeSearch(params, done) {
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getDetailpage();
  done && done();
}



// 处理参数
function handleQueryForm() {
  const { pageSize, pageNum } = queryForm.value.page;
  const filter = {}
  for (let key in queryForm.value.filter) {
    if (queryForm.value.filter[key]?.length) {
      // sn-curd搜索select会生成$prop的变量
      if (key.startsWith("$")) continue;
      filter[key] = queryForm.value.filter[key];
    }
  }
  const searchParams = {
    page: {
      pageSize,
      pageNum,
    },
    filter,
  }
  return searchParams
}



//获取进度计划详细
function getDetailpage() {
  console.log(queryForm);
  const searchParams=handleQueryForm();
  ProjBizDesignAsBuiltdrawOutPlanMApi.detailPageList(searchParams).then(resp => {
    const detailData = resp.data.dataList;
    detailData.forEach(item => {
      item["projectId"] = item.projectId ? item.projectId : sessionStorage.getItem('projectId')
    })
    queryForm.value.page.total = resp.data.totalCount;
    formData.value.projBizDesignAsBuiltdrawOutPlanSDtoList = detailData;
  });
}



//编辑进度计划
function editDetail(row) {

  proxy.$DialogForm.show({
    title: "竣工图出图文件",
    type: projBizDesignAsBuiltdrawOutPlanSOption.value.dialogType,
    el: myRef.value,
    width: "80%",
    content: EditFileView,
    data: {
      formData: row,
    },
    option: {
      submitBtn: false,
      emptyBtn: false,
      submitText: '保存',
      emptyText: '关闭',
    },
    callback: (res) => { },
  });


}



function getFormData() {
  const projectId = sessionStorage.getItem('projectId');
  return formData.value = { ...formData.value, projectId: formData.value.projectId ? formData.value.projectId : projectId };
}





//上传出图计划触发
function uploadData() {
  importZip.value.click()

}

//导入竣工图出图计划
function handleFileChange(event) {
  const file = event.target.files[0];
  if (file) {
    const formDatas = new FormData()
    formDatas.append('file', file)
    formDatas.append('mastId', formData.value.id)
    formDatas.append('projectId', sessionStorage.getItem('projectId'))
    importZip.value.value = null;
    ProjBizDesignAsBuiltdrawOutPlanMApi.importDetailFile(formDatas).then((resp) => {
      getDetailpage();
      proxy.$message.success('导入成功');
    })
  }

}



//下载竣工图出图计划模板
function downloadTemplate() {
  proxy.download("/project/workdrawManager/exportDetailFileTemplate", { ...queryForm.value.filter }, "施工图文件.zip");
}




defineExpose({
  getFormData,
});
onMounted(() => {
  nextTick(() => {
    //获取信息
    getDataView();
  })
})
</script>

<style lang="scss" scoped>
.contents {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: calc(100vh - 88px);
  overflow: hidden;
}

.top {
  height: 80px;
  width: 100%;
  flex: 0 1 auto;
  padding: 0 15px 0px 0px;
  box-sizing: border-box;
}

.centent {
  height: 100px;
  width: 100%;
  flex: auto;
  overflow-y: auto;
  overflow-x: hidden;
}
</style>