import { request, replaceUrl } from "sn-base-utils";

export default class ProjBizWaterConservationApi {
    static config = {
        add: {
            url: '/project/waterConservation/add',
            method: 'POST'
        },
        remove: {
            url: '/project/waterConservation/delete',
            method: 'DELETE'
        },
        update: {
            url: '/project/waterConservation/update',
            method: 'PUT'
        },
        view: {
            url: '/project/waterConservation/get/{id}',
            method: 'GET'
        },
        pageList: {
            url: '/project/waterConservation/page',
            method: "POST"
        },
        list: {
            url: '/project/waterConservation/list',
            method: "POST"
        }
    };

    /**
     * 新增环保水保
     * @param data
     * @returns {*}
     */
    static add(data) {
        return request({
            url: this.config.add.url,
            method: this.config.add.method,
            data: data
        });
    }

    /**
     * 删除环保水保
     * @param data
     * @returns {*}
     */
    static remove(data) {
        return request({
            url: this.config.remove.url,
            method: this.config.remove.method,
            data: data
        });
    }

    /**
     * 更新环保水保
     * @param data
     * @returns {*}
     */
    static update(data) {
        return request({
            url: this.config.update.url,
            method: this.config.update.method,
            data: data
        });
    }

    /**
     * 查询环保水保详细
     * @param id
     * @returns {*}
     */
    static view(id) {
        return request({
            url: replaceUrl(this.config.view.url, { id }),
            method: this.config.view.method
        });
    }

    /**
     * 分页查询环保水保列表
     * @param data
     * @returns {*}
     */
    static pageList(data) {
        return request({
            url: this.config.pageList.url,
            method: this.config.pageList.method,
            data: data
        });
    }

    /**
     * 全部环保水保列表
     * @returns {*}
     */
    static list(data) {
        return request({
            url: this.config.list.url,
            method: this.config.list.method,
            data: data
        });
    }
}
