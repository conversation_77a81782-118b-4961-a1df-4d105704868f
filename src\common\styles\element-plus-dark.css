html.dark {
  color-scheme: dark;
  --ep-color-primary: #00A7DB;
  --ep-color-primary-light-3: rgb(102.2, 177.4, 255);
  /* --ep-color-primary-light-5: rgb(42, 89, 137.5); */
  --ep-color-primary-light-5: rgb(102.2, 177.4, 255);
  --ep-color-primary-light-7: rgb(33.2, 61.4, 90.5);
  --ep-color-primary-light-8: rgb(28.8, 47.6, 67);
  --ep-color-primary-light-9: rgb(24.4, 33.8, 43.5);
  --ep-color-primary-dark-2: rgb(102.2, 177.4, 255);
  --ep-color-success: #67c23a;
  --ep-color-success-light-3: rgb(78.1, 141.8, 46.6);
  --ep-color-success-light-5: rgb(61.5, 107, 39);
  --ep-color-success-light-7: rgb(44.9, 72.2, 31.4);
  --ep-color-success-light-8: rgb(36.6, 54.8, 27.6);
  --ep-color-success-light-9: rgb(28.3, 37.4, 23.8);
  --ep-color-success-dark-2: rgb(133.4, 206.2, 97.4);
  --ep-color-warning: #e6a23c;
  --ep-color-warning-light-3: rgb(167, 119.4, 48);
  --ep-color-warning-light-5: #7d5b28;
  --ep-color-warning-light-7: rgb(83, 62.6, 32);
  --ep-color-warning-light-8: rgb(62, 48.4, 28);
  --ep-color-warning-light-9: rgb(41, 34.2, 24);
  --ep-color-warning-dark-2: rgb(235, 180.6, 99);
  --ep-color-danger: #f56c6c;
  --ep-color-danger-light-3: rgb(177.5, 81.6, 81.6);
  /* --ep-color-danger-light-5: rgb(132.5, 64, 64); */
  --ep-color-danger-light-5: rgb(247, 137.4, 137.4);
  --ep-color-danger-light-7: rgb(87.5, 46.4, 46.4);
  --ep-color-danger-light-8: rgb(65, 37.6, 37.6);
  --ep-color-danger-light-9: rgb(42.5, 28.8, 28.8);
  --ep-color-danger-dark-2: rgb(247, 137.4, 137.4);
  --ep-color-error: #f56c6c;
  --ep-color-error-light-3: rgb(177.5, 81.6, 81.6);
  --ep-color-error-light-5: rgb(132.5, 64, 64);
  --ep-color-error-light-7: rgb(87.5, 46.4, 46.4);
  --ep-color-error-light-8: rgb(65, 37.6, 37.6);
  --ep-color-error-light-9: rgb(42.5, 28.8, 28.8);
  --ep-color-error-dark-2: rgb(247, 137.4, 137.4);
  --ep-color-info: #909399;
  --ep-color-info-light-3: rgb(106.8, 108.9, 113.1);
  --ep-color-info-light-5: rgb(82, 83.5, 86.5);
  --ep-color-info-light-7: rgb(57.2, 58.1, 59.9);
  --ep-color-info-light-8: rgb(44.8, 45.4, 46.6);
  --ep-color-info-light-9: rgb(32.4, 32.7, 33.3);
  --ep-color-info-dark-2: rgb(166.2, 168.6, 173.4);
  --ep-box-shadow: 0px 12px 32px 4px rgba(0, 0, 0, 0.36), 0px 8px 20px rgba(0, 0, 0, 0.72);
  --ep-box-shadow-light: 0px 0px 12px rgba(0, 0, 0, 0.72);
  --ep-box-shadow-lighter: 0px 0px 6px rgba(0, 0, 0, 0.72);
  --ep-box-shadow-dark:
    0px 16px 48px 16px rgba(0, 0, 0, 0.72), 0px 12px 32px #000000, 0px 8px 16px -8px #000000;
  --ep-bg-color-page: #01333d;
  --ep-bg-color: #01333d;
  --ep-bg-color-overlay: #01333d;
  --ep-text-color-primary: #e5eaf3;
  --ep-text-color-regular: #cfd3dc;
  --ep-text-color-secondary: #a3a6ad;
  --ep-text-color-placeholder: #8d9095;
  --ep-text-color-disabled: #6c6e72;
  --ep-border-color-darker: #636466;
  --ep-border-color-dark: #58585b;
  --ep-border-color: rgba(0, 167, 219, 0.7);
  --ep-border-color-light: transparent;
  --ep-border-color-lighter: transparent;
  --ep-border-color-extra-light: #2b2b2c;
  --ep-fill-color-darker: #424243;
  --ep-fill-color-dark: #39393a;
  --ep-fill-color: #303030;
  --ep-fill-color-light: #01333d;
  --ep-fill-color-lighter: #1d1d1d;
  --ep-fill-color-extra-light: #191919;
  --ep-fill-color-blank: transparent;
  --ep-mask-color: rgba(0, 0, 0, 0.8);
  --ep-mask-color-extra-light: rgba(0, 0, 0, 0.3);
  --ep-disabled-bg-color: #01333d;
  --ep-fill-color-blank: #01333d;
  --ep-menu-bg-color: #212029;
  --ep-nav-bg-color: #212029;
  --ep-color-white: #ffffff;
  --ep-color-black: #000000;
}
html.dark .ep-switch {
  --ep-switch-on-color: var(--ep-color-primary);
  --ep-switch-off-color: rgba(0, 0, 0, 0.3);
}
html.dark .ep-menu {
  --ep-menu-active-color: var(--ep-color-primary) !important;
}
html.dark .ep-button {
  --ep-button-disabled-text-color: rgba(255, 255, 255, 0.5);
}
html.dark .ep-card {
  --ep-card-bg-color: var(--ep-bg-color-overlay);
}
html.dark .ep-empty {
  --ep-empty-fill-color-0: var(--ep-color-black);
  --ep-empty-fill-color-1: #686a6c;
  --ep-empty-fill-color-2: #585a5c;
  --ep-empty-fill-color-3: #484a4d;
  --ep-empty-fill-color-4: #3d3f42;
  --ep-empty-fill-color-5: #353638;
  --ep-empty-fill-color-6: #2a2c2f;
  --ep-empty-fill-color-7: #26282b;
  --ep-empty-fill-color-8: #212326;
  --ep-empty-fill-color-9: #1c1e22;
  --ep-text-color-secondary: #ffffff;
}
html.dark .ep-collapse {
  --ep-collapse-content-bg-color: transparent;
}
html.dark .ep-table {
  --ep-table-row-hover-bg-color: rgba(0, 167, 219, 0.4);
}
html.dark .ep-input {
  --ep-input-border-color: rgba(0, 167, 219, 0.7);
  --ep-input-focus-border-color: rgba(0, 167, 219, 1);
}
html.dark .ep-checkbox {
  --ep-checkbox-disabled-input-fill: var(--ep-bg-color);
}
html.dark .sp-group .ep-collapse, .sp-group .ep-collapse-item__wrap {
  border-color: transparent;
}
