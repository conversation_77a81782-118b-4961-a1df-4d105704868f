.ep-dialog__wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}

.ep-avatar {
  background: transparent !important;
}

.ep-form {
  display: block;
  width: 100%;
}
.ep-drawer__body {
  background: #fff !important;
}
.ep-button.is-circle .icon {
  margin-right: 0;
}
.ep-menu--horizontal .ep-menu--popup .nest-menu .ep-menu-item span {
  color: #7c7878 !important;
}
.ep-menu--horizontal .ep-menu--popup .nest-menu .ep-sub-menu span {
  color: #7c7878 !important;
}
.ep-drawer__title {
  font-size: 14px;
  // color: #000
}
.ep-dialog__body {
  max-height: calc(80vh - 96px) !important;
}
.ep-message-box__message {
  font-size: 14px;
}
.ep-tree-node__label {
  font-size: 14px;
}
.custom-tree-node > span {
  font-size: 14px;
}
.ep-dropdown-menu__item {
  list-style: none;
  line-height: 36px;
  padding: 0 20px;
  margin: 0;
  font-size: 14px;
  cursor: pointer;
  outline: none;
  height: 36px;
  padding-top: 3px;
  padding-bottom: 3px;
  box-sizing: content-box;
}

.ep-card__header {
  padding: 18px 20px;
  border-bottom: 1px solid #ebeef5;
  box-sizing: border-box;
}
.ep-menu-item {
  height: 48px;
  line-height: 48px;
  font-size: 14px;
  padding: 0 16px;
  list-style: none;
  cursor: pointer;
  position: relative;
  transition:
    border-color 0.3s,
    background-color 0.3s,
    color 0.3s;
  box-sizing: border-box;
  white-space: nowrap;
}

.ep-sub-menu__title {
  height: 48px;
  line-height: 48px;
  font-size: 14px;
  padding: 0 16px;
  list-style: none;
  cursor: pointer;
  position: relative;
  transition:
    border-color 0.3s,
    background-color 0.3s,
    color 0.3s;
  box-sizing: border-box;
  white-space: nowrap;
  .ep-sub-menu__icon-arrow {
    font-size: 14px;
  }
}

.mr-6 {
  margin-right: 6px !important;
}
.ep-date-table {
  td .ep-date-table-cell {
    height: 30px;
    padding: 3px 0;
    box-sizing: border-box;
    .ep-date-table-cell__text {
      width: 24px;
      height: 24px;
      display: block;
      margin: 0 auto;
      line-height: 24px;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      border-radius: 50%;
    }
  }
}
.ep-date-table td.in-range .ep-date-table-cell {
  background-color: var(--ep-datepicker-inrange-bg-color);
}

.ep-date-table td.in-range .ep-date-table-cell {
  background-color: var(--ep-datepicker-inrange-bg-color);
}
.ep-date-table td.start-date .ep-date-table-cell__text,
.ep-date-table td.end-date .ep-date-table-cell__text {
  background-color: var(--ep-datepicker-active-color);
}

.ep-date-table td.start-date .ep-date-table-cell,
.ep-date-table td.end-date .ep-date-table-cell {
  color: #ffffff;
}
.ep-upload-list__item-preview {
  display: none !important;
}
.ep-upload-list--picture-card .ep-upload-list__item-actions span + span {
  margin-left: 0;
}
