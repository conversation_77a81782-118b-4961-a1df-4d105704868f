<template>
  <div class="contents">
    <div class="top" v-if="isShow">
      <el-card style="width: 100%;">
        <el-button v-if="!formData.procInstanceId" type="primary" @click="saveOrUpdate">保存</el-button>
        <el-button v-if="formData.id" type="primary" @click="deleteData">删除</el-button>
        <el-button v-if="!formData.procInstanceId" type="primary" @click="submitFlow">发起</el-button>
        <el-button v-if="formData.procInstanceId && formData.procStatus == '7'" type="primary"
          @click="submitFlowTask">提交</el-button>
        <el-button v-if="formData.procInstanceId && formData.procStatus == '2'" type="primary"
          @click="recallTask">撤回</el-button>
      </el-card>

    </div>
    <div class="centent">
      <flow-page-container ref="flowPageContainerRef" @handlerAction="handlerAction" @initData="initData"
        @handlerPrint="handlerPrint" :closeBtn="isShowCloseBtn" :approvalOption="approvalOption"
        @approvalOptionCallback="getApprovalOption">
        <el-form :model="formData" :rules="formRules" ref="formRef" label-width="160px" label-position="right">
          <el-row :gutter="16" :span="24">
            <el-card class="box-card" style="width: 100%;">
              <fieldset class="fieldset2">
                <legend>
                  <span class="el-button--primary"></span>基本信息
                </legend>
                <el-row :gutter="16" :span="24">
                  <el-col :span='8'>
                    <el-form-item label="项目编号" prop="">
                      <el-input v-model="projectData.projCode" :disabled="true" type="text" placeholder="" clearable>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span='8'>
                    <el-form-item label="项目名称" prop="">
                      <el-input v-model="projectData.projName" :disabled="true" type="text" placeholder="" clearable>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span='8'>
                    <el-form-item label="项目单位" prop="">
                      <el-input v-model="projectData.projOrgName" :disabled="true" type="text" placeholder="" clearable>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span='8'>
                    <el-form-item label="项目建设规模" prop="">
                      <el-input v-model="projectData.projScale" :disabled="true" type="text" placeholder="" clearable>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span='16'>
                    <el-form-item label="地理位置" prop="">
                      <el-input v-model="position" :disabled="true" type="text" placeholder="" clearable>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <!-- <el-col :span='8'>
                  <el-form-item label="厂址地形选择" prop="">
                    <el-input v-model="projectData.input23077" :disabled="true" type="text" placeholder="" clearable>
                    </el-input>
                  </el-form-item>
                </el-col> -->
                  <el-col :span='8'>
                    <el-form-item label="初设编制单位" prop="company">
                      <el-input v-model="formData.company" :disabled="formData.procStatus!='7' && formData.procStatus!='1'" type="text"
                        placeholder="请输入可研编制单位" clearable>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span='8'>
                    <el-form-item label="初设概算（万元）" prop="compute">
                      <el-input-number v-model="formData.compute" :disabled="formData.procStatus!='7' && formData.procStatus!='1'" :min="0"
                        :max="999999999999" style="width: 100%;" />
                    </el-form-item>
                  </el-col>
                  <el-col :span='8'>
                    <el-form-item label="单位千瓦造价(元/kW)" prop="price">
                      <el-input-number v-model="formData.price" :disabled="formData.procStatus!='7' && formData.procStatus!='1'" :min="0" :max="999999999999"
                        style="width: 100%;" />
                    </el-form-item>
                  </el-col>
                  <el-col :span='24'>
                    <el-form-item label="备注" prop="">
                      <el-input type="textarea" v-model="formData.remarks" :disabled="formData.procStatus!='7' && formData.procStatus!='1'"
                        placeholder="请输入备注" :rows="3" clearable></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
              </fieldset>
            </el-card>

            <el-card class="box-card" style="width: 100%;">
              <fieldset class="fieldset2">
                <legend>
                  <span class="el-button--primary"></span>初设报告
                </legend>
                <project-document-storage-ui-table ref="reportFileRef" type="reportFile" :relevantId="formData.id"
                  :isPageSearch="false" :isDeleteMinio="isDeleteMinio" :isHasAi="isHasAi" @on-add-data="onAddData"
                  @on-ai-review="onAiReview" @on-preview="onPreview" @on-delete="onDelete"
                  :file-serial-number-builder="fileSerialNumberBuilder" :preview-config="previewConfig"
                                                   :isShowLinkBtn="false"
                  :isShowAddBtn="isShowAddBtn" :isShowDelBtn="isShowDelBtn" :isShowPreviewBtn="isShowPreviewBtn"
                  :isShowDownloadBtn="isShowDownloadBtn"></project-document-storage-ui-table>



              </fieldset>
            </el-card>

            <el-card class="box-card" style="width: 100%;">
              <fieldset class="fieldset2">
                <legend>
                  <span class="el-button--primary"></span>初设概算表
                </legend>
                <project-document-storage-ui-table ref="financialEstimatesFileRef" type="financialEstimatesFile"
                  :relevantId="formData.id" :isPageSearch="false" :isDeleteMinio="isDeleteMinio" :isHasAi="isHasAi"
                  @on-add-data="onAddData" @on-ai-review="onAiReview" @on-preview="onPreview" @on-delete="onDelete"
                  :file-serial-number-builder="fileSerialNumberBuilder" :preview-config="previewConfig"
                                                   :isShowLinkBtn="false"
                  :isShowAddBtn="isShowAddBtn" :isShowDelBtn="isShowDelBtn" :isShowPreviewBtn="isShowPreviewBtn"
                  :isShowDownloadBtn="isShowDownloadBtn"></project-document-storage-ui-table>
              </fieldset>
            </el-card>


            <el-card class="box-card" style="width: 100%;">
              <fieldset class="fieldset2">
                <legend>
                  <span class="el-button--primary"></span>外部条件
                </legend>
                <project-document-storage-ui-table ref="outConditionFileRef" type="outConditionFile"
                  :relevantId="formData.id" :isPageSearch="false" :isDeleteMinio="isDeleteMinio" :isHasAi="isHasAi"
                  @on-add-data="onAddData" @on-ai-review="onAiReview" @on-preview="onPreview" @on-delete="onDelete"
                  :file-serial-number-builder="fileSerialNumberBuilder" :preview-config="previewConfig"
                                                   :isShowLinkBtn="false"
                  :isShowAddBtn="isShowAddBtn" :isShowDelBtn="isShowDelBtn" :isShowPreviewBtn="isShowPreviewBtn"
                  :isShowDownloadBtn="isShowDownloadBtn"></project-document-storage-ui-table>
              </fieldset>
            </el-card>


            <el-card class="box-card" style="width: 100%;">
              <fieldset class="fieldset2">
                <legend>
                  <span class="el-button--primary"></span>审查结果
                </legend>
                <el-col :span='24'>
                  <el-form-item label="项目单位审核意见" prop="">
                    <el-input type="textarea" v-model="formData.projectCompanyAdvice" placeholder="请输入项目单位审核意见"
                      :rows="3" clearable></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span='8'>
                  <el-form-item label="项目单位审核日期" prop="">
                    <el-date-picker v-model="formData.projectCompanyDate" type="date" />
                  </el-form-item>
                </el-col>
                <el-col :span='24'>
                  <el-form-item label="项目单位审核附件" prop="price">

                    <project-document-storage-ui-table ref="verifyFileRef" type="verifyFile" :relevantId="formData.id"
                      :isPageSearch="false" :isDeleteMinio="isDeleteMinio" :isHasAi="isHasAi" @on-add-data="onAddData"
                      @on-ai-review="onAiReview" @on-preview="onPreview" @on-delete="onDelete"
                      :file-serial-number-builder="fileSerialNumberBuilder" :preview-config="previewConfig"
                                                       :isShowLinkBtn="false"
                      :isShowAddBtn="isShowAddBtn" :isShowDelBtn="isShowDelBtn" :isShowPreviewBtn="isShowPreviewBtn"
                      :isShowDownloadBtn="isShowDownloadBtn"></project-document-storage-ui-table>

                  </el-form-item>
                </el-col>
              </fieldset>
            </el-card>
            <el-card class="box-card" style="width: 100%;">
              <fieldset class="fieldset2">
                <legend>
                  <span class="el-button--primary"></span>单据信息
                </legend>
                <el-row :gutter="16" :span="24">
                  <el-col :span='8'>
                    <el-form-item label="创建人" prop="createName">
                      <el-input v-model="formData.createName" :disabled="true" type="text" placeholder="" clearable>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span='8'>
                    <el-form-item label="创建时间" prop="createTime">
                      <el-input v-model="formData.createTime" :disabled="true" type="text" placeholder="" clearable>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span='8'>
                    <el-form-item label="最近修改人" prop="updateName">
                      <el-input v-model="formData.updateName" :disabled="true" type="text" placeholder="" clearable>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span='8'>
                    <el-form-item label="最近修改时间" prop="updateTime">
                      <el-input v-model="formData.updateTime" :disabled="true" type="text" placeholder="">
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span='8'>
                    <el-form-item label="审批状态" prop="procStatus">
                      <el-select v-model="formData.procStatus" :disabled="true">
                        <el-option v-for="(item, index) in global_biz_flow_status" :key="index" :label="item.label"
                          :value="item.value"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
              </fieldset>
            </el-card>
          </el-row>
        </el-form>
      </flow-page-container>
    </div>
  </div>

</template>

<script>

export const routerConfig = [{
  menuType: "C",
  menuName: "初设审查",
}];
</script>


<script setup>
import ProjInfoMApi from '@/project/api/projectInfo/ProjInfoM.js'
import ProjBizDesignFirstReviewMApi from '@/project/api/designManagement/firstDesignManager/ProjBizDesignFirstReviewM.js'
import FlowPageContainer from '@/common/components/exportFile/FlowPageContainer.vue'
import BpmTaskApi from "@/project/api/bpm/bpmTask.js";
import { useDicts } from '@/common/hooks/useDicts'

import {
  nextTick,
  onMounted,
  ref,
  reactive,
  toRef,
  defineProps,
  defineExpose,
  getCurrentInstance
} from 'vue';
import {
  useRoute,
  useRouter
} from 'vue-router'
import store from "@/store";
import {getCurrentFormattedTime} from "@/common/utils/datetime";
import AreaInfApi from "@/project/api/areaInfo/AreaInfo";

const { global_biz_flow_status } = useDicts(["global_biz_flow_status"]);

let auth = new Map();


const {
  proxy
} = getCurrentInstance()
const route = useRoute();
const router = useRouter();
const props = defineProps({
  data: Object,
});
const formRef = ref()
const position = ref("")
const isShowCloseBtn = ref(false);
const flowPageContainerRef = ref();
const FlowActionType = ref(proxy.FlowActionType);
let approvalOption = props.data?.approvalOption ? props.data?.approvalOption : route.query?.procInstId ? ref({
  isShowApprovalList: true,
  isShowFlowDiagram: true,
  procInstId: route.query?.procInstId
}) : ref({
  isShowApprovalList: false,
  isShowFlowDiagram: false,
  procInstId: ''
});
const state = reactive({
  flowData: {
    businessKey: "",
    procInstId: "",
    procDefKey: "",
    taskId: "",
    formConfig: [],
    variableList: [],
    customPropertyConfigList: []
  },
  formAuth: {},
  taskFlag: 1,
  firstAct: false,
  handlerClose: null,
  rootFields: {},
  fullFieldNameMap: {},
  formAuthList: {}
})
const type = toRef(props.data?.type);
let formData = ref({
  company: "",
  compute: 0,
  price: 0,
  remarks: "",
  projectId: "",
  projectCompanyAdvice: "",
  projectCompanyDate: "",
  createName: store.state.user && store.state.user.userInfo.userName ? store.state.user.userInfo.userName : null,
  createTime: getCurrentFormattedTime(),
  projectCompanyFiles: ""
});
let formRules = ref({
  company: [{
    required: true,
    message: "请输入初设编制单位"
  }],
  compute: [{
    required: true,
    message: "初设概算（万元）"
  }],
  price: [{
    required: true,
    message: "请输入单位千瓦造价(元/kW)"
  }],
  remarks: [],
  projectId: [],
  projectCompanyAdvice: [],
  projectCompanyDate: [],
  projectCompanyFiles: []
});
formData = toRef(Object.keys(props.data?.formData || {}).length > 0 ? {
  ...props.data?.formData
} : toRef(formData.value));


const reportFileRef = ref(null);
const outConditionFileRef = ref(null);
const financialEstimatesFileRef = ref(null);
const verifyFileRef = ref(null);



const projectData = ref({
  projCode: "",
  projName: "",
  projOrgName: "",
  projScale: ""
});
const isShow = ref(false);

//country  province  city projCode projName vbukr projScale



function getDataView() {
  ProjBizDesignFirstReviewMApi.getOne().then((resp) => {
    if (!resp.data) {
      formData.value = { projectId: sessionStorage.getItem('projectId'),
        createName: store.state.user && store.state.user.userInfo.userName ? store.state.user.userInfo.userName : null,
        createTime: getCurrentFormattedTime(),
        procStatus: '1'};

    } else {
      formData.value = resp.data;
    }
    if (!!formData.value.procInstanceId) {
      approvalOption.value = {
        isShowApprovalList: true,
        isShowFlowDiagram: true,
        procInstId: formData.value.procInstanceId

      }
      flowPageContainerRef.value.approvalOptionFn(approvalOption.value)
      type.value = "view";
    }
    getProjectInfo(formData.value.projectId);

  });
}

function getProjectInfo(projectId) {
  ProjInfoMApi.view(projectId).then((resp) => {
    if (!!resp.data) {
      console.log(resp.data,"12314")
      projectData.value = resp.data;
      let countryName = null
      let provinceName = null
      let cityName = null
      if (resp.data.country) {
        AreaInfApi.selectChildren({id:resp.data.country}).then(res => {
          if (!!res.data && res.data[0].fullName) {
            countryName = res.data[0].fullName;
          }
          if (resp.data.province) {
            AreaInfApi.selectChildren({parentId:resp.data.country,id:resp.data.province}).then(res => {
              if (!!res.data && res.data[0].fullName) {
                provinceName = res.data[0].fullName;
              }
              if (resp.data.city) {
                AreaInfApi.selectChildren({parentId: resp.data.province,id:resp.data.city}).then(res => {
                  console.log(res.data,"6")
                  if (!!res.data && res.data[0].fullName) {
                    cityName = res.data[0].fullName;
                  }
                  position.value = (countryName ? countryName : '--') + '/' + (provinceName ? provinceName : '--') + '/' + (cityName ? cityName : '--');
                })
              } else {
                position.value = (countryName ? countryName : '--') + '/' + (provinceName ? provinceName : '--') + '/' + (cityName ? cityName : '--');
              }
            })
          }else {
            position.value = (countryName ? countryName : '--') + '/' + (provinceName ? provinceName : '--') + '/' + (cityName ? cityName : '--');
          }
        })
      }else {
        position.value = (countryName ? countryName : '--') + '/' + (provinceName ? provinceName : '--') + '/' + (cityName ? cityName : '--');
      }
    }
  })
}







function getFormData() {
  return {
    ...formData.value,
    projectId: formData.value.projectId ? formData.value.projectId : sessionStorage.getItem('projectId'),
    reportFile: getListData(reportFileRef, "reportFile"),
    outConditionFile: getListData(outConditionFileRef, "outConditionFile"),
    financialEstimatesFile: getListData(financialEstimatesFileRef, "financialEstimatesFile"),
    verifyFile: getListData(verifyFileRef, "verifyFile")
  }
};


function saveOrUpdate() {
  //新增操作
  const formData = getFormData();
  ProjBizDesignFirstReviewMApi.add(formData).then(() => {
    proxy.$message.success("操作成功");
    getDataView();
  });
}

function deleteData() {
  proxy.$confirm('确定删除当前记录?', '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
      ProjBizDesignFirstReviewMApi.remove([formData.value.id]).then(resp => {
        proxy.$modal.msgSuccess("操作成功");
        getDataView();
      });
    })
    .catch(() => { })

}


function recallTask() {
  proxy.$confirm('确定撤回当前单据?', '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
      ProjBizDesignFirstReviewMApi.recallTask({ id: formData.value.id }).then(resp => {
        proxy.$modal.msgSuccess("操作成功");
        getDataView();
      });
    })
    .catch(() => { })

}





function submitFlow() {

  proxy.$confirm('确定发起当前单据?', '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
      const formDatas = getFormData();
      ProjBizDesignFirstReviewMApi.startFlow({
        busiData: formDatas,
        startProcDto: {}
      }).then((res) => {
        const taskInfo = res.data[0]
        state.flowData.procDefKey = res.data[0].procDefKey
        state.flowData.procInstId = res.data[0].procInstId
        state.flowData.businessKey = res.data[0].businessKey
        state.flowData.taskId = res.data[0].taskId
        handlerOpenConfirm(taskInfo)
        proxy.$message.success("操作成功");
        getDataView();
      });
    })
    .catch(() => { })
  //新增操作

}

//打开确认框
function handlerOpenConfirm(taskInfo) {
  ProjBizDesignFirstReviewMApi.view(taskInfo.businessKey).then((resp) => {
    if (resp.data) {
      formData.value = resp.data;
      // isShowAddBtn.value = true;
      // isShowDelBtn.value = true;
    }
  })
  flowPageContainerRef.value.handlerActionSubmit(taskInfo,1);
}



function submitFlowTask() {
  proxy.$confirm('确定提交当前单据?', '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
      const formDatas = getFormData();

      BpmTaskApi.listRuTaskByProcInstId({
        procInstId: formDatas.procInstanceId
      }).then((resp) => {
        if (Array.isArray(resp.data) && resp.data.length > 0) {
          ProjBizDesignFirstReviewMApi.submitTask({
            busiDto: formDatas,
            taskActionDto: {
              taskId: resp.data[0].taskId,
              procInstId: formDatas.procInstanceId,
              actionType: "agree",
            }
          }).then(() => {
            proxy.$message.success("操作成功");
            getDataView();
          });
        } else {
          proxy.$message.success("提交失败，未获取到当前任务id");
        }
      })
    })
    .catch(() => { })

  //新增操作

}






const previewConfig = ref({
  // 是否外置预览,必传
  isExternalPreview: false,
  // 内置预览返回的替换字符,akey代表要替换的字符串，avalue代码替换的字符串
  substitute: {
    "akey": "avalue"
  },
  // 内置预览服务地址
  previewServerUrl: "http://127.0.0.1:8012/onlinePreview",
})
const isDeleteMinio = ref(true)
const isHasAi = ref(true)
const isShowAddBtn = ref(true)
const isShowDelBtn = ref(true)
const isShowPreviewBtn = ref(true)
const isShowDownloadBtn = ref(true)

function onAddData(list) {
}

function onAiReview(row) {
}

function onPreview(row) {
}

function onDelete(list) {
}

function getListData(dom, type) {
  if (dom.value) {
    let list = dom.value.getListData();
    list.forEach(item => item.type = type);
    return list;
  }
}

// 文件序列号生成函数
function fileSerialNumberBuilder() {
  return "TEST" + Math.floor(Math.random() * 10000)
}







function processAuthFieldForm(fieldList) {
  /**
   * 工作流表单权限功能：在扫描完成之前，业务可对表单字段进行修改。
   */
  //TODO 这里可以对权限表单的字段进行任意的修改,如果不满足业务需求，可以在这里手动添加你想要的字段
  return fieldList
};

function getApprovalOption(fun) {
  /**
   * 工作流表单权限功能(重要)：应对弹窗无法及时相应的问题，特别增加该函数修复该bug
   */
  nextTick(() => {

    fun(approvalOption.value)
  })
};

function customFormPermi(obj) {
  /**
   * 工作流表单权限功能：自定义表单权限处理函数，将对应的对象的属性还原成对应的权限值
   */
  let {
    field,
    fieldModelId
  } = obj
  let fullFieldName = `${fieldModelId}_${field}`
  //TODO 这里可以自定义处理，默认返回0即可
  return auth.get(fullFieldName) || 0
};

function handlerPrint(taskComment) {
  /**
   * 工作流打印模板功能
   */
  ProjBizDesignFirstReviewMApi.printTemplate({
    id: formData.value.id,
    templateId: taskComment.templateId,
    formData: formData.value
  }).then(
    (res) => {
      router.push({
        name: "PrintDoc",
        query: {
          fileId: res.data,
        },
      });
      taskComment.close && taskComment.close();
    });
};

function getSubmitTask(taskActionDto) {
  /**
   * 工作流提交任务功能
   */
  return ProjBizDesignFirstReviewMApi.submitTask({
    busiDto: formData.value,
    taskActionDto: taskActionDto
  });
};

function getStartFlow(formData, startProcDto) {
  /**
   * 工作流启动流程功能
   */
  ProjBizDesignFirstReviewMApi.startFlow({
    busiData: formData,
    startProcDto: startProcDto
  }).then((res) => {
    state.flowData.procDefKey = res.data(0).procDefKey
    state.flowData.procInstId = res.data(0).procInstId
    state.flowData.businessKey = res.data(0).businessKey
    state.flowData.taskId = res.data(0).taskId
    return res;
  });
};

function handlerAction(operation, taskComment, handlerClose) {
  /**
   * 工作流操作功能
   */
  // 启动并保存草稿后再次提交
  if (
    (operation.type == FlowActionType.value.SAVESTART || operation.type == FlowActionType.value.SAVE || operation.type == FlowActionType.value.START) && !formData.value.taskId) {
    let startProcDto = {
      procDefKey: formData.value.procDefKey,
      businessKey: null,
      clientId: null,
      ...taskComment?.dialogRefs?.getFormData(),
    };
    let httpCall = null;
    if (operation.type == FlowActionType.value.SAVESTART) {
      httpCall = getStartFlow(formData, startProcDto);
    } else if (operation.type == FlowActionType.value.SAVE) {
      httpCall = getSaveFormData(formData);
    }
    httpCall.then(() => {
      proxy.$modal.msgSuccess("提交成功");
      taskComment.close && taskComment.close();
      handlerClose();
    });
  } else {
    operation.type == FlowActionType.value.START || operation.type == FlowActionType.value.SAVESTART ? (operation.type = FlowActionType.value.AGREE) : operation.type;
    let taskActionDto = {
      taskId: state.flowData.taskId,
      procInstId: state.flowData.procInstId,
      actionType: operation.type,
      ...taskComment?.dialogRefs?.getFormData(),
      taskAssignees: taskComment?.dialogRefs?.getFormData().taskAssignees?.length ? taskComment.dialogRefs.getFormData().taskAssignees.join(',') : '',
    };
    getSubmitTask(taskActionDto).then(() => {
      proxy.$modal.msgSuccess("任务办理成功");
      taskComment.close && taskComment.close();
      handlerClose();
    }).catch(() => {
      taskComment.close && taskComment.close();
    });
  }
};

function initBusiForm() {
  /**
   * 工作流初始化表单功能
   */
  let that = proxy
  let routerQueryParams = route.query;
  auth = new Map(Object.entries(state.formAuth));
  if (routerQueryParams) {
    if (routerQueryParams.busiData) {
      formData.value = routerQueryParams.busiData;
    } else {

      if (routerQueryParams.businessKey) {
        let businessKey = route.query.businessKey;
        ProjBizDesignFirstReviewMApi.view(businessKey).then(resp => {
          formData.value = resp.data;
          getProjectInfo(formData.value.projectId)
        });
      } else {
        that.$message.error("初始化失败,因为工作流未将流程信息传入！")
      }
    }
  }
};

function getSaveFormData(formData) {
  if (formData.id) {
    return ProjBizDesignFirstReviewMApi.update(formData);
  } else {
    return ProjBizDesignFirstReviewMApi.add(formData);
  }
};

function getFieldForm() {
  let rootFields = {
    ...state.rootFields
  }
  let fieldList = []
  let keys = []
  for (let key in rootFields) {
    keys.push(key)
    fieldList.push(rootFields[key])
  }
  const result = processAuthFieldForm ? processAuthFieldForm(fieldList) : fieldList;
  const message = {
    type: "getTemplateRoot",
    pathname: window.location.pathname,
    actKey: getQueryParams("actKey"),
    content: result
  };
  window.parent.postMessage(JSON.parse(JSON.stringify(message)), "*");
};

function getQueryParams(key) {
  let url = window.location.href;
  // 使用正则表达式解析URL中的查询字符串
  var queryString = url.split('?')[1];
  if (!queryString) {
    return {};
  }
  var params = {};
  // 分割查询字符串成单个参数
  var vars = queryString.split("&");
  for (var i = 0; i < vars.length; i++) {
    var pair = vars[i].split("=");
    params[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);
  }
  return params[key];
};

function initData(urlParams, taskInfo, handlerClose) {



  // 如果用户从流程设计点击启动, 如果是在线表单, 则阻止执行initData, 防止在线表单报错
  if (urlParams.menuId && urlParams.openInStart) {
    return
  }
  state.flowData.procDefKey = taskInfo.procDefKey;
  state.flowData.procInstId = urlParams.procInstId;
  state.flowData.businessKey = urlParams.businessKey;
  state.flowData.taskId = urlParams.taskId;
  state.flowData.fiedPermission = taskInfo.fiedPermission;
  state.flowData.variableList = taskInfo.variableList;
  state.taskFlag = urlParams.taskFlag;
  state.firstAct = taskInfo.firstAct;
  state.handlerClose = handlerClose;
  state.flowData.customPropertyConfigList = taskInfo.customPropertyConfigList
  let fieldPerList = taskInfo.formConfig?.formFieldConfig?.fieldPerList || []
  handleFormAuth(fieldPerList);
  initBusiForm();
};

function handleFormAuth(data) {
  let formAuth = {};
  for (let item of data) {
    let permi = 1;
    if (item.readonly) {
      permi = 2;
    }
    if (item.hidden) {
      permi = 3;
    }
    if (!isCamelCase(item.fieldModelId)) {
      item.fieldModelId = toCamelCase(item.fieldModelId)
    }
    formAuth = {
      ...formAuth,
      [`${item.fieldModelId}_${item.field}`]: permi,
    };
  }
  state.formAuth = formAuth;
  state.formAuthList = JSON.parse(JSON.stringify(formAuth))
};
/**
 * 处理表单权限相关操作, 开发者在template的v-permi设置中必须使用此函数
 * @param {Object} obj - 包含字段名称的对象
 * @param {Function} [callback] - 可选的回调函数，接收 obj 作为参数
 * 如果传入对象的 fieldName 不在 rootFields 中，则将其添加到 rootFields 数组，然后执行回调函数（如果存在）
 */
function formPermi(obj, callback) {
  if (!state.rootFields[obj.fieldModelId + '__' + obj.field]) {
    state.rootFields[obj.fieldModelId + '__' + obj.field] = obj
  }
  return callback && callback(obj)
};

function toCamelCase(s) {
  return s.toLowerCase().replace(/_(.)/g, function (match, group1) {
    return group1.toUpperCase();
  });
};

function isCamelCase(str) {
  return /^[a-z][a-zA-Z0-9]*$/.test(str)
};
defineExpose({
  getFormData,
  getSaveFormData,
  getStartFlow,
});
onMounted(() => {
  nextTick(() => {
    if (!route.query?.procInstId) {
      isShow.value = true;
      //获取信息
      getDataView();
    }
    getFieldForm()
  })
})
</script>


<style>
.lf-graph {
  height: 100vh;
}
</style>


<style lang="scss" scoped>
.contents {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: calc(100vh - 88px);
  overflow: hidden;
}

.top {
  height: 80px;
  width: 100%;
  flex: 0 1 auto;
  padding: 0 15px 0px 0px;
  box-sizing: border-box;
}

.centent {
  height: 100px;
  width: 100%;
  flex: auto;
  overflow-y: auto;
  overflow-x: hidden;
}
</style>