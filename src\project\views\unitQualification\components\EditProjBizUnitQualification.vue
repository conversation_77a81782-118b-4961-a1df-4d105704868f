<template>
  <flow-page-container ref="flowPageContainerRef" @handlerAction="handlerAction" @initData="initData"
    @handlerPrint="handlerPrint" :closeBtn="isShowCloseBtn" :approvalOption="approvalOption"
    @approvalOptionCallback="getApprovalOption">

    <el-form :model="formData" :rules="formRules" ref="formRef" label-width="100px" label-position="right"
      :disabled="type == 'view'">
      <el-row :gutter="16" :span="24">
        <el-card class="box-card" style="width: 100%;">
          <fieldset class="fieldset2">
            <legend>
              <span class="el-button--primary"></span>单位资质报审
            </legend>
            <el-row :gutter="16" :span="24">
              <el-col :span='8'>
                <el-form-item label="业务编码" prop="code">
                  <el-input v-model="formData.code" type="text" placeholder="请输入" clearable>
                  </el-input>
                </el-form-item>
              </el-col>

              <el-col :span='8'>
                <el-form-item label="单位类型" prop="unitType">
                  <el-select v-model="formData.unitType" clearable placeholder="请选择">
                    <el-option v-for="(item, index) in org_type" :key="index" :label="item.label"
                      :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span='8'>
                <el-form-item label="承包商名称" prop="contractorName">
                  <!-- <el-select v-model="formData.contractorName" clearable filterable placeholder="请选择承包商"
                    @change="updateLabel">
                    <el-option v-for="(item, index) in supplierList" :key="index" :label="item.label"
                      :value="item.value"></el-option>
                  </el-select> -->
                  <el-input v-model="formData.contractorName" type="text" placeholder="请选择承包商"
                    @click="handleClickSupplier" clearable>
                  </el-input>
                </el-form-item>
              </el-col>
              <!-- <el-col :span='8'>
              <el-form-item label="上级单位" prop="superUnit">
                <el-input v-model="formData.superUnit" type="text" placeholder="请输入" clearable>
                </el-input>
              </el-form-item>
            </el-col> -->
            </el-row>
          </fieldset>
        </el-card>
        <el-card class="box-card" style="width: 100%;">
          <legend>
            <span class="el-button--primary"></span>部门设置
          </legend>
          <!-- show-checkbox  -->
          <el-tree style="max-width: 600px" :data="dataSource" node-key="id" default-expand-all
            :expand-on-click-node="false">
            <template #default="{ node, data }">
              <div class="custom-tree-node">
                <span>{{ node.label }}</span>
                <div>
                  <!-- <el-button type="primary" link @click="append(data)">
                    新增
                  </el-button>
                  <el-button style="margin-left: 4px" type="danger" link @click="remove(node, data)">
                    删除
                  </el-button> -->
                  <el-button type="primary" link @click="editNode(data, 'ADD')">
                    新增
                  </el-button>
                  <el-button type="primary" v-if="node.level > 1" link @click="editNode(data, 'UPDATE')">
                    编辑
                  </el-button>
                  <el-button v-if="node.level > 1" style="margin-left: 4px" type="danger" link
                    @click="remove(node, data)">
                    删除
                  </el-button>
                </div>
              </div>
            </template>
          </el-tree>
          <!-- <el-dialog v-model="dialogVisibleTree" title="编辑节点">
            <template #content>
              <el-form :model="tempNode" ref="dialogFormRef">
                <el-form-item label="节点名称" prop="label">
                  <el-input v-model="tempNode.label" placeholder="请输入节点名称"></el-input>
                </el-form-item>
              </el-form>
            </template>
            <template #footer>
              <el-button @click="dialogVisibleTree = false">取消</el-button>
              <el-button type="primary" @click="saveNode">确定</el-button>
            </template>
          </el-dialog> -->
          <!-- <div class="mt-4 p-3 bg-gray-50 rounded">
            <span class="font-medium">当前承包商：</span>
            <span class="ml-2 text-primary">{{ formData.contractorName || '未设置' }}</span>
          </div> -->
        </el-card>
        <el-card class="box-card" style="width: 100%;">
          <fieldset class="fieldset2">
            <legend>
              <span class="el-button--primary"></span>附件信息
            </legend>
            <el-row :gutter="16" :span="24">
              <!-- <sn-crud :data="formData.projBizQualificationFileDtoList" :option="projBizQualificationFileOption"
                @row-save="subRowSave" @row-update="subRowUpdate"
                @row-del="(row, index) => { subDelRow(row, index, 'projBizQualificationFileDtoList'); }">
                <template #menu="{ row, index }">
                  <el-button type="text" @click="yulan(row, index)">预览</el-button>
                  <el-button type="primary" :size="size" icon="el-icon-download" link
                    @click="onDownData(row)">下载</el-button>
                </template>
                <template #empty>
                  <div>无数据</div>
                </template>
              </sn-crud>
              <el-button @click="handleAdd()" type="primary" plain
                style="display: block; width: 100%; margin-top: 10px">上传文件</el-button> -->
            </el-row>
            <fieldset class="fieldset2">
              <!-- <legend>
            <span class="el-button--primary"></span>附件
          </legend> -->
              <el-row :gutter="16" :span="24"></el-row>
              <project-document-storage-ui-table ref="childRef" :type="fileType" :relevantId="formData.id"
                :isPageSearch="false" :isDeleteMinio="false" :isHasAi="false" @on-add-data="onAddData"
                :file-serial-number-builder="fileSerialNumberBuilder" :preview-config="previewConfig"
                :isShowAddBtn="true" :isShowDelBtn="true" :isShowPreviewBtn="true" :isShowDownloadBtn="true"
                :isShowLinkBtn="false" :isShowDownloadBatchBtn="true">
              </project-document-storage-ui-table>
            </fieldset>
          </fieldset>
        </el-card>
        <!-- <el-card class="box-card" style="width: 100%;">
          <fieldset class="fieldset2">
            <legend>
              <span class="el-button--primary"></span>卡片标题
            </legend>
            <el-row :gutter="16" :span="24">
            </el-row>
          </fieldset>
        </el-card> -->
        <el-col :span='8'>
          <el-form-item label="创建人" prop="createName">
            <el-input v-model="formData.createName" :disabled="true" type="text" placeholder="请输入" clearable>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span='8'>
          <el-form-item label="创建时间" prop="createTime">
            <el-input v-model="formData.createTime" type="text" placeholder="" :disabled="true" clearable>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span='8'>
          <el-form-item label="审批状态" prop="state">
            <el-select v-model="formData.state" :disabled="true" clearable placeholder="请选择">
              <el-option v-for="(item, index) in global_biz_flow_status" :disabled="true" :key="index"
                :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span='8'>
          <el-form-item label="最近修改人" prop="updateName">
            <el-input v-model="formData.updateName" :readonly="true" :disabled="true" type="text" placeholder="请输入"
              clearable>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span='8'>
          <el-form-item label="最近修改时间" prop="updateTime">
            <el-input v-model="formData.updateTime" type="text" placeholder="" :disabled="true" clearable>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </flow-page-container>
  <div ref="myRef"></div>
</template>

<script setup>
import ProjBizUnitQualificationApi from '@/project/api/unitQualification/ProjBizUnitQualification.js'
import TreeNameChange from './TreeNameChange.vue'
import PageSupplier from './pageSupplier.vue'
import FileAddForm from './FileAddForm.vue'
import store from "@/store";
import { btnHandle } from '@/project/components/hooks/buttonChangeName'
import BpmTaskApi from '@/project/api/bpm/bpmTask'
import { getCurrentFormattedTime } from "@/common/utils/datetime";
import FlowPageContainer from '@/common/components/exportFile/FlowPageContainer.vue'
import { dayjs } from 'element-plus';
const res = ref()
import { ElButton } from 'element-plus'
import ProjInfoMApi from '@/project/api/projectInfo/ProjInfoM.js'
let auth = new Map();
const supplierList = ref([]);
const myRef = ref(null);
import {
  useDicts
} from "@/common/hooks/useDicts";
import {
  nextTick,
  onMounted,
  watch,
  ref,
  reactive,
  toRef,
  defineProps,
  defineExpose,
  getCurrentInstance
} from 'vue';
import {
  useRoute,
  useRouter
} from 'vue-router'
const childRef = ref(null);
const taskInfo = ref()
import ProjBizConsPlanReportApi from '@/project/api/constructionManagement/planReport/ProjBizConsPlanReport';
const {
  proxy
} = getCurrentInstance()
const route = useRoute();
const router = useRouter();
const props = defineProps({
  data: Object,
});
const flowPageContainerRef = ref();

let id = 1000
const fileType = ref('unitQualification');
const dialogVisibleTree = ref(false);
const dialogFormRef = ref(null);
const tempNode = ref({ label: '' }); // 临时存储节点数据
let currentNode = ref(null); // 当前操作的节点引用

const append = (data) => {
  const newChild = { id: id++, label: 'testtest', children: [] }
  if (!data.children) {
    data.children = []
  }
  data.children.push(newChild)
  dataSource.value = [...dataSource.value]
}

// const remove = (node, data) => {
//   const parent = node.parent
//   const children = parent?.data.children || parent?.data
//   const index = children.findIndex((d) => d.id === data.id)
//   children.splice(index, 1)
//   dataSource.value = [...dataSource.value]
// }
const editNode = (data, type) => {
  currentNode.value = data;

  const title = ref();
  if (type == "ADD") {
    // 根节点：新增子节点
    tempNode.value = { label: '新节点' };
    title.value = "新增部门名称"
  } else {
    // 子节点：编辑当前节点
    tempNode.value = { ...data };
    title.value = "修改部门名称"
  }

  // dialogVisibleTree.value = true;

  let inputData = currentNode.value.label
  if (type == "ADD") {
    inputData = ''
  }
  proxy.$DialogForm.show({
    content: TreeNameChange,
    data: {
      inputValue: inputData
    },
    option: {
      submitBtn: true,
      emptyBtn: true,
      submitText: "保存"
    },
    title: title.value,
    // type: "this.type",
    width: "50%",
    height: "20%",
    callback: (res) => {
      const inputValue = res.dialogRefs.inputValue.value

      if (res.type && res.type !== 'close') {

        // if (currentNode.value.id === "root") {
        // 根节点：执行新增
        if (!currentNode.value.children) {
          currentNode.value.children = [];
        }
        if (type == "ADD") {
          currentNode.value.children.push({
            id: '',
            contractorName: inputValue,
            label: inputValue,
            children: []
          });
        } else {
          currentNode.value.label = inputValue
          currentNode.value.contractorName = inputValue
        }

        // } else {
        //   // 子节点：执行编辑
        //   currentNode.value.label = inputValue;
        // }

        dataSource.value = [...dataSource.value]; // 触发更新
        dialogVisibleTree.value = false; // 关闭对话框

      }
      res.close()
    }
  })
};

// 保存节点
// const saveNode = () => {
//   dialogFormRef.value.validate((valid) => {
//     if (!valid) return;

//     if (currentNode.value.id === "root") {
//       // 根节点：执行新增
//       if (!currentNode.value.children) {
//         currentNode.value.children = [];
//       }
//       currentNode.value.children.push({
//         id: id++,
//         label: tempNode.value.label,
//         children: []
//       });
//     } else {
//       // 子节点：执行编辑
//       currentNode.value.label = tempNode.value.label;
//     }

//     dataSource.value = [...dataSource.value]; // 触发更新
//     dialogVisibleTree.value = false; // 关闭对话框
//   });
// };

// 删除节点
const updateLabel = (selectedValue) => {
  const selectedItem = supplierList.value.find(item => item.value === selectedValue);

  formData.value.contractorName = selectedItem.label
  formData.value.contractorId = selectedItem.value
  formData.value.label = selectedItem.label
}
const handleClickSupplier = (event) => {

  proxy.$DialogForm.show({
    title: "承包商信息",
    // el: myRef.value,
    type: "dialog",
    width: "80%",
    content: PageSupplier,
    data: {},
    option: {
      submitBtn: false,
      emptyBtn: false,
      submitText: '保存',
      emptyText: '取消',
      extendButton: [
        {
          key: 'close',
          text: '关闭',
          icon: 'el-icon-close',
          buttonType: 'primary',
        },
        {
          key: 'save',
          text: '保存',
          buttonType: 'primary',
          icon: 'el-icon-check',
        }
      ],
    },
    callback: (res) => {
      if (res.type !== 'close') {

        if (res.dialogRefs.list.value.length !== 1) {
          proxy.$message.warning("请选择有且只有一个供应商");
        } else {
          //赋值
          formData.value.contractorName = res.dialogRefs.list.value[0].orgFullName
          formData.value.contractorId = res.dialogRefs.list.value[0].id
          res.close();
        }
      } else {
        res.close();
      }


    }
  });

};
const remove = (node, data) => {
  if (data.id === "root") {
    proxy.$message.warning("根节点不能删除");
    return;
  }


  proxy.$modal.confirm("确认删除数据项？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {

    if (data.id) {
      let removeData = ref([]);
      removeData.value.push(data.id);
      ProjBizUnitQualificationApi.remove(removeData.value).then((res) => {
        proxy.$message.success("已删除");
        getPageList();
      });
    } else {

    }

    const parent = node.parent;
    const children = parent?.data.children || parent?.data;
    const index = children.findIndex((d) => d.id === data.id);
    children.splice(index, 1);
    dataSource.value = [...dataSource.value];
  }).catch(() => { });




};

function getTaskInfo() {
  return taskInfo.value
}

function submitFlowTask(resp) {
  res.value = resp;
  return proxy.$confirm('确定提交当前单据?', '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
      const formDatas = getFormData();
      return BpmTaskApi.listRuTaskByProcInstId({
        procInstId: formDatas.processInstanceId
      }).then((params) => {
        flowPageContainerRef.value.handlerActionSubmit(params.data[0], 1);
      })
    }).catch(() => {
      return true;
    })
}
function initializer() {

  if (type.value === 'add') {


    formData.value.createName = store.state.user && store.state.user.userInfo.userName ? store.state.user.userInfo.userName : null;//获取到登陆人
    formData.value.createTime = getCurrentFormattedTime();//获取到登录时间
    // formData.value.state = 0
  }
  //判断是否为查看进来，是则禁用输入框及按钮
  // console.log('getPageList.type==',type.value)
  // if(type.value==='show'){
  //   dialogdisabled = true
  // }
}
onMounted(() => {
  nextTick(() => {
    getFieldForm()
  })
  initializer()
  // ProjBizUnitQualificationApi.listSupplier(formData.value).then(resp => {

  //   supplierList.value = resp.data.map(item => ({
  //     label: item.orgFullName , // Use appropriate field from response
  //     value: item.id  // Use appropriate identifier
  //   }));
  // });

  if (type.value === "add") {

    // 初始化时将根节点赋值给currentNode
    if (dataSource.value.length > 0) {
      currentNode.value = dataSource.value[0];
    }

  } else {
    let businessKey = route.query.businessKey
    if (businessKey) {
      formData.value.id = businessKey
    }
    ProjBizUnitQualificationApi.getTreeById(formData.value.id).then(resp => {
      // 1. 初始化dataSource
      if (resp.data && resp.data.length) {
        dataSource.value = resp.data;
      } else if (resp.data) {
        dataSource.value = [resp.data];
      } else {
        dataSource.value = [{
          id: "",
          label: "请输入承包商名称",
          contractorName: "",
          children: []
        }];
      }

      // 2. 同步formData.contractorName与根节点label
      if (dataSource.value.length > 0) {
        const rootNode = dataSource.value[0];

        formData.value.contractorName = rootNode.contractorName || rootNode.label;

        // 手动触发watch（因immediate可能已执行，此处强制更新）
        formData.value = { ...formData.value };
      }
    });
  }

  getProjectInfo()

})
const previewConfig = ref({
  // 是否外置预览,必传
  isExternalPreview: false,
  // 内置预览返回的替换字符,akey代表要替换的字符串，avalue代码替换的字符串
  substitute: {
    "akey": "avalue"
  },
  // 内置预览服务地址
  previewServerUrl: "http://10.191.64.191:8012/onlinePreview",
})
function fileSerialNumberBuilder() {
  return "inspection" + Math.floor(Math.random() * 10000);
}
function getListData() {
  if (childRef.value) {
    let list = childRef.value.getListData()
    formData.value.projBizDmStgMDtoList = list
    console.log(list, "组件数据。。。。。。。。。")
  }
}

function initData(urlParams, taskInfo, handlerClose) {
  // 如果用户从流程设计点击启动, 如果是在线表单, 则阻止执行initData, 防止在线表单报错
  if (urlParams.menuId && urlParams.openInStart) {
    return
  }
  state.flowData.procDefKey = taskInfo.procDefKey;
  state.flowData.procInstId = urlParams.procInstId;
  state.flowData.businessKey = urlParams.businessKey;
  state.flowData.taskId = urlParams.taskId;
  state.flowData.fiedPermission = taskInfo.fiedPermission;
  state.flowData.variableList = taskInfo.variableList;
  state.taskFlag = urlParams.taskFlag;
  state.firstAct = taskInfo.firstAct;
  state.handlerClose = handlerClose;
  state.flowData.customPropertyConfigList = taskInfo.customPropertyConfigList
  let fieldPerList = taskInfo.formConfig?.formFieldConfig?.fieldPerList || []
  handleFormAuth(fieldPerList);
  initBusiForm();
};
function handleFormAuth(data) {
  let formAuth = {};
  for (let item of data) {
    let permi = 1;
    if (item.readonly) {
      permi = 2;
    }
    if (item.hidden) {
      permi = 3;
    }
    if (!isCamelCase(item.fieldModelId)) {
      item.fieldModelId = toCamelCase(item.fieldModelId)
    }
    formAuth = {
      ...formAuth,
      [`${item.fieldModelId}_${item.field}`]: permi,
    };
  }
  state.formAuth = formAuth;
  state.formAuthList = JSON.parse(JSON.stringify(formAuth))
};
function getFieldForm() {
  let rootFields = {
    ...state.rootFields
  }
  let fieldList = []
  let keys = []
  for (let key in rootFields) {
    keys.push(key)
    fieldList.push(rootFields[key])
  }
  const result = processAuthFieldForm ? processAuthFieldForm(fieldList) : fieldList;
  const message = {
    type: "getTemplateRoot",
    pathname: window.location.pathname,
    actKey: getQueryParams("actKey"),
    content: result
  };
  window.parent.postMessage(JSON.parse(JSON.stringify(message)), "*");
};
function getQueryParams(key) {
  let url = window.location.href;
  // 使用正则表达式解析URL中的查询字符串
  var queryString = url.split('?')[1];
  if (!queryString) {
    return {};
  }
  var params = {};
  // 分割查询字符串成单个参数
  var vars = queryString.split("&");
  for (var i = 0; i < vars.length; i++) {
    var pair = vars[i].split("=");
    params[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);
  }
  return params[key];
};
const relevantId = ref(props.data?.formData?.id ?? null) // 关联业务ID，新增业务的时候传null（⽰例值）
const formRef = ref()
const FlowActionType = ref(proxy.FlowActionType);
const { } = useDicts([])
const isShowCloseBtn = ref(false);
let approvalOption = props.data?.approvalOption ? props.data?.approvalOption : route.query?.procInstId ? ref({
  isShowApprovalList: true,
  isShowFlowDiagram: true,
  procInstId: route.query?.procInstId
}) : ref({
  isShowApprovalList: false,
  isShowFlowDiagram: false,
  procInstId: ''
});
const type = toRef(props.data?.type);

const {
  org_type
} = useDicts(["org_type"])


let formData = ref({
  projectCode: "",
  projectName: "",
  projectUnit: "",
  unitType: "",
  contractorName: "",
  processInstanceId: "",
  projBizDmStgMDtoList: [],
  superUnit: "",
  projBizQualificationFileDtoList: [],
  createName: "",
  createTime: "",
  projectUnitName: "",
  projectParentUnitName: "",
  inspectedUnitId: "",
  inspectedUnitName: "",
  inspectedUnitParentNames: "",
  state: "1",
  label: "",
  children: [],
  updateName: "",
  updateTime: ""
});

const dataSource = ref([
  {
    id: "",
    label: "请输入承包商名称",
    contractorName: "",
    children: [] // 必须包含children字段
  }
]);
const dialogRules = ref({
  label: [
    { required: true, message: '请输入节点名称', trigger: 'blur' }
  ]
});
watch(
  () => formData.value.contractorName, // 监听的变量
  (newValue) => {
    // 当承包商名称变化时，更新根节点的label
    if (dataSource.value.length > 0) {
      // alert("newValue",newValue)
      dataSource.value[0].label = newValue || "请输入承包商名称";
      dataSource.value[0].contractorName = newValue || "请输入承包商名称";
      // 手动触发响应式更新（强制刷新树）
      dataSource.value = [...dataSource.value];
    }
  },
  {
    immediate: true,
    deep: true

  } // 初始化时立即执行一次
);
let formRules = ref({
  projectCode: [],
  projectName: [],
  projectUnit: [],

  code: [
    { required: true, message: '业务编号不能为空', trigger: 'blur' }
  ],
  contractorName: [
    { required: true, message: '承包商名称不能为空', trigger: 'blur' }
  ],
  unitType: [
    { required: true, message: '请选择单位类型', trigger: 'change' }
  ],


  superUnit: [],
  createName: [],
  createTime: [],
  state: [],
  updateName: [],
  updateTime: []
});
let unitTypeOption = ref([{
  label: "选择一",
  value: 1
}, {
  label: "选择二",
  value: 2
}, {
  label: "选择三",
  value: 3
}]);
let delRowData = ref({});
let projBizQualificationFileOption = ref({
  tip: false,
  border: true,
  index: true,
  stripe: true,
  menu: true,
  header: false,
  menuType: "text",
  addBtn: true,
  addBtnText: "新增",
  editBtn: true,
  editBtnText: "编辑",
  delBtn: true,
  delBtnText: "删除",
  cellBtn: true,
  maxHeight: "200px",
  column: [{
    label: "文件名称",
    prop: "fileName",
    type: "input",
    cell: true
  }, {
    label: "文件大小",
    prop: "fileSize",
    type: "input",
    cell: true
  }, {
    label: "AI检查结果",
    prop: "aiResult",
    type: "input",
    cell: true
  }, {
    label: "上传人",
    prop: "createName",
    type: "input",
    cell: true
  }, {
    label: "上传时间",
    prop: "createTime",
    type: "input",
    formatter: (row, column, value) => {
      // 使用dayjs进行时间格式化，确保value存在
      if (!value) return '';
      // 默认格式为YYYY-MM-DD HH:mm:ss，您可以根据需要修改
      return dayjs(value).format('YYYY-MM-DD HH:mm:ss');
    },
    cell: true
  }]
});
const {
  sys_yn,
  global_biz_flow_status
} = useDicts(["sys_yn", "global_biz_flow_status"])

function subRowSave(form, done) {
  //编辑行
  done();
}

/**
 * 下载文件
 * @param row
 */
function onDownData(row) {
  proxy.download("/project/unitQualification/exportFile", [row.id], row.fileName);
}

function subAddRow(name) {
  //新增一行
  if (formData.value[name]) {
    formData.value[name].push({
      $cellEdit: true,
    });
  } else {
    let arr = new Array();
    arr.push({
      $cellEdit: true
    });
    formData.value[name] = arr
  }
}

function subRowUpdate(form, index, done, loading) {
  //编辑行
  done();
}

function subDelRow(row, index, name) {
  //删除行
  if (row[0].id) {
    let data = JSON.parse(JSON.stringify(row[0]));
    if (delRowData.value[name]) {
      delRowData.value[name].push(Object.assign(data, {
        delFlag: 1,
      }));
    } else {
      delRowData.value[name] = [
        Object.assign(data, {
          delFlag: 1,
        }),
      ]
    }
  }
  formData.value[name].splice(index, 1);
}
function showSelectOrgDialog(e) {
  let checkData = [];
  if (e.target.value) {
    checkData.push({
      id: formData.value.inspectedUnitId,
      orgName: e.target.value,
      parentNames: formData.value.inspectedUnitParentNames
    })
  }
  proxy.$SelectOrg({
    checkData: checkData,
    multiple: false,
    onSuccess(res) {
      if (res.data.length > 0) {
        let orgInfo = res.data[0]
        formData.value.inspectedUnitId = orgInfo.id
        formData.value.inspectedUnitName = orgInfo.orgName
        formData.value.inspectedUnitParentNames = orgInfo.parentNames
        formData.value.projectUnit = orgInfo.id
        formData.value.projectUnitName = orgInfo.orgName
        formData.value.projectParentUnitName = orgInfo.parentNames
      }
    }
  })
}
const state = reactive({
  flowData: {
    businessKey: "",
    procInstId: "",
    procDefKey: "",
    taskId: "",
    formConfig: [],
    variableList: [],
    customPropertyConfigList: []
  },
  formAuth: {},
  taskFlag: 1,
  firstAct: false,
  handlerClose: null,
  rootFields: {},
  fullFieldNameMap: {},
  formAuthList: {}
})
function handleAdd() {
  proxy.$DialogForm.show({
    content: FileAddForm,
    data: {
      dialogVisible: true
    },
    option: {
      submitBtn: true,
      emptyBtn: true,
    },
    title: "文件上传",
    // type: "this.type",
    width: "70%",
    callback: (res) => {

    }
  })
}
const formatSize = (bytes) => {
  if (bytes === 0) return '0 B';
  const units = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return `${(bytes / Math.pow(1024, i)).toFixed(2)} ${units[i]}`;
};
function getFormData() {
  //获取formData数据
  for (let item in delRowData.value) {
    formData.value[item] = formData.value[item].concat(delRowData.value[item]);
  }
  // console.log("currentNode",currentNode.value)
  // [dataSource.value[0]].children = currentNode.value
  if (type.value === "add") {
    formData.value.children = [dataSource.value[0]];

  } else {
    //修改

    formData.value.children = dataSource.value

  }
  getListData();




  return formData.value;
}
// formData = toRef(Object.keys(props.data?.formData || {}).length > 0 ? {
//   ...props.data?.formData
// } : toRef(formData.value));

if (props.data?.formData && Object.keys(props.data.formData).length > 0) {
  Object.assign(formData.value, props.data.formData); // 合并属性到原有响应式对象
}


function getProjectInfo() {
  ProjInfoMApi.view(sessionStorage.getItem('projectId')).then((resp) => {

    //赋值项目ID
    formData.value.projectId = resp.data.id
    if (!!resp.data) {

      if (resp.data.projCode?.trim()) {
        // 覆盖：null、undefined、""、"   "
        formData.value.projectCode = resp.data.projCode
      } else {
        formData.value.projectCode = '默认项目编号'
      }
      if (resp.data.projName?.trim()) {
        // 覆盖：null、undefined、""、"   "

        formData.value.projectName = resp.data.projName
      } else {
        formData.value.projectName = '默认项目名称'
      }
      if (resp.data.projOrg?.trim()) {
        // 覆盖：null、undefined、""、"   "
        formData.value.projectUnit = resp.data.projOrg
      } else {
        formData.value.projectUnit = '默认项目单位'
      }
    }
  })
}
function submitData() {
  return new Promise((resolve) => {
    formRef.value.validate((flag) => {
      if (!flag) {
        return false;
      } else {
        if (type.value === "add") {
          resolve(saveData());
        } else {
          resolve(editData());
        }
      }
    });
  });
}

function saveData() {
  //新增操作
  const formData = getFormData();
  return ProjBizUnitQualificationApi.add(formData).then(() => {
    proxy.$message.success("新增成功");
    return true;
  });
}

function editData() {
  //编辑操作
  const formData = getFormData();
  return ProjBizUnitQualificationApi.update(formData).then(() => {
    proxy.$message.success("修改成功");
    return true;
  });
}
defineExpose({
  getFormData,
  getSaveFormData,
  submitData,
  submitFlowTask,
  handlerOpenConfirm,
  getTaskInfo,
  getStartFlow,
});

function processAuthFieldForm(fieldList) {
  /**
   * 工作流表单权限功能：在扫描完成之前，业务可对表单字段进行修改。
   */
  //TODO 这里可以对权限表单的字段进行任意的修改,如果不满足业务需求，可以在这里手动添加你想要的字段
  return fieldList
};

function getApprovalOption(fun) {
  /**
   * 工作流表单权限功能(重要)：应对弹窗无法及时相应的问题，特别增加该函数修复该bug
   */
  nextTick(() => {
    fun(approvalOption.value)
  })
};

function customFormPermi(obj) {
  /**
   * 工作流表单权限功能：自定义表单权限处理函数，将对应的对象的属性还原成对应的权限值
   */
  let {
    field,
    fieldModelId
  } = obj
  let fullFieldName = `${fieldModelId}_${field}`
  //TODO 这里可以自定义处理，默认返回0即可
  return auth.get(fullFieldName) || 0
};

function handlerPrint(taskComment) {
  /**
   * 工作流打印模板功能
   */
  ProjBizUnitQualificationApi.printTemplate({
    id: formData.value.id,
    templateId: taskComment.templateId,
    formData: formData.value
  }).then(
    (res) => {
      router.push({
        name: "PrintDoc",
        query: {
          fileId: res.data,
        },
      });
      taskComment.close && taskComment.close();
    });
};

function getSubmitTask(taskActionDto) {

  /**
   * 工作流提交任务功能
   */
  return ProjBizUnitQualificationApi.submitTask({
    busiDto: formData.value,
    taskActionDto: taskActionDto
  });
};


function getStartFlow(formData, startProcDto) {
  return new Promise((resolve) => {
    formRef.value.validate((flag) => {
      if (!flag) {
        return false
      } else {
        resolve(
          ProjBizUnitQualificationApi.startFlow({
            busiData: formData,
            startProcDto: startProcDto
          }).then((respon) => {
            state.flowData.procDefKey = respon.data[0].procDefKey
            state.flowData.procInstId = respon.data[0].procInstId
            state.flowData.businessKey = respon.data[0].businessKey
            state.flowData.taskId = respon.data[0].taskId
            taskInfo.value = respon.data[0];
            nextTick(() => {
              btnHandle(props.data.el.lastChild)
            })
          })
        )
      }
    })
  })
}

//打开确认框
function handlerOpenConfirm(taskInfo, resp) {

  ProjBizUnitQualificationApi.view(taskInfo.businessKey).then((resp) => {
    if (resp.data) {
      formData.value = resp.data;
      relevantId.value = resp.data.id;
      // isShowAddBtn.value = true;
      // isShowDelBtn.value = true;
    }
  })
  res.value = resp;
  flowPageContainerRef.value.handlerActionSubmit(taskInfo, 1);
}

function handlerAction(operation, taskComment, handlerClose) {
  /**
   * 工作流操作功能
   */
  // 启动并保存草稿后再次提交
  if (

    (operation.type == FlowActionType.value.SAVESTART || operation.type == FlowActionType.value.SAVE || operation.type == FlowActionType.value.START) && !formData.value.taskId) {
    let startProcDto = {
      procDefKey: formData.value.procDefKey,
      businessKey: null,
      clientId: null,
      ...taskComment?.dialogRefs?.getFormData(),
    };
    let httpCall = null;

    if (operation.type == FlowActionType.value.SAVESTART) {

      httpCall = getStartFlow(formData, startProcDto);
    } else if (operation.type == FlowActionType.value.SAVE) {

      httpCall = getSaveFormData(formData);
    }
    console.log("httpCall", httpCall)
    httpCall.then(() => {
      proxy.$modal.msgSuccess("提交成功");
      taskComment.close && taskComment.close();
      handlerClose();
    });
  } else {

    operation.type == FlowActionType.value.START || operation.type == FlowActionType.value.SAVESTART ? (operation.type = FlowActionType.value.AGREE) : operation.type;
    let taskActionDto = {
      taskId: state.flowData.taskId,
      procInstId: state.flowData.procInstId,
      actionType: operation.type,
      ...taskComment?.dialogRefs?.getFormData(),
      taskAssignees: taskComment?.dialogRefs?.getFormData().taskAssignees?.length ? taskComment.dialogRefs.getFormData().taskAssignees.join(',') : ''
    };
    getSubmitTask(taskActionDto).then(() => {
      proxy.$modal.msgSuccess("任务办理成功");
      taskComment.close && taskComment.close();

      let businessKey = route.query.businessKey
      if (res) {
        if (businessKey) {
          handlerClose()
        } else {
          props.data.closeDialog(proxy)
        }
      } else {
        handlerClose()
      }
    }).catch(() => {
      taskComment.close && taskComment.close();
    });



  }
};

function initBusiForm() {
  /**
   * 工作流初始化表单功能
   */
  let that = proxy
  let routerQueryParams = route.query;
  auth = new Map(Object.entries(state.formAuth));
  if (routerQueryParams) {
    if (routerQueryParams.busiData) {
      // formData.value = routerQueryParams.busiData;
      Object.assign(formData.value, routerQueryParams.busiData);
    } else {
      if (routerQueryParams.businessKey) {
        let businessKey = route.query.businessKey;
        ProjBizUnitQualificationApi.view(businessKey).then(resp => {
          formData.value = resp.data;
        });
      } else {
        that.$message.error("初始化失败,因为工作流未将流程信息传入！")
      }
    }
  }
};

function getSaveFormData(formData) {
  if (formData.id) {
    return ProjBizUnitQualificationApi.update(formData);
  } else {
    return ProjBizUnitQualificationApi.add(formData).then((res) => {


      formData.id = res.data.id


    });


  }
};
</script>

<style lang="scss" scoped>
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.bgW {
  height: 100%;
}
</style>
