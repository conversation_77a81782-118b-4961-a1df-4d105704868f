import store from '@/store'
import { reactive, toRefs } from 'vue'

/**
 * 获取字典数据
 */
export function useDicts(keys) {
  const res = reactive({})
  return (() => {
    keys.forEach(async (dictType) => {
      res[dictType] = []
      const dicts = await store.dispatch('getDict', dictType)
      if (dicts) {
        res[dictType] = dicts
      } else {
        try {
          const resp = await store.dispatch('getOriginDict', { dictType })
          res[dictType] = resp.data.map((p) => {
          
            return {
              ...p,
              label: p.dictLabel,
              value: p.dictValue,
              raw:{...p}
            }
          })
          console.log('字典数据', res[dictType])
          store.dispatch('setDict', {
            _key: dictType,
            value: res[dictType]
          })
        } catch (error) {}
      }
    })
    return toRefs(res)
  })()
}
