<template>
  <div>
    <sn-crud :data="listData" :option="option" v-model:page="queryForm.page" v-model:search="queryForm.filter" @on-load="getPageList" @row-excel="onExportData" @search-change="onChangeSearch" @search-reset="onResetSearch" @addBtnHandle="onEditData" @row-del="onDelData">
      <template #docCode="scope">
        <el-link type="primary" @click="onEditData(scope.row,'view')">{{ scope.row.docCode }}</el-link>
      </template>
      <template #menu="{ row, index, size }">
        <template v-if="!row.docCode">
          <!--未提交-->
          <el-button type="primary" :size="size" v-if="!row.approvalStatus" icon="el-icon-edit" link @click="onEditData(row)">发起验收</el-button>
        </template>
        <template v-else>
        <!--草稿-->
        <el-button type="primary" :size="size" v-if="row.approvalStatus === '1'" icon="el-icon-edit" link @click="onEditData(row,'edit')">编辑</el-button>
        <el-button type="primary" :size="size" v-if="row.approvalStatus === '1'" icon="el-icon-download" link @click="onDownloadData(row)">下载</el-button>
        <el-button type="danger" :size="size" v-if="row.approvalStatus === '1' && row.procInstanceId" icon="el-icon-edit" link @click="onCancelData(row,'stop')">作废</el-button>
        <el-button type="danger" :size="size" v-if="row.approvalStatus === '1' && !row.procInstanceId" icon="el-icon-delete" link @click="onDelData([row])">删除</el-button>
        <!--已提交-->
        <el-button type="danger" :size="size" v-if="row.approvalStatus === '2'" icon="el-icon-edit" link @click="onCancelData(row,'revoke')">撤回</el-button>
        <el-button type="primary" :size="size" v-if="row.approvalStatus === '2'" icon="el-icon-download" link @click="onDownloadData(row)">下载</el-button>
        <!--驳回上一节点-->
        <el-button type="primary" :size="size" v-if="row.approvalStatus === '3'" icon="el-icon-download" link @click="onDownloadData(row)">下载</el-button>
        <!--审批中-->
        <el-button type="primary" :size="size" v-if="row.approvalStatus === '4'" icon="el-icon-download" link @click="onDownloadData(row)">下载</el-button>
        <!--已作废-->
        <el-button type="primary" :size="size" v-if="row.approvalStatus === '5'" icon="el-icon-download" link @click="onDownloadData(row)">下载</el-button>
        <!--已审批-->
        <el-button type="primary" :size="size" v-if="row.approvalStatus === '6'" icon="el-icon-download" link @click="onDownloadData(row)">下载</el-button>
        <!--已驳回提交人-->
        <el-button type="primary" :size="size" v-if="row.approvalStatus === '7'" icon="el-icon-edit" link @click="onEditData(row,'edit')">编辑</el-button>
        <el-button type="primary" :size="size" v-if="row.approvalStatus === '7'" icon="el-icon-download" link @click="onDownloadData(row)">下载</el-button>
        <el-button type="danger" :size="size" v-if="row.approvalStatus === '7' && row.procInstanceId" icon="el-icon-edit" link @click="onCancelData(row,'stop')">作废</el-button>
        </template>
      </template>
    </sn-crud>
  <div ref="myRef"></div>
  </div>
</template>

<script>
import ProjBizDesignChangeAcceptanceCheckMApi from '@/project/api/designManagement/designChangeAcceptanceCheck/ProjBizDesignChangeAcceptanceCheckM.js'
  import EditProjBizDesignChangeAcceptanceCheckM from "./components/EditProjBizDesignChangeAcceptanceCheckM.vue";
  export const routerConfig = [{
    menuType: "C",
    menuName: "变更验收单",
  }, {
    menuType: "F",
    menuName: "查看",
    perms: "show",
    api: [ProjBizDesignChangeAcceptanceCheckMApi.config.pageList],
  }, {
    menuType: "F",
    menuName: "新增",
    perms: "add",
    api: [ProjBizDesignChangeAcceptanceCheckMApi.config.add],
  }, {
    menuType: "F",
    menuName: "修改",
    perms: "update",
    api: [ProjBizDesignChangeAcceptanceCheckMApi.config.update, ProjBizDesignChangeAcceptanceCheckMApi.config.view],
  }, {
    menuType: "F",
    menuName: "删除",
    perms: "del",
    api: [ProjBizDesignChangeAcceptanceCheckMApi.config.remove],
  }];
</script>

<script setup>
import {
  ref,
  getCurrentInstance
} from 'vue';
import { dayjs } from 'element-plus'
const {
  proxy
} = getCurrentInstance()
let option = ref({
  tip: false,
  dialogType: "page",
  border: true,
  index: true,
  stripe: true,
  menu: true,
  menuWidth: 450,
  header: true,
  height: "auto",
  searchSpan: 6,
  searchIcon: true,
  searchMenuSpan: 6,
  searchIndex: 3,
  selection: true,
  showTree: false,
  excelBtn: true,
  excelBtnText: "列表导出",
  addBtn: false,
  delBtn: false,
  delBtns: false,
  editBtn: false,
  delBtnsText: "批量删除",
  addBtnText: "新增",
  customEditBtnText: "编辑",
  customDelBtnText: "删除",
  customViewBtnText: "查看",
  searchLabelWidth: 150, //搜索标题宽度
  column: [{
    label: "变更验收单编号",
    width: 180,
    align: "center",
    prop: "docCode"
  }, {
    label: "设计变更单编号",
    width: 180,
    align: "center",
    prop: "changDesignChangCode"
  }, {
    label: "变更类型",
    width: 180,
    align: "center",
    prop: "changeType",
    search: true
  }, {
    label: "变更原因",
    headerAlign: "center", //表头对齐
    overHidden: true,
    prop: "changeReason"
  }, {
    label: "变更内容",
    headerAlign: "center", //表头对齐
    overHidden: true,
    prop: "changeContent",
    search: true
  }, {
    label: "是否验收",
    width: 100,
    align: "center",
    prop: "isCheck",
    search: true
  },  {
    label: "填报单位",
    width: 180,
    align: "center",
    prop: "reportOrganization"
  }, {
    label: "卷册号",
    width: 100,
    align: "center",
    prop: "volumeNumber",
    search: true
  }, {
    label: "卷册名称",
    width: 100,
    align: "center",
    prop: "volumeName",
    search: true
  },
    {
      label: "验收时间",
      prop: "checkTime",
      width: 180,
      align: "center",
      formatter:(row, column, value) => {
        if (!value) return '';
        return dayjs(value).format('YYYY-MM-DD');
      }
    },
 ]
});
let listData = ref([]);
let queryForm = ref({
  page: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  filter: {
    createTime: null
  }
});
let formRules = ref({});
const myRef = ref(null);
function getPageList() {
  //查询分页列表
  // 查询前处理参数
  const params = handleQueryForm();
  ProjBizDesignChangeAcceptanceCheckMApi.pageList(params).then((res) => {
    listData.value = res.data.dataList;
    queryForm.value.page.total = res.data.totalCount;
  });
}

function onResetSearch() {
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
}

function onExportData() {
  const params = handleQueryForm();
  let queryForm = JSON.parse(JSON.stringify(params));
  proxy.download("project/designChangeAcceptanceCheck/export", queryForm, '变更验收单列表导出.xlsx');
}


function handleQueryForm() {
  // 处理参数
  const {
    pageSize,
    pageNum
  } = queryForm.value.page;
  const filter = {}
  for (let key in queryForm.value.filter) {
    if (queryForm.value.filter[key]?.length) {
      // sn-curd搜索select会生成$prop的变量
      if (key.startsWith("$")) continue;
      filter[key] = queryForm.value.filter[key];
    }
  }
  delete filter.createTime;
  if (Array.isArray(queryForm.value.filter.createTime) && queryForm.value.filter.createTime?.length === 2) {
    filter["beginCreateTime"] = queryForm.value.filter.createTime[0];
    filter["endCreateTime"] = queryForm.value.filter.createTime[1];
  }
  const searchParams = {
    page: {
      pageSize,
      pageNum,
    },
    filter,
  }
  return searchParams
}

function onChangeSearch(params, done) {
  //搜索按钮操作
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
  done && done();
}
async function onEditData(row,type) {
  //编辑,新增按钮操作
  let editType = type?type: (row?.procInstanceId ? 'view' : (row?.changDesignChangCode ? 'edit' : 'add'));
  let rowInfo = await (editType !== "add" ? ProjBizDesignChangeAcceptanceCheckMApi.view(row.changDesignChangCode) : {});
  const formData = editType !== "add" ? rowInfo.data : rowInfo;
  delete formData.beginCreateTime;
  // delete formData.createTime;
  delete formData.endCreateTime;
  proxy.$DialogForm.show({
    title: editType == "edit" ? (row?.id ? "编辑" : "发起验收") : (editType == "view" ? "查看" : "新增"),
    type: option.value.dialogType,
    el: myRef.value,
    width: "80%",
    content: EditProjBizDesignChangeAcceptanceCheckM,
    data: editType !== "add" ? {
      formData: rowInfo.data,
      isShowCloseBtn: false,
      approvalOption: ref({
        isShowApprovalList: true,
        isShowFlowDiagram: true,
        procInstId: rowInfo.data.procInstanceId
      }),
      type: editType
    } : {
      approvalOption: ref({
        isShowApprovalList: false,
        isShowFlowDiagram: false,
        procInstId: null
      }),
      type: editType
    },
    option: {
      submitBtn: false,
      emptyBtn: false,
      submitText: '保存',
      emptyText: '关闭',
      extendButton: generateButtons(editType, row),
    },
    callback: (res) => {
      let vm = res.dialogRefs
      if (vm) {
        let formData = vm.getFormData();
        switch (res.type) {
          //直接取消
          // 取消
          case 'cancelDirect':
            res.close();
            break;
          // 取消
          case 'cancel':
            proxy.$modal.confirm("确认关闭？", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }).then(() => {
              res.close();
            }).catch(() => {});
            break;
          // 保存
          case 'save':
            if(!formData.approvalStatus){
              formData.approvalStatus = '1'
            }
            vm.getSaveFormData(formData).then(() => {
              proxy.$message.success("保存成功");
              getPageList();
              // res.close();
            })
            break;
          // 编辑
          case 'edit':
            res.close();
            onEditData(row)
            break;
          // 发起流程
          case 'afterProcessIsInitiated':
            let startProcDto = {
              businessKey: null,
              clientId: null
            };
            vm.getStartFlow(formData, startProcDto).then(() => {
              proxy.$message.success("流程启动成功");
              getPageList();
              res.close();
            })
            break;
          //提交流程
          case 'submitFlowTaskProcess':
            vm.submitFlowTask().then((resp) => {
              if(!resp){
                getPageList();
                res.close();
              }
            })
            break;
        }
      }
    },
  });
}

function generateButtons(editType, row) {
  if (editType !== "view") {
    const hasProcId = row?.procInstanceId;
    const buttons = [
      { key: 'cancel', text: '关闭', buttonType: '', icon: 'el-icon-close' }
    ];

    if (hasProcId) {
      buttons.push({
        key: 'save',
        text: '保存',
        buttonType: 'primary',
        icon: 'el-icon-check'
      }, {
        key: 'submitFlowTaskProcess',
        text: '提交',
        buttonType: 'primary',
        icon: 'sn-button-fasong'
      });
    } else {
      buttons.push({
        key: 'save',
        text: '保存',
        buttonType: 'primary',
        icon: 'el-icon-check'
      }, {
        key: 'afterProcessIsInitiated',
        text: '发起流程',
        buttonType: 'primary',
        icon: 'sn-button-fasong'
      });
    }
    return buttons;
  }
  return row.approvalStatus != '1' ? [
    { key: 'cancelDirect', text: '关闭', buttonType: '', icon: 'el-icon-close' }
  ] : [
    { key: 'edit', text: '编辑', buttonType: 'primary', icon: 'el-icon-edit' },
    { key: 'cancelDirect', text: '关闭', buttonType: '', icon: 'el-icon-close' }
  ];
}

function onDownloadData(rows) {
  let params = {
    relevantId: rows.id,
    type: 'designChangeAcceptanceCheck',
  };
  proxy.download("/project/document/storage/exportZipByRelevantId", params, "变更验收单_"+rows.docCode+"_附件.zip");
}

function onDelData(rows) {
  //删除行
  if (!rows || rows.length == 0) {
    proxy.$message.info("请勾选数据！");
    return false;
  }
  let ids = rows.map((item) => {
    return item.id;
  });
  proxy.$modal.confirm("确认删除数据项？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    ProjBizDesignChangeAcceptanceCheckMApi.remove(ids).then((res) => {
      proxy.$message.success("已删除");
      getPageList();
    });
  }).catch(() => {});
}

/*流程作废*/
function onCancelData(row,type) {
  //删除行
  let formDate = {
    id: row.id,
    operateFlowStatus: type,
  }
  let typeName;
  if(type == 'revoke'){
    typeName = '撤回';
  }else{
    typeName = '作废';
  }
  proxy.$modal.confirm("确认"+typeName+"流程？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    ProjBizDesignChangeAcceptanceCheckMApi.operateFlow(formDate).then((res) => {
      proxy.$message.success("已"+typeName);
      getPageList();
    });
  }).catch(() => {});
}
</script>

<style lang="scss" scoped>

</style>
