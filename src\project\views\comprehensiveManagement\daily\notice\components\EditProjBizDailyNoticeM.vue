<template>
  <flow-page-container
    ref="flowPageContainerRef"
    @handlerAction="handlerAction"
    @initData="initData"
    @handlerPrint="handlerPrint"
    :closeBtn="isShowCloseBtn"
    :approvalOption="approvalOption"
    @approvalOptionCallback="getApprovalOption"
  >
    <div style="width: 100%">
      <el-form :model="formData" :rules="formRules" ref="formRef" label-width="100px" label-position="right" :disabled="type == 'view'">
    <el-row :gutter="16" :span="24">
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>基本信息
          </legend>
          <el-row :gutter="16" :span="24">
            <el-col :span='12'>
              <el-form-item label="业务编号" prop="bizNum">
                <el-input v-model="formData.bizNum" type="text" placeholder="请输入" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='12'>
              <el-form-item label="类型" prop="type">
                <el-select v-model="formData.type" clearable placeholder="请选择">
                  <el-option v-for="(item, index) in noticeType" :key="index" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span='24'>
              <el-form-item label="标题" prop="title">
                <el-input v-model="formData.title" type="text" placeholder="请输入" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='22'>
              <el-form-item label="通知范围" prop="receiverId">
                <el-select v-model="formData.receiverId" :disabled="showUser" type="primary" @click="show" multiple>
                  <el-option v-for="(item, index) in receiverIdOption" :key="index" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span='2'>
              <el-form-item label="" label-width="0" prop="isAll">
                <el-checkbox-group v-model="formData.isAll">
                  <el-checkbox v-for="(item, index) in isAllOption" :key="index" :label="item.value" @change="(e) => isAllChange(e)">{{item.label}}</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="提交人" prop="createName">
                <el-input v-model="formData.createName" :readonly="true" :disabled="true" type="text" placeholder="请输入" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="提交单位" prop="submittingUnit">
                <el-input v-model="formData.submittingUnit" :readonly="true" :disabled="true" type="text" placeholder="请输入" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="提交时间" prop="submissionDate">
                <el-date-picker type="date" v-model="formData.submissionDate" format="YYYY-MM-DD" style="width:100%" value-format="YYYY-MM-DD"></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span='24'>
              <el-form-item label="正文" prop="content">
                <el-input type="textarea" v-model="formData.content" placeholder="请输入" rows="5" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span='24'>
              <el-form-item label="备注" prop="note">
                <el-input type="textarea" v-model="formData.note" placeholder="请输入" rows="3" clearable></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </fieldset>
      </el-card>
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span> 附件信息
          </legend>
          <el-row :gutter="16" :span="24">
            <project-document-storage-ui-table
              ref="childRef"
              :type="`dailyNotice`"
              :relevantId="formData.id"
              :isPageSearch="false"
              :isDeleteMinio = "type !== 'view'"
              :file-serial-number-builder="fileSerialNumberBuilder"
              :preview-config="previewConfig"
              :isShowAddBtn="type !== 'view'"
              :isShowDelBtn="type !== 'view'"
              :isShowLinkBtn="false"
            ></project-document-storage-ui-table>
          </el-row>
        </fieldset>
      </el-card>
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span> 单据信息
          </legend>
          <el-row :gutter="16" :span="24">
            <el-col :span='8'>
              <el-form-item label="创建人" prop="submittingName">
                <el-input v-model="formData.createName" :disabled="true" type="text" placeholder="请输入" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="创建时间" prop="">
                <el-input type="text" v-model="formData.createTime"  disabled clearable placeholder=""/>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="单据状态" prop="docState">
                <el-select v-model="formData.docState" clearable placeholder="请选择" disabled>
                  <el-option v-for="(item, index) in global_biz_flow_status" :key="index" :label="item.label" :value="item.value" ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="最近修改人" prop="updateName">
                <el-input v-model="formData.updateName" :disabled="true" type="text" placeholder="" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="最近修改时间" prop="updateTime">
                <el-input type="text" v-model="formData.updateTime" disabled clearable placeholder=""/>
              </el-form-item>
            </el-col>
          </el-row>
        </fieldset>
      </el-card>
    </el-row>
  </el-form>
    </div>
  </flow-page-container>
</template>

<script setup>
//TODO 引入所需JS
import {
  nextTick,
  onMounted,
  ref,
  reactive,
  toRef,
  defineProps,
  defineExpose,
  getCurrentInstance
} from 'vue';
import {
  useRoute,
  useRouter
} from 'vue-router'
import dayjs from 'dayjs'
import __classNameVariable__Api from "@/project/api/comprehensiveManagement/daily/ProjBizDailyNoticeM"
import BpmTaskApi from '@/project/api/bpm/bpmTask'
const {
  proxy
} = getCurrentInstance()
const route = useRoute();
const router = useRouter();
const props = defineProps({
  data: Object,
});
const formRef = ref()
let showUser = ref(false)
let type = toRef(props.data?.type);
const isShowCloseBtn = ref(false);
import {
  useDicts
} from "@/common/hooks/useDicts";
import store from '@/store'
import ProjBizConsStartWorkReportApi
  from '@/project/api/constructionManagement/sceneStartWorkReport/ProjBizConsStartWorkReport'
import { kkFileViewUrl } from '@/config'
import FlowPageContainer from '@/common/components/exportFile/FlowPageContainer.vue'
import { btnHandle } from '@/project/components/hooks/buttonChangeName'

const {
  noticeType,
  global_biz_flow_status
} = useDicts(["noticeType", "global_biz_flow_status"])
let formData = ref({
  bizNum: "",
  title: "",
  type: "",
  receiverId: "",
  isAll: "",
  createName: "",
  submittingUnit: "",
  submissionDate: "",
  content: "",
  note: "",
  docState: "1",
  updateName: "",
  updateTime: ""
});
let formRules = ref({
  bizNum: [{
    required: true,
    message: "请输入"
  }],
  title: [{
    required: true,
    message: "请输入"
  }],
  type: [{
    required: true,
    message: "请选择"
  }],
  receiverId: [],
  isAll: [],
  createName: [{
    required: true,
    message: "请输入"
  }],
  submittingName: [{
    required: false,
    message: "请输入"
  }],
  submittingUnit: [{
    required: true,
    message: "请输入"
  }],
  submissionDate: [{
    required: true,
    message: "请选择"
  }],
  content: [{
    required: true,
    message: "请输入"
  }],
  note: [],
  docState: [],
  updateName: [],
  updateTime: []
});
let isAllOption = ref([{
  label: "全部人员",
  value: ''
}]);
let receiverIdOption = ref([{
  label: "请选择",
  value: ''
}])
let actKey = ref("")
let auth = new Map();
let userIdOption = ref([])
const FlowActionType = ref(proxy.FlowActionType);
let isShowReply = ref(false)
let contentFlag = ref(true)
const res=ref()
const taskInfo = ref()
const flowPageContainerRef = ref();
const isShowAddBtn = ref(false); //上传
const isShowDelBtn = ref(false); //下载
const state = reactive({
  flowData: {
    businessKey: "",
    procInstId: "",
    procDefKey: "",
    taskId: "",
    formConfig: [],
    variableList: [],
    customPropertyConfigList: []
  },
  formAuth: {},
  taskFlag: 1,
  firstAct: false,
  handlerClose: null,
  rootFields: {},
  fullFieldNameMap: {},
  formAuthList: {}
})
const relevantId = ref(props.data?.formData?.id ?? null) // 关联业务ID，新增业务的时候传null（⽰例值）
let approvalOption = props.data?.approvalOption ? props.data?.approvalOption : route.query?.procInstId ? ref({
  isShowApprovalList: true,
  isShowFlowDiagram: true,
  procInstId: route.query?.procInstId
}) : ref({
  isShowApprovalList: false,
  isShowFlowDiagram: false,
  procInstId: ''
});
formData = toRef(Object.keys(props.data?.formData || {}).length > 0 ? {
  ...props.data?.formData
} : toRef(formData.value));
if (formData.value.isAll === 1) {
  formData.value.isAll = true
  showUser.value = true
} else {
  formData.value.isAll = false
  showUser.value = false
  if(formData.value.projBizNoticeReceiverSDtoList) {
    formData.value.projBizNoticeReceiverSDtoList.forEach(item => {
      receiverIdOption.value.push({
        label: item.userName,
        value: item.userId
      })
      userIdOption.value.push({
        userName: item.userName,
        userId: item.userId,
        userCode: item.userCode
      })
    })
    formData.value.receiverId = formData.value.projBizNoticeReceiverSDtoList.map(item => item.userId)
  }
}
formData.value.type = formData.value.type + ''
formData.value.docState = formData.value.docState + ''
formData.value.projectId = sessionStorage.getItem('projectId')
if(!formData.value.submittingUnit){
  formData.value.submittingUnit = JSON.parse(store.state.user.orgName)?.find((item) => item.id === store.state.user.defaultOrg)?.orgName
}
if(!formData.value.createName){
  formData.value.createName = store.state.user.userInfo.userName
}
if(!formData.value.submissionDate) {
  formData.value.submissionDate = dayjs().format('YYYY-MM-DD HH:mm:ss')
}
if(!formData.value.createTime) {
  formData.value.createTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
} else {
  formData.value.createTime = dayjs(formData.value.createTime).format('YYYY-MM-DD HH:mm:ss')
}
if(formData.value.updateTime) {
  formData.value.updateTime = dayjs(formData.value.updateTime).format('YYYY-MM-DD HH:mm:ss')
}
function getTaskInfo() {
  return taskInfo.value
}

function getFormData() {
  return formData.value
};
function show() {
  if(type.value !== 'view') {
    if(!showUser.value) {
      proxy.$SelectUser({
        userList: userIdOption,
        multiple: true,
        onSuccess(res) {
          userIdOption = ref([])
          res.data.forEach(item => {
            receiverIdOption.value.push({
              label: item.userName,
              value: item.userId
            })
            userIdOption.value.push({
              userName: item.userName,
              userId: item.userId,
              userCode: item.userCode
            })
          })
          formData.value.receiverId = res.ids
        }
      })
    }
  }
}
function getStartFlow(formData, startProcDto) {
  return new Promise((resolve) => {
    formRef.value.validate((flag) => {
      if (!flag) {
        return false;
      } else {
        if(!formData.isAll && (!formData.receiverId || formData.receiverId.length === 0)) {
          proxy.$message.error("请选择通知范围")
          return false
        }
        if(formData.isAll || formData.isAll === 1){
          formData.isAll = 1
        } else {
          formData.isAll = 0
          if(formData.receiverId) {
            formData.projBizNoticeReceiverSDtoList = userIdOption
          }
        }
        getListData()
        formData.submissionDate = formData.submissionDate
          ? dayjs(formData.submissionDate).format('YYYY-MM-DDTHH:mm:ss') // 转为 ISO 8601
          : null
        formData.createTime = null
        formData.updateTime = null
        /*工作流启动流程功能*/
        resolve(__classNameVariable__Api.startFlow({
          busiData: formData,
          startProcDto: startProcDto
        }).then(respon => {
          state.flowData.procDefKey = respon.data[0].procDefKey
          state.flowData.procInstId = respon.data[0].procInstId
          state.flowData.businessKey = respon.data[0].businessKey
          state.flowData.taskId = respon.data[0].taskId
          taskInfo.value = respon.data[0];
          nextTick(() => {
            btnHandle(props.data.el.lastChild)
          })
        }))
      }
    })
  })
};
//打开确认框
function handlerOpenConfirm(taskInfo,resp) {
  __classNameVariable__Api.view(taskInfo.businessKey).then((resp) => {
    if (resp.data) {
      formData.value = resp.data;
      relevantId.value = resp.data.id;
      isShowAddBtn.value = true;
      isShowDelBtn.value = true;
    }
  })
  res.value=resp;
  flowPageContainerRef.value.handlerActionSubmit(taskInfo,1);
}
function isAllChange(e) {
  formData.value.isAll = e
  if(e) {
    showUser.value = true
  } else {
    showUser.value = false
  }
}
function getApprovalOption(fun) {
  /**
   * 工作流表单权限功能(重要)：应对弹窗无法及时相应的问题，特别增加该函数修复该bug
   */
  nextTick(() => {
    fun(approvalOption.value)
  })
};
function handlerPrint(taskComment) {
  /**
   * 工作流打印模板功能
   */
  ProjBizConsStartWorkReportApi.printTemplate({
    id: formData.value.id,
    templateId: taskComment.templateId,
    formData: formData.value
  }).then(
    (res) => {
      router.push({
        name: "PrintDoc",
        query: {
          fileId: res.data,
        },
      });
      taskComment.close && taskComment.close();
    });
};
function initData(urlParams, taskInfo, handlerClose) {
  // 如果用户从流程设计点击启动, 如果是在线表单, 则阻止执行initData, 防止在线表单报错
  if (urlParams.menuId && urlParams.openInStart) {
    return
  }
  type.value = 'view'
  if (taskInfo.actKey === 'Activity_e0251ac') {
    isShowReply.value = true
    contentFlag.value = false
    isShowDelBtn.value = true
    isShowAddBtn.value = true
  } else if(taskInfo.actKey === 'Activity_5facc6f' || taskInfo.actKey === 'Activity_494423f') {
    type.value = 'edit'
  }
  actKey = taskInfo.actKey
  state.flowData.procDefKey = taskInfo.procDefKey
  state.flowData.procInstId = urlParams.procInstId
  state.flowData.businessKey = urlParams.businessKey
  state.flowData.taskId = urlParams.taskId
  state.flowData.fiedPermission = taskInfo.fiedPermission
  state.flowData.variableList = taskInfo.variableList
  state.taskFlag = urlParams.taskFlag
  state.firstAct = taskInfo.firstAct
  state.handlerClose = handlerClose
  state.flowData.customPropertyConfigList = taskInfo.customPropertyConfigList
  let fieldPerList = taskInfo.formConfig?.formFieldConfig?.fieldPerList || []
  handleFormAuth(fieldPerList)
  initBusiForm()
}
function initBusiForm() {
  /**
   * 工作流初始化表单功能
   */
  let that = proxy
  let routerQueryParams = route.query;
  auth = new Map(Object.entries(state.formAuth));
  if (routerQueryParams) {
    if (routerQueryParams.busiData) {
      formData.value = routerQueryParams.busiData;
    } else {
      if (routerQueryParams.businessKey) {
        formData.id = routerQueryParams.businessKey
        let businessKey = route.query.businessKey;
        __classNameVariable__Api.view(businessKey).then(resp => {
          formData.value = resp.data;
          formData.value.createTime = dayjs(formData.value.createTime).format('YYYY-MM-DD HH:mm:ss')
          formData.value.updateTime = dayjs(formData.value.updateTime).format('YYYY-MM-DD HH:mm:ss')
          formData.value.type = formData.value.type + ''
          if (formData.value.isAll === 1) {
            formData.value.isAll = true
            showUser.value = true
          } else {
            formData.value.isAll = false
            showUser.value = false
          }
          if(formData.value.projBizNoticeReceiverSDtoList) {
            formData.value.projBizNoticeReceiverSDtoList.forEach(item => {
              receiverIdOption.value.push({
                label: item.userName,
                value: item.userId
              })
            })
            formData.value.receiverId = formData.value.projBizNoticeReceiverSDtoList.map(item => item.userId)
          }
          relevantId.value = resp.data.id;
        });
      } else {
        that.$message.error("初始化失败,因为工作流未将流程信息传入！")
      }
    }
  }
};
function handleFormAuth(data) {
  let formAuth = {}
  for (let item of data) {
    let permi = 1
    if (item.readonly) {
      permi = 2
    }
    if (item.hidden) {
      permi = 3
    }
    if (!isCamelCase(item.fieldModelId)) {
      item.fieldModelId = toCamelCase(item.fieldModelId)
    }
    formAuth = {
      ...formAuth,
      [`${item.fieldModelId}_${item.field}`]: permi
    }
  }
  state.formAuth = formAuth
  state.formAuthList = JSON.parse(JSON.stringify(formAuth))
}

function submitFlowTask(resp) {
  res.value=resp;
  return proxy.$confirm('确定提交当前单据?', '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
    const formData = getFormData();
    if(actKey === 'Activity_5facc6f' || actKey === 'Activity_494423f'){
      if(!formData.value.isAll && !formData.value.receiverId) {
        proxy.$message.error("请选择通知范围")
        return false
      }
      if(formData.value.isAll){
        formData.value.isAll = 1
      } else {
        formData.value.isAll = 0
        if(formData.value.receiverId) {
          formData.value.projBizNoticeReceiverSDtoList = userIdOption
        }
      }
      formData.value.submissionDate = null
      formData.value.createTime = null
      formData.value.updateTime = formData.value.updateTime = dayjs().format('YYYY-MM-DDTHH:mm:ss')
    } else {
      if(formData.isAll){
        formData.isAll = 1
      } else {
        formData.isAll = 0
        if(formData.receiverId) {
          formData.projBizNoticeReceiverSDtoList = userIdOption
        }
      }
      formData.submissionDate = null
      formData.createTime = null
      formData.updateTime = formData.updateTime = dayjs().format('YYYY-MM-DDTHH:mm:ss')
    }
    return BpmTaskApi.listRuTaskByProcInstId({
      procInstId: formData.procInstanceId
    }).then((params) => {
      flowPageContainerRef.value.handlerActionSubmit(params.data[0],1);
    })
  }).catch(() => {
    return true;
  })
}
function handlerAction(operation, taskComment, handlerClose) {
  /**
   * 工作流操作功能
   */
  // 启动并保存草稿后再次提交
  if (
    (operation.type == FlowActionType.value.SAVESTART ||
      operation.type == FlowActionType.value.SAVE ||
      operation.type == FlowActionType.value.START) &&
    !formData.value.taskId
  ) {
    let startProcDto = {
      procDefKey: formData.value.procDefKey,
      businessKey: null,
      clientId: null,
      ...taskComment?.dialogRefs?.getFormData()
    }
    let httpCall = null
    if (operation.type == FlowActionType.value.SAVESTART) {
      httpCall = getStartFlow(formData, startProcDto)
    } else if (operation.type == FlowActionType.value.SAVE) {
      httpCall = getSaveFormData(formData)
    }
    httpCall.then(() => {
      proxy.$modal.msgSuccess('提交成功')
      taskComment.close && taskComment.close()
      handlerClose()
    })
  } else {
    operation.type == FlowActionType.value.START || operation.type == FlowActionType.value.SAVESTART
      ? (operation.type = FlowActionType.value.AGREE)
      : operation.type
    let taskActionDto = {
      taskId: state.flowData.taskId,
      procInstId: state.flowData.procInstId,
      actionType: operation.type,
      ...taskComment?.dialogRefs?.getFormData(),
      taskAssignees:taskComment?.dialogRefs?.getFormData()?.taskAssignees ? taskComment.dialogRefs.getFormData().taskAssignees.join(',') : ''
    }
    getSubmitTask(taskActionDto)
      .then(() => {
        proxy.$modal.msgSuccess('任务办理成功')
        taskComment.close && taskComment.close()
        let businessKey = route.query.businessKey
        if(res){
          if (businessKey) {
            handlerClose()
          } else {
            props.data.closeDialog(proxy)
          }
        }else {
          handlerClose()
        }
      })
      .catch(() => {
        taskComment.close && taskComment.close()
      })
  }
}
function getSubmitTask(taskActionDto) {
  if(actKey === 'Activity_5facc6f' || actKey === 'Activity_494423f'){
    if(!formData.value.isAll && !formData.value.receiverId) {
      proxy.$message.error("请选择人员")
      return false
    }
    if(formData.value.isAll){
      formData.value.isAll = 1
    } else {
      formData.value.isAll = 0
      if(formData.value.receiverId) {
        formData.value.projBizNoticeReceiverSDtoList = userIdOption
      }
    }
    formData.submissionDate = null
  } else {
    if(formData.value.isAll){
      formData.value.isAll = 1
    } else {
      formData.value.isAll = 0
    }
  }
  formData.value.updateTime = dayjs().format('YYYY-MM-DDTHH:mm:ss')
  formData.value.createTime = null
  /**
   * 工作流提交任务功能
   */
  return __classNameVariable__Api.submitTask({
    busiDto: formData.value,
    taskActionDto: taskActionDto
  });
};
function getSaveFormData(formData) {
  return new Promise((resolve) => {
    formRef.value.validate((flag) => {
      if (!flag) {
        return false;
      } else {
        getListData()
        let form = {
          ...formData
        }
        if(!form.isAll && form.receiverId.length === 0) {
          proxy.$message.error("请选择通知范围")
          return false
        }
        form.submissionDate = form.submissionDate
          ? dayjs(form.submissionDate).format('YYYY-MM-DDTHH:mm:ss') // 转为 ISO 8601
          : null
        form.createTime = form.createTime
          ? dayjs(form.createTime).format('YYYY-MM-DDTHH:mm:ss') // 转为 ISO 8601
          : null
        if(form.isAll){
          form.isAll = 1
        } else {
          form.isAll = 0
          if(form.receiverId) {
            form.projBizNoticeReceiverSDtoList = userIdOption.value
          }
        }
        if (form.id) {
          form.updateTime = dayjs().format('YYYY-MM-DDTHH:mm:ss')
          resolve(__classNameVariable__Api.update(form))
        } else {
          resolve(__classNameVariable__Api.add(form).then((resp) => {
            if(resp.data) {
              formData.id = resp.data.id;
            }
          }))
        }
      }
    });
  });
};

const childRef = ref(null);
const previewConfig = ref({
  // 是否外置预览,必传
  isExternalPreview: false,
  // 内置预览返回的替换字符,akey代表要替换的字符串，avalue代码替换的字符串
  substitute: {
    "akey": "avalue"
  },
  // 内置预览服务地址
  previewServerUrl: kkFileViewUrl,
})

function getListData() {
  if(childRef.value){
    let list = childRef.value.getListData()
    if(list) {
      list = list.map(item => ({
        ...item,
        createName: store.state.user.userInfo.userName
      }));
      formData.value.fileId = list.map(item => item.fileId).join(',')
      formData.value.projBizDmStgMDtoList = list
    }
  }
}

// 文件序列号生成函数
function fileSerialNumberBuilder() {
  return "NOTICE" + Math.floor(Math.random()*10000)
}

defineExpose({
  getFormData,
  getStartFlow,
  getSaveFormData,
  submitFlowTask,
  handlerOpenConfirm,
  getTaskInfo
});
</script>

<style lang="scss" scoped>
.bgW {
  height: 100%;
}
</style>