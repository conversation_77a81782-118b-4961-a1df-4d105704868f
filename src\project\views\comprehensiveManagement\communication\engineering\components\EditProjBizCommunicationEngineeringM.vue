<template>
  <flow-page-container ref="flowPageContainerRef" @handlerAction="handlerAction" @initData="initData"
                       @handlerPrint="handlerPrint" :closeBtn="isShowCloseBtn" :approvalOption="approvalOption" @approvalOptionCallback="getApprovalOption">
    <div style="width: 100%">
      <el-form :model="formData" :rules="rules" ref="formRef" label-width="100px" label-position="right" :disabled="type == 'view'">
          <el-card class="box-card" style="width: 100%;">
            <fieldset class="fieldset2">
              <legend>
                <span class="el-button--primary"></span>基本信息
              </legend>
              <el-row :gutter="16" :span="24">
                <el-col :span='24'>
                  <el-form-item label="编号" prop="engineeringNum">
                    <el-input v-model="formData.engineeringNum" type="text" placeholder="请输入" clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span='24'>
                  <el-form-item label="主题" prop="title">
                    <el-input v-model="formData.title" type="text" placeholder="请输入" clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span='24'>
                  <el-form-item label="主送单位" prop="mainUnit">
                    <el-select v-model="formData.mainUnit" clearable placeholder="请选择" @click="showMain" type="primary">
                      <el-option v-for="(item, index) in mainUnitOption" :key="index" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span='24'>
                  <el-form-item label="抄送单位" prop="copyUnit">
                    <el-select v-model="formData.copyUnit" clearable placeholder="请选择" @click="showCopy" multiple>
                      <el-option v-for="(item, index) in copyUnitOption" :key="index" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span='8'>
                  <el-form-item label="提出人" prop="submissionName">
                    <el-input v-model="formData.submissionName" type="text" placeholder="请输入" clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span='8'>
                  <el-form-item label="提出单位" prop="submittingUnit">
                    <el-input v-model="formData.submittingUnit" :disabled="true" type="text" placeholder="请输入" clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span='8'>
                  <el-form-item label="提出日期" prop="submissionDate">
                    <el-date-picker type="date" v-model="formData.submissionDate" style="width: 100%" format="YYYY-MM-DD" value-format="YYYY-MM-DD" clearable></el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span='24'>
                  <el-form-item label="内容" prop="content">
                    <el-input type="textarea" v-model="formData.content" placeholder="请输入" rows="5" clearable></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span='12'>
                  <el-form-item label="是否回复" prop="isReply">
                    <el-radio-group v-model="formData.isReply" @change="changeIsReply" :disabled="formData.procInstanceId !== ''">
                      <el-radio v-for="(item, index) in sys_yn" :key="index" :label="item.value">{{item.label}}</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span='12' v-if="formData.isReply == 'Y'">
                  <el-form-item label="回复日期" prop="replyDate">
                    <el-date-picker type="date" v-model="formData.replyDate" style="width: 100%" format="YYYY-MM-DD" value-format="YYYY-MM-DD" clearable></el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span='24'>
                  <el-form-item label="备注" prop="note">
                    <el-input type="textarea" v-model="formData.note" placeholder="请输入" rows="3" clearable></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </fieldset>
          </el-card>
      </el-form>
        <el-card class="box-card" style="width: 100%;" v-if="isShowReply">
          <fieldset class="fieldset2">
            <legend>
              <span class="el-button--primary"></span>回复信息及附件
            </legend>
            <el-row :gutter="16" :span="24">
              <el-col :span='24'>
                <el-form>
                  <el-form-item label="主送单位回复信息" prop="" label-width="160">
                    <el-input type="textarea" v-model="formData.replyContent" placeholder="请输入" rows="5" clearable :disabled="contentFlag"></el-input>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
            <project-document-storage-ui-table
              ref="replyRef"
              :type="`engineerReply`"
              :relevantId="formData.id"
              :isPageSearch="false"
              :isDeleteMinio="false"
              :isHasAi="false"
              :file-serial-number-builder="replySerialNumberBuilder"
              :preview-config="previewConfig"
              :isShowAddBtn="isShowAddBtn"
              :isShowDelBtn="isShowDelBtn"
              :isShowPreviewBtn="true"
              :isShowDownloadBtn="true"
              :isShowLinkBtn="false"
              :isShowDownloadBatchBtn="true"
            ></project-document-storage-ui-table>
          </fieldset>
        </el-card>
        <el-card class="box-card" style="width: 100%;">
          <fieldset class="fieldset2">
            <legend>
              <span class="el-button--primary"></span>附件信息
            </legend>
            <el-row :gutter="16" :span="24">
              <project-document-storage-ui-table
                ref="engineerRef"
                :type="`engineer`"
                :relevantId="formData.id"
                :isPageSearch="false"
                :isDeleteMinio = "type !== 'view'"
                :file-serial-number-builder="fileSerialNumberBuilder"
                :preview-config="previewConfig"
                :isShowAddBtn="type !== 'view'"
                :isShowDelBtn="type !== 'view'"
                :isShowLinkBtn="false"
              ></project-document-storage-ui-table>
            </el-row>
          </fieldset>
        </el-card>
        <el-form :disabled="type == 'view'" label-position="right" label-width="100px">
          <el-row :gutter="16" :span="24">
        <el-card class="box-card" style="width: 100%;">
          <fieldset class="fieldset2">
            <legend>
              <span class="el-button--primary"></span>单据信息
            </legend>
            <el-row :gutter="16" :span="24">
              <el-col :span='8'>
                <el-form-item label="创建人" prop="createName">
                  <el-input v-model="formData.createName" :disabled="true" type="text" clearable>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span='8'>
                <el-form-item label="创建时间" prop="createTime">
                  <el-input v-model="formData.createTime" :disabled="true" type="text" placeholder="请输入" clearable>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span='8'>
                <el-form-item label="单据状态" prop="docState">
                  <el-select v-model="formData.docState" :disabled="true" clearable placeholder="请选择">
                    <el-option v-for="(item, index) in global_biz_flow_status" :key="index" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span='8'>
                <el-form-item label="最近修改人" prop="updateName">
                  <el-input v-model="formData.updateName" :disabled="true" type="text" placeholder="" clearable>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span='8'>
                <el-form-item label="最近修改时间" prop="updateTime">
                  <el-input v-model="formData.updateTime" :disabled="true" type="text" placeholder="" clearable>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </fieldset>
        </el-card>
    </el-row>
  </el-form>
    </div>
  </flow-page-container>
</template>

<script setup>
import __classNameVariable__Api from '@/project/api/comprehensiveManagement/communication/engineeringindex/ProjBizCommunicationEngineeringM.js'
import FlowPageContainer from '@/common/components/exportFile/FlowPageContainer.vue'
import {
  nextTick,
  ref,
  reactive,
  toRef,
  defineProps,
  defineExpose,
  getCurrentInstance
} from 'vue';
import {
  useRoute,
  useRouter
} from 'vue-router'
const {
  proxy
} = getCurrentInstance()
const route = useRoute();
const router = useRouter();
const props = defineProps({
  data: Object,
});
const type = toRef(props.data?.type);
import {
  useDicts
} from "@/common/hooks/useDicts";
import store from '@/store'
import dayjs from 'dayjs'
import ProjBizDesignChangeRequestMApi
  from '@/project/api/designManagement/designChangeRequest/ProjBizDesignChangeRequestM'
import { kkFileViewUrl } from '@/config'
import BpmTaskApi from '@/project/api/bpm/bpmTask'
import { btnHandle } from '@/project/components/hooks/buttonChangeName'
const {
  sys_yn,
  global_biz_flow_status
} = useDicts(["sys_yn", "global_biz_flow_status"])
const isShowCloseBtn = ref(false)
let isShowReply = ref(false)
let auth = new Map();
let contentFlag = ref(false)
let isShowAddBtn = ref(true)
let isShowDelBtn = ref(true)
const taskInfo = ref()
const res=ref()
const flowPageContainerRef = ref();
let actKey = ref("")
const relevantId = ref(props.data?.formData?.id ?? null) // 关联业务ID，新增业务的时候传null（⽰例值）
const FlowActionType = ref(proxy.FlowActionType);
let approvalOption = props.data?.approvalOption ? props.data?.approvalOption : route.query?.procInstId ? ref({
  isShowApprovalList: true,
  isShowFlowDiagram: true,
  procInstId: route.query?.procInstId
}) : ref({
  isShowApprovalList: false,
  isShowFlowDiagram: false,
  procInstId: ''
});
let formData = ref({
  id: "",
  engineeringNum: "",
  title: "",
  mainUnit: "",
  copyUnit: "",
  createName: "",
  submittingUnit: "",
  submissionDate: "",
  content: "",
  isReply: "",
  note: "",
  createTime: "",
  docState: "1",
  updateName: "",
  updateTime: "",
  replyDate: "",
  submittingName: "",
  mainUnitName: "",
  mainUnitParentnames: "",
  procInstanceId: ""
});
const state = reactive({
  flowData: {
    businessKey: "",
    procInstId: "",
    procDefKey: "",
    taskId: "",
    formConfig: [],
    variableList: [],
    customPropertyConfigList: []
  },
  formAuth: {},
  taskFlag: 1,
  firstAct: false,
  handlerClose: null,
  rootFields: {},
  fullFieldNameMap: {},
  formAuthList: {}
})
let rules = ref({
  engineeringNum: [{
    required: true,
    message: "请输入"
  }],
  title: [{
    required: true,
    message: "请输入"
  }],
  mainUnit: [{
    required: true,
    message: "请选择"
  }],
  copyUnit: [{
    required: true,
    message: "请选择"
  }],
  createName: [{
    required: false,
    message: ""
  }],
  submittingUnit: [],
  submissionDate: [{
    required: true,
    message: "请选择"
  }],
  submissionName: [{
    required: true,
    message: "请输入"
  }],
  content: [{
    required: true,
    message: "请输入"
  }],
  isReply: [{
    required: true,
    message: "请选择"
  }],
  replyDate: [{
    required: true,
    message: "请选择"
  }],
  note: [],
  createTime: [],
  docState: [],
  updateName: [],
  updateTime: []
});
let mainUnitOption = ref([{
  label: "请选择",
  value: ''
}]);
let mainUnitCheck = ref([]);
let copyUnitOption = ref([{
  label: "请选择",
  value: ''
}]);
let copyUnitCheck = ref([]);
const formRef = ref()
formData = toRef(Object.keys(props.data?.formData || {}).length > 0 ? {
  ...props.data?.formData
} : toRef(formData.value));
formData.value.docState = formData.value.docState + ''
formData.value.projectId = sessionStorage.getItem('projectId')
if(!formData.value.submittingUnit){
  formData.value.submittingUnit = JSON.parse(store.state.user.orgName)?.find((item) => item.id === store.state.user.defaultOrg)?.orgName
}
if(!formData.value.createName){
  formData.value.createName = store.state.user.userInfo.userName
}
if(!formData.value.submissionName) {
  formData.value.submissionName = store.state.user.userInfo.userName
}
if(!formData.value.submissionDate) {
  formData.value.submissionDate = dayjs().format('YYYY-MM-DD HH:mm:ss')
}
if(!formData.value.createTime) {
  formData.value.createTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
} else {
  formData.value.createTime = dayjs(formData.value.createTime).format('YYYY-MM-DD HH:mm:ss')
}
if(formData.value.updateTime) {
  formData.value.updateTime = dayjs(formData.value.updateTime).format('YYYY-MM-DD HH:mm:ss')
}
if(formData.value.isReply === 'Y' && type.value === 'view') {
  contentFlag = true
  isShowReply = true
  isShowAddBtn = false
  isShowDelBtn = false
}
if(formData.value.projBizCommunicationUnitSDtoList) {
  formData.value.projBizCommunicationUnitSDtoList.forEach(item => {
    copyUnitCheck.value.push({
      orgName: item.orgName,
      id: item.orgId,
      parentNames: item.parentNames,
    })
    copyUnitOption.value.push({
      label: item.orgName,
      value: item.orgId
    })
  })
  formData.value.copyUnit = formData.value.projBizCommunicationUnitSDtoList.map(item => item.orgId)
}
if(formData.value.mainUnit) {
  mainUnitOption.value.push({
    label: formData.value.mainUnitName,
    value: formData.value.mainUnit
  })
  mainUnitCheck.value.push({
    orgName: formData.value.mainUnitName,
    id: formData.value.mainUnit,
    parentNames: formData.value.mainUnitParentnames,
  })
}
function getFormData() {
  return formData.value
};
function getTaskInfo() {
  return taskInfo.value
}
function showMain() {
  if(type.value !== 'view') {
    proxy.$SelectOrg({
      multiple: false,
      checkData: mainUnitCheck,
      onSuccess(res) {
        mainUnitCheck.value = []
        res.data.forEach(item => {
          mainUnitOption.value.push({
            label: item.orgName,
            value: item.id
          })
          mainUnitCheck.value.push({
            orgName: item.orgName,
            id: item.id,
            parentNames: item.parentNames,
          })
          formData.value.mainUnitName = item.orgName
          formData.value.mainUnitParentnames = item.parentNames
        })
        formData.value.mainUnit = res.ids[0] ? res.ids[0] : res.ids[1]
      }
    })
  }
}
function showCopy() {
  if(type.value !== 'view') {
    proxy.$SelectOrg({
      multiple: true,
      checkData: copyUnitCheck,
      onSuccess(res) {
        copyUnitCheck = ref([])
        res.data.forEach(item => {
          copyUnitOption.value.push({
            label: item.orgName,
            value: item.id
          })
          copyUnitCheck.value.push({
            orgName: item.orgName,
            id: item.id,
            parentNames: item.parentNames,
          })
        })
        formData.value.copyUnit = res.ids
      }
    })
  }
}
function changeIsReply(e) {
  if(e === 'Y'){
    formData.value.replyDate = dayjs().format('YYYY-MM-DD HH:mm:ss')
  } else {
    formData.value.replyDate = null
  }
}
function getListData() {
  if (engineerRef.value) {
    let list = engineerRef.value.getListData()
    if(list) {
      list = list.map(item => ({
        ...item,
        createName: store.state.user.userInfo.userName
      }));
      formData.value.fileId = list.map(item => item.fileId).join(',')
      formData.value.projBizDmStgMDtoList = list
    }
  }
}
function getReplyData() {
  if (replyRef.value) {
    let list = replyRef.value.getListData()
    if(list) {
      formData.value.replyFileId =  list.map(item => item.fileId).join(',')
    }
  }
}
function getStartFlow(formData, startProcDto) {
  return new Promise((resolve) => {
    formRef.value.validate((flag) => {
      if (!flag) {
        return false;
      } else {
        formData.replyDate = formData.replyDate
          ? dayjs(formData.replyDate).format('YYYY-MM-DDTHH:mm:ss') // 转为 ISO 8601
          : null
        formData.submissionDate = formData.submissionDate
          ? dayjs(formData.submissionDate).format('YYYY-MM-DDTHH:mm:ss') // 转为 ISO 8601
          : null
        formData.updateTime = dayjs().format('YYYY-MM-DDTHH:mm:ss')
        formData.createTime = formData.createTime
          ? dayjs(formData.createTime).format('YYYY-MM-DDTHH:mm:ss') // 转为 ISO 8601
          : null
        if (copyUnitCheck.value) {
          formData.projBizCommunicationUnitSDtoList = copyUnitCheck.value.map(item => ({
            ...item,
            orgId: item.id,
          }));
        }
        /**
         * 工作流启动流程功能
         */
        resolve(__classNameVariable__Api.startFlow({
          busiData: formData,
          startProcDto: startProcDto
        }).then(respon => {
          state.flowData.procDefKey = respon.data[0].procDefKey
          state.flowData.procInstId = respon.data[0].procInstId
          state.flowData.businessKey = respon.data[0].businessKey
          state.flowData.taskId = respon.data[0].taskId
          taskInfo.value = respon.data[0];
          nextTick(() => {
            btnHandle(props.data.el.lastChild)
          })
        }))
      }
    })
  })
};
//打开确认框
function handlerOpenConfirm(taskInfo,resp) {
  __classNameVariable__Api.view(taskInfo.businessKey).then((resp) => {
    if (resp.data) {
      formData.value = resp.data;
      if(formData.value.projBizCommunicationUnitSDtoList) {
        formData.value.copyUnit = formData.value.projBizCommunicationUnitSDtoList.map(item => item.orgId)
      }
      relevantId.value = resp.data.id;
      isShowAddBtn.value = true;
      isShowDelBtn.value = true;
    }
  })
  res.value=resp;
  flowPageContainerRef.value.handlerActionSubmit(taskInfo,1);
}
function handlerAction(operation, taskComment, handlerClose) {
  /**
   * 工作流操作功能
   */
  // 启动并保存草稿后再次提交
  if (
    (operation.type == FlowActionType.value.SAVESTART ||
      operation.type == FlowActionType.value.SAVE ||
      operation.type == FlowActionType.value.START) &&
    !formData.value.taskId
  ) {
    let startProcDto = {
      procDefKey: formData.value.procDefKey,
      businessKey: null,
      clientId: null,
      ...taskComment?.dialogRefs?.getFormData()
    }
    let httpCall = null
    if (operation.type == FlowActionType.value.SAVESTART) {
      httpCall = getStartFlow(formData, startProcDto)
    } else if (operation.type == FlowActionType.value.SAVE) {
      httpCall = getSaveFormData(formData)
    }
    httpCall.then(() => {
      proxy.$modal.msgSuccess('提交成功')
      taskComment.close && taskComment.close()
      handlerClose()
    })
  } else {
    operation.type == FlowActionType.value.START || operation.type == FlowActionType.value.SAVESTART
      ? (operation.type = FlowActionType.value.AGREE)
      : operation.type
    let taskActionDto = {
      taskId: state.flowData.taskId,
      procInstId: state.flowData.procInstId,
      actionType: operation.type,
      ...taskComment?.dialogRefs?.getFormData(),
      taskAssignees:taskComment?.dialogRefs?.getFormData()?.taskAssignees ? taskComment.dialogRefs.getFormData().taskAssignees.join(',') : ''
    }
    getSubmitTask(taskActionDto)
      .then(() => {
        proxy.$modal.msgSuccess('任务办理成功')
        taskComment.close && taskComment.close()
        let businessKey = route.query.businessKey
        if(res){
          if (businessKey) {
            handlerClose()
          } else {
            props.data.closeDialog(proxy)
          }
        }else {
          handlerClose()
        }
      })
      .catch(() => {
        taskComment.close && taskComment.close()
      })
  }
}
function getSaveFormData(formData) {
  return new Promise((resolve) => {
    formRef.value.validate((flag) => {
      if (!flag) {
        return false;
      } else {
        getListData()
        let form = {
          ...formData
        }
        form.replyDate = form.replyDate
          ? dayjs(form.replyDate).format('YYYY-MM-DDTHH:mm:ss') // 转为 ISO 8601
          : null
        form.submissionDate = form.submissionDate
          ? dayjs(form.submissionDate).format('YYYY-MM-DDTHH:mm:ss') // 转为 ISO 8601
          : null
        form.createTime = null
        if (copyUnitCheck.value) {
          form.projBizCommunicationUnitSDtoList = copyUnitCheck.value.map(item => ({
            ...item,
            orgId: item.id,
          }));
        }
        if (form.id) {
          form.updateTime = null
          resolve(__classNameVariable__Api.update(form))
        } else {
          resolve(__classNameVariable__Api.add(form).then((resp) => {
            if(resp.data) {
              formData.id = resp.data.id;
            }
          }))
        }
      }
    });
  });
};
function getSubmitTask(taskActionDto) {
  getReplyData()
  if(isShowReply.value && taskActionDto.actionType === 'agree' && (!formData.value.replyFileId || !formData.value.replyContent)) {
    proxy.$message.error("请回复信息并上传附件");
    return false
  }
  if(actKey === 'Activity_21c177c' || actKey === 'Activity_fca053a') {
    if (copyUnitCheck.value) {
      formData.value.projBizCommunicationUnitSDtoList = copyUnitCheck.value.map(item => ({
        ...item,
        orgId: item.id,
      }));
    }
    formData.value.submissionDate = formData.value.submissionDate
      ? dayjs(formData.value.submissionDate).format('YYYY-MM-DDTHH:mm:ss') // 转为 ISO 8601
      : null
    formData.value.createTime = null
    formData.value.updateTime = null
  }
  formData.value.updateTime = dayjs().format('YYYY-MM-DDTHH:mm:ss')
  formData.value.createTime = null
  /**
   * 工作流提交任务功能
   */
  return __classNameVariable__Api.submitTask({
    busiDto: formData.value,
    taskActionDto: taskActionDto
  });
};
function initData(urlParams, taskInfo, handlerClose) {
  // 如果用户从流程设计点击启动, 如果是在线表单, 则阻止执行initData, 防止在线表单报错
  if (urlParams.menuId && urlParams.openInStart) {
    return
  }
  type.value = 'view'
  actKey = taskInfo.actKey
  if (taskInfo.actKey === 'Activity_e0251ac') {
    isShowReply.value = true
    contentFlag.value = false
    isShowDelBtn.value = true
    isShowAddBtn.value = true
  } else if(taskInfo.actKey === 'Activity_21c177c' || taskInfo.actKey === 'Activity_fca053a') {
    type.value = 'edit'
  }
  state.flowData.procDefKey = taskInfo.procDefKey
  state.flowData.procInstId = urlParams.procInstId
  state.flowData.businessKey = urlParams.businessKey
  state.flowData.taskId = urlParams.taskId
  state.flowData.fiedPermission = taskInfo.fiedPermission
  state.flowData.variableList = taskInfo.variableList
  state.taskFlag = urlParams.taskFlag
  state.firstAct = taskInfo.firstAct
  state.handlerClose = handlerClose
  state.flowData.customPropertyConfigList = taskInfo.customPropertyConfigList
  let fieldPerList = taskInfo.formConfig?.formFieldConfig?.fieldPerList || []
  handleFormAuth(fieldPerList)
  initBusiForm()
}
function initBusiForm() {
  /**
   * 工作流初始化表单功能
   */
  let that = proxy
  let routerQueryParams = route.query;
  auth = new Map(Object.entries(state.formAuth));
  if (routerQueryParams) {
    if (routerQueryParams.busiData) {
      formData.value = routerQueryParams.busiData;
    } else {
      if (routerQueryParams.businessKey) {
        formData.id = routerQueryParams.businessKey
        let businessKey = route.query.businessKey;
        __classNameVariable__Api.view(businessKey).then(resp => {
          formData.value = resp.data;
          if(formData.value.projBizCommunicationUnitSDtoList) {
            formData.value.projBizCommunicationUnitSDtoList.forEach(item => {
              copyUnitCheck.value.push({
                orgName: item.orgName,
                id: item.orgId,
                parentNames: item.parentNames,
              })
              copyUnitOption.value.push({
                label: item.orgName,
                value: item.orgId
              })
            })
            formData.value.copyUnit = formData.value.projBizCommunicationUnitSDtoList.map(item => item.orgId)
          }
          formData.value.createTime = dayjs(formData.value.createTime).format('YYYY-MM-DD HH:mm:ss')
          formData.value.updateTime = dayjs(formData.value.updateTime).format('YYYY-MM-DD HH:mm:ss')
          mainUnitOption.value.push({
            label: formData.value.mainUnitName,
            value: formData.value.mainUnit
          })
          mainUnitCheck.value.push({
            orgName: formData.value.mainUnitName,
            id: formData.value.mainUnit,
            parentNames: formData.value.mainUnitParentnames,
          })
          relevantId.value = resp.data.id;
        });
      } else {
        that.$message.error("初始化失败,因为工作流未将流程信息传入！")
      }
    }
  }
};

function submitFlowTask(resp) {
  res.value=resp;
  return proxy.$confirm('确定提交当前单据?', '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
    const formDatas = getFormData();
    // formDatas.value.createTime = null
    // formDatas.value.updateTime = formDatas.value.updateTime = dayjs().format('YYYY-MM-DDTHH:mm:ss')
    return BpmTaskApi.listRuTaskByProcInstId({
      procInstId: formDatas.procInstanceId
    }).then((params) => {
      flowPageContainerRef.value.handlerActionSubmit(params.data[0],1);
    })
  }).catch(() => {
    return true;
  })
}
function handleFormAuth(data) {
  let formAuth = {}
  for (let item of data) {
    let permi = 1
    if (item.readonly) {
      permi = 2
    }
    if (item.hidden) {
      permi = 3
    }
    if (!isCamelCase(item.fieldModelId)) {
      item.fieldModelId = toCamelCase(item.fieldModelId)
    }
    formAuth = {
      ...formAuth,
      [`${item.fieldModelId}_${item.field}`]: permi
    }
  }
  state.formAuth = formAuth
  state.formAuthList = JSON.parse(JSON.stringify(formAuth))
}
function handlerPrint(taskComment) {
  /**
   * 工作流打印模板功能
   */
  ProjBizDesignChangeRequestMApi.printTemplate({
    id: formData.value.id,
    templateId: taskComment.templateId,
    formData: formData.value
  }).then((res) => {
    router.push({
      name: 'PrintDoc',
      query: {
        fileId: res.data
      }
    })
    taskComment.close && taskComment.close()
  })
}
function getApprovalOption(fun) {
  /**
   * 工作流表单权限功能(重要)：应对弹窗无法及时相应的问题，特别增加该函数修复该bug
   */
  nextTick(() => {
    fun(approvalOption.value)
  })
}

function fileSerialNumberBuilder() {
  return "ENGINEERING" + Math.floor(Math.random()*10000)
}
function replySerialNumberBuilder() {
  return "ENGINEERING_REPLY" + Math.floor(Math.random()*10000)
}
const engineerRef = ref(null);
const replyRef = ref(null);
const previewConfig = ref({
  // 是否外置预览,必传
  isExternalPreview: false,
  // 内置预览返回的替换字符,akey代表要替换的字符串，avalue代码替换的字符串
  substitute: {
    "akey": "avalue"
  },
  // 内置预览服务地址
  previewServerUrl: kkFileViewUrl,
})

defineExpose({
  getFormData,
  getSaveFormData,
  getStartFlow,
  submitFlowTask,
  handlerOpenConfirm,
  getTaskInfo
});
</script>

<style lang="scss" scoped>
.bgW {
  height: 100%;
}
</style>