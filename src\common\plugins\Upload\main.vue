<!--
 * @Author: 王云飞
 * @Date: 2025-01-09 15:05:28
 * @LastEditTime: 2025-02-08 17:11:58
 * @LastEditors: 王云飞
 * @Description: 
 * 
-->
<template>
  <div class="select-dialog-wrapper">
    <el-dialog class="select-dialog" v-model="data.visible" :title="data.title" width="1000px">
      <el-form
        :model="formData"
        ref="formRef"
        :rules="formRules"
        label-width="80px"
        style="padding: 30px"
        close-on-click-modal="false"
        close-on-press-escape="false"
      >
        <el-form-item label="文档目录" prop="selectDocDir">
          <el-tree-select
            v-model="formData.selectDocDir"
            :data="menuList"
            placeholder="请选择"
            :multiple="props.multiple"
            :render-after-expand="false"
            show-checkbox
            check-strictly
            check-on-click-node
            style="width: 100%"
            value-key="id"
            node-key="id"
            :props="menuTreeProps"
            ref="docDirRef"
          >
          </el-tree-select>
        </el-form-item>
        <el-form-item label="标签" prop="selectLabel">
          <el-tree-select
            v-model="formData.selectLabel"
            :data="labelDataList"
            placeholder="请选择"
            multiple
            :render-after-expand="false"
            show-checkbox
            check-strictly
            check-on-click-node
            style="width: 100%"
          >
          </el-tree-select>
        </el-form-item>
        <el-form-item label="文件" class="rest-label" prop="file">
          <sn-upload @input="onUploadSuccess" :autoUpload="true" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :disabled="disabledBtn" type="primary" icon="" @click="success">
            <sn-icon icon="sn-button-baocun"></sn-icon>保 存</el-button
          >
          <el-button @click="data.visible = false"
            ><sn-icon icon="sn-button-quxiao"></sn-icon>取 消</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import LabelFileApi from '@/common/api/fileLabel'
import { reactive, defineProps, ref, computed } from 'vue'
import { useStore } from 'vuex'
const formRef = ref(null)
const store = useStore()
const menuId = store.state.meta.meta?.menuId
const disabledBtn = ref(true)
const docDirRef = ref(null)
const props = defineProps({
  onClose: {
    type: Function
  },
  onSuccess: {
    type: Function
  },
  multiple: {
    type: Boolean,
    default: false
  }
})
const labelDataList = ref([])
const menuTreeProps = {
  label: 'dirName'
}
const data = reactive({
  visible: true,
  title: '上传文件'
})
const formData = reactive({
  file: null,
  selectLabel: null,
  selectDocDir: null
})
const formRules = reactive({
  file: [{ required: true, message: '请选择文件', trigger: 'change' }],
  selectLabel: [{ required: true, message: '请选择文档目录', trigger: 'change' }],
  selectDocDir: [{ required: true, message: '请选择文档标签', trigger: 'change' }]
})
const getLabelValueData = (data) => {
  return data.map((item) => {
    item.label = item.labelName
    item.value = item.id
    if (item.children && item.children.length > 0) {
      item.children = getLabelValueData(item.children)
    }
    return item
  })
}
const onUploadSuccess = (data) => {
  disabledBtn.value = false
  formData.file = data
  formRef.value.validateField('file')
}
//调用获取目录接口
LabelFileApi.listTree().then((res) => {
  labelDataList.value = getLabelValueData(res.data || [])
})
const menuList = ref([])
const getMenuMap = (list) => {
  const map = {}
  list.forEach((item) => {
    map[item.id] = { ...item }
    if (item.children && item.children.length > 0) {
      Object.assign(map, getMenuMap(item.children))
    }
  })
  return map
}
const menuMap = computed(() => getMenuMap(menuList.value || []))
const getDocMenuTree = async () => {
  const res = await LabelFileApi.listMenuTree(menuId)
  menuList.value = res.data || []
}
getDocMenuTree()
const getDataFromMap = (data) => {
  if (Array.isArray(data)) {
    return data.map((id) => menuMap.value[id])
  }
  return menuMap.value[data]
}
function success() {
  if (!formRef) return
  formRef.value.validate((valid) => {
    if (!valid) {
      return
    }
    data.visible = false
    //调用保存接口
    if (typeof props.onSuccess === 'function') {
      props.onSuccess(formData, {
        selectDocDir: getDataFromMap(formData.selectDocDir)
      })
    }
  })
}
</script>
<style lang="scss" scoped>
.rest-label {
  :deep(.ep-form-item__label) {
    padding-top: 10px;
  }
}
</style>
<style lang="scss">
.select-dialog-wrapper {
  .select-dialog {
    .ep-dialog__body {
      max-height: 600px;
    }
  }
}
</style>
