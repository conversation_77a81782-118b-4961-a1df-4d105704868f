<template>
  <div class="container">
    <template v-for="(levelGroup, index) in levelData" :key="levelGroup.level">
      <!-- 层级行 -->
      <div class="row">
        <div
            v-for="item in levelGroup.items"
            :key="item.id || item.position"
            class="card"
            :ref="el => addCardRef(levelGroup.level, el)"
        >
          <div class="card-top">
            {{ item.positionName }}
          </div>
          <div class="card-bottom">
            {{ item.position }}
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup>
import { ref, computed, toRef, defineProps, watchEffect, nextTick } from 'vue';
import store from "@/store";

// 定义组件props
const props = defineProps({
  data: Object,
});

// 表单数据处理
let formData = ref({
  projectCode: "",
  projectName: "",
  orgType: "",
  foundTime: "",
  code: "",
  duty: "",
  projBizSafetyOrgOpsSDtoList: [],
  fileDtoList: [],
  createName: store.state.user && store.state.user.userName ? store.state.user.userName : null,
  createTime: "",
  status: "0",
  updateName: "",
  updateTime: ""
});

// 从props初始化表单数据
if (props.data?.formData && Object.keys(props.data.formData).length > 0) {
  formData = toRef(props.data, 'formData');
}

// 获取层级数据
const data = ref(formData.value['projBizSafetyOrgOpsSDtoList'] || []);

// 动态获取所有存在的层级并排序
const levels = computed(() => {
  if (!data.value || !data.value.length) return [];

  // 提取所有唯一的sort值并转换为数字
  const uniqueLevels = [...new Set(data.value.map(item => {
    const level = parseInt(item.sort);
    return isNaN(level) ? 0 : level;
  }))];

  // 排序并过滤掉非数字值
  return uniqueLevels.filter(level => !isNaN(level)).sort((a, b) => a - b);
});

// 根据层级动态生成数据
const levelData = computed(() => {
  return levels.value.map(level => ({
    level,
    items: data.value.filter(item => {
      const itemLevel = parseInt(item.sort);
      return !isNaN(itemLevel) && itemLevel === level;
    })
  })).filter(group => group.items.length > 0); // 过滤掉没有项目的层级
});

// 卡片引用管理
const cardRefs = ref({});

// 添加卡片引用
const addCardRef = (level, el) => {
  if (!el) return;

  if (!cardRefs.value[level]) {
    cardRefs.value[level] = [];
  }

  // 避免重复添加
  if (!cardRefs.value[level].includes(el)) {
    cardRefs.value[level].push(el);
  }
};

// 获取指定层级的卡片引用
const getLevelCards = (level) => {
  return cardRefs.value[level] || [];
};

// 窗口大小变化时更新连接线
watchEffect(() => {
  const handleResize = () => {
    document.querySelectorAll('.connector-svg').forEach(svg => {
      const path = svg.querySelector('path');
      if (path) {
        const d = path.getAttribute('d');
        path.setAttribute('d', d); // 触发重绘
      }
    });
  };

  window.addEventListener('resize', handleResize);

  return () => {
    window.removeEventListener('resize', handleResize);
  };
});
</script>

<style scoped>
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  gap: 40px; /* 增加层级间间距 */
  position: relative;
}

.row {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  gap: 50px; /* 卡片间距 */
}

.card {
  display: flex;
  flex-direction: column;
  width: 150px;
  border: 1px solid #ccc;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  background-color: white;
  z-index: 1; /* 确保卡片在连接线上方 */
}

.card-top {
  background-color: #4CAF50;
  color: white;
  padding: 10px;
  text-align: center;
  font-weight: bold;
}

.card-bottom {
  background-color: white;
  color: black;
  padding: 10px;
  text-align: center;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .row {
    flex-wrap: wrap;
    gap: 30px;
  }

  .card {
    width: 120px;
  }
}
</style>