/*
 * @Author: 王云飞
 * @Date: 2024-12-20 09:57:02
 * @LastEditTime: 2024-12-20 14:43:53
 * @LastEditors: 王云飞
 * @Description: 
 * 
 */
import { createWebHistory, createRouter } from 'vue-router'

const ctx = require.context("@/", true, /router\/index.js/);
let constantRoutes = [];
for (const key of ctx.keys()) {
  constantRoutes = [...constantRoutes, ...ctx(key).default];
}

export { constantRoutes };

const router = createRouter({
  history: createWebHistory(),
  routes:constantRoutes,
  scrollBehavior: () => ({
    top: 0,
  }),
})

export default router
