<template>
  <div class="project-header">
    <div class="project-name">{{ projectInfo.projectName }}</div>
    <div class="project-meta">
      <span>项目编码：{{ projectInfo.projCode }}</span>
      <span>项目经理：{{ projectInfo.projectManager }}</span>
    </div>
  </div>
  <div class="project-dashboard">
    <!-- 右侧导航栏 -->
    <div class="sidebar-nav">
      <el-menu mode="vertical" :default-active="activeTab.toString()" @select="scrollToSection" class="sidebar-menu">
        <el-menu-item v-for="(tab, index) in tabs" :key="index" :index="index.toString()">
          {{ tab.label }}
        </el-menu-item>
      </el-menu>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
      <!-- 概览卡片 //===============================================================================-->
      <section :ref="(el) => setSectionRef(el, 0)" div class="progress-card">
        <div class="card-header">
          <span class="title">概览</span>
        </div>
        <div class="card-body">
          <el-steps :active="activeStep" finish-status="success" align-center>
            <el-step v-for="(step, index) in steps" :key="index" :title="step.title" :status="step.status" />
          </el-steps>
        </div>
      </section>

      <!-- 设计卡片 //===============================================================================-->

      <section :ref="(el) => setSectionRef(el, 1)" class="design-card">
        <div class="card-header">
          <span class="title">设计</span>
        </div>
        <div class="card-body">
          <div class="design-container">
            <!-- 表格部分 -->
            <div class="design-table">
              <sn-crud :data="designlistData" :option="designoption" @on-load="getView()">
                <template #fileName="scope">
                  <el-link type="primary" @click="toPreview(scope.row)">
                    {{ scope.row.fileName }}
                  </el-link>
                </template>
              </sn-crud>
            </div>

            <!-- 分隔竖线 -->
            <div class="design-divider"></div>

            <!-- 圆圈指示器部分 -->
            <div class="design-indicators">
              <div class="indicator-item">
                <div class="indicator-label">施工图图纸</div>

                <div class="indicator-circle blue"></div>
                <div class="indicator-value">{{ designStats.total }}</div>
              </div>
              <div class="indicator-item">
                <div class="indicator-label">已提交施工图</div>
                <div class="indicator-circle green"></div>

                <div class="indicator-value">{{ designStats.reviewed }}</div>
              </div>
              <div class="indicator-item">
                <div class="indicator-label">已延期施工图</div>
                <div class="indicator-circle red"></div>

                <div class="indicator-value">{{ designStats.delayed }}</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 进度卡片 //===============================================================================-->
      <section :ref="(el) => setSectionRef(el, 2)" class="card">
        <div class="card-header">
          <span class="title">进度</span>
        </div>
        <div class="progress-comparison">
          <!-- 计划进度条 -->
          <div class="progress-item">
            <div class="progress-title">计划完成百分比</div>
            <el-progress :percentage="projectPhasem.planCompletePercent" :stroke-width="16" :text-inside="true" status="primary" class="custom-progress" />
          </div>

          <!-- 实际进度条 -->
          <div class="progress-item">
            <div class="progress-title">实际完成百分比</div>
            <el-progress :percentage="projectPhasem.actualCompletePercent" :stroke-width="16" :text-inside="true" status="success" class="custom-progress" />
          </div>
        </div>
        <div class="progress-table">
          <sn-crud :data="progresslistData" :option="progressoption" @on-load="getProjectPhasem">
            <template #status="{ row }">
              {{ row.sourceActualEndDate ? '已完成' : '未完成' }}
            </template>
          </sn-crud>
        </div>
      </section>

      <!-- 费控卡片 //===============================================================================-->
      <section :ref="(el) => setSectionRef(el, 3)" class="card">
        <div class="card-header">
          <span class="title">费控</span>
        </div>
        <div class="card-body">
          <sn-crud :data="costlistData" :option="costoption" @on-load="getmanufacturingList">
            <!-- 自定义设备购置费列 -->
            <template #equipmentPurchaseCost="{ row }">
              <span>
                {{ row.equipmentPurchaseCost }}
                <span v-if="row.equipmentPurchaseCostTrend === 1 || row.equipmentPurchaseCostTrend === 2" :style="{ color: row.equipmentPurchaseCostTrend === 1 ? 'red' : 'green' }">
                  {{ row.equipmentPurchaseCostTrend === 1 ? '↑' : '↓' }}
                </span>
              </span>
            </template>

            <!-- 建安工程费 - 材料费 -->
            <template #materialCostValue="{ row }">
              <span>
                {{ row.materialCostValue }}
                <span v-if="row.materialCostValueTrend === 1 || row.materialCostValueTrend === 2" :style="{ color: row.materialCostValueTrend === 1 ? 'red' : 'green' }">
                  {{ row.materialCostValueTrend === 1 ? '↑' : '↓' }}
                </span>
              </span>
            </template>

            <!-- 建安工程费 - 安装费 -->
            <template #installationCost="{ row }">
              <span>
                {{ row.installationCost }}
                <span v-if="row.installationCostTrend === 1 || row.installationCostTrend === 2" :style="{ color: row.installationCostTrend === 1 ? 'red' : 'green' }">
                  {{ row.installationCostTrend === 1 ? '↑' : '↓' }}
                </span>
              </span>
            </template>

            <!-- 建安工程费 - 建筑工程费 -->
            <template #constructionCost="{ row }">
              <span>
                {{ row.constructionCost }}
                <span v-if="row.constructionCostTrend === 1 || row.constructionCostTrend === 2" :style="{ color: row.constructionCostTrend === 1 ? 'red' : 'green' }">
                  {{ row.constructionCostTrend === 1 ? '↑' : '↓' }}
                </span>
              </span>
            </template>

            <!-- 其他费用-->
            <template #otherCosts="{ row }">
              <span>
                {{ row.otherCosts }}
                <span v-if="row.otherCostsTrend === 1 || row.otherCostsTrend === 2" :style="{ color: row.otherCostsTrend === 1 ? 'red' : 'green' }">
                  {{ row.otherCostsTrend === 1 ? '↑' : '↓' }}
                </span>
              </span>
            </template>

            <!-- 项目总投资-->
            <template #totalInvestment="{ row }">
              <span>
                {{ row.totalInvestment }}
                <span v-if="row.totalInvestmentTrend === 1 || row.totalInvestmentTrend === 2" :style="{ color: row.totalInvestmentTrend === 1 ? 'red' : 'green' }">
                  {{ row.totalInvestmentTrend === 1 ? '↑' : '↓' }}
                </span>
              </span>
            </template>

            <!-- 单位千瓦静态投资-->
            <template #staticInvestmentPerKw="{ row }">
              <span>
                {{ row.staticInvestmentPerKw }}
                <span v-if="row.staticInvestmentPerKwTrend === 1 || row.staticInvestmentPerKwTrend === 2" :style="{ color: row.staticInvestmentPerKwTrend === 1 ? 'red' : 'green' }">
                  {{ row.staticInvestmentPerKwTrend === 1 ? '↑' : '↓' }}
                </span>
              </span>
            </template>

            <!-- 单位千瓦动态投资-->
            <template #dynamicInvestmentPerKw="{ row }">
              <span>
                {{ row.dynamicInvestmentPerKw }}
                <span v-if="row.dynamicInvestmentPerKwTrend === 1 || row.dynamicInvestmentPerKwTrend === 2" :style="{ color: row.dynamicInvestmentPerKwTrend === 1 ? 'red' : 'green' }">
                  {{ row.dynamicInvestmentPerKwTrend === 1 ? '↑' : '↓' }}
                </span>
              </span>
            </template>
          </sn-crud>
        </div>
      </section>

      <!-- 合同卡片 //===============================================================================-->
      <section :ref="(el) => setSectionRef(el, 4)" class="card">
        <div class="card-header">
          <span class="title">合同</span>
        </div>
        <div class="card-body">
          <sn-crud :data="contractlistData" :option="contractoption" v-model:search="queryForm.filter" @on-load="getContractList()"> </sn-crud>
        </div>
      </section>
      <!-- 物资卡片 //===============================================================================-->
      <section :ref="(el) => setSectionRef(el, 5)" class="card">
        <div class="card-header">
          <span class="title">物资</span>
        </div>
        <div class="flow-chart">
          <!-- 每个步骤和箭头的容器 -->
          <div class="stepwz-container" v-for="(step, index) in stepswz" :key="index">
            <!-- 步骤卡片 -->
            <div class="stepwz">
              <div class="stepwz-header">{{ step.name }}</div>
              <div class="stepwz-content">
                <div v-for="(item, i) in step.items" :key="i" class="stepwz-item">
                  <div class="itemwz-title">{{ item.type }}</div>
                  <div class="itemwz-value">{{ item.count }}个</div>
                  <div v-if="item.sub" class="itemwz-sub">{{ item.sub }}</div>
                </div>
              </div>
            </div>
            <!-- 箭头连接线 -->
            <div class="arrow-container" v-if="index < stepswz.length - 1">
              <div class="arrow"></div>
            </div>
          </div>
        </div>
      </section>
      <!-- 质量卡片 //===============================================================================-->
      <section :ref="(el) => setSectionRef(el, 6)" class="card">
        <div class="card-header">
          <span class="title">质量</span>
        </div>
        <div class="card-body">
          <sn-crud :data="qualitylistData" :option="qualityoption" @on-load="getQualityList"> </sn-crud>
        </div>
      </section>
    </div>
  </div>
</template>
<script>
export const routerConfig = [
  {
    menuType: 'C',
    menuName: '项目看板'
  }
]
</script>
<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { defineOptions } from 'vue'
import store from '@/store'
import ProjInfoMApi from '@/project/api/projectInfo/ProjInfoM'
import ProjBoardApi from '@/project/api/projectBoard/proboard.js'
import FileApi from 'sn-base-layout-vue3/packLayout/api/File'
defineOptions({
  name: 'ProjectBoard'
})

// 导航标签数据===============================================================================
//===============================================================================
const tabs = [
  { label: '概览', key: 'overview' },
  { label: '设计', key: 'design' },
  { label: '进度', key: 'progress' },
  { label: '费控', key: 'cost' },
  { label: '合同', key: 'contract' }, // 合同管理
  { label: '物资', key: 'materials' }, // 物资管理
  { label: '质量', key: 'quality' } // 质量管理
]

// 响应式数据
const sectionRefs = ref([])
const activeTab = ref(0)

// 动态设置卡片引用
const setSectionRef = (el, index) => {
  if (el) {
    sectionRefs.value[index] = el
  }
}

// 滚动到指定卡片
const scrollToSection = (index) => {
  activeTab.value = index
  const targetEl = sectionRefs.value[index]

  if (targetEl) {
    targetEl.scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    })
  } else {
    console.warn('未找到对应的卡片元素')
  }
}

// 滚动监听处理
const handleScroll = () => {
  tabs.forEach((_, index) => {
    const el = sectionRefs.value[index]
    if (el) {
      const rect = el.getBoundingClientRect()
      const isInViewport = rect.top <= 100 && rect.bottom >= 100
      if (isInViewport) activeTab.value = index
    }
  })
}

// 概览步骤数据 //===============================================================================
//===============================================================================
const steps = ref([
  { title: '储备', status: 'success' },
  { title: '立项', status: 'success' },
  { title: '复合优选', status: 'success' },
  { title: '投决', status: 'success' },
  { title: '开工批复', status: 'success' },
  { title: '现场开工', status: '' },
  { title: '投产', status: '' },
  { title: '达标', status: '' },
  { title: '竣工', status: '' },
  { title: '关闭', status: '' }
])

// 当前激活步骤（已完成步骤数+1）
const activeStep = ref(5) // 5表示前5个已完成

/* //动态更新进度
  const updateProgress = (completedSteps) => {
    activeStep.value = completedSteps;

    // 更新所有步骤状态
    steps.value.forEach((step, index) => {
      step.status = index < completedSteps ? 'success' : '';
    });
  };

  //3秒后更新进度
  setTimeout(() => {
    updateProgress(7); // 完成前7个步骤
  }, 3000); */

//设计数据//===============================================================================
//===============================================================================

let designlistData = ref([])
const designStats = ref({
  total: 0,
  reviewed: 0,
  delayed: 0
})
let designoption = ref({
  tip: false,
  dialogType: 'dialog',
  border: true,
  index: false,
  stripe: true,
  menu: false,
  header: false,
  //height: 'auto',
  searchSpan: 6,
  searchIcon: true,

  selection: false,
  showTree: false,
  excelBtn: false,
  importBtn: false,
  headerAlign: 'center',
  column: [
    {
      label: '类型',
      overHidden: true,
      prop: 'type',
      align: 'center',
      width: 120
    },
    {
      label: '收口板',
      overHidden: true,
      prop: 'fileName'
    }
  ]
})

function getView() {
  let id = sessionStorage.getItem('projectId')

  ProjBoardApi.view(id).then((resp) => {
    if (!!resp.data) {
      console.log(resp.data)
      designStats.value.total = resp.data.allNum || 0
      designStats.value.reviewed = resp.data.alreadyNum || 0
      designStats.value.delayed = resp.data.overdueNum || 0
      designlistData.value = resp.data.files || []
    }
  })
}

// 预览文件
const toPreview = async (row) => {
  if (!row.fileId) return
  const fileInfo = await FileApi.getFileNameAndUrlById([row.fileId])
  console.log(fileInfo, 'fileInfo')
  //window.open(fileInfo.data.filePreviewUrl, '_blank')
  // 对原始URL进行base64编码
  const encodedUrl = btoa(fileInfo.data.filePreviewUrl)
  // 构建新的预览URL
  const previewUrl = `http://*************:8012/onlinePreview?url=${encodeURIComponent(encodedUrl)}`

  window.open(previewUrl, '_blank')
}
//进度数据//===============================================================================
//===============================================================================

// 进度条
const planProgress = ref(40)
const actualProgress = ref(30)
//表格
let progresslistData = ref([])

let progressoption = ref({
  tip: false,
  dialogType: 'dialog',
  border: true,
  index: false,
  stripe: true,
  menu: false,
  header: false,
  height: '200px',
  searchSpan: 6,
  searchIcon: true,

  selection: false,
  showTree: false,
  excelBtn: false,
  importBtn: false,

  column: [
    {
      label: '里程碑名称',
      overHidden: true,
      prop: 'taskName'
    },
    {
      label: '计划日期',
      overHidden: true,
      prop: 'sourcePlanEndDate'
    },
    {
      label: '实际日期',
      overHidden: true,
      prop: 'sourceActualEndDate'
    },
    {
      label: '状态',
      overHidden: true,
      prop: 'status'
    }
  ]
})
const projectPhasem = ref({})
function getProjectPhasem() {
  let id = sessionStorage.getItem('projectId')

  ProjBoardApi.getProjectPhasem(id).then((resp) => {
    progresslistData = resp.data.milestoneList
    projectPhasem = resp.data
    console.log(progresslistData, 'progresslistData')
  })
}
//费控数据//===============================================================================
//===============================================================================
let costlistData = ref([])

let costoption = ref({
  tip: false,
  dialogType: 'dialog',
  border: true,
  index: false,
  stripe: true,
  menu: false,
  header: false,
  headerAlign: 'center',
  height: 'auto',
  searchSpan: 6,
  searchIcon: true,
  align: 'center',
  selection: false,
  showTree: false,
  excelBtn: false,
  importBtn: false,

  column: [
    {
      label: '',
      prop: 'budgetType'
    },
    {
      label: '设备购置费',
      prop: 'equipmentPurchaseCost'
    },

    {
      label: '建安工程费',
      children: [
        {
          label: '材料费',
          prop: 'materialCostValue'
        },
        {
          label: '安装费',
          prop: 'installationCost'
        },
        {
          label: '建筑工程费',
          prop: 'constructionCost'
        }
      ]
    },
    {
      label: '其他费用',
      prop: 'otherCosts'
    },
    {
      label: '项目总投资',
      prop: 'totalInvestment'
    },
    {
      label: '静态投资',
      prop: 'staticInvestment'
    },
    {
      label: '单位千瓦静态投资',
      prop: 'staticInvestmentPerKw'
    },
    {
      label: '单位千瓦动态投资',
      prop: 'dynamicInvestmentPerKw'
    }
  ]
})

function getmanufacturingList() {
  let id = sessionStorage.getItem('projectId')

  ProjBoardApi.getmanufacturingList(id).then((resp) => {
    if (!!resp.data) {
      costlistData.value = resp.data
      // 确保 costlistData.value 至少有 2 项数据才能比较
      if (costlistData.value.length >= 2) {
        // 遍历从第 2 项开始（索引 1）
        for (let i = 1; i < costlistData.value.length; i++) {
          const current = costlistData.value[i]
          const previous = costlistData.value[0]

          // 定义需要比较的字段
          const costFields = ['equipmentPurchaseCost', 'installationCost', 'constructionCost', 'otherCosts', 'totalInvestment', 'staticInvestmentPerKw', 'dynamicInvestmentPerKw']
          costlistData.value.forEach((item) => {
            // 3.1 新增 materialCostValue 和 staticInvestment，默认值 0
            item.materialCostValue = 0
            item.staticInvestment = 0

            // 3.2 遍历 costFields，如果值为空则赋值为 0
            costFields.forEach((field) => {
              if (item[field] == null || item[field] === '') {
                item[field] = 0
              }
            })
          })

          // 遍历每个成本字段
          costFields.forEach((field) => {
            if (current[field] !== undefined && previous[field] !== undefined) {
              // 如果当前值 > 前一项值，趋势为 1（增大）
              if (current[field] * 1 > previous[field] * 1) {
                current[`${field}Trend`] = 1
              }
              // 如果当前值 < 前一项值，趋势为 2（减小）
              else if (current[field] * 1 < previous[field] * 1) {
                current[`${field}Trend`] = 2
              }
              // 如果相等，可以忽略或设为 0（可选）
              else {
                current[`${field}Trend`] = 0 // 可选，表示无变化
              }
            }
          })
        }
      }
    }
  })
}
//合同数据//===============================================================================
//===============================================================================

let contractoption = ref({
  tip: false,
  dialogType: 'dialog',
  border: true,
  index: true,
  stripe: true,
  menu: false,
  header: false,
  height: 'auto',
  searchSpan: 6,
  searchIcon: true,

  selection: false,
  showTree: false,
  excelBtn: false,
  importBtn: false,

  column: [
    {
      label: '合同类型',
      overHidden: true,
      prop: 'categoryName',
      dictDatas: 'htlb',
      type: 'select',
      dicUrl: '/system/dict/data/type/htlb',
      dicMethod: 'get',
      props: {
        label: 'dictLabel',
        value: 'dictValue'
      },
      width: 150
    },
    {
      label: '合同编号',
      overHidden: true,
      prop: 'contractNumber'
    },

    {
      label: '合同名称',
      overHidden: true,
      prop: 'contractName'
    },
    {
      label: '供应商',
      overHidden: true,
      prop: 'performer',
      width: 150
    },

    {
      label: '合同金额',
      overHidden: true,
      prop: 'originAmount',
      width: 120
    },
    {
      label: '实际支付金额',
      overHidden: true,
      prop: 'actualPaymentAmount',
      width: 120
    },
    {
      label: '付款进度',
      overHidden: true,
      prop: 'progress',
      width: 80
    }
  ]
})
const contractPage = ref({
  total: 0,
  currentPage: 1,
  pageSize: 5
})

const contractlistData = ref([]) // 确保这是响应式的

function getContractList() {
  const id = sessionStorage.getItem('projectId')
  ProjBoardApi.getContractList(id).then((resp) => {
    contractPage.value.total = resp.data.length

    contractlistData.value = resp.data
  })
}

//物资数据//===============================================================================
//===============================================================================
const stepswz = ref([
  {
    name: '需求计划',
    items: [{ type: '需求计划单', count: '0' }]
  },
  {
    name: '采购',
    items: [{ type: '采购订单', count: '0' }]
  },
  {
    name: '设备监造',
    items: [
      { type: '监造设备', count: '0' },
      { type: '已具备发货条件', count: '0' }
    ]
  },
  {
    name: '项目现场验收',
    items: [
      { type: '乙供材入场报审单', count: '0' },
      { type: '甲供材现场验收', count: '0' },
      { type: '验收不通过物资', count: '0' }
    ]
  },
  {
    name: '项目现场领料',
    items: [
      { type: '领料单', count: '0' },
      { type: '退料单', count: '0' }
    ]
  },
  {
    name: '代保管库盘点',
    items: [
      { type: '乙供材', count: '0' },
      { type: '甲供材', count: '0' }
    ]
  }
])

const arrows = ref([1, 1, 1, 1, 1]) // 5个箭头

function getmaterialList() {
  let id = sessionStorage.getItem('projectId')

  ProjBoardApi.getmaterialList(id).then((resp) => {
    // 遍历 resp.data 中的每个步骤
    resp.data.forEach((respStep) => {
      // 在 stepswz 中找到对应 name 的步骤
      const step = stepswz.value.find((s) => s.name === respStep.name)
      if (step) {
        // 遍历 respStep 中的每个 item
        respStep.items.forEach((respItem) => {
          // 在当前步骤的 items 中找到对应 type 的 item
          const item = step.items.find((i) => i.type === respItem.type)
          if (item) {
            // 更新 count 值
            item.count = respItem.count
          }
        })
      }
    })
  })
}

//质量数据//===============================================================================
//===============================================================================
let qualitylistData = ref([])

let qualityoption = ref({
  tip: false,
  dialogType: 'dialog',
  border: true,
  index: true,
  stripe: true,
  menu: false,
  header: false,
  height: 'auto',
  searchSpan: 6,
  searchIcon: true,

  selection: false,
  showTree: false,
  excelBtn: false,
  importBtn: false,

  column: [
    {
      label: '单位/子单位工程',
      overHidden: true,
      prop: 'name'
    },
    {
      label: '质检点数',
      overHidden: true,
      prop: 'qualityInspectionPoints'
    },
    {
      label: '已检点数',
      overHidden: true,
      prop: 'verifiedPoints'
    },
    {
      label: '合格数',
      overHidden: true,
      prop: 'qualifiedQuantity'
    },
    {
      label: '合格率',
      overHidden: true,
      prop: 'passRate'
    }
    /*    {
      label: '验收状态',
      overHidden: true,
      prop: 'isKeyIndicator'
    } */
  ]
})

function getQualityList() {
  let id = sessionStorage.getItem('projectId')
  ProjBoardApi.getlistQualityProjectSum(id).then((resp) => {
    qualitylistData.value = resp.data
  })
}

let queryForm = ref({
  page: {
    pageSize: 5,
    pageNum: 1,
    total: 0
  },
  filter: {
    createTime: null
  }
})
let formRules = ref({})

function getPageList() {
  //查询分页列表
  // 查询前处理参数
  const params = handleQueryForm()

  I

  queryForm.value.page.total = 10
}

function handleQueryForm() {
  // 处理参数
  const { pageSize, pageNum } = queryForm.value.page
  const filter = {}
  for (let key in queryForm.value.filter) {
    if (queryForm.value.filter[key]?.length) {
      // sn-curd搜索select会生成$prop的变量
      if (key.startsWith('$')) continue
      filter[key] = queryForm.value.filter[key]
    }
  }
  delete filter.createTime
  if (Array.isArray(queryForm.value.filter.createTime) && queryForm.value.filter.createTime?.length === 2) {
    filter['beginCreateTime'] = queryForm.value.filter.createTime[0]
    filter['endCreateTime'] = queryForm.value.filter.createTime[1]
  }
  const searchParams = {
    page: {
      pageSize,
      pageNum
    },
    filter
  }
  return searchParams
}

let projectInfo = ref({
  projCode: '',
  projPhase: '',
  projName: '',
  projectManager: ''
})

function getProjectInfo() {
  let id = sessionStorage.getItem('projectId')

  ProjInfoMApi.view(id).then((resp) => {
    if (!!resp.data) {
      console.log(resp.data)
      projectInfo.value.projectName = resp.data.projName || '-'
      projectInfo.value.projCode = resp.data.projCode || '-'
      projectInfo.value.projPhase = resp.data.projPhase || '-'
      projectInfo.value.projectManager = resp.data.projectManager || '-'
    }
  })
}
// 生命周期钩子
onMounted(() => {
  window.addEventListener('scroll', handleScroll)

  // 初始化时确保第一个卡片可见
  /*  if (sectionRefs.value[0]) {
    sectionRefs.value[0].scrollIntoView({ block: 'start' })
  } */
  getProjectInfo()
  getView()
  getmaterialList()
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped>
/*=========================顶部=======================================================================*/
/* 项目信息容器样式 */
.project-header {
  top: 0; /* 固定在顶部 */
  width: 100%; /* 使其宽度充满整个屏幕 */
  z-index: 1000; /* 确保其在其他内容之上 */
  padding: 16px 20px; /* 内边距 */

  background-color: #ffffff; /* 白色背景 */
  margin-bottom: 16px; /* 与下方内容的间距 */
}

/* 项目名称样式 */
.project-name {
  font-size: 20px; /* 较大字号 */
  font-weight: 600; /* 中等加粗 */
  color: #000; /* 黑色文字 */
  margin-bottom: 8px; /* 与下方项目的间距 */
  line-height: 1.4; /* 行高 */
}

/* 项目编码和项目经理样式 */
.project-meta {
  color: #000; /* 黑色文字 */
  line-height: 1.5; /* 行高 */
}

/* 项目编码和项目经理之间的间隔 */
.project-meta span {
  margin-right: 20px; /* 右侧间距 */
}

/* 响应式调整 */
@media (max-width: 768px) {
  .project-header {
    padding: 12px 16px;
  }

  .project-name {
    font-size: 16px;
  }
}
/*=========================下部=======================================================================*/
.project-dashboard {
  position: relative;
  margin-top: 0;
}

.content-area {
  margin-right: 100px; /* 留出侧边栏空间 */
}
/*=========================卡片样式=======================================================================*/
.card {
  background-color: #fff;

  padding: 20px;
  margin-bottom: 20px;
}

.progress-card {
  background-color: #fff;

  padding: 20px;
  margin-bottom: 20px;
}
.design-card {
  background-color: #fff;

  padding: 20px;
  margin-bottom: 20px;
}

.card-header {
  margin-bottom: 20px;
}

.title {
  font-weight: 600;
  color: #000;
}
/*=========================概览step=======================================================================*/
/* 调整步骤条样式 */
:deep(.ep-steps) {
  padding: 0 20px;
}

/* 已完成步骤的样式 */
:deep(.ep-step__head.is-success) {
  color: #67c23a;
  border-color: #67c23a;
}

:deep(.ep-step__title.is-success) {
  color: #67c23a;
}
/* 进行中步骤的样式 */
:deep(.ep-step__head.is-process) {
  color: #67c23a;
  border-color: #67c23a;
}

:deep(.ep-step__title.is-process) {
  color: #67c23a;
}

/* 未完成步骤的样式 */
:deep(.ep-step__head) {
  color: #c0c4cc;
  border-color: #c0c4cc;
}

:deep(.ep-step__title) {
  color: #606266;
}

/* 连接线样式 */
:deep(.ep-step__line) {
  background-color: #c0c4cc;
  top: 15px;
}

/*=========================设计卡片样式=======================================================================*/
/* 设计卡片容器 */
.design-container {
  display: flex;
  gap: 20px;
}

/* 表格部分 */
.design-table {
  flex: 1;
  width: 60%;
}

/* 分隔竖线 */
.design-divider {
  width: 1px;
  background-color: #e8e8e8;
  margin: 0 10px;
}

/* 指示器容器 - 水平排列 */
.design-indicators {
  display: flex;
  gap: 30px; /* 调整圆圈之间的间距 */
  align-items: center;
  padding: 10px 0;
}

/* 单个指示器项 */
.indicator-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px; /* 调整标签和圆圈之间的间距 */
  width: 100px; /* 固定宽度确保对齐 */
}

/* 彩色圆圈 - 确保数字居中 */
.indicator-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  margin: 0 auto; /* 水平居中 */
}

.blue {
  background-color: #409eff;
  box-shadow: 0 0 10px rgba(64, 158, 255, 0.5);
}

.green {
  background-color: #67c23a;
  box-shadow: 0 0 10px rgba(103, 194, 58, 0.5);
}

.red {
  background-color: #f56c6c;
  box-shadow: 0 0 10px rgba(245, 108, 108, 0.5);
}

/* 数字样式 - 已经居中，无需额外样式 */
.indicator-value {
  font-size: 18px;
  font-weight: bold;
  color: white;
  position: absolute;
  padding-top: 53px;
}

/* 标签样式 - 圆圈正上方 */
.indicator-label {
  font-size: 14px;
  color: #606266;
  text-align: center;
  white-space: nowrap;
  margin-top: 8px; /* 调整标签和圆圈之间的间距 */
}

/*=========================进度卡片样式=======================================================================*/
.progress-comparison {
  display: flex;
  gap: 30px; /* 两个进度条之间的间距 */
  padding: 20px;
}

.progress-item {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.progress-table {
  min-height: 200px;
}

.progress-title {
  font-size: 14px;
  color: #000;
  margin-bottom: 8px;
  font-weight: 500;
  margin-right: 20px;
}

.custom-progress {
  width: 60%;
  margin-bottom: 4px;
}

/* 自定义进度条样式 */
:deep(.ep-progress-bar__outer) {
  background-color: #f5f5f5 !important; /* 浅灰色背景 */
  border-radius: 8px !important;
}

:deep(.ep-progress-bar__inner) {
  border-radius: 8px !important;
}

/* 计划进度条颜色 */
:deep(.ep-progress--primary .ep-progress-bar__inner) {
  background-color: #409eff !important; /* 蓝色 */
}

/* 实际进度条颜色 */
:deep(.ep-progress--success .ep-progress-bar__inner) {
  background-color: #67c23a !important; /* 绿色 */
}

/*=========================物资数据=======================================================================*/
.flow-chart {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
}

/* 新增：每个步骤和箭头的容器 */
.stepwz-container {
  width: 100%;
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px; /* 每个步骤之间的间距 */
  z-index: 1;
}

.stepwz {
  width: 100%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
  padding: 10px;
}

.stepwz-header {
  font-weight: bold;
  color: white;
  background: #409eff;
  padding: 5px;
  border-radius: 4px;
  margin-bottom: 10px;
}

.stepwz-content {
  font-size: 14px;
}

.stepwz-item {
  padding: 5px;
  border-bottom: 1px solid #eee;
}

.stepwz-item:last-child {
  border-bottom: none;
}

.itemwz-title {
  color: #666;
}

.itemwz-value {
  color: #333;
  font-weight: bold;
}

.itemwz-sub {
  color: #999;
  font-size: 12px;
}

/* 箭头容器，确保箭头在两个步骤中间 */
.arrow-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px; /* 箭头容器的宽度，与箭头宽度一致 */
}

.arrow {
  width: 30px;
  height: 2px;
  background: #ccc;
  position: relative;
  top: 20px;
}

.arrow::after {
  content: '';
  position: absolute;
  right: -5px;
  top: -3px;
  border-top: 5px solid transparent;
  border-bottom: 5px solid transparent;
  border-left: 5px solid #ccc;
}
/*=========================右侧导航栏=======================================================================*/
.sidebar-nav {
  position: fixed;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  background: #ffffff00;

  padding: 10px 0;
  z-index: 100;
}

.sidebar-menu {
  border: none;
}

.ep-menu-item {
  height: 40px;
  background-color: #f1f2f6 !important;
}
/* 激活状态样式 */
.ep-menu-item.is-active {
  background-color: #e8e8e8 !important;
  color: rgb(0, 0, 0) !important;
  border-left: 3px solid #2196f3; /* 添加3px宽的蓝色左侧边框 */
}

/* 左侧竖线 - 所有菜单项都有 */
.ep-menu-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 1px; /* 竖线宽度 */
  background-color: #b9b8b8;
}
</style>
