<template>
  <div>
    <sn-crud
        :data="listData"
        :option="option"
        v-model:page="queryForm.page"
        v-model:search="queryForm.filter"
        @on-load="getPageList"
        @search-change="onChangeSearch"
        @search-reset="onResetSearch"
        @addBtnHandle="onEditData"
        @row-del="onDelData">
      <template #menu="{ row, index, size }">
        <template v-if="row.docStatus === '0' || !row.docStatus">
          <el-button type="primary" :size="size" icon="el-icon-edit" link @click="onEditData(row)">编辑</el-button>
          <el-button type="danger" :size="size" icon="el-icon-delete" link @click="onDelData([row])">删除</el-button>
        </template>


        <template v-else>
          <el-button type="warning" :size="size" icon="el-icon-refresh-left" link @click="onRevoke(row)">撤回</el-button>
          <el-button type="primary" :size="size" icon="el-icon-download" link @click="onDownData(row)">下载</el-button>
        </template>
      </template>
      <template #docStatus="{row}">
      <span :style="{color : row.docStatus && row.docStatus==='1' ? 'green' : 'red'}">
          {{ row.docStatus && row.docStatus === '1' ? '已发布' : '草稿' }}
        </span>
      </template>

      <template #code="{ row }">
        <el-button type="text" @click="onDetailClick(row)">{{ row.code }}</el-button>
      </template>
      <template #header>
        <el-button type="primary" icon="el-icon-plus" style="margin-left: 10px;" @click="onEditData(row)">新增</el-button>
<!--        <el-button type="primary" icon="el-icon-document" @click="onListTemplate">列表模板</el-button>-->
<!--        <el-button type="primary" icon="el-icon-upload" @click="onListImport">列表导入</el-button>-->
        <el-button type="primary" icon="el-icon-download" @click="onExportData">列表导出</el-button>
      </template>

      <template #trainStartTimeSearch = "{ row, size }">
        <el-date-picker
            v-model="queryForm.filter.trainStartTime"
            type="date"
            value-format="YYYY-MM"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
        </el-date-picker>
      </template>

      <template #trainName ="{ row }">
        <el-tooltip class="item" effect="dark" :content="row.trainName" placement="top">
          <div class="text-overflow">{{ row.trainName }}</div>
        </el-tooltip>
      </template>

<!--      <template #trainContentSearch = "{ row, size }">-->
<!--        <div style="width: 122px">{{ row.trainContent }}</div>-->
<!--      </template>-->
<!--      <template #trainName ="{ row }">-->
<!--        <el-tooltip class="item" effect="dark" :content="row.trainName" placement="top">-->
<!--          <div class="text-overflow">{{ row.trainName }}</div>-->
<!--        </el-tooltip>-->
<!--      </template>-->
    </sn-crud>
    <div ref="myRef"></div>
  </div>
</template>

<script>
import ProjBizSafeTrainingMApi from '@/project/api/safeTrain/ProjBizSafeTrainingM.js'
import EditProjBizSafeTrainingM from "./components/EditProjBizSafeTrainingM.vue";
import {getToken, hasPermi} from "sn-base-utils";
import {ref} from "vue";

export const routerConfig = [{
  menuType: "C",
  menuName: "安全培训",
}, {
  menuType: "F",
  menuName: "查看",
  perms: "show",
  api: [ProjBizSafeTrainingMApi.config.pageList],
}, {
  menuType: "F",
  menuName: "新增",
  perms: "add",
  api: [ProjBizSafeTrainingMApi.config.add],
}, {
  menuType: "F",
  menuName: "修改",
  perms: "update",
  api: [ProjBizSafeTrainingMApi.config.update, ProjBizSafeTrainingMApi.config.view],
}, {
  menuType: "F",
  menuName: "删除",
  perms: "del",
  api: [ProjBizSafeTrainingMApi.config.remove],
}];
const myRef = ref(null);
</script>

<script setup>
import {
  ref,
  getCurrentInstance
} from 'vue';
import dayjs from 'dayjs';

const {
  proxy
} = getCurrentInstance()
let option = ref({
  tip: false,
  dialogType: "page",
  border: true,
  index: true,
  stripe: true,
  menu: true,
  header: false,
  height: "auto",
  searchSpan: 6,
  searchIcon: true,
  searchMenuSpan: 6,
  searchIndex: 3,
  selection: true,
  showTree: false,
  excelBtn: false,
  delBtn: false,
  editBtn: false,
  delBtnsText: "批量删除",
  addBtnText: "新增",
  customEditBtnText: "编辑",
  customDelBtnText: "删除",
  customViewBtnText: "查看",
  column: [
    {
      label: "业务编号",
      prop: "code",
      columnSlot: true,
      overHidden: true
    },
    {
      label: "培训名称",
      prop: "trainName",
      search: true,
      columnSlot: true,
      overHidden: true,
    },
    {
      label: "培训负责人",
      prop: "trainPeopleCharge",
      columnSlot: true,
    }, {
      label: "培训单位",
      prop: "trainUnit",
      search: true,
      columnSlot: true,
      overHidden: true
    },
    {
      label: "培训要求及内容",
      prop: "trainContent",
      searchSlot: true,
      search: true,
      columnSlot: true,
      overHidden: true,
      searchLabelWidth: 120
    },
    {
      label: "培训时间",
      prop: "trainStartTime",
      columnSlot: true,
      search: true,
    },
    {
      label: "培训结束时间",
      prop: "trainEndTime",
      columnSlot: true,
      width: 130,
    }, {
      label: "发布状态",
      prop: "docStatus",
      columnSlot: false,
      searchSlot: false,
    }, {
    label: "培训人数",
    prop: "trainPeopleNumber",
    columnSlot: true,
  }]
});
let listData = ref([]);
let queryForm = ref({
  page: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  filter: {
    createTime: null
  }
});
let formRules = ref({});
// 根据文档状态动态添加编辑按钮


function getPageList() {
  //查询分页列表
  // 查询前处理参数
  const params = handleQueryForm();
  ProjBizSafeTrainingMApi.pageList(params).then((res) => {
    listData.value = res.data.dataList;
    listData.value.forEach(item => {
      if (item.trainStartTime !== null) {
        item.trainStartTime = dayjs(item.trainStartTime).format('YYYY-MM-DD')
      }
      if (item.trainEndTime !== null) {
        item.trainEndTime = dayjs(item.trainEndTime).format('YYYY-MM-DD')
      }

    })
    queryForm.value.page.total = res.data.totalCount;
  });
}

async function onDetailClick(row) {
  //编辑,新增按钮操作
  let editType = "show";
  let rowInfo = await (editType !== "add" ? ProjBizSafeTrainingMApi.view(row.id) : {});
  const formData = editType !== "add" ? rowInfo.data : rowInfo;
  // 根据文档状态动态添加编辑按钮
  let extendButtons = []
  // 首先检查是否需要添加“编辑”按钮
  if (row.docStatus === '0') {
    extendButtons.push({
      key: 'edit',
      text: '编辑',
      icon: 'el-icon-edit',
      buttonType: 'primary',
      click: () => {
        res.close();
        onEditData(row);
      },
    });
  }
// 然后添加“关闭”按钮
  extendButtons.push({
    key: 'close',
    text: '关闭',
    icon: 'el-icon-close',
    buttonType: '',
  });
  //编辑,新增按钮操作
  proxy.$DialogForm.show({
    title: editType === "show" ? "查看" : "编辑",
    type: option.value.dialogType,
    el: myRef.value,
    width: "80%",
    content: EditProjBizSafeTrainingM,
    data: {
      formData: formData,
      type: editType,
      id: row ? row.id : null,
    },
    option: {
      submitBtn: false,
      emptyBtn: false,
      emptyText: '取消',
      extendButton: extendButtons
    },
    callback: (res) => {
      if (res.type === 'edit'){
        // 当文档状态为草稿时， 显示编辑按钮
        onEditData(row);
        res.close();
      }
      else if (res.type && res.type !== 'close') {
        if (res.dialogRefs) {
          res.dialogRefs.submitData(res.type).then((flag) => {
            if (flag) {
              getPageList();
              res.close();
            }
          });
        }
      }

      else {
        res.close();
      }
    }
  });

  // 动态添加编辑按钮
  if (row.docStatus === '0') {
    proxy.$DialogForm.extendButton.push({
      key: 'edit',
      text: '编辑',
      icon: 'el-icon-edit',
      buttonType: 'primary',
      click: () => {
        onEditData(row);
      },
    });
  }
}

function onExportData() {
  const params = handleQueryForm();
  let queryForm = JSON.parse(JSON.stringify(params));
  proxy.download("/project/safeTrain/export", queryForm.filter, '安全培训.xlsx');
}

function onListImport() {
}

function onResetSearch() {
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
}

function onListTemplate() {
}

function handleQueryForm() {
  // 处理参数
  const {
    pageSize,
    pageNum
  } = queryForm.value.page;
  const filter = {}
  filter.projectId = sessionStorage.getItem('projectId')
  for (let key in queryForm.value.filter) {
    if (queryForm.value.filter[key]?.length) {
      // sn-curd搜索select会生成$prop的变量
      if (key.startsWith("$")) continue;
      filter[key] = queryForm.value.filter[key];
    }
  }
  delete filter.createTime;
  if (Array.isArray(queryForm.value.filter.createTime) && queryForm.value.filter.createTime?.length === 2) {
    filter["beginCreateTime"] = queryForm.value.filter.createTime[0];
    filter["endCreateTime"] = queryForm.value.filter.createTime[1];
  }
  const searchParams = {
    page: {
      pageSize,
      pageNum,
    },
    filter,
  }
  return searchParams
}

function onRevoke(row) {
  proxy.$modal.confirm("确认撤回该条数据项？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    row.docStatus = '0'
    ProjBizSafeTrainingMApi.update(row).then((res) => {
      proxy.$message.success("已撤回");
      getPageList();
    });
  }).catch(() => {
  });
}

function onDownData(row) {
  // proxy.downloadFiles(fileIds, row.receiptNumber + '-' + timestamp + '.zip');
  proxy.download("/project/safeTrain/exportZip", [row.id], "安全培训"+".zip");
}

function onChangeSearch(params, done) {
  //搜索按钮操作
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
  done && done();
}

async function onEditData(row) {
  //编辑,新增按钮操作
  let editType = row ? "edit" : "add";
  let rowInfo = await (editType !== "add" ? ProjBizSafeTrainingMApi.view(row.id) : {});
  const formData = editType !== "add" ? rowInfo.data : rowInfo;
  //编辑,新增按钮操作
  proxy.$DialogForm.show({
    title: editType == "edit" ? "安全培训详情页" : "安全培训详情页",
    type: option.value.dialogType,
    el: myRef.value,
    width: "80%",
    content: EditProjBizSafeTrainingM,
    data: {
      formData: formData,
      type: editType,
      id:row ? row.id:null,
    },
    option: {
      submitBtn: false,
      emptyBtn: false,
      emptyText: '取消',
      extendButton: [
        {
          key: 'save',
          text: '保存',
          icon: 'el-icon-plus',
          buttonType: 'primary',
        },
        {
          key: 'submit',
          text: '发布',
          icon: 'el-icon-check',
          buttonType: 'primary',
        },
        {
          key: 'close',
          text: '关闭',
          icon: 'el-icon-close',
          buttonType: '',
        },
      ],
    },
    callback: (res) => {
      if (res.type === 'save') {
        if (res.dialogRefs) {
          res.dialogRefs.submitData(res.type).then((flag) => {
            if (flag) {
              getPageList();
            }
          });
        }
      } else if (res.type === 'close' ) {
        // 当点击关闭按钮且处于编辑模式时，弹出确认对话框
        proxy.$confirm('确认关闭？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          res.close();
        }).catch(() => {
          // 用户点击取消，不关闭弹窗
        });
      }else if(res.type === 'submit'){
        if (res.dialogRefs) {
          res.dialogRefs.submitData(res.type).then((flag) => {
            if (flag) {
              getPageList();
              res.close();
            }
          });
        }
      }else {
        res.close();
      }
    /*  if (res.type && res.type !== 'close') {
        if (res.dialogRefs) {
          res.dialogRefs.submitData(res.type).then((flag) => {
            if (flag) {
              getPageList();
              res.close();
            }
          });
        }
      }
      else if (res.type === 'close' && editType === 'edit') {
        // 当点击关闭按钮且处于编辑模式时，弹出确认对话框
        proxy.$confirm('确认关闭？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          res.close();
        }).catch(() => {
          // 用户点击取消，不关闭弹窗
        });
      }
      else if (res.type === 'close' && editType === 'add' && (this.formData !== '' || this.formData !== null)) {
        // 当点击关闭按钮且处于编辑模式时，弹出确认对话框
        proxy.$confirm('确认关闭？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          res.close();
        }).catch(() => {
          // 用户点击取消，不关闭弹窗
        });
      }
      else {
        res.close();
      }*/
    }
  });
}

function onDelData(rows) {
  //删除行
  if (!rows || rows.length == 0) {
    proxy.$message.info("请勾选数据！");
    return false;
  }
  let ids = rows.map((item) => {
    return item.id;
  });
  proxy.$modal.confirm("确认删除数据项？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    ProjBizSafeTrainingMApi.remove(ids).then((res) => {
      proxy.$message.success("已删除");
      getPageList();
    });
  }).catch(() => {
  });
}
</script>

<style lang="scss" scoped>
//文本过长省略号
.text-overflow {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

</style>

