<template>
  <el-form :model="formData" :rules="formRules" ref="formRef" label-width="140px" label-position="right" :disabled="type == 'view'">
    <el-row :gutter="16" :span="24">
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>复工令
          </legend>
          <el-row :gutter="16" :span="24">
            <el-col :span='8'>
              <el-form-item label="工程名称" prop="projectName">
                <el-input v-model="formData.projectName" :readonly="true" type="text" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="申请单编号" prop="relatedResumeApply">
                <el-input v-model="formData.relatedResumeApply" :readonly="true" type="text" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="施工部位/工序" prop="constructionPart">
                <el-input v-model="formData.constructionPart" :readonly="true" type="text" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="复工时间" prop="resumeDate">
                <el-date-picker type="date" v-model="formData.resumeDate" format="YYYY-MM-DD" value-format="YYYY-MM-DD" style="width: 100%;" clearable></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
        </fieldset>
      </el-card>
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>附件信息（请上传复工申请及相关材料）
          </legend>
          <el-row :gutter="16" :span="24"></el-row>
            <project-document-storage-ui-table
                ref="childRef"
                :type="resumeOrder"
                :relevantId="formData.id"
                :isPageSearch="false"
                :isDeleteMinio="false"
                :isHasAi="false"
                @on-add-data="onAddData"
                :file-serial-number-builder="fileSerialNumberBuilder"
                :preview-config="previewConfig"
                :isShowAddBtn="type !== 'view'"
                :isShowDelBtn="type !== 'view'"
                :isShowPreviewBtn="true"
                :isShowDownloadBtn="type !== 'view'"
                :isShowLinkBtn="type !== 'view'"
                :isShowDownloadBatchBtn="type !== 'view'"
            ></project-document-storage-ui-table>
        </fieldset>
      </el-card>
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>单据信息
          </legend>
          <el-row :gutter="16" :span="24">
            <el-col :span='8'>
              <el-form-item label="创建人" prop="createName">
                <el-input v-model="formData.createName" :readonly="true" type="text" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="创建时间" prop="createTime">
                <el-date-picker type="datetime" v-model="formData.createTime"  format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%;" clearable></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="单据状态" prop="publishStatus">
                <el-input v-if="type === 'view' || type === 'edit'" :value="getPublishStatusLabel(formData.publishStatus)" readonly />
                <el-select v-else v-model="formData.publishStatus" clearable placeholder="请选择">
                  <el-option v-for="item in publishStatusOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="最近修改人" prop="updateName">
                <el-input v-model="formData.updateName" :readonly="true" type="text" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="最近修改时间" prop="updateTime">
                <el-date-picker type="datetime" v-model="formData.updateTime" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%;"  clearable></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
        </fieldset>
      </el-card>
    </el-row>
  </el-form>
</template>

<script setup>
const setting = require('../../../../config.js')
import store from "@/store";
import {getCurrentFormattedTime} from "@/common/utils/datetime";
import ProjBizResumeOrderMApi from '@/project/api/resumeOrder/ProjBizResumeOrderM.js' 
import {
  nextTick,
  onMounted,
  ref,
  reactive,
  toRef,
  defineProps,
  defineExpose,
  getCurrentInstance
} from 'vue';
import {
  useRoute,
  useRouter
} from 'vue-router'
const {
  proxy
} = getCurrentInstance()
const route = useRoute();
const router = useRouter();
const props = defineProps({
  data: Object,
});
const formRef = ref()
const type = toRef(props.data?.type);
const resumeOrder = ref("resumeOrder");
const childRef = ref(null);
const publishStatusOptions = [
    { label: '草稿', value: 0 },
    { label: '已发布', value: 1 }
  ];
import {
  useDicts
} from "@/common/hooks/useDicts";
const {} = useDicts([])
let formData = ref({
  projectName: "",
  relatedResumeApply: "",
  constructionPart: "",
  resumeDate: "",
  projBizResumeOrderMDtoList: [],
  createName: store.state.user && store.state.user.userInfo.userName ? store.state.user.userInfo.userName : null,
  createTime: getCurrentFormattedTime(),
  publishStatus: 0,
  updateName: "",
  updateTime: "",
  projBizDmStgMDtoList: []
});
let formRules = ref({
  projectName: [],
  relatedResumeApply: [],
  constructionPart: [],
  resumeDate: [],
  createName: [],
  createTime: [],
  publishStatus: [],
  updateName: [],
  updateTime: [],
  projBizDmStgMDtoList: []
});
let delRowData = ref({});
let projBizResumeOrderMOption = ref({
  tip: false,
  border: true,
  index: true,
  stripe: true,
  menu: true,
  header: false,
  menuType: "text",
  addBtn: true,
  addBtnText: "新增",
  editBtn: true,
  editBtnText: "编辑",
  delBtn: true,
  delBtnText: "删除",
  cellBtn: true,
  maxHeight: "200px",
  column: [{
    label: "文件名称",
    prop: "Column1",
    type: "input",
    columnSlot: false,
    searchSlot: false,
    cell: true
  }]
});

function subRowSave(form, done) {
  //编辑行
  done();
}

function subAddRow(name) {
  //新增一行
  if (formData.value[name]) {
    formData.value[name].push({
      $cellEdit: true,
    });
  } else {
    let arr = new Array();
    arr.push({
      $cellEdit: true
    });
    formData.value[name] = arr
  }
}

function subRowUpdate(form, index, done, loading) {
  //编辑行
  done();
}

function subDelRow(row, index, name) {
  //删除行
  if (row[0].id) {
    let data = JSON.parse(JSON.stringify(row[0]));
    if (delRowData.value[name]) {
      delRowData.value[name].push(Object.assign(data, {
        delFlag: 1,
      }));
    } else {
      delRowData.value[name] = [
        Object.assign(data, {
          delFlag: 1,
        }),
      ]
    }
  }
  formData.value[name].splice(index, 1);
}

const getAttachmentList = () => {
  // 假设子组件有 getListData 方法
  return childRef.value?.getListData() || [];
};


function getFormData() {
  // 保证publishStatus默认为0（仅在新增时）
  if (type.value === "add" && (formData.value.publishStatus === undefined || formData.value.publishStatus === "" || formData.value.publishStatus === null)) {
    formData.value.publishStatus = 0;
  }
  //获取formData数据
  for (let item in delRowData.value) {
    formData.value[item] = formData.value[item].concat(delRowData.value[item]);
  }
  // 获取附件列表并赋值
  formData.value.projBizDmStgMDtoList = getAttachmentList();
  return formData.value;
}
formData = toRef(Object.keys(props.data?.formData || {}).length > 0 ? {
  ...props.data?.formData
} : toRef(formData.value));

function submitData(buttonType) {
  return new Promise((resolve) => {
    formRef.value.validate((flag) => {
      if (!flag) {
        return false;
      } else {
        if (type.value === "add") {
          resolve(saveData(buttonType));
        } else {
          resolve(editData(buttonType));
        }
      }
    });
  });
}

function saveData(buttonType) {
  //新增操作
  const formData = getFormData();
  if(buttonType==='submit'){
    formData.publishStatus = 1
  }
  return ProjBizResumeOrderMApi.add(formData).then(() => {
    proxy.$message.success("新增成功");
    return true;
  });
}

function editData(buttonType) {
  //编辑操作
  const formData = getFormData();
  if(buttonType==='submit'){
    formData.publishStatus = 1
  }
  return ProjBizResumeOrderMApi.update(formData).then(() => {
    proxy.$message.success("修改成功");
    return true;
  });
}

function getPublishStatusLabel(val) {
  const found = publishStatusOptions.find(item => item.value === val || String(item.value) === String(val));
  return found ? found.label : val;
}

// 文件序列号生成函数
function fileSerialNumberBuilder() {
  return "TEST" + Math.floor(Math.random()*10000)
}

const previewConfig = ref({
  // 内置预览返回的替换字符,akey代表要替换的字符串，avalue代码替换的字符串
  substitute: {
    "akey": "avalue"
  },
  // 内置预览服务地址
  previewServerUrl: setting.kkFileViewUrl,
})

defineExpose({
  getFormData,
  submitData,
});
</script>

<style lang="scss" scoped>

</style>
