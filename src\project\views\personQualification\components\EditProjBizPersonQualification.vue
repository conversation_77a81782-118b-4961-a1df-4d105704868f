<template>
  <flow-page-container ref="flowPageContainerRef" @handlerAction="handlerAction" @initData="initData"
    @handlerPrint="handlerPrint" :closeBtn="isShowCloseBtn" :approvalOption="approvalOption"
    @approvalOptionCallback="getApprovalOption">
    <el-form :model="formData" :rules="formRules" ref="formRef" label-width="100px" label-position="right"
      :disabled="type == 'view'">
      <el-row :gutter="16" :span="24">
        <el-card class="box-card" style="width: 100%;">
          <fieldset class="fieldset2">
            <legend>
              <span class="el-button--primary"></span>人员资质报审
            </legend>
            <el-row :gutter="16" :span="24">

              <el-col :span='8'
                v-permi="formPermi({ fieldModelId: 'projBizPersonQualification', field: 'businessCode', fieldName: '业务编码' }, customFormPermi)">
                <el-form-item label="业务编码" prop="businessCode">
                  <el-input v-model="formData.businessCode" type="text" placeholder="请输入" clearable>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span='8'
                v-permi="formPermi({ fieldModelId: 'projBizPersonQualification', field: 'unitType', fieldName: '单位类型' }, customFormPermi)">
                <el-form-item label="单位类型" prop="unitType">
                  <el-select disabled v-model="formData.unitType" clearable placeholder="请选择" aria-readonly="true">
                    <el-option v-for="(item, index) in org_type" :key="index" :label="item.label"
                      :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span='8'
                v-permi="formPermi({ fieldModelId: 'projBizPersonQualification', field: 'contractorName', fieldName: '承包商名称' }, customFormPermi)">
                <el-form-item label="承包商名称" prop="contractorName">
                  <!-- <el-input v-model="formData.contractorName" type="text" placeholder="请输入" clearable>
                  </el-input> -->

                  <el-select v-model="formData.unitQualificationId" @change="handleChange" clearable placeholder="请选择">
                    <el-option v-for="(item, index) in dropdownOptions" :key="index" :label="item.label"
                      :value="item.value"></el-option>
                  </el-select>


                </el-form-item>
              </el-col>
              <el-col :span='8'
                v-permi="formPermi({ fieldModelId: 'projBizPersonQualification', field: 'batchNumber', fieldName: '批次号' }, customFormPermi)">
                <el-form-item label="批次号" prop="batchNumber">
                  <el-input v-model="formData.batchNumber" type="text" placeholder="请输入" clearable>
                  </el-input>
                </el-form-item>
              </el-col>

            </el-row>
          </fieldset>
        </el-card>
        <el-card class="box-card" style="width: 100%;">
          <fieldset class="fieldset2">
            <legend>
              <span class="el-button--primary"></span>人员信息
            </legend>
            <el-row :gutter="16" :span="24">
              <el-col :span="24" style="text-align: right;">
                <!-- <el-button type="primary" plain @click="handleImport">导入</el-button> -->
                <el-button type="success" plain @click="downloadPeronData">导出</el-button>
                <!-- <el-button type="info" plain @click="handleExportTemplate">导出模板</el-button> -->
              </el-col>
              <sn-crud :data="formData.projBizPersonQualificationPersonDtoList"
                :option="projBizPersonQualificationPersonOption" @row-save="subRowSave" @row-update="subRowUpdate"
                @row-del="(row, index) => { subDelRow(row, index, 'projBizPersonQualificationPersonDtoList'); }">
                <template #empty>
                  <!-- <div>无数据</div> -->
                </template>
                <template #action="{ row, index }">
                  <el-button @click="subEditRow(row, index)" type="text">编辑</el-button>
                  <el-button @click="subDelRow([row], index, 'projBizPersonQualificationPersonDtoList')"
                    type="text">删除</el-button>
                </template>
                <!-- <template slot="personRoleForm" slot-scope="{ row }">
                  <div v-permi="formPermi({fieldModelId: 'projBizPersonQualificationPerson', field:'personRole', fieldName: '人员角色'}, customFormPermi)">
                    <el-input v-model="row.personRole"></el-input>
                  </div>
                </template> -->
                <!-- <template slot="fullNameForm" slot-scope="{ row }">
                  <div v-permi="formPermi({fieldModelId: 'projBizPersonQualificationPerson', field:'fullName', fieldName: '姓名'}, customFormPermi)">
                    <el-input v-model="row.fullName"></el-input>
                  </div>
                </template>
                <template slot="idCardForm" slot-scope="{ row }">
                  <div v-permi="formPermi({fieldModelId: 'projBizPersonQualificationPerson', field:'idCard', fieldName: '身份证号'}, customFormPermi)">
                    <el-input v-model="row.idCard"></el-input>
                  </div>
                </template>
                <template slot="ageForm" slot-scope="{ row }">
                  <div v-permi="formPermi({fieldModelId: 'projBizPersonQualificationPerson', field:'age', fieldName: '年龄'}, customFormPermi)">
                    <el-input v-model="row.age"></el-input>
                  </div>
                </template>
                <template slot="contactForm" slot-scope="{ row }">
                  <div v-permi="formPermi({fieldModelId: 'projBizPersonQualificationPerson', field:'contact', fieldName: '联系方式'}, customFormPermi)">
                    <el-input v-model="row.contact"></el-input>
                  </div>
                </template>
                <template slot="entryTimeForm" slot-scope="{ row }">
                  <div v-permi="formPermi({fieldModelId: 'projBizPersonQualificationPerson', field:'entryTime', fieldName: '入场时间'}, customFormPermi)">
                    <el-input v-model="row.entryTime"></el-input>
                  </div>
                </template>
                <template slot="safetyEducationForm" slot-scope="{ row }">
                  <div v-permi="formPermi({fieldModelId: 'projBizPersonQualificationPerson', field:'safetyEducation', fieldName: '是否安全交底'}, customFormPermi)">
                    <el-input v-model="row.safetyEducation"></el-input>
                  </div>
                </template>
                <template slot="positionForm" slot-scope="{ row }">
                  <div v-permi="formPermi({fieldModelId: 'projBizPersonQualificationPerson', field:'position', fieldName: '岗位'}, customFormPermi)">
                    <el-input v-model="row.position"></el-input>
                  </div>
                </template>
                <template slot="examResultForm" slot-scope="{ row }">
                  <div v-permi="formPermi({fieldModelId: 'projBizPersonQualificationPerson', field:'examResult', fieldName: '考试情况'}, customFormPermi)">
                    <el-input v-model="row.examResult"></el-input>
                  </div>
                </template> -->
                <template #entryTime="{ row }">
                  <el-tooltip class="item" effect="dark" :content="row.entryTime" placement="top">
                    <div class="text-overflow">{{ row.entryTime }}</div>
                  </el-tooltip>
                </template>
                <template #deptQualificationName="{ row }">
                  <el-tooltip class="item" effect="dark" :content="row.deptQualificationName" placement="top">
                    <div class="text-overflow">{{ row.deptQualificationName }}</div>
                  </el-tooltip>
                </template>


              </sn-crud>
              <el-button @click="subAddRow('projBizPersonQualificationPersonDtoList')" type="primary" plain
                style="display: block; width: 100%; margin-top: 10px">新增</el-button>
            </el-row>
          </fieldset>
        </el-card>
        <el-card class="box-card" style="width: 100%;">
          <fieldset class="fieldset2">
            <legend>
              <span class="el-button--primary"></span>附件信息
            </legend>
            <el-row :gutter="16" :span="24">

              <project-document-storage-ui-table ref="childRef" :type="fileType" :relevantId="formData.id"
                :isPageSearch="false" :isDeleteMinio="false" :isHasAi="false" @on-add-data="onAddData"
                :file-serial-number-builder="fileSerialNumberBuilder" :preview-config="previewConfig"
                :isShowAddBtn="true" :isShowDelBtn="true" :isShowPreviewBtn="true" :isShowDownloadBtn="true"
                :isShowLinkBtn="false" :isShowDownloadBatchBtn="true">
              </project-document-storage-ui-table>
            </el-row>
          </fieldset>
        </el-card>
        <el-card class="box-card" style="width: 100%;">
          <fieldset class="fieldset2">
            <legend>
              <span class="el-button--primary"></span>单据信息
            </legend>
            <el-row :gutter="16" :span="24">
              <el-col :span='8'
                v-permi="formPermi({ fieldModelId: 'projBizPersonQualification', field: 'createName', fieldName: '创建人' }, customFormPermi)">
                <el-form-item label="创建人" prop="createName">
                  <el-input v-model="formData.createName" disabled :readonly="true" type="text" placeholder="请输入"
                    clearable>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span='8'
                v-permi="formPermi({ fieldModelId: 'projBizPersonQualification', field: 'createTime', fieldName: '创建时间' }, customFormPermi)">
                <el-form-item label="创建时间" prop="createTime">
                  <el-input v-model="formData.createTime" type="text" placeholder="" :disabled="true" clearable>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span='8'
                v-permi="formPermi({ fieldModelId: 'projBizPersonQualification', field: 'state', fieldName: '审批状态' }, customFormPermi)">
                <el-form-item label="审批状态" prop="state">
                  <el-select v-model="formData.state" :disabled="true" clearable placeholder="请选择">
                    <el-option v-for="(item, index) in global_biz_flow_status" :disabled="true" :key="index"
                      :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span='8'
                v-permi="formPermi({ fieldModelId: 'projBizPersonQualification', field: 'updateName', fieldName: '最近修改人' }, customFormPermi)">
                <el-form-item label="最近修改人" prop="updateName">
                  <el-input disabled v-model="formData.updateName" :disabled="true" type="text" placeholder=""
                    clearable>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span='8'
                v-permi="formPermi({ fieldModelId: 'projBizPersonQualification', field: 'updateTime', fieldName: '最近修改时间' }, customFormPermi)">
                <el-form-item label="最近修改时间" prop="updateTime">
                  <el-input v-model="formData.updateTime" type="text" placeholder="" :disabled="true" clearable>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </fieldset>
        </el-card>
        <!-- <el-table v-permi="formPermi({fieldModelId:'projBizPersonQualification', field:'processId', fieldName: '流程id'}, customFormPermi)" :data="approveList" style="width: 100%">
          <el-table-column type="index" width="50" />
          <el-table-column label="任务名称" prop="taskName" show-overflow-tooltip />
          <el-table-column label="审批人" prop="auditorName" show-overflow-tooltip />
          <el-table-column label="操作" prop="statusVal" show-overflow-tooltip />
          <el-table-column label="任务接收时间" prop="createTime" show-overflow-tooltip />
          <el-table-column label="审批时间" prop="completeTime" show-overflow-tooltip />
          <el-table-column label="审批意见" prop="opinion" show-overflow-tooltip />
        </el-table> -->
      </el-row>
    </el-form>
  </flow-page-container>
</template>

<script setup>
import ProjBizPersonQualificationApi from '@/project/api/personQualification/ProjBizPersonQualification.js'
import FlowPageContainer from '@/common/components/exportFile/FlowPageContainer.vue'
import { getCurrentFormattedTime } from "@/common/utils/datetime";
import BpmTaskApi from '@/project/api/bpm/bpmTask'
import { btnHandle } from '@/project/components/hooks/buttonChangeName'

import EditPeople from './EditPeople.vue'
const res = ref()
// import FileAddForm from '../../unitQualification/components/FileAddForm.vue'
import ProjInfoMApi from '@/project/api/projectInfo/ProjInfoM.js'
import { dayjs } from 'element-plus';
import store from "@/store";
import ProjBizUnitQualificationApi from '@/project/api/unitQualification/ProjBizUnitQualification.js'
let auth = new Map();
const flowPageContainerRef = ref();
const taskInfo = ref()
import {
  nextTick,
  onMounted,
  ref,
  reactive,
  toRef,
  defineProps,
  defineExpose,
  getCurrentInstance
} from 'vue';
import {
  useRoute,
  useRouter
} from 'vue-router'
const {
  proxy
} = getCurrentInstance()
const route = useRoute();
const router = useRouter();
const props = defineProps({
  data: Object,
});
let option = ref({
  dialogType: "page"

});
const formRef = ref()
const fileType = ref('personQualification');
const dropdownOptions = ref([]);
const isShowCloseBtn = ref(false);

const FlowActionType = ref(proxy.FlowActionType);
let approvalOption = props.data?.approvalOption ? props.data?.approvalOption : route.query?.procInstId ? ref({
  isShowApprovalList: true,
  isShowFlowDiagram: true,
  procInstId: route.query?.procInstId
}) : ref({
  isShowApprovalList: false,
  isShowFlowDiagram: false,
  procInstId: ''
});
const {
  sys_yn,
  global_biz_flow_status
} = useDicts(["sys_yn", "global_biz_flow_status"])
const state = reactive({
  flowData: {
    businessKey: "",
    procInstId: "",
    procDefKey: "",
    taskId: "",
    formConfig: [],
    variableList: [],
    customPropertyConfigList: []
  },
  formAuth: {},
  taskFlag: 1,
  firstAct: false,
  handlerClose: null,
  rootFields: {},
  fullFieldNameMap: {},
  formAuthList: {}
})

const {
  positionType
} = useDicts(["positionType"])

const formatSize = (bytes) => {
  if (bytes === 0) return '0 B';
  const units = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return `${(bytes / Math.pow(1024, i)).toFixed(2)} ${units[i]}`;
};
const type = toRef(props.data?.type);
import {
  useDicts
} from "@/common/hooks/useDicts";
const {
  org_type
} = useDicts(["org_type"])
let formData = ref({
  projectCode: "",
  projectName: "",
  projectUnit: "",
  unitType: "",
  projectUnitName: "",
  projectParentUnitName: "",
  unitQualificationId: "",
  inspectedUnitId: "",
  inspectedUnitName: "",
  inspectedUnitParentNames: "",
  contractorName: "",
  batchNumber: "",
  processInstanceId: "",
  businessCode: "",
  // projBizPersonQualificationPersonDtoList: [{
  //   $cellEdit: true
  // }],
  projBizPersonQualificationPersonDtoList: [],
  // projBizDmStgMDtoList: [{
  //   $cellEdit: true
  // }],
  projBizDmStgMDtoList: [],
  createName: "",
  createTime: "",
  state: "1",
  updateName: "",
  updateTime: ""
});

const childRef = ref(null);
let formRules = ref({
  projectCode: [],
  projectName: [],
  projectUnit: [],




  businessCode: [
    { required: true, message: '业务编号不能为空', trigger: 'blur' }
  ],
  batchNumber: [
    { required: true, message: '批次号不能为空', trigger: 'blur' }
  ],
  contractorName: [
    { required: true, message: '承包商名称不能为空', trigger: 'blur' }
  ],
  unitType: [
    { required: true, message: '请选择单位类型', trigger: 'change' }
  ],

  businessCode: [],
  createName: [],
  createTime: [],
  state: [],
  updateName: [],
  updateTime: []
});
let delRowData = ref({});
let projBizPersonQualificationPersonOption = ref({
  tip: false,
  border: true,
  index: true,
  stripe: true,
  menu: false,
  header: false,
  menuType: "text",
  addBtn: true,
  addBtnText: "新增",
  editBtn: false,
  editBtnText: "编辑",
  delBtn: false,
  delBtnText: "删除",
  cellBtn: false,
  maxHeight: "200px",
  column: [{
    label: "人员角色",
    prop: "personRole",
    type: "input",
    cell: true
  }, {
    label: "姓名",
    prop: "fullName",
    type: "input",
    cell: true
  }, {
    label: "身份证号",
    prop: "idCard",
    type: "input",
    cell: true
  }, {
    label: "年龄",
    prop: "age",
    type: "input",
    cell: true
  }, {
    label: "联系方式",
    prop: "contact",
    type: "input",
    cell: true
  },
  {
    label: "入场年龄",
    prop: "entryAge",
    type: "input",
    cell: true
  },
  {
    label: "部门",
    prop: "deptQualificationName",
    type: "input",
    cell: true
  },
  {
    label: "是否安全交底",
    prop: "safetyEducation",
    type: "select",
    dicData: [{ label: '否', value: "0" },
    { label: '是', value: "1" }],
    cell: true
  },
  {
    label: "岗位",
    prop: "position",
    type: "select",
    cell: true,
    dicData: positionType
  },
  {
    label: "入场时间",
    prop: "entryTime",
    type: "date",
    formatter: (row, column, value) => {
      // 使用dayjs进行时间格式化，确保value存在
      if (!value) return '';
      // 默认格式为YYYY-MM-DD HH:mm:ss，您可以根据需要修改
      return dayjs(value).format('YYYY-MM-DD HH:mm:ss');
    },
    cell: true
  }, {
    label: "考试情况",
    prop: "examResult",
    type: "input",
    cell: true
  }, {
    label: "操作",
    prop: "action",
    width: "150px",
    cell: true,
    slot: true // 启用插槽自定义内容
  }]
});
let projBizDmStgMOption = ref({
  tip: false,
  border: true,
  index: true,
  stripe: true,
  menu: true,
  header: false,
  menuType: "text",
  addBtn: true,
  addBtnText: "新增",
  editBtn: true,
  editBtnText: "编辑",
  delBtn: true,
  delBtnText: "删除",
  cellBtn: true,
  maxHeight: "200px",
  column: [{
    label: "文件名称",
    prop: "fileName",
    type: "input",
    cell: true
  },
  {
    label: "文件大小",
    prop: "fileSize",
    type: "input",
    cell: true
  }, {
    label: "上传人",
    prop: "createName",
    type: "input",
    cell: true
  }, {
    label: "上传时间",
    prop: "createTime",
    type: "input",
    cell: true
  }]
});
let getHtFlowDrawUrl = ref("");
let approveList = ref([]);

function subRowSave(form, done) {
  //编辑行
  // done();
}

/**
 * 下载文件
 * @param row
 */
function handleExportTemplate() {
  proxy.download("/project/personQualification/exportTemplate", {}, "导出模板.xlsx");
}

function subAddRow() {

  proxy.$DialogForm.show({
    title: "新增人员",
    type: "dialog",
    width: "80%",
    content: EditPeople,
    data: { unitQualificationId: formData.value.unitQualificationId },
    option: {
      submitBtn: false,
      emptyBtn: false,
      submitText: '保存',
      emptyText: '取消',
      extendButton: [{
        key: 'cancel',
        text: '取消',
        buttonType: '',
        icon: 'el-icon-close',
      }, {
        key: 'save',
        text: '保存',
        buttonType: 'primary',
        icon: 'el-icon-check',
      }],
    },
    callback: (res) => {
      if (res.type === 'save') {  // 确保是点击"保存"按钮才添加数据
        const newPersonData = res.dialogRefs.getFormData();
        if (newPersonData) {
          // 确保 `projBizPersonQualificationPersonDtoList` 是数组
          if (!Array.isArray(formData.value.projBizPersonQualificationPersonDtoList)) {
            formData.value.projBizPersonQualificationPersonDtoList = [];
          }
          // 添加新数据，并标记为可编辑
          formData.value.projBizPersonQualificationPersonDtoList.push({
            ...newPersonData,
            // $cellEdit: true  // 确保行可编辑
          });
        }
      }
      res.close();
    },
  });
}


function subEditRow(row, index) {
  proxy.$DialogForm.show({
    title: "编辑人员",
    width: "80%",
    content: EditPeople,
    data: {
      ...row, // 将当前行数据传入弹窗
      unitQualificationId: formData.value.unitQualificationId
    },
    option: {
      extendButton: [
        { key: 'cancel', text: '取消', icon: 'el-icon-close' },
        { key: 'save', text: '保存', buttonType: 'primary', icon: 'el-icon-check' }
      ]
    },
    callback: (res) => {
      if (res.type === 'save') {
        const updatedData = res.dialogRefs.getFormData();
        // 更新对应行数据
        formData.value.projBizPersonQualificationPersonDtoList.splice(index, 1, updatedData);
      }
      res.close();
    }
  });
}
const previewConfig = ref({
  // 是否外置预览,必传
  isExternalPreview: false,
  // 内置预览返回的替换字符,akey代表要替换的字符串，avalue代码替换的字符串
  substitute: {
    "akey": "avalue"
  },
  // 内置预览服务地址
  previewServerUrl: "http://10.191.64.191:8012/onlinePreview",
})
function fileSerialNumberBuilder() {
  return "inspection" + Math.floor(Math.random() * 10000);
}
function getListData() {
  if (childRef.value) {
    let list = childRef.value.getListData()
    formData.value.projBizDmStgMDtoList = list

  }
}
/**
 * 下载文件
 * @param row
 */
function onDownData(row) {
  proxy.download("/project/unitQualification/exportFile", [row.id], row.fileName);
}
function downloadPeronData() {
  proxy.download('/project/personQualification/downloadPeronData?baseId=' + formData.value.id, {}, '人员数据.xlsx')
}
function handleChange(value) {
  // value为选中的承包商ID（dropdownOptions中选项的value）
  if (!value) {
    // 若未选择承包商，清空单位类型
    formData.value.unitType = "";
    return;
  }
  console.log("承包商", value)
  formData.value.unitQualificationId = value;

  // 在下拉选项中找到匹配的承包商
  const selectedContractor = dropdownOptions.value.find(
    item => item.value === value
  );
  formData.value.contractorName = selectedContractor.label;
  // 若找到匹配项，回显单位类型
  if (selectedContractor) {
    formData.value.unitType = selectedContractor.unitType;

  } else {
    // 未找到匹配项时清空
    formData.value.unitType = "";
  }
}
function subRowUpdate(form, index, done, loading) {



}

function showSelectOrgDialog(e) {
  let checkData = [];
  if (e.target.value) {
    checkData.push({
      id: formData.value.inspectedUnitId,
      orgName: e.target.value,
      parentNames: formData.value.inspectedUnitParentNames
    })
  }
  proxy.$SelectOrg({
    checkData: checkData,
    multiple: false,
    onSuccess(res) {
      if (res.data.length > 0) {
        let orgInfo = res.data[0]
        formData.value.inspectedUnitId = orgInfo.id
        formData.value.inspectedUnitName = orgInfo.orgName
        formData.value.inspectedUnitParentNames = orgInfo.parentNames
        formData.value.projectUnit = orgInfo.id
        formData.value.projectUnitName = orgInfo.orgName
        formData.value.projectParentUnitName = orgInfo.parentNames
      }
    }
  })
}
// 生命周期钩子
// onMounted(() => {
//   window.addEventListener('scroll', handleScroll)
// getUnitPageList();
// })
function getUnitPageList() {
  //查询分页列表
  // 查询前处理参数
  // const params = handleQueryForm();
  const params = ref({});
  //审批完成  同一个projectId 
  params.state = 6
  params.projectId = sessionStorage.getItem('projectId')
  params.treeType = 'UNIT'
  ProjBizUnitQualificationApi.list(params).then((res) => {

    if (
      res &&
      res.data &&
      Array.isArray(res.data) &&
      res.data.length > 0
    ) {
      dropdownOptions.value = res.data.map(item => ({
        label: item.contractorName, // 使用名称或ID作为显示文本
        value: item.id,   // 使用ID或编码作为值
        unitType: item.unitType
      }));

    } else {
      dropdownOptions.value = [];

      // errorMessage.value = '暂无可用数据';
    }

  });
}
function getTaskInfo() {
  return taskInfo.value
}
function subDelRow(row, index, name) {
  //删除行
  if (row[0].id) {
    let data = JSON.parse(JSON.stringify(row[0]));
    if (delRowData.value[name]) {
      delRowData.value[name].push(Object.assign(data, {
        delFlag: 1,
      }));
    } else {
      delRowData.value[name] = [
        Object.assign(data, {
          delFlag: 1,
        }),
      ]
    }
  }
  formData.value[name].splice(index, 1);
}


function getProjectInfo() {
  ProjInfoMApi.view(sessionStorage.getItem('projectId')).then((resp) => {

    if (!!resp.data) {

      if (resp.data.projCode?.trim()) {
        // 覆盖：null、undefined、""、"   "
        formData.value.projectCode = resp.data.projCode
      } else {
        formData.value.projectCode = '默认项目编号'
      }
      if (resp.data.projName?.trim()) {
        // 覆盖：null、undefined、""、"   "

        formData.value.projectName = resp.data.projName
      } else {
        formData.value.projectName = '默认项目名称'
      }
      if (resp.data.projOrg?.trim()) {
        // 覆盖：null、undefined、""、"   "
        formData.value.projectUnit = resp.data.projOrg

      } else {
        formData.value.projectUnit = '默认项目单位'
      }
    }
  })
}
function getFormData() {
  //获取formData数据
  for (let item in delRowData.value) {
    formData.value[item] = formData.value[item].concat(delRowData.value[item]);
  }
  getListData()
  formData.value.projectId = sessionStorage.getItem('projectId')
  return formData.value;
}
formData = toRef(Object.keys(props.data?.formData || {}).length > 0 ? {
  ...props.data?.formData
} : toRef(formData.value));

function processAuthFieldForm(fieldList) {
  /**
   * 工作流表单权限功能：在扫描完成之前，业务可对表单字段进行修改。
   */
  //TODO 这里可以对权限表单的字段进行任意的修改,如果不满足业务需求，可以在这里手动添加你想要的字段
  return fieldList
};

function getApprovalOption(fun) {
  /**
   * 工作流表单权限功能(重要)：应对弹窗无法及时相应的问题，特别增加该函数修复该bug
   */
  nextTick(() => {
    fun(approvalOption.value)
  })
};

function customFormPermi(obj) {
  /**
   * 工作流表单权限功能：自定义表单权限处理函数，将对应的对象的属性还原成对应的权限值
   */
  let {
    field,
    fieldModelId
  } = obj
  let fullFieldName = `${fieldModelId}_${field}`
  //TODO 这里可以自定义处理，默认返回0即可
  return auth.get(fullFieldName) || 0
};

function handlerPrint(taskComment) {
  /**
   * 工作流打印模板功能
   */
  ProjBizPersonQualificationApi.printTemplate({
    id: formData.value.id,
    templateId: taskComment.templateId,
    formData: formData.value
  }).then(
    (res) => {
      router.push({
        name: "PrintDoc",
        query: {
          fileId: res.data,
        },
      });
      taskComment.close && taskComment.close();
    });
};

function getSubmitTask(taskActionDto) {
  /**
   * 工作流提交任务功能
   */
  return ProjBizPersonQualificationApi.submitTask({
    busiDto: formData.value,
    taskActionDto: taskActionDto
  });
};

//打开确认框
function handlerOpenConfirm(taskInfo, resp) {
  ProjBizUnitQualificationApi.view(taskInfo.businessKey).then((resp) => {
    if (resp.data) {
      formData.value = resp.data;
      relevantId.value = resp.data.id;
      // isShowAddBtn.value = true;
      // isShowDelBtn.value = true;
    }
  })
  res.value = resp;
  flowPageContainerRef.value.handlerActionSubmit(taskInfo, 1);
}
function getStartFlow(formData, startProcDto) {


  return new Promise((resolve) => {
    formRef.value.validate((flag) => {

      if (!flag) {
        return false
      } else {

        resolve(
          ProjBizPersonQualificationApi.startFlow({
            busiData: formData,
            startProcDto: startProcDto
          }).then((respon) => {
            state.flowData.procDefKey = respon.data[0].procDefKey
            state.flowData.procInstId = respon.data[0].procInstId
            state.flowData.businessKey = respon.data[0].businessKey
            state.flowData.taskId = respon.data[0].taskId
         formData.processInstanceId =  respon.data[0].procInstId
            taskInfo.value = respon.data[0];
            nextTick(() => {
              btnHandle(props.data.el.lastChild)
            })
          })
        )
      }
    })
  })
};

function handlerAction(operation, taskComment, handlerClose) {
  /**
   * 工作流操作功能
   */
  // 启动并保存草稿后再次提交
  if (
    (operation.type == FlowActionType.value.SAVESTART || operation.type == FlowActionType.value.SAVE || operation.type == FlowActionType.value.START) && !formData.value.taskId) {
    let startProcDto = {
      procDefKey: formData.value.procDefKey,
      businessKey: null,
      clientId: null,
      ...taskComment?.dialogRefs?.getFormData(),
    };
    let httpCall = null;
    if (operation.type == FlowActionType.value.SAVESTART) {
      httpCall = getStartFlow(formData, startProcDto);
    } else if (operation.type == FlowActionType.value.SAVE) {
      httpCall = getSaveFormData(formData);
    }
    httpCall.then(() => {
      proxy.$modal.msgSuccess("提交成功");
      taskComment.close && taskComment.close();
      handlerClose();
    });
  } else {
    operation.type == FlowActionType.value.START || operation.type == FlowActionType.value.SAVESTART ? (operation.type = FlowActionType.value.AGREE) : operation.type;
    let taskActionDto = {
      taskId: state.flowData.taskId,
      procInstId: state.flowData.procInstId,
      actionType: operation.type,
      ...taskComment?.dialogRefs?.getFormData(),
      taskAssignees: taskComment?.dialogRefs?.getFormData().taskAssignees?.length ? taskComment.dialogRefs.getFormData().taskAssignees.join(',') : ''

    };
    getSubmitTask(taskActionDto).then(() => {
      proxy.$modal.msgSuccess("任务办理成功");
      taskComment.close && taskComment.close();
      let businessKey = route.query.businessKey

            if (res) {
        if (businessKey) {
          handlerClose()
        } else {
          props.data.closeDialog(proxy)
        }
      } else {
        handlerClose()
      }
    }).catch(() => {
      taskComment.close && taskComment.close();
    });
  }
};

function initBusiForm() {
  /**
   * 工作流初始化表单功能
   */
  let that = proxy
  let routerQueryParams = route.query;
  auth = new Map(Object.entries(state.formAuth));
  if (routerQueryParams) {
    if (routerQueryParams.busiData) {
      formData.value = routerQueryParams.busiData;
    } else {
      if (routerQueryParams.businessKey) {
        let businessKey = route.query.businessKey;
        ProjBizPersonQualificationApi.view(businessKey).then(resp => {
          formData.value = resp.data;
        });
      } else {
        that.$message.error("初始化失败,因为工作流未将流程信息传入！")
      }
    }
  }
};

function getSaveFormData(formData) {
  if (formData.id) {
    return ProjBizPersonQualificationApi.update(formData);
  } else {

    return ProjBizPersonQualificationApi.add(formData).then((res) => {


      formData.id = res.data.id


    });
  }
};

function getFieldForm() {
  let rootFields = {
    ...state.rootFields
  }
  let fieldList = []
  let keys = []
  for (let key in rootFields) {
    keys.push(key)
    fieldList.push(rootFields[key])
  }
  const result = processAuthFieldForm ? processAuthFieldForm(fieldList) : fieldList;
  const message = {
    type: "getTemplateRoot",
    pathname: window.location.pathname,
    actKey: getQueryParams("actKey"),
    content: result
  };
  window.parent.postMessage(JSON.parse(JSON.stringify(message)), "*");
};

function getQueryParams(key) {
  let url = window.location.href;
  // 使用正则表达式解析URL中的查询字符串
  var queryString = url.split('?')[1];
  if (!queryString) {
    return {};
  }
  var params = {};
  // 分割查询字符串成单个参数
  var vars = queryString.split("&");
  for (var i = 0; i < vars.length; i++) {
    var pair = vars[i].split("=");
    params[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);
  }
  return params[key];
};

function initData(urlParams, taskInfo, handlerClose) {
  // 如果用户从流程设计点击启动, 如果是在线表单, 则阻止执行initData, 防止在线表单报错
  if (urlParams.menuId && urlParams.openInStart) {
    return
  }
  state.flowData.procDefKey = taskInfo.procDefKey;
  state.flowData.procInstId = urlParams.procInstId;
  state.flowData.businessKey = urlParams.businessKey;
  state.flowData.taskId = urlParams.taskId;
  state.flowData.fiedPermission = taskInfo.fiedPermission;
  state.flowData.variableList = taskInfo.variableList;
  state.taskFlag = urlParams.taskFlag;
  state.firstAct = taskInfo.firstAct;
  state.handlerClose = handlerClose;
  state.flowData.customPropertyConfigList = taskInfo.customPropertyConfigList
  let fieldPerList = taskInfo.formConfig?.formFieldConfig?.fieldPerList || []
  handleFormAuth(fieldPerList);
  initBusiForm();
};

function handleFormAuth(data) {
  let formAuth = {};
  for (let item of data) {
    let permi = 1;
    if (item.readonly) {
      permi = 2;
    }
    if (item.hidden) {
      permi = 3;
    }
    if (!isCamelCase(item.fieldModelId)) {
      item.fieldModelId = toCamelCase(item.fieldModelId)
    }
    formAuth = {
      ...formAuth,
      [`${item.fieldModelId}_${item.field}`]: permi,
    };
  }
  state.formAuth = formAuth;
  state.formAuthList = JSON.parse(JSON.stringify(formAuth))
};
/**
 * 处理表单权限相关操作, 开发者在template的v-permi设置中必须使用此函数
 * @param {Object} obj - 包含字段名称的对象
 * @param {Function} [callback] - 可选的回调函数，接收 obj 作为参数
 * 如果传入对象的 fieldName 不在 rootFields 中，则将其添加到 rootFields 数组，然后执行回调函数（如果存在）
 */
function formPermi(obj, callback) {
  if (!state.rootFields[obj.fieldModelId + '__' + obj.field]) {
    state.rootFields[obj.fieldModelId + '__' + obj.field] = obj
  }
  return callback && callback(obj)
};

function toCamelCase(s) {
  return s.toLowerCase().replace(/_(.)/g, function (match, group1) {
    return group1.toUpperCase();
  });
};

function isCamelCase(str) {
  return /^[a-z][a-zA-Z0-9]*$/.test(str)
};
defineExpose({
  getFormData,
  submitFlowTask,
  getSaveFormData,
  getStartFlow,
  getTaskInfo,
  handlerOpenConfirm,
});
function submitFlowTask(resp) {
  res.value = resp;
  return proxy.$confirm('确定提交当前单据?', '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
      const formDatas = getFormData();
      return BpmTaskApi.listRuTaskByProcInstId({
        procInstId: formDatas.processInstanceId
      }).then((params) => {
        flowPageContainerRef.value.handlerActionSubmit(params.data[0], 1);
      })
    }).catch(() => {
      return true;
    })
}
function initializer() {

  if (type.value === 'add') {


    formData.value.createName = store.state.user && store.state.user.userInfo.userName ? store.state.user.userInfo.userName : null;//获取到登陆人
    formData.value.createTime = getCurrentFormattedTime();//获取到登录时间
    // formData.value.state = 0
  }
  //判断是否为查看进来，是则禁用输入框及按钮
  // console.log('getPageList.type==',type.value)
  // if(type.value==='show'){
  //   dialogdisabled = true
  // }
}
onMounted(() => {
  nextTick(() => {
    getFieldForm()
  })
  initializer()
  if (type.value === "add") {
    getProjectInfo();
  }

  getUnitPageList()
})
</script>


<style lang="scss" scoped>
//文本过长省略号
.text-overflow {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.bgW {
  height: 100%;
}
</style>
