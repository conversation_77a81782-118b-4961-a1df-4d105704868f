import { request, replaceUrl } from "sn-base-utils";

export default class ProjBizDrillPlanApi {
    static config = {
        add: {
            url: '/project/drillExecute/add',
            method: 'POST'
        },
        remove: {
            url: '/project/drillExecute/delete',
            method: 'DELETE'
        },
        update: {
            url: '/project/drillExecute/update',
            method: 'PUT'
        },
        view: {
            url: '/project/drillExecute/get/{id}',
            method: 'GET'
        },
        pageList: {
            url: '/project/drillExecute/page',
            method: "POST"
        },
        list: {
            url: '/project/drillExecute/list',
            method: "POST"
        },downloadZip: {
            url: '/project/drillExecute/downloadZip',
            method: "POST"
        }
    };

    /**
     * 新增演练计划执行
     * @param data
     * @returns {*}
     */
    static add(data) {
        return request({
            url: this.config.add.url,
            method: this.config.add.method,
            data: data
        });
    }

    /**
     * 删除演练计划执行
     * @param data
     * @returns {*}
     */
    static remove(data) {
        return request({
            url: this.config.remove.url,
            method: this.config.remove.method,
            data: data
        });
    }

    /**
     * 更新演练计划执行
     * @param data
     * @returns {*}
     */
    static update(data) {
        return request({
            url: this.config.update.url,
            method: this.config.update.method,
            data: data
        });
    }

    /**
     * 查询演练计划执行详细
     * @param id
     * @returns {*}
     */
    static view(id) {
        return request({
            url: replaceUrl(this.config.view.url, { id }),
            method: this.config.view.method
        });
    }

    /**
     * 分页查询演练计划执行列表
     * @param data
     * @returns {*}
     */
    static pageList(data) {
        return request({
            url: this.config.pageList.url,
            method: this.config.pageList.method,
            data: data
        });
    }

    /**
     * 全部演练计划执行列表
     * @returns {*}
     */
    static list(data) {
        return request({
            url: this.config.list.url,
            method: this.config.list.method,
            data: data
        });
    }
}
