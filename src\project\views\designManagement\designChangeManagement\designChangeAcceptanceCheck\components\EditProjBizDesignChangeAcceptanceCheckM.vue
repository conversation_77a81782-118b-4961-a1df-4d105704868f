<template>
  <flow-page-container ref="flowPageContainerRef" @handlerAction="handlerAction" @initData="initData" @handlerPrint="handlerPrint" :closeBtn="isShowCloseBtn" :approvalOption="approvalOption" @approvalOptionCallback="getApprovalOption">
    <el-form :model="formData" :rules="rules" ref="formRef" label-width="150px" label-position="right" :disabled="type == 'view'">
      <el-row :gutter="16" :span="24">
        <el-card class="box-card" style="width: 100%;">
          <fieldset class="fieldset2">
            <legend>
              <span class="el-button--primary"></span>主表信息
            </legend>
            <el-row :gutter="16" :span="24">
              <el-col :span='8' v-permi="formPermi({fieldModelId:'projBizDesignChangeAcceptanceCheckM', field:'docCode', fieldName: '变更验收单编号'}, customFormPermi)">
                <el-form-item label="变更验收单编号" prop="docCode">
                  <el-input v-model="formData.docCode" type="text" placeholder="请输入" clearable>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span='8' v-permi="formPermi({fieldModelId:'projBizDesignChangeAcceptanceCheckM', field:'changDesignChangCode', fieldName: '设计变更单编号'}, customFormPermi)">
                <el-form-item label="设计变更单编号" prop="changDesignChangCode">
                  <el-input v-model="formData.changDesignChangCode" type="text" :disabled="true" placeholder="请输入" clearable>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span='8' v-permi="formPermi({fieldModelId:'projBizDesignChangeAcceptanceCheckM', field:'changeType', fieldName: '变更类型'}, customFormPermi)">
                <el-form-item label="变更类型" prop="changeType">
                  <el-select v-model="formData.changeType" clearable :disabled="true" placeholder="请选择">
                    <el-option v-for="(item, index) in changeTypeOption" :key="index" :disabled="true" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span='8' v-permi="formPermi({fieldModelId:'projBizDesignChangeAcceptanceCheckM', field:'volumeNumber', fieldName: '卷册号'}, customFormPermi)">
                <el-form-item label="卷册号" prop="volumeNumber">
                  <el-input v-model="formData.volumeNumber" type="text" :disabled="true" placeholder="请输入" clearable>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span='8' v-permi="formPermi({fieldModelId:'projBizDesignChangeAcceptanceCheckM', field:'reportOrganization', fieldName: '编制单位'}, customFormPermi)">
                <el-form-item label="编制单位" prop="reportOrganization">
                  <el-input v-model="formData.reportOrganization" type="text" :disabled="true" clearable>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span='8' v-permi="formPermi({fieldModelId:'projBizDesignChangeAcceptanceCheckM', field:'volumeName', fieldName: '卷册名称'}, customFormPermi)">
                <el-form-item label="卷册名称" prop="volumeName">
                  <el-input v-model="formData.volumeName" type="text" :disabled="true" placeholder="请输入" clearable>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span='24' v-permi="formPermi({fieldModelId:'projBizDesignChangeAcceptanceCheckM', field:'changeReason', fieldName: '变更原因'}, customFormPermi)">
                <el-form-item label="变更原因" prop="changeReason">
                  <el-input type="textarea" v-model="formData.changeReason" :disabled="true" placeholder="请输入" rows="3" clearable></el-input>
                </el-form-item>
              </el-col>
              <el-col :span='24' v-permi="formPermi({fieldModelId:'projBizDesignChangeAcceptanceCheckM', field:'changeContent', fieldName: '变更内容'}, customFormPermi)">
                <el-form-item label="变更内容" prop="changeContent">
                  <el-input type="textarea" v-model="formData.changeContent" :disabled="true" placeholder="请输入" rows="3" clearable></el-input>
                </el-form-item>
              </el-col>
              <el-col :span='8' v-permi="formPermi({fieldModelId:'projBizDesignChangeAcceptanceCheckM', field:'isCheck', fieldName: '是否验收'}, customFormPermi)">
                <el-form-item label="是否验收" prop="isCheck">
                  <el-radio-group v-model="formData.isCheck">
                    <el-radio v-for="(item, index) in isCheckOption" :key="index" :label="item.value">{{item.label}}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span='8' v-permi="formPermi({fieldModelId:'projBizDesignChangeAcceptanceCheckM', field:'checkTime', fieldName: '验收时间'}, customFormPermi)">
                <el-form-item label="验收时间" prop="checkTime">
                  <el-date-picker v-model="formData.checkTime" type="datetime" format="YYYY-MM-DD HH:mm:ss"
                                  value-format="YYYY-MM-DD HH:mm:ss" placeholder="选择时间" clearable></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span='24' v-permi="formPermi({fieldModelId:'projBizDesignChangeAcceptanceCheckM', field:'remarks', fieldName: '备注'}, customFormPermi)">
                <el-form-item label="备注" prop="remarks">
                  <el-input type="textarea" v-model="formData.remarks" placeholder="请输入" rows="3" clearable></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </fieldset>
        </el-card>
        <el-card class="box-card" style="width: 100%;">
          <fieldset class="fieldset2">
            <legend>
              <span class="el-button--primary"></span>附件信息
            </legend>
            <project-document-storage-ui-table
              ref="childRef"
              :type="fileType"
              :relevantId="relevantId"
              :isPageSearch="false"
              :isDeleteMinio = "isDeleteMinio"
              :isHasAi = "isHasAi"
              :file-serial-number-builder="fileSerialNumberBuilder"
              :preview-config="previewConfig"
            ></project-document-storage-ui-table>
          </fieldset>
        </el-card>
        <el-card class="box-card" style="width: 100%;">
          <fieldset class="fieldset2">
            <legend>
              <span class="el-button--primary"></span>单据信息
            </legend>
            <el-row :gutter="16" :span="24">
              <el-col :span='8' v-permi="formPermi({fieldModelId:'projBizDesignChangeAcceptanceCheckM', field:'createName', fieldName: '创建人'}, customFormPermi)">
                <el-form-item label="创建人" prop="createName">
                  <el-input v-model="formData.createName" :disabled="true" type="text" placeholder="请输入" clearable>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span='8' v-permi="formPermi({fieldModelId:'projBizDesignChangeAcceptanceCheckM', field:'createTime', fieldName: '创建时间'}, customFormPermi)">
                <el-form-item label="创建时间" prop="createTime">
                  <el-input v-model="formData.createTime" :disabled="true" type="text" placeholder="请输入" clearable>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span='8' v-permi="formPermi({fieldModelId:'projBizDesignChangeRequestM', field:'approvalStatus', fieldName: '单据状态'}, customFormPermi)">
                <el-form-item label="单据状态" prop="approvalStatus">
                  <el-select v-model="formData.approvalStatus" clearable placeholder="" :disabled="true" filterable remote>
                    <el-option v-for="(item, index) in global_biz_flow_status" :key="index" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span='8' v-permi="formPermi({fieldModelId:'projBizDesignChangeAcceptanceCheckM', field:'updateName', fieldName: '最近修改人'}, customFormPermi)">
                <el-form-item label="最近修改人" prop="updateName">
                  <el-input v-model="formData.updateName" :disabled="true" type="text" placeholder="请输入" clearable>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span='8' v-permi="formPermi({fieldModelId:'projBizDesignChangeAcceptanceCheckM', field:'updateTime', fieldName: '最近修改时间'}, customFormPermi)">
                <el-form-item label="最近修改时间" prop="updateTime">
                  <el-input v-model="formData.updateTime" :disabled="true" type="text" placeholder="请输入" clearable>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </fieldset>
        </el-card>
      </el-row>
    </el-form>
  </flow-page-container>
</template>

<script setup>
import ProjBizDesignChangeAcceptanceCheckMApi from '@/project/api/designManagement/designChangeAcceptanceCheck/ProjBizDesignChangeAcceptanceCheckM.js'
import FlowPageContainer from '@/common/components/exportFile/FlowPageContainer.vue'
import { kkFileViewUrl } from "@/config";
let auth = new Map();
import {
  nextTick,
  onMounted,
  ref,
  reactive,
  toRef,
  defineProps,
  defineExpose,
  getCurrentInstance
} from 'vue';
import {
  useRoute,
  useRouter
} from 'vue-router'
const {
  proxy
} = getCurrentInstance()
const route = useRoute();
const router = useRouter();
const props = defineProps({
  data: Object,
});
const formRef = ref()
const isShowCloseBtn = ref(false);
const FlowActionType = ref(proxy.FlowActionType);
// 文件组件定义参数
const fileType = ref("designChangeRequest")
const isDeleteMinio = ref(true)
const isShowAddBtn = ref(false); //上传
const isShowDelBtn = ref(false); //下载
const isHasAi = ref(true)
const childRef = ref(null); // 组件实例引⽤
const previewConfig = ref({
  // 是否外置预览,必传
  isExternalPreview: false,
  // 内置预览返回的替换字符,akey代表要替换的字符串，avalue代码替换的字符串
  substitute: {
    "akey":"avalue"
  },
  // 内置预览服务地址
  previewServerUrl: kkFileViewUrl,
})
const relevantId = ref(props.data?.formData?.id ?? null)

let approvalOption = props.data?.approvalOption ? props.data?.approvalOption : route.query?.procInstId ? ref({
  isShowApprovalList: true,
  isShowFlowDiagram: true,
  procInstId: route.query?.procInstId
}) : ref({
  isShowApprovalList: false,
  isShowFlowDiagram: false,
  procInstId: ''
});
const state = reactive({
  flowData: {
    businessKey: "",
    procInstId: "",
    procDefKey: "",
    taskId: "",
    formConfig: [],
    variableList: [],
    customPropertyConfigList: []
  },
  formAuth: {},
  taskFlag: 1,
  firstAct: false,
  handlerClose: null,
  rootFields: {},
  fullFieldNameMap: {},
  formAuthList: {}
})
const type = toRef(props.data?.type);
const title = toRef(props.data?.title);
import {
  useDicts
} from "@/common/hooks/useDicts";
import store from '@/store'
import { getCurrentFormattedTime } from '@/common/utils/datetime'
import BpmTaskApi from '@/project/api/bpm/bpmTask'
import ProjBizDesignChangeDesignMApi from '@/project/api/designManagement/designChangeDesign/ProjBizDesignChangeDesignM'
const {global_biz_flow_status} = useDicts(["global_biz_flow_status"])
let formData = ref({
  docCode: "",
  changDesignChangCode: "",
  changeType: "",
  volumeNumber: "",
  volumeName: "",
  changeReason: "",
  changeContent: "",
  isCheck: "",
  checkTime: "",
  remarks: "",
  createName: store.state.user && store.state.user.userInfo.userName ? store.state.user.userInfo.userName : '-',
  createTime:getCurrentFormattedTime(),
  reportOrganization: JSON.parse(store.state.user.orgName)?.find((item) => item.id === store.state.user.defaultOrg)?.orgName,
  status: '',
  updateName: "",
  updateTime: ""
});
console.log('formData.reportOrganization',formData.reportOrganization)
let rules = ref({
  docCode: [{
    required: true,
    message: "请填写变更验收单编号"
  },
    {
      pattern: /^(?=.*[a-zA-Z])(?=.*\d)[A-Za-z0-9]+$/,
      trigger: ['blur', 'change'],
      message: '变更验收单编号必须包含字母和数字'
    }
    ],
  changDesignChangCode: [],
  changeType: [],
  volumeNumber: [],
  volumeName: [],
  changeReason: [],
  changeContent: [],
  isCheck: [{
    required: true,
    message: "请选择是否验收"
  }],
  checkTime: [{
    required: true,
    message: "请选择验收时间"
  }],
  remarks: [{
    required: true,
    message: "请填写备注"
  }],
  createName: [],
  createTime: [],
  status: [],
  updateName: [],
  updateTime: []
});
let changeTypeOption = ref([{
  label: "选择一",
  value: 1
}, {
  label: "选择二",
  value: 2
}, {
  label: "选择三",
  value: 3
}]);
let isCheckOption = ref([{
  label: "是",
  value: "0"
}, {
  label: "否",
  value: "1"
}]);
formData = toRef(Object.keys(props.data?.formData || {}).length > 0 ? {
  ...props.data?.formData
} : toRef(formData.value));

if(!formData.value.id){
  formData.value.createName= store.state.user && store.state.user.userInfo.userName ? store.state.user.userInfo.userName : '-';
  formData.value.createTime=getCurrentFormattedTime();
  formData.value.reportOrganization= JSON.parse(store.state.user.orgName)?.find((item) => item.id === store.state.user.defaultOrg)?.orgName;
}

function getFormData() {
  return formData.value
};

function processAuthFieldForm(fieldList) {
  /**
   * 工作流表单权限功能：在扫描完成之前，业务可对表单字段进行修改。
   */
  //TODO 这里可以对权限表单的字段进行任意的修改,如果不满足业务需求，可以在这里手动添加你想要的字段
  return fieldList
};

function getApprovalOption(fun) {
  /**
   * 工作流表单权限功能(重要)：应对弹窗无法及时相应的问题，特别增加该函数修复该bug
   */
  nextTick(() => {
    fun(approvalOption.value)
  })
};

function customFormPermi(obj) {
  /**
   * 工作流表单权限功能：自定义表单权限处理函数，将对应的对象的属性还原成对应的权限值
   */
  let {
    field,
    fieldModelId
  } = obj
  let fullFieldName = `${fieldModelId}_${field}`
  //TODO 这里可以自定义处理，默认返回0即可
  return auth.get(fullFieldName) || 0
};

function handlerPrint(taskComment) {
  /**
   * 工作流打印模板功能
   */
  ProjBizDesignChangeAcceptanceCheckMApi.printTemplate({
    id: formData.value.id,
    templateId: taskComment.templateId,
    formData: formData.value
  }).then(
    (res) => {
      router.push({
        name: "PrintDoc",
        query: {
          fileId: res.data,
        },
      });
      taskComment.close && taskComment.close();
    });
};

function getSubmitTask(taskActionDto) {
  /**
   * 工作流提交任务功能
   */
  return ProjBizDesignChangeAcceptanceCheckMApi.submitTask({
    busiDto: formData.value,
    taskActionDto: taskActionDto
  });
};

function getStartFlow(formData, startProcDto) {
  return new Promise((resolve) => {
    formRef.value.validate((flag) => {
      if (!flag) {
        return false;
      } else {
        /** 工作流启动流程功能*/
        resolve(ProjBizDesignChangeAcceptanceCheckMApi.startFlow({
          busiData: formData,
          startProcDto: startProcDto
        }));
      }
    })
  })
};

function handlerAction(operation, taskComment, handlerClose) {
  /**
   * 工作流操作功能
   */
  // 启动并保存草稿后再次提交
  if (
    (operation.type == FlowActionType.value.SAVESTART || operation.type == FlowActionType.value.SAVE || operation.type == FlowActionType.value.START) && !formData.value.taskId) {
    let startProcDto = {
      procDefKey: formData.value.procDefKey,
      businessKey: null,
      clientId: null,
      ...taskComment?.dialogRefs?.getFormData(),
    };
    let httpCall = null;
    if (operation.type == FlowActionType.value.SAVESTART) {
      httpCall = getStartFlow(formData, startProcDto);
    } else if (operation.type == FlowActionType.value.SAVE) {
      httpCall = getSaveFormData(formData);
    }
    httpCall.then(() => {
      proxy.$modal.msgSuccess("提交成功");
      taskComment.close && taskComment.close();
      handlerClose();
    });
  } else {
    operation.type == FlowActionType.value.START || operation.type == FlowActionType.value.SAVESTART ? (operation.type = FlowActionType.value.AGREE) : operation.type;
    let taskActionDto = {
      taskId: state.flowData.taskId,
      procInstId: state.flowData.procInstId,
      actionType: operation.type,
      ...taskComment?.dialogRefs?.getFormData(),
    };
    getSubmitTask(taskActionDto).then(() => {
      proxy.$modal.msgSuccess("任务办理成功");
      taskComment.close && taskComment.close();
      handlerClose();
    }).catch(() => {
      taskComment.close && taskComment.close();
    });
  }
};

function submitFlowTask() {
  return proxy.$confirm('确定提交当前单据?', '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
    const formDatas = getFormData();
    return BpmTaskApi.listRuTaskByProcInstId({
      procInstId: formDatas.procInstanceId
    }).then((resp) => {
      if (Array.isArray(resp.data) && resp.data.length > 0) {
        return  ProjBizDesignChangeDesignMApi.submitTask({
          busiDto: formDatas,
          taskActionDto: {
            taskId: resp.data[0].taskId,
            procInstId: formDatas.procInstanceId,
            actionType: "agree",
          }
        })
          .then(() => {
            proxy.$message.success("提交成功");
          });
      } else {
        proxy.$message.success("提交失败，未获取到当前任务id");
      }
    })
  })
    .catch(() => {
      return true;
    })

}

function initBusiForm() {
  /**
   * 工作流初始化表单功能
   */
  let that = proxy
  let routerQueryParams = route.query;
  auth = new Map(Object.entries(state.formAuth));
  if (routerQueryParams) {
    if (routerQueryParams.busiData) {
      formData.value = routerQueryParams.busiData;
    } else {
      if (routerQueryParams.businessKey) {
        let businessKey = route.query.businessKey;
        ProjBizDesignChangeAcceptanceCheckMApi.view(businessKey).then(resp => {
          if (resp.data) {
            formData.value = resp.data;
            relevantId.value = resp.data.id;
            isShowAddBtn.value = true;
            isShowDelBtn.value = true;
          }
        });
      } else {
        that.$message.error("初始化失败,因为工作流未将流程信息传入！")
      }
    }
  }
};

function getSaveFormData(formData) {
  return new Promise((resolve) => {
    formRef.value.validate((flag) => {
      if (!flag) {
        return false;
      } else {
        if (formData.id) {
          resolve(ProjBizDesignChangeAcceptanceCheckMApi.update(formData));
        } else {
          formData.fileList = getListData()
          formData.fileList.map((item) => {
            item.type = fileType.value
          })
          resolve(ProjBizDesignChangeAcceptanceCheckMApi.add(formData));
        }
      }
    });
  });
};

function getFieldForm() {
  let rootFields = {
    ...state.rootFields
  }
  let fieldList = []
  let keys = []
  for (let key in rootFields) {
    keys.push(key)
    fieldList.push(rootFields[key])
  }
  const result = processAuthFieldForm ? processAuthFieldForm(fieldList) : fieldList;
  const message = {
    type: "getTemplateRoot",
    pathname: window.location.pathname,
    actKey: getQueryParams("actKey"),
    content: result
  };
  window.parent.postMessage(JSON.parse(JSON.stringify(message)), "*");

  if(type.value != 'view' && (formData.value.approvalStatus == '1' || !formData.value.approvalStatus )){
    isShowAddBtn.value = true;
    isShowDelBtn.value = true;
  }
};

function getQueryParams(key) {
  let url = window.location.href;
  // 使用正则表达式解析URL中的查询字符串
  var queryString = url.split('?')[1];
  if (!queryString) {
    return {};
  }
  var params = {};
  // 分割查询字符串成单个参数
  var vars = queryString.split("&");
  for (var i = 0; i < vars.length; i++) {
    var pair = vars[i].split("=");
    params[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);
  }
  return params[key];
};

function initData(urlParams, taskInfo, handlerClose) {
  // 如果用户从流程设计点击启动, 如果是在线表单, 则阻止执行initData, 防止在线表单报错
  if (urlParams.menuId && urlParams.openInStart) {
    return
  }
  state.flowData.procDefKey = taskInfo.procDefKey;
  state.flowData.procInstId = urlParams.procInstId;
  state.flowData.businessKey = urlParams.businessKey;
  state.flowData.taskId = urlParams.taskId;
  state.flowData.fiedPermission = taskInfo.fiedPermission;
  state.flowData.variableList = taskInfo.variableList;
  state.taskFlag = urlParams.taskFlag;
  state.firstAct = taskInfo.firstAct;
  state.handlerClose = handlerClose;
  state.flowData.customPropertyConfigList = taskInfo.customPropertyConfigList
  let fieldPerList = taskInfo.formConfig?.formFieldConfig?.fieldPerList || []
  handleFormAuth(fieldPerList);
  initBusiForm();
};

function handleFormAuth(data) {
  let formAuth = {};
  for (let item of data) {
    let permi = 1;
    if (item.readonly) {
      permi = 2;
    }
    if (item.hidden) {
      permi = 3;
    }
    if (!isCamelCase(item.fieldModelId)) {
      item.fieldModelId = toCamelCase(item.fieldModelId)
    }
    formAuth = {
      ...formAuth,
      [`${item.fieldModelId}_${item.field}`]: permi,
    };
  }
  state.formAuth = formAuth;
  state.formAuthList = JSON.parse(JSON.stringify(formAuth))
};
/**
 * 处理表单权限相关操作, 开发者在template的v-permi设置中必须使用此函数
 * @param {Object} obj - 包含字段名称的对象
 * @param {Function} [callback] - 可选的回调函数，接收 obj 作为参数
 * 如果传入对象的 fieldName 不在 rootFields 中，则将其添加到 rootFields 数组，然后执行回调函数（如果存在）
 */
function formPermi(obj, callback) {
  if (!state.rootFields[obj.fieldModelId + '__' + obj.field]) {
    state.rootFields[obj.fieldModelId + '__' + obj.field] = obj
  }
  return callback && callback(obj)
};

function toCamelCase(s) {
  return s.toLowerCase().replace(/_(.)/g, function(match, group1) {
    return group1.toUpperCase();
  });
};

function isCamelCase(str) {
  return /^[a-z][a-zA-Z0-9]*$/.test(str)
};


function getListData() {
  if (childRef.value) {
    return childRef.value.getListData()
  }
}

// 文件序列号生成函数
function fileSerialNumberBuilder() {
  return 'TEST' + Math.floor(Math.random() * 10000)
}

defineExpose({
  getFormData,
  getSaveFormData,
  getStartFlow,
  submitFlowTask
});
onMounted(() => {
  nextTick(() => {
    getFieldForm()
  })
})
</script>

<style lang="scss" scoped>
.bgW {
  height: 100%;
}
</style>