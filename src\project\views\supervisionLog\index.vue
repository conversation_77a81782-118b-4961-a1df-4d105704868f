<template>
  <sn-crud :data="listData" :option="option" v-model:page="queryForm.page" v-model:search="queryForm.filter" @on-load="getPageList" @search-change="onChangeSearch" @search-reset="onResetSearch" @addBtnHandle="onAddRow" @row-del="onDelData" @row-save="onRowSave" @row-update="onRowUpdate">
    <template #menu="{ row, index, size }">
      <!-- 草稿状态显示的按钮 -->
      <template v-if="row.publishStatus !== 1">
        <el-button type="primary" :size="size" icon="el-icon-upload" link @click="onUpload(row, index)">上传</el-button>
        <el-button type="primary" :size="size" icon="el-icon-download" link @click="onDownload(row, index)">下载</el-button>
        <el-button type="primary" :size="size" icon="el-icon-edit" link @click="onEditRow(row, index)">编辑</el-button>
        <el-button type="danger" :size="size" icon="el-icon-delete" link @click="onDelData([row])">删除</el-button>
        <el-button type="primary" :size="size" icon="el-icon-plus"  link @click="onPublish(row, index)">发布</el-button>
      </template>
      <!-- 已发布状态显示的按钮 -->
      <template v-else>
        <el-button type="primary" :size="size" icon="el-icon-download" link @click="onDownload(row, index)">下载</el-button>
        <el-button type="danger" :size="size" icon="el-icon-refresh-left" link @click="onUnpublish(row, index)">撤回</el-button>
      </template>
    </template>
    <template #createTimeSearch = "{ row, size }">
      <el-date-picker
          v-model="queryForm.filter.createTime"
          type="daterange"
          value-format="YYYY-MM-DD"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 100%;"
      />
    </template>
    <template #header>
      <el-button type="primary" icon="el-icon-plus" style="margin-left: 10px;margin-bottom: 10px;" @click="onAddRow">新增</el-button>
      <!-- <el-button type="primary" icon="el-icon-document" style="margin-left: 10px;margin-bottom: 10px;" @click="onListTemplate">列表模板</el-button>
      <el-button type="primary" icon="el-icon-upload" style="margin-left: 10px;margin-bottom: 10px;" @click="onListImport">列表导入</el-button> -->
      <el-button type="primary" icon="el-icon-download" style="margin-left: 10px;margin-bottom: 10px;" @click="onListExport">列表导出</el-button>
    </template>
    <!-- 附件列自定义渲染 -->
    <template #attachment="{ row }">
      <span v-if="row.attachment" class="image-link" @click="onViewAttachment(row)">{{ row.attachment }}</span>
      <span v-else style="color: #ccc;">无</span>
    </template>
    <template #publishStatus="{row}">
      <span :style="{color : row.publishStatus && row.publishStatus===1 ? 'green' : 'red'}">
        {{row.publishStatus && row.publishStatus===1 ? '已发布': '草稿'}}
      </span>
    </template>
    <!--添加如果文本太长展示省略号-->
    <template #businessName="{ row }">
      <el-tooltip class="item" effect="dark" :content="row.businessName" placement="top">
        <div class="text-overflow">{{ row.businessName }}</div>
      </el-tooltip>
    </template>
    <template #supervisionUnit="{ row }">
      <el-tooltip class="item" effect="dark" :content="row.supervisionUnit" placement="top">
        <div class="text-overflow">{{ row.supervisionUnit }}</div>
      </el-tooltip>
    </template>
    <template #createTime="{ row }">
      <span>{{ row.createTime ? row.createTime.slice(0, 10) : '' }}</span>
    </template>
  </sn-crud>
  <el-dialog
      v-model="dialogVisible"
      title="文件上传"
      width="500"
  >
    <sn-upload :drag="true" :key="componentKey" :limit="1" listType="text" :autoUpload="true" :fileMaxSize="209715200"
               @input="(fileList) => uploadData(fileList)"/>
  </el-dialog>
</template>

<script>
import FileApi from "sn-base-layout-vue3/packLayout/api/File";
import store from "@/store";
import * as Base64 from "js-base64";
import ProjBizSupervisionLogMApi from '@/project/api/supervisionLog/ProjBizSupervisionLogM.js'
const setting = require('../../../config.js')
  import { getToken } from "sn-base-utils";
  export const routerConfig = [{
    menuType: "C",
    menuName: "监理日志",
  }, {
    menuType: "F",
    menuName: "查看",
    perms: "supervisionLog:show",
    api: [ProjBizSupervisionLogMApi.config.pageList],
  }, {
    menuType: "F",
    menuName: "新增",
    perms: "supervisionLog:add",
    api: [ProjBizSupervisionLogMApi.config.add],
  }, {
    menuType: "F",
    menuName: "修改",
    perms: "supervisionLog:update",
    api: [ProjBizSupervisionLogMApi.config.update, ProjBizSupervisionLogMApi.config.view],
  }, {
    menuType: "F",
    menuName: "删除",
    perms: "supervisionLog:del",
    api: [ProjBizSupervisionLogMApi.config.remove],
  },
  {
    menuType: "F",
    menuName: "下载",
    perms: "supervisionLog:download",
    api: [ProjBizSupervisionLogMApi.config.download],
  },
  {
    menuType: "F",
    menuName: "链接",
    perms: "supervisionLog:getAttachmentUrl",
    api: [ProjBizSupervisionLogMApi.config.getAttachmentUrl],
  }
  ];
</script>

<script setup>
import {
  ref,
  getCurrentInstance
} from 'vue';
const {
  proxy
} = getCurrentInstance()

// 新增：弹窗控制、上传组件重置、当前行
const dialogVisible = ref(false); // 控制弹窗显示
const componentKey = ref(Date.now()); // 用于重置上传组件
const currentRow = ref(null); // 记录当前上传的行
let option = ref({
  tip: false,
  dialogType: "dialog",
  border: true,
  index: true,
  stripe: true,
  menuWidth: 400,
  menu: true,
  header: false,
  height: "auto",
  searchSpan: 6,
  searchIcon: true,
  searchMenuSpan: 6,
  searchIndex: 3,
  selection: true,
  showTree: false,
  excelBtn: false,
  delBtn: false,
  delBtns: false,
  editBtn: false,
  delBtnsText: "批量删除",
  addBtnText: "新增",
  customEditBtnText: "编辑",
  customDelBtnText: "删除",
  customViewBtnText: "查看",
  cellBtn: true,
  column: [{
    label: "业务编号",
    prop: "businessName",
    type: "input",
    cell: true
  }, {
    label: "监理单位",
    prop: "supervisionUnit",
    search: true,
    type: "input",
    cell: true
  }, {
    label: "记录日期",
    prop: "createTime",
    width:120,
    search: true,
    columnSlot: false,
    searchSlot: true,
    queryType: "BETWEEN",
    type: "date",
    cell: false,
    valueFormat: "YYYY-MM-DD" // 关键
  }, {
    label: "附件",
    prop: "attachment",
    type: "input",
    cell: false
  }
  , {
    label: "记录人",
    prop: "createName",
    width:120,
    search: true,
    type: "input",
    cell: false
  }, {
    label: "发布状态",
    prop: "publishStatus",
    width:100,
    search: true,
    type: "select",
    cell: false,
    dicData: [{
      label: "草稿",
      value: 0
    }, {
      label: "已发布",
      value: 1
    }]
  }]
});
let listData = ref([]);
let queryForm = ref({
  page: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  filter: {
    createTime: null
  }
});


function getPageList() {
  //查询分页列表
  // 查询前处理参数
  const params = handleQueryForm();
  ProjBizSupervisionLogMApi.pageList(params).then((res) => {
    listData.value = res.data.dataList;
    queryForm.value.page.total = res.data.totalCount;
  });
}

function onResetSearch() {
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
}

function handleQueryForm() {
  // 处理参数
  const {
    pageSize,
    pageNum
  } = queryForm.value.page;
  const filter = {}
  for (let key in queryForm.value.filter) {
    if (
      queryForm.value.filter[key] !== undefined &&
      queryForm.value.filter[key] !== null &&
      queryForm.value.filter[key] !== ''
    ) {
      // sn-curd搜索select会生成$prop的变量
      if (key.startsWith("$")) continue;
      filter[key] = queryForm.value.filter[key];
    }
  }
  delete filter.createTime;
  if (Array.isArray(queryForm.value.filter.createTime) && queryForm.value.filter.createTime?.length === 2) {
    filter["beginCreateTime"] = queryForm.value.filter.createTime[0];
    filter["endCreateTime"] = queryForm.value.filter.createTime[1];
  }
  const searchParams = {
    page: {
      pageSize,
      pageNum,
    },
    filter,
  }
  return searchParams
}

function onChangeSearch(params, done) {
  //搜索按钮操作
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
  done && done();
}
async function onAddRow() {
  // 新增一行到第一行
  listData.value.unshift({
    $cellEdit: true,
    businessName: "",
    supervisionUnit: JSON.parse(store.state.user.orgName)?.find((item) => item.id === store.state.user.defaultOrg)?.orgName,
    createTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
    attachment: "",
    createName: store.state.user && store.state.user.userInfo.userName ? store.state.user.userInfo.userName : "",
    publishStatus: 0
  });
  // 同步更新分页总数
  if (queryForm.value && queryForm.value.page) {
    queryForm.value.page.total += 1;
  }
}

function onEditRow(row, index) {
  //编辑行
  listData.value[index].$cellEdit = true;
}

function onRowSave(form, done) {
  //保存行
  if (!form.businessName) {
    proxy.$message.info("请填写业务名称！");
    return;
  }
  if (!form.supervisionUnit) {
    proxy.$message.info("请填写监理单位！");
    return;
  }

  // 如果是新增的行（没有id），则调用新增API
  if (!form.id) {
    ProjBizSupervisionLogMApi.add(form).then(() => {
      proxy.$message.success("新增成功");
      getPageList();
      done();
    }).catch(() => {
      done();
    });
  } else {
    // 如果是编辑的行，则调用更新API
    ProjBizSupervisionLogMApi.update(form).then(() => {
      proxy.$message.success("修改成功");
      getPageList();
      done();
    }).catch(() => {
      done();
    });
  }
}

function onRowUpdate(form, index, done, loading) {
  //编辑行
  done();
}

function onUpload(row, index) {
  // 打开上传弹窗并记录当前行
  currentRow.value = row;
  dialogVisible.value = true;
  componentKey.value = Date.now(); // 重置上传组件
}

function onDownload(row, index) {
  //下载附件
  if (!row.attachment) {
    proxy.$message.info("暂无附件可下载");
    return;
  }
  proxy.download(ProjBizSupervisionLogMApi.config.download.url, [row.id], row.attachment);
}

function onPublish(row, index) {
  //发布
  if (!row.businessName || !row.supervisionUnit) {
    proxy.$message.info("请先填写业务名称和监理单位");
    return;
  }

  proxy.$modal.confirm("确认发布该记录？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    // 更新发布状态
    listData.value[index].publishStatus = 1;
    // 如果有id，调用更新API
    if (row.id) {
      ProjBizSupervisionLogMApi.update({
        ...listData.value[index],
        publishStatus: 1
      }).then(() => {
        proxy.$message.success("发布成功");
      }).catch(() => {
        listData.value[index].publishStatus = 0;
      });
    } else {
      proxy.$message.success("发布成功");
    }
  }).catch(() => {});
}

function onUnpublish(row, index) {
  //撤回
  proxy.$modal.confirm("确认撤回该记录？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    // 更新发布状态
    listData.value[index].publishStatus = 0;
    // 如果有id，调用更新API
    if (row.id) {
      ProjBizSupervisionLogMApi.update({
        ...listData.value[index],
        publishStatus: 0
      }).then(() => {
        proxy.$message.success("撤回成功");
      }).catch(() => {
        listData.value[index].publishStatus = 1;
      });
    } else {
      proxy.$message.success("撤回成功");
    }
  }).catch(() => {});
}

function onDelData(rows) {
  //删除行
  if (!rows || rows.length == 0) {
    proxy.$message.info("请勾选数据！");
    return false;
  }
  let ids = rows.map((item) => {
    return item.id;
  });
  proxy.$modal.confirm("确认删除数据项？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    ProjBizSupervisionLogMApi.remove(ids).then((res) => {
      proxy.$message.success("已删除");
      getPageList();
    });
  }).catch(() => {});
}

function onListTemplate() {
  proxy.$message.info("功能待实现");
}
function onListImport() {
  proxy.$message.info("功能待实现");
}
function onListExport() {
  proxy.$message.info("功能待实现");
}

/**
 * 上传文件
 * @param e
 */
function uploadData(e) {
  if (e.length && currentRow.value) {
    // 假设只允许单文件上传
    const file = e[0];
    if (currentRow.value.id) {
      ProjBizSupervisionLogMApi.uploadAttachment && ProjBizSupervisionLogMApi.uploadAttachment({
        relevantId: currentRow.value.id,
        fileId: file.id,
        fileName: file.fileNameOld,
        fileSize: file.fileSize,
        fileSerialNumber: file.uid,
        type: 'supervisionLog'
      }).then(() => {
        // 上传成功后将文件名和fileId保存到当前行
        currentRow.value.attachment = file.fileNameOld;
        currentRow.value.fileId = file.id;
        // 调用update方法保存到后台
        ProjBizSupervisionLogMApi.update({
          id: currentRow.value.id,
          attachment: file.fileNameOld,
        }).then(() => {
          proxy.$message.success("上传并保存成功");
          getPageList();
        }).catch(() => {
          proxy.$message.error("保存附件信息失败");
        });
      }).catch(() => {
        proxy.$message.error("上传失败");
      });
    } else {
      proxy.$message.info("请先保存该行数据后再上传附件");
    }
  }
  dialogVisible.value = false;
}

const previewConfig = ref({
  // 内置预览返回的替换字符,akey代表要替换的字符串，avalue代码替换的字符串
  substitute: {
    "akey": "avalue"
  },
  // 内置预览服务地址
  previewServerUrl: setting.kkFileViewUrl,
})

// 查看附件方法
function onViewAttachment(row) {
  // 这里可以根据实际情况打开预览或下载
  if (!row.attachment) {
    proxy.$message.info("无附件");
    return;
  }else{
    // builtInPreview(row);
  }
  if (ProjBizSupervisionLogMApi.getAttachmentUrl) {
    ProjBizSupervisionLogMApi.getAttachmentUrl({ id: row.id }).then(res => {
      const fileId = res.data;
      FileApi.getFileNameAndUrlById(fileId).then(res => {
        let url = res.data.filePreviewUrl;
        if(previewConfig.value.substitute){ // 替换字符串
          Object.entries(previewConfig.value.substitute).forEach((ent) => {
            url = url.replace(ent[0],ent[1]);
          })
        }
        let base64Url = encodeURIComponent(Base64.encode(url));
        let previewUrl = previewConfig.value.previewServerUrl + "?url=" + base64Url;
        window.open(previewUrl,'_blank')
      }).catch((err) => {

      })
    }).catch(() => {
      proxy.$message.error("获取附件失败");
    });
  } else {
    proxy.$message.info("未实现附件预览接口");
  }
}

/**
 * 内置预览方法
 */
 function builtInPreview(fileId) {
  FileApi.getFileNameAndUrlById(fileId).then(res => {
    let url = res.data.filePreviewUrl;
    if(previewConfig.value.substitute){ // 替换字符串
      Object.entries(previewConfig.value.substitute).forEach((ent) => {
        url = url.replace(ent[0],ent[1])
      })
    }
    let base64Url = encodeURIComponent(Base64.encode(url))
    let previewUrl = previewConfig.value.previewServerUrl + "?url=" + base64Url;
    window.open(previewUrl,'_blank')
  }).catch((err) => {

  })
}

</script>

<style lang="scss" scoped>
.image-link {
  color: #1a5cff !important;
  cursor: pointer;
  text-decoration: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.image-link:hover {
  text-decoration: underline;
}
.text-overflow {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
