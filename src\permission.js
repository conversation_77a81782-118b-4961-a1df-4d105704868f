import router from './router'
import store from './store'
import { isCloud, appName, iamLogin, baseUrl } from './config'
import { getToken, setToken, setAppName, Store, setAppInfo, getAppInfo } from 'sn-base-utils'
import { setLocalThemeMode } from '@/common/utils/chageTheme'
import { doQiankun } from './common/utils/qiankun'
import { validateUrl } from './common/utils/index'
import appPortalSDK from 'enos-app-portal-sdk'
const whiteList = ['/login', '/init']
Store.setStore(store)
const checkQkRouter = (to, next) => {
  if (store.getters.permission_routes.length > 0) {
    next({ ...to, replace: true })
  } else {
    setTimeout(() => {
      checkQkRouter(to, next)
    }, 1000)
  }
}
const initUserInfo = (to, next) => {
  if (window.__POWERED_BY_QIANKUN__) {
    if (store.getters.permission_routes.length > 0) {
      next()
    } else {
      checkQkRouter(to, next)
    }
    return
  }
  if (store.getters.roles.length === 0 && !to.query.client_id) {
    store.dispatch('GetInfo').then(() => {
      store
        .dispatch('GenerateRoutes', {
          routerArr: [],
          extendParams: { projectId: sessionStorage.getItem('projectId') }
        })
        .then((accessRoutes) => {
          for (let x of accessRoutes) {
            if (x.isFrame == 1) {
              router.addRoute(x)
            }
          }
          next({ ...to, replace: true }) // hack方法 确保addRoutes已完成
        })
        .catch((err) => {
          //本地无登录标识
          // store.dispatch('LogOut')
        })
    })
  } else {
    doQiankun(to, next)
  }
}
router.beforeEach((to, from, next) => {
  // 处理暗黑主题 theme
  const localAppInfo = getAppInfo()
  if (localAppInfo.theme && localAppInfo.theme === 'DARK') {
    setLocalThemeMode('dark')
  }
  // 处理中台外置App
  if (to.query.appId && !to.query.access_token) {
    appPortalSDK.getUserInfo().then((appInfo) => {
      if (appInfo?.theme && appInfo.theme === 'DARK') {
        setLocalThemeMode('dark')
      }
      if (appInfo?.accessToken) {
        setAppInfo({ ...appInfo, appId: to.query.appId })
        to.query.access_token = appInfo.accessToken
        to.query.isFullscreen = true
        delete to.query.appId
        next({ ...to })
        return false
      }
    })
    // 模拟
    // const appInfo = {
    //   id: 'snpit',
    //   name: '00948690',
    //   domain: '',
    //   description: '',
    //   nickName: '',
    //   phoneArea: '',
    //   email: '<EMAIL>',
    //   createdTime: '2025-05-22 09:33:53.0',
    //   isInitPassword: true,
    //   theme: 'DARK',
    //   company: '电投数科',
    //   department: '国核信息',
    //   position: '技术开发部',
    //   organization: { id: 'o16576069867621674', name: '国家电投UAT' },
    //   accessToken: 'APP_PORTAL_S_5cqfNrjuHx2G2yycmWAwSWmBCbZDjxeQ'
    // }
    // console.log('appInfo', appInfo)
    // if (appInfo?.theme && appInfo.theme === 'DARK') {
    //   setLocalThemeMode('dark')
    // }
    // if (appInfo?.accessToken) {
    //   setAppInfo({ ...appInfo, appId: to.query.appId })
    //   to.query.access_token = appInfo.accessToken
    //   to.query.isFullscreen = true
    //   delete to.query.appId
    //   next({ ...to })
    //   return false
    // }
  }
  // 兼容iframe
  if (!window.__POWERED_BY_QIANKUN__) {
    if (to.query.theme) {
      localStorage.setItem('theme', `#${to.query.theme}`)
    }
    if (to.query.application_name) {
      setAppName(to.query.application_name)
    }
    if (to.query.extRoleIds) {
      sessionStorage.setItem('extRoleIds', to.query.extRoleIds)
    }
    if (to.query.isFullscreen) {
      store.dispatch('app/changeIsFullscreen', false)
    }
    if (to.query.projectId) {
      sessionStorage.setItem('projectId', to.query.projectId)
      delete to.query.projectId
    }
  }
  if (to.query.access_token) {
    setToken(to.query.access_token)
    delete to.query.access_token
    next({ ...to })
    return false
  }

  //本地有token标识
  if (getToken()) {
    store.dispatch('setMeta', to.meta)
    store.commit('SET_DATA_SCOPE', '')
    if (to.path === '/login' && !to.query.client_id) {
      next({ path: '/' })
      return
    } else {
      initUserInfo(to, next)
    }
  } else {
    if (whiteList.indexOf(to.path) !== -1) {
      next()
    } else {
      if (to.query.access_token) {
        setToken(to.query.access_token)
        location.href = '/'
        return false
      }
      //本地无登录标识
      if (iamLogin) {
        location.href = validateUrl(`${baseUrl}/oauth/iam/login`)
        return
      }
      if (isCloud) {
        //微服务
        location.href = validateUrl(
          `${baseUrl}/${appName}/oauth/login?redirect_url=${location.href}`
        )
      } else {
        //单体
        next('/login')
      }
    }
  }
})
