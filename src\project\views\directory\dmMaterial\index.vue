<template>
  <el-form :model="formData" :rules="formRules" ref="formRef" label-width="0px" label-position="right">
    <!-- 左侧菜单 -->
    <el-row :gutter="14" :span="24">
      <el-col :span="4" class="tree-container">
        <div>
          <el-row :span="24">
            <el-col :span='24' style="margin-top: 10px;">
              <el-form-item label="" prop="">
                <el-input v-model="treeSearchKeyword" type="text" placeholder="根据目录名称过滤" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='24'>
              <div style="height: calc(100vh - 215px);">
                <el-scrollbar style="height: calc(100% - 40px);overflow-y: auto;width: 100%;">
                  <el-tree :data="filteredTreeData" node-key="id" default-expand-all :props="defaultProps" highlight-current
                    @node-click="handleNodeClick">
                    <template #default="{ node, data }">
                      <span class="custom-tree-node">
                        <template v-if="!data.isEdit">
                          <span class="tree-node-label">{{ node.label }}</span>
                          <el-popover placement="bottom" width="100" trigger="click">
                            <div class="popover-content">
                              <el-button type="text"  @click="changeItem(data)" style="font-size: 14px;  margin-left: 0px !important">编辑</el-button>
                              <el-button type="text"  @click="deleteItem(data)" style="font-size: 14px; margin-left: 0px !important">删除</el-button>
                            </div>
                            <template #reference>
                              <!-- <el-button v-if="data.roleType === 'owner'" type="text"style="margin-left: auto; font-weight: 3000;">···</el-button> -->
                              <i class="el-icon-more" v-if="data.roleType === 'owner' || hasPermi('dmMaterial:delMenu')" style="margin-left: auto;"></i>
                            </template>
                          </el-popover>
                        </template>
                        <el-input v-else v-model="data.name" @blur="handleInputBlur(data)"
                          @keyup.enter="handleInputBlur(data)" :autofocus="data.isEdit" ref="inputRef" />
                      </span>
                    </template>
                  </el-tree>
                </el-scrollbar>
              </div>
              <el-input v-if="hasPermi('dmMaterial:addMenu')" style="margin-bottom: 15px;" v-model="menuInput" placeholder="请输入新增菜单名称" @change="handleAddInputBlur(menuInput)"/>
            </el-col>
          </el-row>
        </div>
      </el-col>
      <!-- 右侧表格 -->
      <el-col :span="20">
        <div style="display: flex; flex-direction: column;">
          <sn-crud class="table" :data="listData" :option="option" v-model:page="queryForm.page"
            v-model:search="queryForm.filter" @on-load="getPageList" @search-change="onChangeSearch"
            @search-reset="onResetSearch" @addBtnHandle="onAddData" @row-del="onDelData" @row-excel="onExportData"
            @selection-change="onSelectionChange">
            <template #header>
              <el-form class="search-form" ref="form" :model="form" label-width="80px"
                style="display: flex; flex-direction: row-reverse;">
                <el-button v-if="hasPermi('dmMaterial:showMember')" type="primary" :size="size" icon="el-icon-plus"
                  style="margin-left: 20px;" @click="onOrganization()">文档权限</el-button>
                <el-form-item label="创建时间:">
                  <el-input v-model="formData.createTime" disabled></el-input>
                </el-form-item>
                <el-form-item label="创建人:">
                  <el-input v-model="formData.createName" disabled></el-input>
                </el-form-item>
              </el-form>
            </template>
          </sn-crud>
        </div>
      </el-col>
    </el-row>
  </el-form>

  <!-- 上传弹框 -->
  <el-dialog v-model="dialogVisible" title="文件上传" width="500" @close="handleDialogClose">
    <sn-upload :drag="true" :key="componentKey" :limit="10" listType="text" :autoUpload="true" :fileMaxSize="209715200"
      @input="handleFileInput" />
  </el-dialog>

  <!-- 文档权限弹窗 -->
  <el-dialog v-model="showOrgDialog" title="团队成员" width="500px" :center="true" :close-on-click-modal="false">
      <div style="padding: 10px 0;">
        <span>添加成员</span>
      </div>
      <div>
        <i class="el-icon-user"></i>
        <el-button v-if="hasPermi('dmMaterial:addMember')" type="" link style="margin-left: 10px;color: #417ff9;" @click="addOrg()">从组织机构选择</el-button>
      </div>
      <el-divider></el-divider>
      <div class="org-box">
        <span>已加入的成员</span>
        <el-input v-model="searchKeyword" placeholder="搜索成员" style="width: 40%;" clearable />
      </div>
      <div v-for="(item1, index) in filteredOrgData" :key="index" :label="item1.value" class="org-box">
        <div class="memberAvatar">
          <img src="@/project/assets/images/avatar.png" alt="">
          <span style="padding-left: 10px;">{{ item1.teamMemberName }}</span>
        </div>
        <div>
          <span v-if="item1.roleType === 'owner'" style="padding-right:19px ;">{{ item1.roleTypeName }}</span>
          <span v-else>{{ item1.roleTypeName }}</span>
          <el-popover v-if="item1.roleType === 'admin'" v-model:visible="item1.popoverVisible" placement="bottom"
            width="100" trigger="manual">
            <div class="popover-content">
              <!-- <el-button v-if="hasPermi('dmMaterial:setAdmin')"  type="text"  @click="changeRoleType(item1.id, item1)" style="margin-left: 0px !important">设置管理员</el-button> -->
              <el-button v-if="hasPermi('dmMaterial:cancelAdmin')"  type="text"  @click="deleteRoleType(item1.id, item1)" style="font-size: 14px; margin-left: 0px !important">取消管理员</el-button>
              <el-button v-if="hasPermi('dmMaterial:removeMember')" type="text"  @click="deleteMember(item1.id, item1)" style="font-size: 14px; margin-left: 0px !important">删除成员</el-button>
            </div>
            <template #reference>
              <i class="el-icon-arrow-down el-icon--right" @click.stop="item1.popoverVisible = true"></i>
            </template>
          </el-popover>
          <el-popover v-if="item1.roleType === 'member'" v-model:visible="item1.popoverVisible" placement="bottom"
            width="100" trigger="manual">
            <div class="popover-content">
              <el-button v-if="hasPermi('dmMaterial:setAdmin')" type="text"  @click="changeRoleType(item1.id, item1)" style="font-size: 14px; margin-left: 0px !important">设置管理员</el-button>
              <!-- <el-button v-if="hasPermi('dmMaterial:cancelAdmin')" type="text"  @click="deleteRoleType(item1.id, item1)" style="margin-left: 0px !important">取消管理员</el-button> -->
              <el-button v-if="hasPermi('dmMaterial:removeMember')" type="text" @click="deleteMember(item1.id, item1)" style="font-size: 14px; margin-left: 0px !important">删除成员</el-button>
            </div>
            <template #reference>
              <i class="el-icon-arrow-down el-icon--right" @click.stop="item1.popoverVisible = true"></i>
            </template>
          </el-popover>
        </div>
      </div>
  </el-dialog>

</template>


<script>
import ProjBizDmStgMApi from '@/project/api/dm/ProjBizDmStgM.js'
import ProjBizDmPdcMApi from '@/project/api/dm/ProjBizDmPdcM.js'
import ProjBizDmPdcpRApi from '@/project/api/dm/ProjBizDmPdcpR.js'
import { getToken } from "sn-base-utils";
export const routerConfig = [{
  menuType: "C",
  menuName: "项目资料",
}, {
  menuType: "F",
  menuName: "查看",
  perms: "dmMaterial:show",
  api: [ProjBizDmStgMApi.config.pageList],
}, {
  menuType: "F",
  menuName: "上传文件",
  perms: "dmMaterial:add",
  api: [ProjBizDmStgMApi.config.add],
}, {
  menuType: "F",
  menuName: "删除",
  perms: "dmMaterial:del",
  api: [ProjBizDmStgMApi.config.remove],
}, {
  menuType: "F",
  menuName: "新增菜单",
  perms: "dmMaterial:addMenu",
  api: [ProjBizDmPdcMApi.config.add],
}, {
  menuType: "F",
  menuName: "删除编辑菜单",
  perms: "dmMaterial:delMenu",
  api: [ProjBizDmPdcMApi.config.update,ProjBizDmPdcMApi.config.remove],
},
// {
//   menuType: "M",
//   menuName: "查看菜单",
//   perms: "dmMaterial:showMenu",
//   api: [ProjBizDmPdcMApi.config.all],
// },
{
  menuType: "F",
  menuName: "查看权限列表",
  perms: "dmMaterial:showMember",
  api: [ProjBizDmPdcpRApi.config.listByPdcId],
}, {
  menuType: "F",
  menuName: "设置管理员",
  perms: "dmMaterial:setAdmin",
  api: [ProjBizDmPdcpRApi.config.setAdmin],
}, {
  menuType: "F",
  menuName: "取消管理员",
  perms: "dmMaterial:cancelAdmin",
  api: [ProjBizDmPdcpRApi.config.cancelAdmin],
}, {
  menuType: "F",
  menuName: "删除成员",
  perms: "dmMaterial:removeMember",
  api: [ProjBizDmPdcpRApi.config.remove],
}, {
  menuType: "F",
  menuName: "添加成员",
  perms: "dmMaterial:addMember",
  api: [ProjBizDmPdcpRApi.config.addBatch],
},
];
</script>

<script setup>
import {
  ref,
  getCurrentInstance,
  nextTick,
  onMounted,
  computed
} from 'vue';
import { hasPermi } from "sn-base-utils"
const {
  proxy
} = getCurrentInstance();

let formData = ref({
  createName: '',
  createTime: ''
});
let treeData = ref([]);
const defaultProps = {
  children: 'children',
  label: 'name'
};
let option = ref({
  tip: false,
  dialogType: "dialog",
  border: true,
  index: true,
  stripe: true,
  menu: false,
  header: true,
  height: "auto",
  searchSpan: 6,
  searchIcon: true,
  searchMenuSpan: 6,
  searchIndex: 3,
  selection: true,
  showTree: false,
  excelBtn: true,
  excelBtnText: "下载",
  delBtn: false,
  // importBtn: true,
  // importBtnText: "关联",
  editBtn: false,
  delBtnsText: "删除",
  addBtnText: "上传文件",
  customEditBtnText: "编辑",
  customDelBtnText: "删除",
  customViewBtnText: "查看",
  searchShowBtn: false,
  refreshBtn: false,
  columnBtn: false,
  column: [{
    label: "文件编号",
    prop: "fileSerialNumber",
    columnSlot: false,
    searchSlot: false,
    overHidden:true,
    width: 200
  }, {
    label: "文件名称",
    prop: "fileName",
    search: true,
    columnSlot: false,
    searchSlot: false,
    queryType: "LIKE",
    overHidden:true,
  }, {
    label: "责任人",
    prop: "createName",
    search: true,
    columnSlot: false,
    searchSlot: false,
    queryType: "LIKE",
    overHidden:true,
    width: 200
  }, {
    label: "创建时间",
    prop: "createTime",
    columnSlot: false,
    searchSlot: false,
    overHidden:true,
    width: 200
  }]
});
const orgData = ref([]);
let listData = ref([]);
let queryForm = ref({
  page: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  filter: {
    type: "material",
    fileName: "",
    relevantId: "",
  }
});

const showOrgDialog = ref(false); // 文档权限卡片显示状态
let dialogVisible = ref(false)
const componentKey = ref(0); // 上传组件key,保证重新渲染
let selectedRows = ref([]);
const inputRef = ref(null)
const menuInput = ref("")
const selectedMenuId = ref(null); // 当前选中菜单id
const selectedMenuRoleType = ref(null); // 前选中菜单角色类型
const orgMemberData = ref(null);
const searchKeyword = ref("");
const treeSearchKeyword = ref("");
const currentFileList = ref([]); // 保存当前上传的文件列表


// 过滤树形菜单数据
const filteredTreeData = computed(() => {
  const keyword = treeSearchKeyword.value.trim().toLowerCase();
  if (!keyword) {
    // 搜索框为空，返回全部数据
    return treeData.value;
  }
  // 递归过滤树形节点
  // const filterNode = (nodes) => {
  //   return nodes
  //     .map(node => {
  //       // 深拷贝节点避免修改原始数据
  //       const copyNode = { ...node };
  //       // 检查当前节点是否匹配关键词
  //       const isMatch = copyNode.name?.toLowerCase().includes(keyword);
  //       return isMatch ? copyNode : null;
  //     })
  //     .filter(Boolean); // 过滤掉null值（不匹配的节点）
  // };
  // return filterNode(treeData.value);
  return treeData.value.filter(node => 
    node.name?.toLowerCase().includes(keyword)
  );
});

// 过滤成员权限列表
const filteredOrgData = computed(() => {
  if (!searchKeyword.value.trim()) {
    // 若搜索框为空，返回全部数据
    return orgData.value;
  }
  // 过滤teamMemberName包含搜索关键词的成员
  const keyword = searchKeyword.value.trim().toLowerCase();
  return orgData.value.filter(item =>
    item.teamMemberName?.toLowerCase().includes(keyword)
  );
});

onMounted(() => {
  getMenuList()
});

// 获取菜单列表
function getMenuList() {
  ProjBizDmPdcMApi.all().then((res) => {
    treeData.value = res.data.map(item => ({
      name: item.name,
      id: item.id,
      isEdit: false,
      _originalName: item.name,
      createName: item.createName,
      createTime: item.createTime,
      roleType: item.roleType,
      children: []
    }));
  });
}

// 菜单新增
function handleAddInputBlur(event) {
  // 输入为空直接返回
  if (!event || event.trim() === "") {
    return;
  }
  const params = {
    name: event
  }
  ProjBizDmPdcMApi.add(params).then((res) => {
    proxy.$message.success("新增成功");
    menuInput.value = ""; // 清空输入框
    getMenuList();
  })
    .catch((err) => {
      proxy.$message.error("新增失败");
    });
};

// 菜单删除
function deleteItem(e) {
  proxy.$modal.confirm("确认删除数据项？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    ProjBizDmPdcMApi.remove([e.id]).then((res) => {
      proxy.$message.success("删除成功");
      // 如果删除的是当前选中的菜单，清空右侧数据
      if (selectedMenuId.value === e.id) {
        selectedMenuId.value = null;
        selectedMenuRoleType.value = null;
        queryForm.value.filter.relevantId = "";
        listData.value = [];
        queryForm.value.page.total = 0;
        formData.value.createName = '';
        formData.value.createTime = '';
      }
      getMenuList();
    }).catch((err) => {
      proxy.$message.error("删除失败");
    });
  })
};

// 菜单编辑按钮
async function changeItem(item) {
    // 在开始编辑时保存原始名称（只在第一次编辑时保存）
  if (!item._originalName) {
    item._originalName = item.name;
  }
  item.isEdit = true
  await nextTick(() => {
    if (inputRef.value) {
      inputRef.value.focus()
    }
  })
}

// 菜单编辑
function handleInputBlur(item) {
  item.isEdit = false;
  inputRef.value = null
  // 保存原始名称
  // const originalName = item._originalName || item.name;
  // 如果没有原始名称，保存一份到 _originalName
  // if (!item._originalName) {
  //   item._originalName = originalName;
  // }
  const params = {
    id: item.id,
    name: item.name
  };
  ProjBizDmPdcMApi.update(params)
    .then((res) => {
      proxy.$message.success("修改成功");
      // 更新成功后，更新原始名称为新值
      item._originalName = item.name;
    })
    .catch((err) => {
      proxy.$message.error("修改失败");
      // 还原为原始名称
      item.name = item._originalName;
    });
  // item.isEdit = false;
};

// 点击左侧树形菜单节点
function handleNodeClick(data, node) {
  selectedMenuId.value = data.id; // 当前选中菜单ID
  selectedMenuRoleType.value = data.roleType;
  
   queryForm.value.filter.relevantId = data.id;
  // 查找 treeData 中对应的数据
  const selectedItem = treeData.value.find(item => item.id === data.id);
  if (selectedItem) {
    formData.value.createName = selectedItem.createName || '';  // 赋值 createName
    formData.value.createTime = selectedItem.createTime || '';  // 赋值 createTime
  }
  onResetSearch();
}

// 表格选中行
function onSelectionChange(rows) {
  selectedRows.value = rows;
}

// 上传文件
function uploadData(e) {
    if (e.length) {
    let addList = [];
    e.forEach((file) => {
      addList.push({
        type: queryForm.value.filter.type,
        relevantId: queryForm.value.filter.relevantId,
        fileId: file.id,
        fileName: file.fileName,
        fileSize: file.fileSize
      });
    });
    ProjBizDmStgMApi.addBatch(addList).then((res) => {
      proxy.$message.success("上传成功");
      // componentKey.value += 1
      getPageList();
    }).catch((err) => {
      proxy.$message.error("上传失败");
    });
  }
}

// 处理文件输入
function handleFileInput(fileList) {
  currentFileList.value = fileList;
}

// 处理对话框关闭
function handleDialogClose() {
  if (currentFileList.value.length > 0) {
    uploadData(currentFileList.value);
    currentFileList.value = []; // 清空文件列表
  }
}

// 上传文件
function onAddData() {
  if (!queryForm.value.filter.relevantId) {
    proxy.$message.info("请先选择左侧菜单");
    return
  }
  // 重新计算key,保证上传组件重新渲染
  if (componentKey.value > 100) {
    componentKey.value = 0
  } else {
    componentKey.value += 1
  }
  currentFileList.value = []; // 清空之前的文件列表
  dialogVisible.value = true
}

// 获取分页列表
function getPageList() {
   // 没有选中菜单，不加载数据
  if (!queryForm.value.filter.relevantId) {
    listData.value = [];
    queryForm.value.page.total = 0;
    return;
  }
  const params = handleQueryForm();
  ProjBizDmStgMApi.pageList(params).then((res) => {
    listData.value = res.data.dataList;
    queryForm.value.page.total = res.data.totalCount;
  });
}

// 重置
function onResetSearch() {
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
}

// 构建查询条件
function handleQueryForm() {
  // 处理参数
  const {
    pageSize,
    pageNum
  } = queryForm.value.page;
  const filter = {}
  for (let key in queryForm.value.filter) {
    if (queryForm.value.filter[key]?.length) {
      // sn-curd搜索select会生成$prop的变量
      if (key.startsWith("$")) continue;
      filter[key] = queryForm.value.filter[key];
    }
  }
  delete filter.createTime;
  if (Array.isArray(queryForm.value.filter.createTime) && queryForm.value.filter.createTime?.length === 2) {
    filter["beginCreateTime"] = queryForm.value.filter.createTime[0];
    filter["endCreateTime"] = queryForm.value.filter.createTime[1];
  }
  const searchParams = {
    page: {
      pageSize,
      pageNum,
    },
    filter,
  }
  return searchParams
}


// 点击搜索
function onChangeSearch(params, done) {
  //搜索按钮操作
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
  done && done();
}

// 批量下载
function onExportData() {
  if (selectedRows.value.length == 0) {
    proxy.$message.info("请勾选要下载的文件！");
    return false;
  }
  let ids = selectedRows.value.map((item) => {
    return item.id;
  });
  proxy.download("/project/dmStg/exportZip", ids, '项目资料.zip');
}

// 删除数据
function onDelData(rows) {
  //删除行
  if (!rows || rows.length == 0) {
    proxy.$message.info("请勾选数据！");
    return false;
  }
  let ids = rows.map((item) => {
    return item.id;
  });
  proxy.$modal.confirm("确认删除数据项？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    ProjBizDmStgMApi.remove(ids).then((res) => {
      proxy.$message.success("已删除");
      getPageList();
    });
  }).catch(() => { });
}

// 文档权限
function onOrganization(row) {
  showOrgDialog.value = true;
  getListOfOrganizationalMembers()
}

// 选择人员
function addOrg() {
  proxy.$SelectUser({
    multiple: true,
    onSuccess(res) {
      if (res.data && Array.isArray(res.data)) {
        orgMemberData.value = res.data.map(item => ({
          pdcId: selectedMenuId.value,
          teamMemberId: item.userId,
          teamMemberName: item.userName,
        }));
      }
      addMember(orgMemberData.value)
    }
  })
}

// 添加成员
function addMember(e) {
  const params = selectedMenuId.value
  ProjBizDmPdcpRApi.addBatch(e, params).then((res) => {
    proxy.$message.success("添加成功");
    getListOfOrganizationalMembers()
  })
}
// 成员列表
function getListOfOrganizationalMembers() {
  const menuId = selectedMenuId.value;
  if (!menuId) {
    proxy.$message.info("请先选择左侧菜单");
    showOrgDialog.value = false
    return;
  }
  ProjBizDmPdcpRApi.listByPdcId(menuId).then((res) => {
    if (res.code === 200 && res.data) {
      const roleMap = {
        owner: '拥有者',
        admin: '管理员',
        member: '成员'
      };
      // 将 res.data 映射为带中文角色名的数组，并赋值给 orgData
      orgData.value = res.data.map(item => ({
        ...item,
        roleTypeName: roleMap[item.roleType] || item.roleType, // 如果找不到则保留原值
        popoverVisible: false
      }));
    }
  })
}

// 设置管理员
function changeRoleType(e, item1) {
  ProjBizDmPdcpRApi.setAdmin(e).then((res) => {
    proxy.$message.success("设置成功");
    item1.popoverVisible = false; // 关闭 popover
    getListOfOrganizationalMembers()
  }).catch((err) => {
      proxy.$message.error("设置失败");
    });
}

// 取消管理员
function deleteRoleType(e, item1) {
  ProjBizDmPdcpRApi.cancelAdmin(e).then((res) => {
    proxy.$message.success("取消成功");
    item1.popoverVisible = false; // 关闭 popover
    getListOfOrganizationalMembers()
  }).catch((err) => {
      proxy.$message.error("取消失败");
    });
}
// 删除成员
function deleteMember(e, item1) {
  ProjBizDmPdcpRApi.remove(e).then((res) => {
    proxy.$message.success("删除成功");
    item1.popoverVisible = false; // 关闭 popover
    getListOfOrganizationalMembers()
  }).catch((err) => {
      proxy.$message.error("删除失败");
    });
}

</script>

<style lang="scss" scoped>
.tree-container {
  background-color: #fff;
  border-radius: 4px;
}

.org-card {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #ebeef5;
}

.table {

  ::v-deep .sp-crud--card .ep-card+.ep-card {
    position: relative !important;
    padding-top: 50px !important;
  }

  .search-form {
    position: absolute !important;
    top: 12px !important;
    right: 20px;
  }
}

.popover-content {
  display: flex;
  flex-direction: column;
  align-items: center;

}

.org-box {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.memberAvatar {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.tree-node-label {
  max-width: 140px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}

::v-deep .ep-row {
  margin-left: 0px !important;
}
</style>
