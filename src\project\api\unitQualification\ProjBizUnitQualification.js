import { request, replaceUrl } from "sn-base-utils";

export default class ProjBizUnitQualificationApi {
    static config = {
        add: {
            url: '/project/unitQualification/add',
            method: 'POST'
        },
        listSupplier: {
            url: '/project/unitQualification/listSupplier',
            method: 'POST'
        },
       pageSupplier: {
            url: '/project/unitQualification/pageSupplier',
            method: 'POST'
        },
        remove: {
            url: '/project/unitQualification/delete',
            method: 'DELETE'
        },
        update: {
            url: '/project/unitQualification/update',
            method: 'PUT'
        },
        getTreeById: {
            url: '/project/unitQualification/getTreeById/{id}',
            method: 'GET'
        },
        view: {
            url: '/project/unitQualification/get/{id}',
            method: 'GET'
        },
        pageList: {
            url: '/project/unitQualification/page',
            method: "POST"
        },
        startFlow : {
            url: '/project/unitQualification/saveAndSubmitProc',
            method: "POST"
        },
        submitTask: {
            url: '/project/unitQualification/saveAndSubmitTask',
            method: "POST"
        },
        printTemplate: {
            url: `/project/unitQualification/printTemplate`,
            method: "POST"
        },
        
        operateFlow: {
            url: `/project/unitQualification/operateFlow`,
            method: "POST"
        },
        list: {
            url: '/project/unitQualification/list',
            method: "POST"
        }
    };

    /**
     * 新增单位资质报审
     * @param data
     * @returns {*}
     */
    static add(data) {
        return request({
            url: this.config.add.url,
            method: this.config.add.method,
            data: data
        });
    }

    /**
     * 删除单位资质报审
     * @param data
     * @returns {*}
     */
    static remove(data) {
        return request({
            url: this.config.remove.url,
            method: this.config.remove.method,
            data: data
        });
    }

   /**
     * 单位资质的流程状态变更
     * @param data
     * @returns {*}
     */
    static operateFlow(data) {
        return request({
            url: this.config.operateFlow.url,
            method: this.config.operateFlow.method,
            data: data
        });
    }
    /**
     * 更新单位资质报审
     * @param data
     * @returns {*}
     */
    static update(data) {
        return request({
            url: this.config.update.url,
            method: this.config.update.method,
            data: data
        });
    }

    /**
     * 查询单位资质报审详细
     * @param id
     * @returns {*}
     */
    static view(id) {
        return request({
            url: replaceUrl(this.config.view.url, { id }),
            method: this.config.view.method
        });
    }

    /**
     * 获取组织树
     * @param id
     * @returns {*}
     */
    static getTreeById(id) {
        return request({
            url: replaceUrl(this.config.getTreeById.url, { id }),
            method: this.config.view.method
        });
    }
    /**
     * 分页查询单位资质报审列表
     * @param data
     * @returns {*}
     */
    static pageList(data) {
        return request({
            url: this.config.pageList.url,
            method: this.config.pageList.method,
            data: data
        });
    }

    /**
     * 全部单位资质报审列表
     * @returns {*}
     */
    static list(data) {
        return request({
            url: this.config.list.url,
            method: this.config.list.method,
            data: data
        });
    }
    /**
     * 获取承包商list
     * 
     * @returns {*}
     */
    static listSupplier(data) {
        console.log("调用的url",this.config.listSupplier.url)
        console.log("调用的data",data)
        data
        return request({
            url: this.config.listSupplier.url,
            method: this.config.listSupplier.method,
            data: data
        });
    }

    
    /**
     * 承包商分页
     * 
     * @returns {*}
     */
    static pageSupplier(data) {
        console.log("调用的url",this.config.pageSupplier.url)
        console.log("调用的data",data)
        data
        return request({
            url: this.config.pageSupplier.url,
            method: this.config.pageSupplier.method,
            data: data
        });
    }

       

    /**
     * 工作流-启动流程
     * @returns {*}
     */
    static startFlow(data) {
        return request({
            url: this.config.startFlow.url,
            method: this.config.startFlow.method,
            data: data
        });
    }

    /**
     * 工作流-完成任务
     * @returns {*}
     */
    static submitTask(data) {
        return request({
            url: this.config.submitTask.url,
            method: this.config.submitTask.method,
            data: data
        });
    }

    /**
     * 工作流-打印模板
     */
    static printTemplate(data) {
        return request({
            url: this.config.printTemplate.url,
            method: this.config.printTemplate.method,
            data: data
        });
    }
}
