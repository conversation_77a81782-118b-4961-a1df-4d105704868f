import { request, replaceUrl } from "sn-base-utils";

export default class ProjBizResumeOrderMApi {
    static config = {
        add: {
            url: '/project/resumeOrder/add',
            method: 'POST'
        },
        remove: {
            url: '/project/resumeOrder/delete',
            method: 'DELETE'
        },
        update: {
            url: '/project/resumeOrder/update',
            method: 'PUT'
        },
        view: {
            url: '/project/resumeOrder/get/{id}',
            method: 'GET'
        },
        pageList: {
            url: '/project/resumeOrder/page',
            method: "POST"
        },
        list: {
            url: '/project/resumeOrder/list',
            method: "POST"
        }
    };

    /**
     * 新增复工令
     * @param data
     * @returns {*}
     */
    static add(data) {
        return request({
            url: this.config.add.url,
            method: this.config.add.method,
            data: data
        });
    }

    /**
     * 删除复工令
     * @param data
     * @returns {*}
     */
    static remove(data) {
        return request({
            url: this.config.remove.url,
            method: this.config.remove.method,
            data: data
        });
    }

    /**
     * 更新复工令
     * @param data
     * @returns {*}
     */
    static update(data) {
        return request({
            url: this.config.update.url,
            method: this.config.update.method,
            data: data
        });
    }

    /**
     * 查询复工令详细
     * @param id
     * @returns {*}
     */
    static view(id) {
        return request({
            url: replaceUrl(this.config.view.url, { id }),
            method: this.config.view.method
        });
    }

    /**
     * 分页查询复工令列表
     * @param data
     * @returns {*}
     */
    static pageList(data) {
        return request({
            url: this.config.pageList.url,
            method: this.config.pageList.method,
            data: data
        });
    }

    /**
     * 全部复工令列表
     * @returns {*}
     */
    static list(data) {
        return request({
            url: this.config.list.url,
            method: this.config.list.method,
            data: data
        });
    }
}
