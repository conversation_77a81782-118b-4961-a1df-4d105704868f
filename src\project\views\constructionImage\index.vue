<template>
  <div>
    <sn-crud :data="listData" :option="option" v-model:page="queryForm.page" v-model:search="queryForm.filter" @on-load="getPageList" @search-change="onChangeSearch" @search-reset="onResetSearch" @addBtnHandle="onEditData" @row-del="onDelData">
      <template #menu="{ row, index, size }">
        <!-- 草稿状态显示的按钮 -->
        <template v-if="Number(row.publishStatus) !== 1">
          <el-button type="primary" :size="size" icon="el-icon-edit" link @click="onEditData(row)">编辑</el-button>
          <el-button type="danger" :size="size" icon="el-icon-delete" link @click="onDelData([row])">删除</el-button>
          <!-- <el-button type="info" :size="size" icon="el-icon-circle-check" link @click="onPublish(row, index)">发布</el-button> -->
        </template>
        <!-- 已发布状态显示的按钮 -->
        <template v-else>
          <el-button type="danger" :size="size" icon="el-icon-refresh-left"  link @click="onUnpublish(row, index)">撤回</el-button>
          <!-- <el-button type="primary" :size="size" icon="el-icon-download" link @click="onDownload(row)">下载</el-button> -->
        </template>
      </template>
      <template #createTimeSearch = "{ row, size }">
        <el-date-picker
            v-model="queryForm.filter.createTime"
            type="daterange"
            value-format="YYYY-MM-DD"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 100%;"
        />
      </template>
      <template #createTime="{ row }">
        <span>{{ row.createTime ? row.createTime.slice(0, 10) : '' }}</span>
      </template>
      <template #publishStatus="{row}">
        <span :style="{color : row.publishStatus && row.publishStatus==='1' ? 'green' : 'red'}">
          {{row.publishStatus && row.publishStatus==='1' ? '已发布': '草稿'}}
        </span>
      </template>
      <template #imageDescription="{ row }">
        <el-tooltip class="item" effect="dark" :content="row.imageDescription" placement="top">
          <el-link class="image-link text-overflow" @click="onViewData(row)">
            {{ row.imageDescription }}
          </el-link>
        </el-tooltip>
      </template>
      <template #header>
        <el-button type="primary" icon="el-icon-plus" style="margin-left: 10px;margin-bottom: 10px;" @click="onEditData">新增</el-button>
        <!-- <el-button type="primary" icon="el-icon-document" style="margin-left: 10px;margin-bottom: 10px;" @click="onListTemplate">列表模板</el-button>
        <el-button type="primary" icon="el-icon-upload" style="margin-left: 10px;margin-bottom: 10px;" @click="onListImport">列表导入</el-button> -->
        <el-button type="primary" icon="el-icon-download" style="margin-left: 10px;margin-bottom: 10px;" @click="onListExport">列表导出</el-button>
      </template>
    </sn-crud>
    <div ref="myRef"></div>
  </div>
</template>

<script>
import ProjBizConstructionImageMApi from '@/project/api/constructionImage/ProjBizConstructionImageM.js'
  import EditProjBizConstructionImageM from "./components/EditProjBizConstructionImageM.vue";
  import { getToken } from "sn-base-utils";
  export const routerConfig = [{
    menuType: "C",
    menuName: "施工影像记录",
  }, {
    menuType: "F",
    menuName: "查看",
    perms: "show",
    api: [ProjBizConstructionImageMApi.config.pageList],
  }, {
    menuType: "F",
    menuName: "新增",
    perms: "add",
    api: [ProjBizConstructionImageMApi.config.add],
  }, {
    menuType: "F",
    menuName: "修改",
    perms: "update",
    api: [ProjBizConstructionImageMApi.config.update, ProjBizConstructionImageMApi.config.view],
  }, {
    menuType: "F",
    menuName: "删除",
    perms: "del",
    api: [ProjBizConstructionImageMApi.config.remove],
  }];
</script>

<script setup>
import {
  ref,
  getCurrentInstance
} from 'vue';
const {
  proxy
} = getCurrentInstance()
const myRef = ref(null);
let option = ref({
  tip: false,
  dialogType: "page",
  border: true,
  index: true,
  stripe: true,
  menu: true,
  header: false,
  height: "auto",
  searchSpan: 6,
  searchIcon: true,
  searchMenuSpan: 6,
  searchIndex: 3,
  selection: true,
  showTree: false,
  excelBtn: false,
  delBtn: false,
  editBtn: false,
  delBtnsText: "批量删除",
  addBtnText: "新增",
  customEditBtnText: "编辑",
  customDelBtnText: "删除",
  customViewBtnText: "查看",
  column: [{
    label: "影像描述",
    prop: "imageDescription",
    search: true,
    columnSlot: true
  }, {
    label: "影像分类",
    prop: "imageCategory",
    width:150
  }, {
    label: "记录日期",
    prop: "createTime",
    search: true,
    columnSlot: false,
    searchSlot: true,
    queryType: "BETWEEN",
    type: "date",
    cell: false,
    valueFormat: "YYYY-MM-DD",
    width:180
  }, {
    label: "记录人",
    prop: "createName",
    search: true,
    width:180
  }, {
    label: "发布状态",
    prop: "publishStatus",
    width:150,
    search: true,
    type: "select",
    dicData: [
      { label: "草稿", value: "0" },
      { label: "已发布", value: "1" }
    ]
  }]
});
let listData = ref([]);
let queryForm = ref({
  page: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  filter: {
    createTime: null
  }
});
let formRules = ref({});

function getPageList() {
  //查询分页列表
  // 查询前处理参数
  const params = handleQueryForm();
  ProjBizConstructionImageMApi.pageList(params).then((res) => {
    listData.value = res.data.dataList;
    queryForm.value.page.total = res.data.totalCount;
  });
}

function onResetSearch() {
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
}

function handleQueryForm() {
  // 处理参数
  const {
    pageSize,
    pageNum
  } = queryForm.value.page;
  const filter = {}
  for (let key in queryForm.value.filter) {
    if (queryForm.value.filter[key]?.length) {
      // sn-curd搜索select会生成$prop的变量
      if (key.startsWith("$")) continue;
      filter[key] = queryForm.value.filter[key];
    }
  }
  delete filter.createTime;
  if (Array.isArray(queryForm.value.filter.createTime) && queryForm.value.filter.createTime?.length === 2) {
    filter["beginCreateTime"] = queryForm.value.filter.createTime[0];
    filter["endCreateTime"] = queryForm.value.filter.createTime[1];
  }
  const searchParams = {
    page: {
      pageSize,
      pageNum,
    },
    filter,
  }
  return searchParams
}

function onChangeSearch(params, done) {
  //搜索按钮操作
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
  done && done();
}
async function onEditData(row) {
  //编辑,新增按钮操作
  let editType = row ? "edit" : "add";
  let rowInfo = await (editType !== "add" ? ProjBizConstructionImageMApi.view(row.id) : {});
  const formData = editType !== "add" ? rowInfo.data : rowInfo;
  //编辑,新增按钮操作
  proxy.$DialogForm.show({
    title: editType == "edit" ? "编辑" : "新增",
    type: option.value.dialogType,
    width: "80%",
    el: myRef.value,
    content: EditProjBizConstructionImageM,
    data: {
      formData: formData,
      type: editType,
    },
    option: {
      submitBtn: false,
      emptyBtn: false,
      emptyText:'取消',
      extendButton:[
        {
          key: 'save',
          text: '保存',
          icon: 'el-icon-plus',
          buttonType: 'primary',
        },
        {
          key: 'submit',
          text: '发布',
          icon: 'el-icon-check',
          buttonType: 'primary',
        },
        {
          key: 'close',
          text: '关闭',
          icon: 'el-icon-close',
          buttonType: '',
        },
      ],
    },
    callback: (res) => {
      if (res.type && res.type == 'save') {
        if (res.dialogRefs) {
          res.dialogRefs.submitData(res.type).then((flag) => {
            if (flag) {
              getPageList();
            }
          });
        }
      }else if (res.type && res.type == 'submit') {
        if (res.dialogRefs) {
          res.dialogRefs.submitData(res.type).then((flag) => {
            if (flag) {
              getPageList();
              res.close();
            }
          });
        }
      }
       else {
        if (editType === 'edit') {
          proxy.$confirm('确认关闭？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            res.close();
          }).catch(() => {});
        } else {
          res.close();
        }
      }
    }
  });
}

async function onViewData(row) {
  //查看操作
  let editType = "view";
  let extendButtons = [];
  if (row.publishStatus === '0') {
    extendButtons.push({
      key: 'edit',
      text: '编辑',
      icon: 'el-icon-edit',
      buttonType: 'primary',
      click: (res) => {
        res.close();
        onEditData(row);
      },
    });
  }
  extendButtons.push({
    key: 'close',
    text: '关闭',
    icon: 'el-icon-close',
    buttonType: '',
  });
  let rowInfo = await ProjBizConstructionImageMApi.view(row.id);
  const formData = rowInfo.data;
  proxy.$DialogForm.show({
    title: "查看",
    type: option.value.dialogType,
    width: "80%",
    el: myRef.value,
    content: EditProjBizConstructionImageM,
    data: {
      formData: formData,
      type: editType,
    },
    option: {
      submitBtn: false,
      emptyBtn: false,
      emptyText: '取消',
      extendButton: extendButtons
    },
    callback: (res) => {
      if (res.type === 'edit') {
        res.close();
        onEditData(row);
      } else if (res.type === 'close') {
        if (editType === 'edit') {
          proxy.$confirm('确认关闭？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            res.close();
          }).catch(() => {});
        } else {
          res.close();
        }
      } else if (res.type && res.type !== 'close') {
        if (res.dialogRefs) {
          res.dialogRefs.submitData(res.type).then((flag) => {
            if (flag) {
              getPageList();
              res.close();
            }
          });
        }
      } else {
        res.close();
      }
    }
  });
}

function onDelData(rows) {
  //删除行
  if (!rows || rows.length == 0) {
    proxy.$message.info("请勾选数据！");
    return false;
  }
  let ids = rows.map((item) => {
    return item.id;
  });
  proxy.$modal.confirm("确认删除数据项？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    ProjBizConstructionImageMApi.remove(ids).then((res) => {
      proxy.$message.success("已删除");
      getPageList();
    });
  }).catch(() => {});
}

function onPublish(row, index) {
  proxy.$modal.confirm("确认发布该记录？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    listData.value[index].publishStatus = 1;
    if (row.id) {
      ProjBizConstructionImageMApi.update({
        ...listData.value[index],
        publishStatus: 1
      }).then(() => {
        proxy.$message.success("发布成功");
        getPageList();
      }).catch(() => {
        listData.value[index].publishStatus = 0;
      });
    } else {
      proxy.$message.success("发布成功");
      getPageList();
    }
  }).catch(() => {});
}

function onUnpublish(row, index) {
  proxy.$modal.confirm("确认撤回该记录？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    listData.value[index].publishStatus = 0;
    if (row.id) {
      ProjBizConstructionImageMApi.update({
        ...listData.value[index],
        publishStatus: 0
      }).then(() => {
        proxy.$message.success("撤回成功");
        getPageList();
      }).catch(() => {
        listData.value[index].publishStatus = 1;
      });
    } else {
      proxy.$message.success("撤回成功");
      getPageList();
    }
  }).catch(() => {});
}

</script>

<style lang="scss">
.image-link {
  color: #1a5cff !important;
  cursor: pointer;
  text-decoration: none;
}
.image-link:hover {
  text-decoration: underline;
}
.text-overflow {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>

