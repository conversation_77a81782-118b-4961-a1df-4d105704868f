@use './element-ui.scss';
@use './sidebar.scss';
@use './var.scss';
:root {
  --ep-font-size-base: 12px; /* 覆盖基础字号 */
}
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  list-style: none;
  font-family:
    PingFang SC,
    Microsoft YaHei,
    Arial,
    sans-serif;
}

html {
  height: 100%;
}

body {
  width: 100%;
  height: 100%;
  font-family:
    PingFang SC,
    Microsoft YaHei,
    Arial,
    sans-serif;
  background: var(--bg-f1f2f6);
  overflow-x: hidden;
}

*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

#app {
  height: 100%;
}

::-webkit-scrollbar-track {
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(144, 147, 153, 0.3);
  border-radius: 10px;
  min-height: 20px;
  background-clip: content-box;
}

::-webkit-scrollbar {
  width: 16px;
  height: 16px;
}

::-webkit-scrollbar-track,
::-webkit-scrollbar-thumb {
  border-radius: 999px;
  border: 5px solid transparent;
}

::-webkit-scrollbar-corner {
  background: transparent;
}

html::-webkit-scrollbar,
body::-webkit-scrollbar,
#app::-webkit-scrollbar {
  width: 0;
}

.dialog_content {
  height: 400px;
  padding: 10px;
  overflow-y: auto;
}

:deep(.ep-dialog__headerbtn) {
  top: 20px;
}

fieldset {
  border: 1px solid #e1e5ec;
  padding: 5px 10px;
  margin-bottom: 10px;
  border-radius: 2px;
}

legend {
  font-weight: bold;
  font-size: 14px;
  color: #333;
  padding: 0 10px;
  background: var(--bg-fff-dark-t);
}

.box-card {
  margin: 10px 0;
}

.ep-form-item__label {
  padding: 0;
  padding-right: 10px;
}
.viewVox {
  padding: 10px;
  background: var(--bg-fff-dark-t);

  .ep-form {
    border: 1px solid var(--color-dddddd);
    background: var(--bg-fff-dark-t);
    display: table;
    border-bottom: none;
    border-right: none;
  }

  .ep-input-number__decrease,
  .ep-input-number__increase {
    display: none;
  }

  .ep-textarea {
    margin-bottom: 10px;
  }

  .static-content-item {
    border-right: 1px solid var(--color-dddddd);
  }

  .ep-select {
    width: 100%;
  }

  .ep-form-item__error {
    top: 61%;
    left: 24px;
    z-index: 2;
  }

  .ep-form-item {
    margin-bottom: 0;
    border-bottom: 1px solid var(--color-dddddd);

    .ep-form-item__content {
      border-right: 1px solid var(--color-dddddd);
      border-left: 1px solid var(--color-dddddd);
      padding: 5px 10px 0;

      .ep-input__inner,
      .ep-textarea__inner {
        border: none;
      }
    }

    label {
      padding: 5px 0 5px 10px;
    }
  }
}
.ep-select {
  width: 100%;
}
.ep-tree-node__content {
  height: 40px;
}
.svg-icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
.sp-dialog__footer {
  .ep-button + .ep-button {
    margin-left: 16px !important;
  }
}
.sp-form__menu--center {
  .ep-button {
    margin: 0;
  }
  .ep-button + .ep-button {
    margin-left: 16px !important;
  }
}
.ep-drawer__header {
  border-bottom: 1px solid #dcdfe6 !important;
  margin-bottom: 0;
}

.main_contation {
  width: 100%;
}
.ep-dialog__title {
  font-size: 14px;
  font-weight: bold;
}
.ep-menu--collapse {
  .ep-sub-menu {
    .ep-sub-menu__title {
      display: flex;
      align-items: center;
      justify-content: center;
      .title {
        height: 0;
        width: 0;
        overflow: hidden;
        visibility: hidden;
        display: inline-block;
      }
    }
  }
  .ep-menu-item {
    justify-content: center;
    .title {
      height: 0;
      width: 0;
      overflow: hidden;
      visibility: hidden;
      display: inline-block;
    }
  }
}
.ep-form-item__label,
.ep-tabs__item,
.custom-tree-node,
.ep-input,
.ep-textarea {
  font-size: 14px;
}
.ep-tag {
  height: 30px;
}
.ep-button:focus-visible {
  outline: none !important;
  outline-offset: 0 !important;
}
.ep-textarea__inner {
  font-family:
    PingFang SC,
    Microsoft YaHei,
    Arial,
    sans-serif !important;
}
