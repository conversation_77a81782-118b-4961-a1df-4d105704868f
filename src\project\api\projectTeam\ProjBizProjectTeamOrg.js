import { request, replaceUrl } from "sn-base-utils";

export default class ProjBizProjectTeamOrgApi {
    static config = {
        add: {
            url: '/project/projectTeam/add',
            method: 'POST'
        },

        addUser: {
            url: '/project/projectTeam/addUser',
            method: 'POST'
        },




        remove: {
            url: '/project/projectTeam/delete',
            method: 'DELETE'
        },
        update: {
            url: '/project/projectTeam/update',
            method: 'PUT'
        },
        view: {
            url: '/project/projectTeam/get/{id}',
            method: 'GET'
        },

        viewDetail: {
            url: '/project/projectTeam/viewDetail/{id}',
            method: 'GET'
        },

        pageList: {
            url: '/project/projectTeam/page',
            method: "POST"
        },
        list: {
            url: '/project/projectTeam/list',
            method: "POST"
        },


        queryOrgTreeList: {
            url: '/project/projectTeam/queryOrgTreeList',
            method: "POST"
        },
        queryOrgCompanyList: {
            url: '/project/projectTeam/queryOrgCompanyList',
            method: "POST"
        },

        pageDetailList: {
            url: '/project/projectTeam/pageDetailList',
            method: "POST"
        },



    };

    /**
     * 新增项目团队
     * @param data
     * @returns {*}
     */
    static add(data) {
        return request({
            url: this.config.add.url,
            method: this.config.add.method,
            data: data
        });
    }



    /**
    * 新增项目团队
    * @param data
    * @returns {*}
    */
    static addUser(data) {
        return request({
            url: this.config.addUser.url,
            method: this.config.addUser.method,
            data: data
        });
    }



    /**
     * 删除项目团队
     * @param data
     * @returns {*}
     */
    static remove(data) {
        return request({
            url: this.config.remove.url,
            method: this.config.remove.method,
            data: data
        });
    }

    /**
     * 更新项目团队
     * @param data
     * @returns {*}
     */
    static update(data) {
        return request({
            url: this.config.update.url,
            method: this.config.update.method,
            data: data
        });
    }

    /**
     * 查询项目团队详细
     * @param id
     * @returns {*}
     */
    static view(id) {
        return request({
            url: replaceUrl(this.config.view.url, { id }),
            method: this.config.view.method
        });
    }

    /**
    * 查询项目团队详细
    * @param id
    * @returns {*}
    */
    static viewDetail(id) {
        return request({
            url: replaceUrl(this.config.viewDetail.url, { id }),
            method: this.config.viewDetail.method
        });
    }





    /**
     * 分页查询项目团队列表
     * @param data
     * @returns {*}
     */
    static pageList(data) {
        return request({
            url: this.config.pageList.url,
            method: this.config.pageList.method,
            data: data
        });
    }

    /**
    * 分页查询项目团队列表
    * @param data
    * @returns {*}
    */
    static pageDetailList(data) {
        return request({
            url: this.config.pageDetailList.url,
            method: this.config.pageDetailList.method,
            data: data
        });
    }

    /**
     * 全部项目团队列表
     * @returns {*}
     */
    static list(data) {
        return request({
            url: this.config.list.url,
            method: this.config.list.method,
            data: data
        });
    }


    static queryOrgTreeList(data) {
        return request({
            url: this.config.queryOrgTreeList.url,
            method: this.config.queryOrgTreeList.method,
            data: data
        });
    }
    static queryOrgCompanyList(data) {
        return request({
            url: this.config.queryOrgCompanyList.url,
            method: this.config.queryOrgCompanyList.method,
            data: data
        });
    }


}
