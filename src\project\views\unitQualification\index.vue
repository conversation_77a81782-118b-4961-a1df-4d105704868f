<template>
  <div>
    <sn-crud :data="listData" :option="option" v-model:page="queryForm.page" v-model:search="queryForm.filter"
      @on-load="getPageList" @search-change="onChangeSearch" @search-reset="onResetSearch" @addBtnHandle="onEditData"
      @row-del="onDelData">

      <template #menu="{ row, index, size }">
        <!--草稿-->
        <el-button type="primary" :size="size" v-if="row.state === '1'" icon="el-icon-edit" link
          @click="onEditData(row,'edit')">编辑</el-button>
        <el-button type="primary" :size="size" v-if="row.state === '1'" icon="el-icon-download" link
          @click="onDownloadData(row)">下载</el-button>
        <el-button type="danger" :size="size" v-if="row.state === '1' && row.processInstanceId" icon="el-icon-edit" link
          @click="onCancelData(row, 'stop')">作废</el-button>
        <el-button type="danger" :size="size" v-if="row.state === '1' && !row.processInstanceId" icon="el-icon-delete"
          link @click="onDelData([row])">删除</el-button>
        <!--已提交-->
        <el-button type="danger" :size="size" v-if="row.state === '2'" icon="el-icon-edit" link
          @click="onCancelData(row, 'revoke')">撤回</el-button>
        <el-button type="primary" :size="size" v-if="row.state === '2'" icon="el-icon-download" link
          @click="onDownloadData(row)">下载</el-button>
        <!--驳回上一节点-->
        <el-button type="primary" :size="size" v-if="row.state === '3'" icon="el-icon-download" link
          @click="onDownloadData(row)">下载</el-button>
        <!--审批中-->
        <el-button type="primary" :size="size" v-if="row.state === '4'" icon="el-icon-download" link
          @click="onDownloadData(row)">下载</el-button>
        <!--已作废-->
        <el-button type="primary" :size="size" v-if="row.state === '5'" icon="el-icon-download" link
          @click="onDownloadData(row)">下载</el-button>
        <!--已审批-->
        <el-button type="primary" :size="size" v-if="row.state === '6'" icon="el-icon-download" link
          @click="onDownloadData(row)">下载</el-button>
        <!--已驳回提交人-->
        <el-button type="primary" :size="size" v-if="row.state === '7'" icon="el-icon-edit" link
          @click="onEditData(row, 'edit')">编辑</el-button>
        <el-button type="primary" :size="size" v-if="row.state === '7'" icon="el-icon-download" link
          @click="onDownloadData(row)">下载</el-button>
        <el-button type="danger" :size="size" v-if="row.state === '7' && row.processInstanceId" icon="el-icon-edit" link
          @click="onCancelData(row, 'stop')">作废</el-button>
      </template>

      <template #code="{ row }">
        <el-button type="text" @click="onEditData(row,'view')">{{ row.code }}</el-button>
      </template>




      <template #header>
        <el-button type="primary" icon="el-icon-plus" style="margin-left: 10px;" @click="onEditData(row,'add')">新增</el-button>
        <!-- <el-button type="primary" icon="el-icon-document" @click="onListTemplate">列表模板</el-button>
        <el-button type="primary" icon="el-icon-upload" @click="onListImport">列表导入</el-button> -->
        <el-button type="primary" icon="el-icon-download" @click="onExportData">列表导出</el-button>
      </template>
      <template #contractorName="{ row }">
        <el-tooltip class="item" effect="dark" :content="row.contractorName" placement="top">
          <div class="text-overflow">{{ row.contractorName }}</div>
        </el-tooltip>
      </template>
    </sn-crud>
    <div ref="myRef"></div>
  </div>
</template>

<script>
import ProjBizUnitQualificationApi from '@/project/api/unitQualification/ProjBizUnitQualification.js'
import EditProjBizUnitQualification from "./components/EditProjBizUnitQualification.vue";
import { getToken } from "sn-base-utils";
import { dayjs } from 'element-plus';
export const routerConfig = [{
  menuType: "C",
  menuName: "单位资质填报",
}, {
  menuType: "F",
  menuName: "查看",
  perms: "view",
  api: [ProjBizUnitQualificationApi.config.pageList],
}, {
  menuType: "F",
  menuName: "新增",
  perms: "add",
  api: [ProjBizUnitQualificationApi.config.add],
}, {
  menuType: "F",
  menuName: "修改",
  perms: "update",
  api: [ProjBizUnitQualificationApi.config.update, ProjBizUnitQualificationApi.config.view],
}, {
  menuType: "F",
  menuName: "删除",
  perms: "del",
  api: [ProjBizUnitQualificationApi.config.remove],
}];
</script>

<script setup>

import {
  ref,
  getCurrentInstance
} from 'vue';
const {
  proxy
} = getCurrentInstance()
let option = ref({
  tip: false,
  // dialogType: "page",
  dialogType: "page",
  border: true,
  index: true,
  searchLabelWidth: 100,
  stripe: true,
  menu: true,
  header: false,
  height: "auto",
  searchSpan: 6,
  searchIcon: true,
  searchMenuSpan: 6,
  searchIndex: 3,
  selection: false,
  menuWidth: 300,
  showTree: false,
  excelBtn: false,
  headerAlign: true,
  delBtn: false,
  editBtn: false,
  delBtnsText: "批量删除",
  addBtnText: "新增",
  customEditBtnText: "编辑",
  customDelBtnText: "删除",
  customViewBtnText: "查看",
  column: [{
    label: "业务编码",

    prop: "code",
    columnSlot: false,
    searchSlot: false,
    width: 150,
  }, {
    label: "承包商名称",
    prop: "contractorName",
    search: true,
    searchLabelWidth: 130,
    queryType: "LIKE"
  }, {
    label: "附件数量",
    width: 100,
    prop: "fileNum"
  }, {
    label: "创建人",
    prop: "createName"
  }, {
    label: "创建时间",
    width: 100,
    prop: "createTime",

    formatter: (row, column, value) => {
      // 使用dayjs进行时间格式化，确保value存在
      if (!value) return '';
      // 默认格式为YYYY-MM-DD HH:mm:ss，您可以根据需要修改
      return dayjs(value).format('YYYY-MM-DD');
    }
  }, {
    label: "审批状态",
    prop: "state",
    search: true,
    width: 100,
    align: "center", //对齐方式：居中
    type: 'select',
    dicUrl: "/system/dict/data/type/global_biz_flow_status",
    dicMethod: "get",
    props: {
      label: "dictLabel",
      value: "dictValue"
    },
    html: true,
    formatter: (val, value) => {
      value = value + ''
      if (value === '1') {
        return `<span style="color:#4871C0">草稿</span>`;
      } else if (value === '2') {
        return `<span style="color:#4871C0">已提交</span>`;
      } else if (value === '4') {
        return `<span style="color:#4871C0">审批中</span>`;
      } else if (value === '3') {
        return `<span style="color:red">上级驳回</span>`;
      } else if (value === '5') {
        return `<span style="color:red">已作废</span>`;
      } else if (value === '6') {
        return `<span style="color:dodgerblue">已审批</span>`;
      } else if (value === '7') {
        return `<span style="color:red">已驳回</span>`;
      }
      return `<p></p>`;
    }
  }]
});
let listData = ref([]);
let queryForm = ref({
  page: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  filter: {
    createTime: null
  }
});
let formRules = ref({});
const myRef = ref(null);

function getPageList() {
  //查询分页列表
  // 查询前处理参数
  const params = handleQueryForm();
  ProjBizUnitQualificationApi.pageList(params).then((res) => {
    listData.value = res.data.dataList;
    queryForm.value.page.total = res.data.totalCount;
  });
}

function onDownloadData(row) {
  let params = {
    relevantId: row.id,
    type: 'unitQualification'
  }
  let timestamp = proxy.parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')
  proxy.download('/project/document/storage/exportZipByRelevantId', params, '单位资质申报-' + row.code + '-' + timestamp + '.zip')
}

/*流程作废*/
function onCancelData(row, type) {
  //删除行
  let formDate = {
    id: row.id,
    operateFlowStatus: type,
  }
  let typeName;
  if (type == 'revoke') {
    typeName = '撤回';
  } else {
    typeName = '作废';
  }
  proxy.$modal.confirm("确认" + typeName + "流程？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    ProjBizUnitQualificationApi.operateFlow(formDate).then((res) => {
      proxy.$message.success("已" + typeName);
      getPageList();
    });
  }).catch(() => { });
}
function onResetSearch() {
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
}
function operateFlow(row, type) {
  console.log("type......", type)
  let typeName;
  if (type === 'revoke') {
    typeName = '撤回';
  } else if (type === 'stop') {
    typeName = '作废';
  }
  proxy.$modal.confirm("确认要" + typeName + "流程？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    ProjBizUnitQualificationApi.operateFlow({
      id: row.id,
      operateFlowStatus: type,
    }).then((res) => {
      proxy.$message.success("已" + typeName);
      // 重新请求接口刷新列表
      getList()
    });
  }).catch(() => { });
}

// 列表导出
function onExportData() {
  const params = handleQueryForm()
  let queryForm = JSON.parse(JSON.stringify(params))
  let timestamp = proxy.parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')
  proxy.download('/project/unitQualification/export', queryForm, '单位资质填报.xlsx')
}
async function onDetailClick(row) {
  let extendButtons = [
    {
      key: 'close',
      text: '关闭',
      icon: 'el-icon-close',
      buttonType: '',
    },
  ];
  if (!(row.processInstanceId && row.processInstanceId.trim() !== '')) {

    extendButtons = [
      {
        key: 'edit',
        text: '编辑',
        icon: 'el-icon-edit',
        buttonType: 'primary',
        click: () => {
          onEditData(row);
        }
      },
      {
        key: 'close',
        text: '关闭',
        icon: 'el-icon-close',
        buttonType: '',
      },
    ]
  }

  //编辑,新增按钮操作
  let editType = "view";
  console.log('editType:', editType)
  let rowInfo = await (editType !== "add" ? ProjBizUnitQualificationApi.view(row.id) : {});
  const formData = editType !== "add" ? rowInfo.data : rowInfo;
  //编辑,新增按钮操作
  proxy.$DialogForm.show({
    title: editType == "view" ? "查看" : "编辑",
    type: option.value.dialogType,
    el: myRef.value,
    content: EditProjBizUnitQualification,
    data:
      (row.processInstanceId == null || row.processInstanceId === "") ?

        {
          formData: formData,
          type: editType,
          id: row ? row.id : null,
          approvalOption: ref({
            isShowApprovalList: false,
            isShowFlowDiagram: false,
            procInstId: rowInfo.data.processInstanceId
          }),
        }
        :
        {
          formData: formData,
          type: editType,
          id: row ? row.id : null,
          approvalOption: ref({
            isShowApprovalList: true,
            isShowFlowDiagram: true,
            procInstId: rowInfo.data.processInstanceId
          }),
        },
    option: {
      submitBtn: false,
      emptyBtn: false,
      emptyText: '取消',
      extendButton: extendButtons,
    },
    callback: (res) => {
      if (res.type && res.type !== 'close') {
        if (res.dialogRefs) {
          console.log('res.type==', res.type)
          res.dialogRefs.submitData(res.type).then((flag) => {
            if (flag) {
              getPageList();
              res.close();
            }
          });
        }
      } else {
        res.close();
      }
    }
  });
}
function handleQueryForm() {
  // 处理参数
  const {
    pageSize,
    pageNum
  } = queryForm.value.page;
  const filter = {}
  for (let key in queryForm.value.filter) {
    if (queryForm.value.filter[key]?.length) {
      // sn-curd搜索select会生成$prop的变量
      if (key.startsWith("$")) continue;
      filter[key] = queryForm.value.filter[key];
    }
  }
  delete filter.createTime;
  if (Array.isArray(queryForm.value.filter.createTime) && queryForm.value.filter.createTime?.length === 2) {
    filter["beginCreateTime"] = queryForm.value.filter.createTime[0];
    filter["endCreateTime"] = queryForm.value.filter.createTime[1];
  }
  filter.projectId = sessionStorage.getItem('projectId')
  filter['treeType'] = 'UNIT';
  const searchParams = {
    page: {
      pageSize,
      pageNum,
    },
    filter,
  }
  return searchParams
}

function onChangeSearch(params, done) {
  //搜索按钮操作
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
  done && done();
}
/**
 * 下载文件
 * @param row
 */
function onDownData(row) {
  proxy.download("/project/unitQualification/exportZip", [row.id], row.fileName);
}
async function onEditData(row,type) {
  //编辑,新增按钮操作
  // let editType = row?.processInstanceId ? 'view' : (row?.id ? 'edit' : 'add');
    let editType =type?type: (row?.processInstanceId ? 'view' : (row?.id ? 'edit' : 'add'));
  let rowInfo = await (editType !== "add" ? ProjBizUnitQualificationApi.view(row.id) : {});
  // const formData = editType !== "add" ? rowInfo.data : rowInfo;
  //编辑,新增按钮操作
  proxy.$DialogForm.show({
    // title: editType == "edit" ? "编辑" : "新增",
    title: editType == "edit" ? "编辑" : (editType == "view" ? "查看" : "新增"),
    type: option.value.dialogType,
    el: myRef.value,
    content: EditProjBizUnitQualification,
    data: editType !== "add" ? {
      formData: rowInfo.data,
      isShowCloseBtn: false,
      approvalOption: ref({
        isShowApprovalList: true,
        isShowFlowDiagram: true,
        procInstId: rowInfo.data.processInstanceId
      }),
        el: myRef.value,
       closeDialog: closeDialog,
      type: editType
    } : {
      approvalOption: ref({
        isShowApprovalList: false,
        isShowFlowDiagram: false,
        procInstId: null
      }),
        el: myRef.value,
                  closeDialog: closeDialog,
      type: editType
    },
    option: {
      submitBtn: false,
      emptyBtn: false,
      submitText: '保存',
      emptyText: '取消',
      extendButton: generateButtons(editType,row),
    },
    callback: (res) => {

      let vm = res.dialogRefs
      if (vm) {
        let formData = vm.getFormData();
        switch (res.type) {
           case 'cancelDirect':
            getPageList();
            res.close();
            break;
          // 取消
          case 'cancel':
            if (editType === 'edit') {
              proxy.$confirm('确认关闭？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                res.close();
              }).catch(() => {
                // 用户点击取消，不关闭弹窗
              });
            } else {
              res.close();
            }

            break;
          // 保存
          case 'save':
            if(!formData.state){
              formData.state = '1'
            }
            vm.getSaveFormData(formData).then(resp => {
              proxy.$message.success("保存成功");
              getPageList();
              // res.close();
            })
            break;
          case 'edit':
            res.close();
            onEditData(row)
            break;  
          // 发起流程
          case 'afterProcessIsInitiated':
            let startProcDto = {
              businessKey: null,
              clientId: null
            };

            if(formData.processInstanceId){
              let taskInfo = vm.getTaskInfo();
              vm.handlerOpenConfirm(taskInfo,res);
              getPageList();
            }else {
          vm.getStartFlow(formData, startProcDto).then(resp => {
              proxy.$message.success("流程启动成功");
               let taskInfo = vm.getTaskInfo();
              vm.handlerOpenConfirm(taskInfo,res)
              getPageList();
            })
            }
            break;
          case 'submitFlowTaskProcess':
            vm.submitFlowTask()
            // res.close();
            break;
        }
      }


    }
  });
}


function generateButtons(editType, row) {
  if (editType !== "view") {
    const hasProcId = row?.processInstanceId;
    const buttons = [
      { key: 'cancel', text: '关闭', buttonType: '', icon: 'el-icon-close' }
    ];

    if (hasProcId) {
      buttons.push({
        key: 'save',
        text: '保存',
        buttonType: 'primary',
        icon: 'el-icon-check'
      }, {
        key: 'submitFlowTaskProcess',
        text: '提交',
        buttonType: 'primary',
        icon: 'sn-button-fasong'
      });
    } else {
      buttons.push({
        key: 'save',
        text: '保存',
        buttonType: 'primary',
        icon: 'el-icon-check'
      }, {
        key: 'afterProcessIsInitiated',
        text: '发起流程',
        buttonType: 'primary',
        icon: 'sn-button-fasong'
      });
    }
    return buttons;
  }
  return row.approvalStatus != '1' ? [
    { key: 'cancelDirect', text: '关闭', buttonType: '', icon: 'el-icon-close' }
  ] : [
    { key: 'edit', text: '编辑', buttonType: 'primary', icon: 'el-icon-edit' },
    { key: 'cancelDirect', text: '关闭', buttonType: '', icon: 'el-icon-close' }
  ];
}
function closeDialog(e) {
  getPageList();
  e.close()
}
function onDelData(rows) {
  //删除行
  if (!rows || rows.length == 0) {
    proxy.$message.info("请勾选数据！");
    return false;
  }
  let ids = rows.map((item) => {
    return item.id;
  });
  proxy.$modal.confirm("确认删除数据项？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    ProjBizUnitQualificationApi.remove(ids).then((res) => {
      proxy.$message.success("已删除");
      getPageList();
    });
  }).catch(() => { });
}
</script>

<style lang="scss" scoped>
//文本过长省略号
.text-overflow {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
