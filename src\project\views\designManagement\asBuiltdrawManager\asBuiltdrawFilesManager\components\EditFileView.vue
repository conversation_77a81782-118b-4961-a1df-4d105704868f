<template>
    <div class="contents">
        <div class="centent">
            <el-form :model="formData" :rules="formRules" ref="formRef" label-width="170px" label-position="right">
                <el-row :gutter="16" :span="24">
                    <el-card class="box-card" style="width: 100%;">
                        <fieldset class="fieldset2">
                            <legend>
                                <span class="el-button--primary"></span>基本信息
                            </legend>
                            <el-row :gutter="16" :span="24">
                                <el-col :span='8'>
                                    <el-form-item label="专业" prop="">
                                        <el-select v-model="formData.major" :disabled="true">
                                            <el-option v-for="(item, index) in designManagement_major" :key="index"
                                                :label="item.label" :value="item.value"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span='8'>
                                    <el-form-item label="卷册号" prop="">
                                        <el-input v-model="formData.volumeNum" :disabled="true" type="text"
                                            placeholder="" clearable>
                                        </el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span='8'>
                                    <el-form-item label="卷册名称" prop="">
                                        <el-input v-model="formData.volumeName" :disabled="true" type="text"
                                            placeholder="" clearable>
                                        </el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </fieldset>
                    </el-card>
                    <el-card class="box-card" style="width: 100%;">
                        <fieldset class="fieldset2">
                            <legend>
                                <span class="el-button--primary"></span>文件列表
                            </legend>
                            <project-document-storage-ui-table ref="outConditionFileRef" :type="formData.volumeNum"
                                :relevantId="formData.mainId" :isPageSearch="false" :isDeleteMinio="isDeleteMinio"
                                :isHasAi="isHasAi" @on-add-data="onAddData" @on-ai-review="onAiReview"
                                @on-preview="onPreview" @on-delete="onDelete"
                                :file-serial-number-builder="fileSerialNumberBuilder" :preview-config="previewConfig"
                                :isShowAddBtn="isShowAddBtn" :isShowDelBtn="isShowDelBtn"
                                :isShowPreviewBtn="isShowPreviewBtn"
                                :isShowDownloadBtn="isShowDownloadBtn"></project-document-storage-ui-table>
                        </fieldset>
                    </el-card>
                </el-row>
            </el-form>
        </div>
    </div>
</template>




<script setup>
import {
    ref,
    toRef,
    defineProps,
} from 'vue';
import { useDicts } from '@/common/hooks/useDicts'
const { designManagement_major } = useDicts(["designManagement_major"]);
const props = defineProps({
  data: Object
})
const formRef = ref()
let formData = ref({
    major: "",
    volumeName: "",
    volumeNum: "",
    projectId: "",
    mainId: ""
});

formData = toRef(
    Object.keys(props.data?.formData || {}).length > 0
        ? {
            ...props.data?.formData
        }
        : toRef(formData.value)
)


let formRules = ref({});


const outConditionFileRef = ref(null);









const previewConfig = ref({
    // 是否外置预览,必传
    isExternalPreview: false,
    // 内置预览返回的替换字符,akey代表要替换的字符串，avalue代码替换的字符串
    substitute: {
        "akey": "avalue"
    },
    // 内置预览服务地址
    previewServerUrl: "http://127.0.0.1:8012/onlinePreview",
})
const isDeleteMinio = ref(true)
const isHasAi = ref(true)
const isShowAddBtn = ref(true)
const isShowDelBtn = ref(true)
const isShowPreviewBtn = ref(true)
const isShowDownloadBtn = ref(true)

function onAddData(list) {
    console.log(list, "onAddData-sssss")
}

function onAiReview(row) {
    console.log(row, "onAiReview-sssss")
}

function onPreview(row) {
    console.log(row, "onPreview-sssss")
}

function onDelete(list) {
    console.log(list, "onDelete-sssss")
}

function getListData(dom, type) {
    if (dom.value) {
        let list = dom.value.getListData();
        list.forEach(item => item.type = type);
        return list;
    }
}

// 文件序列号生成函数
function fileSerialNumberBuilder() {
    return "TEST" + Math.floor(Math.random() * 10000)
}







</script>

<style lang="scss" scoped>
.contents {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: calc(100vh - 88px);
    overflow: hidden;
}

.top {
    height: 80px;
    width: 100%;
    flex: 0 1 auto;
    padding: 0 15px 0px 0px;
    box-sizing: border-box;
}

.centent {
    height: 100px;
    width: 100%;
    flex: auto;
    overflow-y: auto;
    overflow-x: hidden;
}
</style>
