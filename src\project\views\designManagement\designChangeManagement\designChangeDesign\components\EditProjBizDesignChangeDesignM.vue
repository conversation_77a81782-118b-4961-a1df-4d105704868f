<template>
  <flow-page-container ref="flowPageContainerRef" @handlerAction="handlerAction" @initData="initData" @handlerPrint="handlerPrint" :closeBtn="isShowCloseBtn" :approvalOption="approvalOption" @approvalOptionCallback="getApprovalOption">
    <el-form :model="formData" :rules="formRules" ref="formRef" label-width="165px" label-position="right" :disabled="type == 'view'">
      <el-row :gutter="16" :span="24">
        <el-card class="box-card" style="width: 100%;">
          <fieldset class="fieldset2">
            <legend>
              <span class="el-button--primary"></span>基本信息
            </legend>
            <el-row :gutter="16" :span="24">
              <el-col :span='8' v-permi="formPermi({fieldModelId:'projBizDesignChangeDesignM', field:'docCode', fieldName: '设计变更单编号'}, customFormPermi)">
                <el-form-item label="设计变更单编号" prop="docCode">
                  <el-input v-model="formData.docCode" type="text" placeholder="请输入" clearable></el-input>
                </el-form-item>
              </el-col>
              <el-col :span='8' v-permi="formPermi({fieldModelId:'projBizDesignChangeDesignM', field:'changDesignCode', fieldName: '变更申请单编号'}, customFormPermi)">
                <el-form-item label="变更申请单编号" prop="changDesignCode">
                  <el-select v-model="formData.changDesignCode" clearable placeholder="请选择" @change="changeRequestCode">
                    <el-option v-for="(item, index) in designChangeRequestListData" :key="index" :label="item.docCode" :value="item.docCode"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span='8' v-permi="formPermi({fieldModelId:'projBizDesignChangeDesignM', field:'changeType', fieldName: '变更类型'}, customFormPermi)">
                <el-form-item label="变更类型" prop="changeType">
                  <el-select v-model="formData.changeType" :disabled="isDisabled" clearable placeholder="请选择">
                    <el-option v-for="(item, index) in changeTypeOption" :key="index" :label="item.value" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span='8' v-permi="formPermi({fieldModelId:'projBizDesignChangeDesignM', field:'volumeNumber', fieldName: '卷册号'}, customFormPermi)">
                <el-form-item label="卷册号" prop="volumeNumber">

<!--                  <el-select v-model="formData.volumeNumber" clearable placeholder="请选择" @change="changeVolumeNumn">-->
<!--                  <el-option v-for="(item, index) in designWorkdrawOutPlanData" :key="index" :label="item.volumeNum" :value="item.volumeNum"></el-option>-->
<!--                </el-select>-->

                  <el-input v-model="formData.volumeNumber" type="text" :readonly="true" @click="onEditDataj" placeholder="请选择" clearable></el-input>
                </el-form-item>
              </el-col>
              <el-col :span='8' v-permi="formPermi({fieldModelId:'projBizDesignChangeDesignM', field:'volumeName', fieldName: '卷册名称'}, customFormPermi)">
                <el-form-item label="卷册名称" prop="volumeName">
                  <el-input v-model="formData.volumeName" type="text" :disabled="true" placeholder="请输入" clearable>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span='24' v-permi="formPermi({fieldModelId:'projBizDesignChangeDesignM', field:'changeReason', fieldName: '变更原因'}, customFormPermi)">
                <el-form-item label="变更原因" prop="changeReason">
                  <el-input type="textarea" v-model="formData.changeReason" :disabled="isDisabled" placeholder="请输入" rows="3" clearable></el-input>
                </el-form-item>
              </el-col>
              <el-col :span='24' v-permi="formPermi({fieldModelId:'projBizDesignChangeDesignM', field:'changeContent', fieldName: '变更内容'}, customFormPermi)">
                <el-form-item label="变更内容" prop="changeContent">
                  <el-input type="textarea" v-model="formData.changeContent" :disabled="isDisabled" placeholder="请输入" rows="3" clearable></el-input>
                </el-form-item>
              </el-col>
              <el-col :span='8' v-permi="formPermi({fieldModelId:'projBizDesignChangeDesignM', field:'isContractText', fieldName: '是否涉及合同文本变更'}, customFormPermi)">
                <el-form-item label="是否涉及合同文本变更" prop="isContractText">
                  <el-select v-model="formData.isContractText" clearable placeholder="请选择">
                    <el-option v-for="(item, index) in isContractTextOption" :key="index" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span='8' v-permi="formPermi({fieldModelId:'projBizDesignChangeDesignM', field:'isAmountChange', fieldName: '是否涉及合同金额变更'}, customFormPermi)">
                <el-form-item label="是否涉及合同金额变更" prop="isAmountChange">
                  <el-select v-model="formData.isAmountChange" clearable placeholder="请选择">
                    <el-option v-for="(item, index) in isAmountChangeOption" :key="index" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span='8' v-permi="formPermi({fieldModelId:'projBizDesignChangeRequestM', field:'reportOrganization', fieldName: '编制单位'}, customFormPermi)">
                <el-form-item label="编制单位" prop="reportOrganization">
                  <el-input v-model="formData.reportOrganization" type="text" :disabled="true" clearable>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span='24' v-permi="formPermi({fieldModelId:'projBizDesignChangeDesignM', field:'remarks', fieldName: '备注'}, customFormPermi)">
                <el-form-item label="备注" prop="remarks">
                  <el-input type="textarea" v-model="formData.remarks"  placeholder="请输入" rows="3" clearable></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </fieldset>
        </el-card>
        <el-card class="box-card" style="width: 100%;">
          <fieldset class="fieldset2">
            <legend>
              <span class="el-button--primary"></span>附件信息
            </legend>
            <project-document-storage-ui-table
              ref="childRef"
              :type="fileType"
              :relevantId="relevantId"
              :isPageSearch="false"
              :isDeleteMinio = "isDeleteMinio"
              :isHasAi = "isHasAi"
              :file-serial-number-builder="fileSerialNumberBuilder"
              :preview-config="previewConfig"
              :isShowLinkBtn="false"
            ></project-document-storage-ui-table>
          </fieldset>
        </el-card>
        <el-card class="box-card" style="width: 100%;">
          <fieldset class="fieldset2">
            <legend>
              <span class="el-button--primary"></span>单据信息
            </legend>
            <el-row :gutter="16" :span="24">
              <el-col :span='8' v-permi="formPermi({fieldModelId:'projBizDesignChangeDesignM', field:'createName', fieldName: '创建人'}, customFormPermi)">
                <el-form-item label="创建人" prop="createName">
                  <el-input v-model="formData.createName" type="text" :disabled="true" placeholder="请输入" clearable>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span='8' v-permi="formPermi({fieldModelId:'projBizDesignChangeDesignM', field:'createTime', fieldName: '创建时间'}, customFormPermi)">
                <el-form-item label="创建时间" prop="createTime">
                  <el-input v-model="formData.createTime" type="text" :disabled="true" placeholder="请输入" clearable>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span='8' v-permi="formPermi({fieldModelId:'projBizDesignChangeRequestM', field:'approvalStatus', fieldName: '单据状态'}, customFormPermi)">
                <el-form-item label="单据状态" prop="approvalStatus">
                  <el-select v-model="formData.approvalStatus" clearable placeholder="" :disabled="true" filterable remote>
                    <el-option v-for="(item, index) in global_biz_flow_status" :key="index" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span='8' v-permi="formPermi({fieldModelId:'projBizDesignChangeDesignM', field:'updateName', fieldName: '最近修改人'}, customFormPermi)">
                <el-form-item label="最近修改人" prop="updateName">
                  <el-input v-model="formData.updateName" type="text" :disabled="true" placeholder="" clearable>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span='8' v-permi="formPermi({fieldModelId:'projBizDesignChangeDesignM', field:'updateTime', fieldName: '最近修改时间'}, customFormPermi)">
                <el-form-item label="最近修改时间" prop="updateTime">
                  <el-input v-model="formData.updateTime" type="text" :disabled="true" placeholder="" clearable>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </fieldset>
        </el-card>
      </el-row>
    </el-form>
  </flow-page-container>
</template>

<script setup>
import ProjBizDesignChangeDesignMApi from '@/project/api/designManagement/designChangeDesign/ProjBizDesignChangeDesignM.js'
import FlowPageContainer from '@/common/components/exportFile/FlowPageContainer.vue'
let auth = new Map();
import {
  nextTick,
  onMounted,
  ref,
  reactive,
  toRef,
  defineProps,
  defineExpose,
  getCurrentInstance
} from 'vue';
import {
  useRoute,
  useRouter
} from 'vue-router'
import {
  useDicts
} from "@/common/hooks/useDicts";
import ProjBizDesignChangeRequestMApi
  from '@/project/api/designManagement/designChangeRequest/ProjBizDesignChangeRequestM'
import store from '@/store'
import EditProjBizDesignIntentionsvolume from "@/project/views/design/components/EditProjBizDesignIntentionsvolume";
import { getCurrentFormattedTime } from '@/common/utils/datetime'
import { kkFileViewUrl } from '@/config'
import BpmTaskApi from '@/project/api/bpm/bpmTask'
import ProjBizDesignWorkdrawOutPlanMApi
  from '@/project/api/designManagement/workdrawManager/ProjBizDesignWorkdrawOutPlanM'
import ProjBizDesignIntentionMApi from '@/project/api/design/ProjBizDesignIntentionM'
const props = defineProps({
  data: Object,
});
const {
  proxy
} = getCurrentInstance()
// 文件组件定义参数
const fileType = ref("designChangeDesign")
const relevantId = ref(props.data?.formData?.id ?? null) // 关联业务ID，新增业务的时候传null（⽰例值）
const isDeleteMinio = ref(true)
const isShowAddBtn = ref(false); //上传
const isShowDelBtn = ref(false); //下载
const isHasAi = ref(true)
const previewConfig = ref({
  // 是否外置预览,必传
  isExternalPreview: false,
  // 内置预览返回的替换字符,akey代表要替换的字符串，avalue代码替换的字符串
  substitute: {
    "akey":"avalue"
  },
  // 内置预览服务地址
  previewServerUrl: kkFileViewUrl,
})

const route = useRoute();
const router = useRouter();
const formRef = ref()
const isShowCloseBtn = ref(false);
const isDisabled = toRef(false);
const FlowActionType = ref(proxy.FlowActionType);
let approvalOption = props.data?.approvalOption ? props.data?.approvalOption : route.query?.procInstId ? ref({
  isShowApprovalList: true,
  isShowFlowDiagram: true,
  procInstId: route.query?.procInstId
}) : ref({
  isShowApprovalList: false,
  isShowFlowDiagram: false,
  procInstId: ''
});
const state = reactive({
  flowData: {
    businessKey: "",
    procInstId: "",
    procDefKey: "",
    taskId: "",
    formConfig: [],
    variableList: [],
    customPropertyConfigList: []
  },
  formAuth: {},
  taskFlag: 1,
  firstAct: false,
  handlerClose: null,
  rootFields: {},
  fullFieldNameMap: {},
  formAuthList: {}
})
const type = toRef(props.data?.type);
const designChangeRequestListData = ref([]);
const designWorkdrawOutPlanData = ref([]);
const res=ref()
const flowPageContainerRef = ref();
let formData = ref({
  docCode: "",
  changDesignCode: "",
  changeType: "",
  volumeNumber: "",
  volumeName: "",
  changeReason: "",
  changeContent: "",
  isContractText: "",
  isAmountChange: "",
  remarks: "",
  createName: store.state.user && store.state.user.userInfo.userName ? store.state.user.userInfo.userName : '-',
  createTime:getCurrentFormattedTime(),
  reportOrganization: JSON.parse(store.state.user.orgName)?.find((item) => item.id === store.state.user.defaultOrg)?.orgName,
  status: "",
  updateName: "",
  updateTime: "",
  projBizDesignIntentionSDtoList: [],
});
let optionj = ref({
  tip: false,
  dialogType: "dialog",
  border: true,
  index: true,
  stripe: true,
  menu: true,
  header: true,
  height: "auto",
  searchSpan: 6,
  searchIcon: true,
  searchMenuSpan: 6,
  searchIndex: 3,
  selection: true,
  showTree: false,
  excelBtn: false,
  delBtnText: '删除',//管理卷册表格增加删除按钮提示
  delBtn: true,
  delBtns: false,            // 👈 显式隐藏批量删除按钮
  editBtn: false,
  delBtnsText: "批量删除",
  addBtnText: "关联卷册",
  customEditBtnText: "编辑",
  customDelBtnText: "删除",
  customViewBtnText: "查看",
  column: [{
    label: "专业",
    prop: "major",
    columnSlot: false,
    searchSlot: false
  }, {
    label: "卷册号",
    prop: "volumeNum",
    columnSlot: false,
    searchSlot: false
  }, {
    label: "卷册名称",
    prop: "volumeName",
    columnSlot: false,
    searchSlot: false
  }]
});
let formRules = ref({
  docCode: [{
    required: true,
    message: "请填写设计变更单编号"
  },
    {
      pattern: /^(?=.*[a-zA-Z])(?=.*\d)[A-Za-z0-9]+$/,
      trigger: ['blur', 'change'],
      message: '变更申请单编号必须包含字母和数字'
    }],
  changDesignCode: [],
  changeType: [
    {
      required: true,
      message: "请填写变更类型"
    }
  ],
  volumeNumber: [{
    required: true,
    message: "请填写卷册号",
    trigger: 'blur',
  }],
  volumeName: [{
    required: false,
    message: "请填写卷册名称"
  }],
  changeReason: [{
    required: true,
    message: "请填写变更原因"
  }],
  changeContent: [{
    required: true,
    message: "请填写变更内容"
  }],
  isContractText: [{
    required: true,
    message: "请填写是否涉及合同金额变更"
  }],
  isAmountChange: [{
    required: true,
    message: "请填写是否涉及合同文本变更"
  }],
  remarks: [{
    required: true,
    message: "请填写备注"
  }],
  createName: [],
  createTime: [],
  status: [],
  updateName: [],
  updateTime: []
});
let listvolumeData = ref([]);
let changeTypeOption = ref([
  {
    value: "一般变更"
  },
  {
    value: "较大变更"
  },
  {
    value: "重大变更"
  }
]);
let isContractTextOption = ref([{
  label: "是",
  value: "0"
}, {
  label: "否",
  value: "1"
}]);
let isAmountChangeOption = ref([{
  label: "是",
  value: "0"
}, {
  label: "否",
  value: "1"
}]);
formData = toRef(Object.keys(props.data?.formData || {}).length > 0 ? {
  ...props.data?.formData
} : toRef(formData.value));

function getFormData() {
  return formData.value
};
const {global_biz_flow_status} = useDicts(["global_biz_flow_status"])

const childRef = ref(null);

function getDesignChangeRequestDataInfo() {
  ProjBizDesignChangeRequestMApi.list({approvalStatus: 6}).then((resp) => {
    if (!!resp.data) {
      designChangeRequestListData.value = resp.data;
    }
  })
}

/*获取卷册号*/
function getVolumeInfo() {
  ProjBizDesignWorkdrawOutPlanMApi.list({}).then((resp) => {
    if (!!resp.data) {
      designWorkdrawOutPlanData.value = resp.data;
    }
  })
}

function changeVolumeNumn(value) {
  if(value){
    formData.value.volumeName = designWorkdrawOutPlanData.value.find((item) => item.volumeNum === value)?.volumeName;
  }
}

//关联卷册按钮操作
let existingData = []
async function onEditDataj(row) {
  //编辑,新增按钮操作
  let editType = row ? "edit" : "add";
  let rowInfo = await (editType !== "add" ? ProjBizDesignIntentionMApi.view(row.id) : {});
  const formData = editType !== "add" ? rowInfo.data : rowInfo;
  existingData =  getFormData().projBizDesignIntentionSDtoList
  proxy.$DialogForm.show({
    title: editType == "edit" ? "编辑" : "关联卷册",
    type: optionj.value.dialogType,
    width: "80%",
    content: EditProjBizDesignIntentionsvolume,
    data: {
      formData: formData,
      type: editType,
      existingData: existingData // 当前已关联的卷册数据
    },
    callback: (res) => {
      if (res.type && res.type !== 'close') {
        res.dialogRefs.submitData();
        listvolumeData.value = res.dialogRefs.sData;
        //获取formdata
        const formData = getFormData();
        formData.projBizDesignIntentionSDtoList=listvolumeData.value
        if(formData.projBizDesignIntentionSDtoList.length > 0) {
          // 获取已关联的卷册数据
          existingData = formData.projBizDesignIntentionSDtoList
          formData.volumeNumber = formData.projBizDesignIntentionSDtoList.map(item => item.volumeNum).join(',')
          formData.volumeName = formData.projBizDesignIntentionSDtoList.map(item => item.volumeName).join(',')
        }
        res.close();
      } else {
        res.close();
      }
    }
  });
}

/*获取变更申请单编号*/
function changeRequestCode(value){
  if(value){
    isDisabled.value = true;
    const designChangeRequestData = designChangeRequestListData.value.filter(
      item => {
        return item.docCode.indexOf(value) >= 0
      }
    )
    formData.value.changDesignCode=designChangeRequestData[0].docCode;
    formData.value.changeReason=designChangeRequestData[0].changeReason;
    formData.value.changeContent=designChangeRequestData[0].changeContent;
    formData.value.estimatedChangeAmount=designChangeRequestData[0].estimatedChangeAmount;
    formData.value.changeType=designChangeRequestData[0].changeType;
  }else {
    isDisabled.value = false;
    formData.value.changDesignCode=null;
    formData.value.changeReason=null;
    formData.value.changeContent=null;
    formData.value.estimatedChangeAmount=null;
    formData.value.changeType=null;
  }
}

function processAuthFieldForm(fieldList) {
  /**
   * 工作流表单权限功能：在扫描完成之前，业务可对表单字段进行修改。
   */
  //TODO 这里可以对权限表单的字段进行任意的修改,如果不满足业务需求，可以在这里手动添加你想要的字段
  return fieldList
};

function getApprovalOption(fun) {
  /**
   * 工作流表单权限功能(重要)：应对弹窗无法及时相应的问题，特别增加该函数修复该bug
   */
  nextTick(() => {
    fun(approvalOption.value)
  })
};

function customFormPermi(obj) {
  /**
   * 工作流表单权限功能：自定义表单权限处理函数，将对应的对象的属性还原成对应的权限值
   */
  let {
    field,
    fieldModelId
  } = obj
  let fullFieldName = `${fieldModelId}_${field}`
  //TODO 这里可以自定义处理，默认返回0即可
  return auth.get(fullFieldName) || 0
};

function handlerPrint(taskComment) {
  /**
   * 工作流打印模板功能
   */
  ProjBizDesignChangeDesignMApi.printTemplate({
    id: formData.value.id,
    templateId: taskComment.templateId,
    formData: formData.value
  }).then(
    (res) => {
      router.push({
        name: "PrintDoc",
        query: {
          fileId: res.data,
        },
      });
      taskComment.close && taskComment.close();
    });
};

function getSubmitTask(taskActionDto) {
  /**
   * 工作流提交任务功能
   */
  return ProjBizDesignChangeDesignMApi.submitTask({
    busiDto: formData.value,
    taskActionDto: taskActionDto
  });
};

function getStartFlow(formData, startProcDto) {
  return new Promise((resolve) => {
    formRef.value.validate((flag) => {
      if (!flag) {
        return false;
      } else {
        /*工作流启动流程功能*/
        resolve(ProjBizDesignChangeDesignMApi.startFlow({
          busiData: formData,
          startProcDto: startProcDto
        }).then(respon => {
          state.flowData.procDefKey = respon.data[0].procDefKey
          state.flowData.procInstId = respon.data[0].procInstId
          state.flowData.businessKey = respon.data[0].businessKey
          state.flowData.taskId = respon.data[0].taskId
          return respon
        }))
      }
    })
  })

};
//打开确认框
function handlerOpenConfirm(taskInfo,resp) {
  ProjBizDesignChangeDesignMApi.view(taskInfo.businessKey).then((resp) => {
    if (resp.data) {
      formData.value = resp.data;
      relevantId.value = resp.data.id;
      isShowAddBtn.value = true;
      isShowDelBtn.value = true;
    }
  })
  res.value=resp;
  flowPageContainerRef.value.handlerActionSubmit(taskInfo,1);
}
function handlerAction(operation, taskComment, handlerClose) {
  /**
   * 工作流操作功能
   */
  // 启动并保存草稿后再次提交
  if (
    (operation.type == FlowActionType.value.SAVESTART || operation.type == FlowActionType.value.SAVE || operation.type == FlowActionType.value.START) && !formData.value.taskId) {
    let startProcDto = {
      procDefKey: formData.value.procDefKey,
      businessKey: null,
      clientId: null,
      ...taskComment?.dialogRefs?.getFormData(),
    };
    let httpCall = null;
    if (operation.type == FlowActionType.value.SAVESTART) {
      httpCall = getStartFlow(formData, startProcDto);
    } else if (operation.type == FlowActionType.value.SAVE) {
      httpCall = getSaveFormData(formData);
    }
    httpCall.then(() => {
      proxy.$modal.msgSuccess("提交成功");
      taskComment.close && taskComment.close();
      handlerClose();
    });
  } else {
    operation.type == FlowActionType.value.START || operation.type == FlowActionType.value.SAVESTART ? (operation.type = FlowActionType.value.AGREE) : operation.type;
    let taskActionDto = {
      taskId: state.flowData.taskId,
      procInstId: state.flowData.procInstId,
      actionType: operation.type,
      ...taskComment?.dialogRefs?.getFormData(),
    };
    getSubmitTask(taskActionDto).then(() => {
      proxy.$modal.msgSuccess("任务办理成功");
      taskComment.close && taskComment.close();
      handlerClose();
    }).catch(() => {
      taskComment.close && taskComment.close();
    });
  }
};

function submitFlowTask() {
  return proxy.$confirm('确定提交当前单据?', '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
    const formDatas = getFormData();
    return BpmTaskApi.listRuTaskByProcInstId({
      procInstId: formDatas.procInstanceId
    }).then((resp) => {
      if (Array.isArray(resp.data) && resp.data.length > 0) {
        return  ProjBizDesignChangeDesignMApi.submitTask({
          busiDto: formDatas,
          taskActionDto: {
            taskId: resp.data[0].taskId,
            procInstId: formDatas.procInstanceId,
            actionType: "agree",
          }
        })
          .then(() => {
            proxy.$message.success("提交成功");
          });
      } else {
        proxy.$message.success("提交失败，未获取到当前任务id");
      }
    })
  })
    .catch(() => {
      return true;
    })

}

function initBusiForm() {
  /**
   * 工作流初始化表单功能
   */
  let that = proxy
  let routerQueryParams = route.query;
  auth = new Map(Object.entries(state.formAuth));
  if (routerQueryParams) {
    if (routerQueryParams.busiData) {
      formData.value = routerQueryParams.busiData;
    } else {
      if (routerQueryParams.businessKey) {
        let businessKey = route.query.businessKey;
        ProjBizDesignChangeDesignMApi.view(businessKey).then(resp => {
          if (resp.data) {
            formData.value = resp.data;
            relevantId.value = resp.data.id;
            isShowAddBtn.value = true;
            isShowDelBtn.value = true;
          }
        });
      } else {
        that.$message.error("初始化失败,因为工作流未将流程信息传入！")
      }
    }
  }
};

function getSaveFormData(formData) {
  return new Promise((resolve) => {
    formRef.value.validate((flag) => {
      if (!flag) {
        return false;
      } else {
        if (formData.id) {
          resolve(ProjBizDesignChangeDesignMApi.update(formData))
        } else {
          formData.fileList = getListData()
          formData.fileList.map((item) => {
            item.type = fileType.value
          })
          resolve(ProjBizDesignChangeDesignMApi.add(formData).then((resp) => {
            if(resp.data) {
              formData.id = resp.data.id;
            }
          }))
        }
      }
    });
  });
};

function getFieldForm() {
  let rootFields = {
    ...state.rootFields
  }
  let fieldList = []
  let keys = []
  for (let key in rootFields) {
    keys.push(key)
    fieldList.push(rootFields[key])
  }
  const result = processAuthFieldForm ? processAuthFieldForm(fieldList) : fieldList;
  const message = {
    type: "getTemplateRoot",
    pathname: window.location.pathname,
    actKey: getQueryParams("actKey"),
    content: result
  };
  window.parent.postMessage(JSON.parse(JSON.stringify(message)), "*");

  if(type.value != 'view' && (formData.value.approvalStatus == '1' || !formData.value.approvalStatus )){
    isShowAddBtn.value = true;
    isShowDelBtn.value = true;
  }
};

function getQueryParams(key) {
  let url = window.location.href;
  // 使用正则表达式解析URL中的查询字符串
  var queryString = url.split('?')[1];
  if (!queryString) {
    return {};
  }
  var params = {};
  // 分割查询字符串成单个参数
  var vars = queryString.split("&");
  for (var i = 0; i < vars.length; i++) {
    var pair = vars[i].split("=");
    params[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);
  }
  return params[key];
};

function initData(urlParams, taskInfo, handlerClose) {
  // 如果用户从流程设计点击启动, 如果是在线表单, 则阻止执行initData, 防止在线表单报错
  if (urlParams.menuId && urlParams.openInStart) {
    return
  }
  state.flowData.procDefKey = taskInfo.procDefKey;
  state.flowData.procInstId = urlParams.procInstId;
  state.flowData.businessKey = urlParams.businessKey;
  state.flowData.taskId = urlParams.taskId;
  state.flowData.fiedPermission = taskInfo.fiedPermission;
  state.flowData.variableList = taskInfo.variableList;
  state.taskFlag = urlParams.taskFlag;
  state.firstAct = taskInfo.firstAct;
  state.handlerClose = handlerClose;
  state.flowData.customPropertyConfigList = taskInfo.customPropertyConfigList
  let fieldPerList = taskInfo.formConfig?.formFieldConfig?.fieldPerList || []
  handleFormAuth(fieldPerList);
  initBusiForm();
};

function handleFormAuth(data) {
  let formAuth = {};
  for (let item of data) {
    let permi = 1;
    if (item.readonly) {
      permi = 2;
    }
    if (item.hidden) {
      permi = 3;
    }
    if (!isCamelCase(item.fieldModelId)) {
      item.fieldModelId = toCamelCase(item.fieldModelId)
    }
    formAuth = {
      ...formAuth,
      [`${item.fieldModelId}_${item.field}`]: permi,
    };
  }
  state.formAuth = formAuth;
  state.formAuthList = JSON.parse(JSON.stringify(formAuth))
};
/**
 * 处理表单权限相关操作, 开发者在template的v-permi设置中必须使用此函数
 * @param {Object} obj - 包含字段名称的对象
 * @param {Function} [callback] - 可选的回调函数，接收 obj 作为参数
 * 如果传入对象的 fieldName 不在 rootFields 中，则将其添加到 rootFields 数组，然后执行回调函数（如果存在）
 */
function formPermi(obj, callback) {
  if (!state.rootFields[obj.fieldModelId + '__' + obj.field]) {
    state.rootFields[obj.fieldModelId + '__' + obj.field] = obj
  }
  return callback && callback(obj)
};

function toCamelCase(s) {
  return s.toLowerCase().replace(/_(.)/g, function(match, group1) {
    return group1.toUpperCase();
  });
};

function isCamelCase(str) {
  return /^[a-z][a-zA-Z0-9]*$/.test(str)
};

function getListData() {
  if (childRef.value) {
    return childRef.value.getListData()
  }
}

// 文件序列号生成函数
function fileSerialNumberBuilder() {
  return 'TEST' + Math.floor(Math.random() * 10000)
}

defineExpose({
  getFormData,
  getSaveFormData,
  getStartFlow,
  submitFlowTask,
  handlerOpenConfirm
});
onMounted(() => {
  nextTick(() => {
    getFieldForm()
    getDesignChangeRequestDataInfo()
    getVolumeInfo()
  })
})
</script>

<style lang="scss" scoped>
.bgW {
  height: 100%;
}
</style>