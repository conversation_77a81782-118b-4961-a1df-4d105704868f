import { request, replaceUrl } from "sn-base-utils";

import { projectManagerServiceCode } from "@/config";

export default class ProjInfoMApi {
    static config = {
        add: {
            url: '/project/projectInfo/add',
            method: 'POST'
        },
        remove: {
            url: '/project/projectInfo/delete',
            method: 'DELETE'
        },
        update: {
            url: '/project/projectInfo/update',
            method: 'PUT'
        },
        view: {
            url: '/project/projectInfo/get/{id}',
            method: 'GET'
        },
        pageList: {
            url: '/project/projectInfo/page',
            method: "POST"
        },
        list: {
            url: '/project/projectInfo/list',
            method: "POST"
        }
    };

    /**
     * 新增项目空间-项目信息
     * @param data
     * @returns {*}
     */
    static add(data) {
        return request({
            url: this.config.add.url,
            method: this.config.add.method,
            data: data,
            requestPrefix: projectManagerServiceCode
        });
    }

    /**
     * 删除项目空间-项目信息
     * @param data
     * @returns {*}
     */
    static remove(data) {
        return request({
            url: this.config.remove.url,
            method: this.config.remove.method,
            data: data,
            requestPrefix: projectManagerServiceCode
        });
    }

    /**
     * 更新项目空间-项目信息
     * @param data
     * @returns {*}
     */
    static update(data) {
        return request({
            url: this.config.update.url,
            method: this.config.update.method,
            data: data,
            requestPrefix: projectManagerServiceCode
        });
    }

    /**
     * 查询项目空间-项目信息详细
     * @param id
     * @returns {*}
     */
    static view(id) {
        return request({
            url: replaceUrl(this.config.view.url, { id }),
            method: this.config.view.method,
            requestPrefix: projectManagerServiceCode
        });
    }

    /**
     * 分页查询项目空间-项目信息列表
     * @param data
     * @returns {*}
     */
    static pageList(data) {
        return request({
            url: this.config.pageList.url,
            method: this.config.pageList.method,
            data: data,
            requestPrefix: projectManagerServiceCode
        });
    }

    /**
     * 全部项目空间-项目信息列表
     * @returns {*}
     */
    static list(data) {
        return request({
            url: this.config.list.url,
            method: this.config.list.method,
            data: data,
            requestPrefix: projectManagerServiceCode
        });
    }
}
