import { request, replaceUrl } from "sn-base-utils";

export default class ProjBizEmergencyContingencyPlanMApi {
    static config = {
        add: {
            url: '/project/emergency/add',
            method: 'POST'
        },
        remove: {
            url: '/project/emergency/delete',
            method: 'DELETE'
        },
        update: {
            url: '/project/emergency/update',
            method: 'PUT'
        },
        view: {
            url: '/project/emergency/get/{id}',
            method: 'GET'
        },
        pageList: {
            url: '/project/emergency/page',
            method: "POST"
        },
        list: {
            url: '/project/emergency/list',
            method: "POST"
        },downloadZip: {
            url: '/project/emergency/downloadZip',
            method: "POST"
        }
    };

    /**
     * 新增应急预案
     * @param data
     * @returns {*}
     */
    static add(data) {
        return request({
            url: this.config.add.url,
            method: this.config.add.method,
            data: data
        });
    }

    /**
     * 删除应急预案
     * @param data
     * @returns {*}
     */
    static remove(data) {
        return request({
            url: this.config.remove.url,
            method: this.config.remove.method,
            data: data
        });
    }

    /**
     * 更新应急预案
     * @param data
     * @returns {*}
     */
    static update(data) {
        return request({
            url: this.config.update.url,
            method: this.config.update.method,
            data: data
        });
    }

    /**
     * 查询应急预案详细
     * @param id
     * @returns {*}
     */
    static view(id) {
        return request({
            url: replaceUrl(this.config.view.url, { id }),
            method: this.config.view.method
        });
    }

    /**
     * 分页查询应急预案列表
     * @param data
     * @returns {*}
     */
    static pageList(data) {
        return request({
            url: this.config.pageList.url,
            method: this.config.pageList.method,
            data: data
        });
    }

    /**
     * 全部应急预案列表
     * @returns {*}
     */
    static list(data) {
        return request({
            url: this.config.list.url,
            method: this.config.list.method,
            data: data
        });
    }
}
