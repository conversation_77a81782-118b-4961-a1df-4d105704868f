/**
 * 获取当前日期
 * @returns {string}
 */
import FileApi from "sn-base-layout-vue3/packLayout/api/File";
import {Base64} from "js-base64";
import { kkFileViewUrl } from "@/config";
/**
 * 内置预览方法
 */
export function previewOpenBlank(fileId) {
  FileApi.getFileNameAndUrlById(fileId).then(res => {
    let url = res.data.filePreviewUrl;
    let base64Url = encodeURIComponent(Base64.encode(url))
    let previewUrl = kkFileViewUrl + "?url=" + base64Url;
    window.open(previewUrl,'_blank')
  }).catch((err) => {

  })
}


