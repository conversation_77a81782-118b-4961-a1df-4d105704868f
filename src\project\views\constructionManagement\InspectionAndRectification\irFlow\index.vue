<template>
  <sn-crud :data="listData" :option="option" @on-load="getList">
    <template #menu="{ row, index, size }">
      <el-button type="primary" :size="size" icon="el-icon-edit" link @click="viewDetail(row)">查看</el-button>
      <el-button type="primary" :size="size" icon="el-icon-edit" v-if="row.approvalStatus === '1' || row.approvalStatus === '2'" link @click="operateFlow(row,'stop')">作废</el-button>
<!--      <el-button type="primary" :size="size" icon="el-icon-edit" v-if="row.approvalStatus === '2'" link @click="operateFlow(row,'revoke')">撤回</el-button>-->
<!--      <el-button type="primary" :size="size" icon="el-icon-edit" v-if="row.approvalStatus === '1'" link @click="operateFlow(row,'stop')">作废</el-button>-->
    </template>
  </sn-crud>
</template>

<script>
import ProjBizIrPrMApi from '@/project/api/constructionManagement/InspectionAndRectification/ProjBizIrPrM.js'
  import EditProjBizIrPrM from "./components/EditProjBizIrPrM.vue";
  import { getToken } from "sn-base-utils";
  export const routerConfig = [{
    menuType: "C",
    menuName: "检查与整改",
  }, {
    menuType: "F",
    menuName: "查看",
    perms: "show",
    api: [ProjBizIrPrMApi.config.list],
  },{
    menuType: "F",
    menuName: "撤回",
    perms: "revoke",
    api: [ProjBizIrPrMApi.config.revoke],
  }, {
    menuType: "F",
    menuName: "作废",
    perms: "cancellation",
    api: [ProjBizIrPrMApi.config.cancellation],
  }];
</script>

<script setup>
import {
  ref,
  getCurrentInstance, defineProps
} from 'vue';
import {requestImgUrl} from "@/common/api/MinioFile";
import ProjBizIrRecordMApi from "@/project/api/constructionManagement/InspectionAndRectification/ProjBizIrRecordM";
const props = defineProps({
  data: Object,
});
const {
  proxy
} = getCurrentInstance()
let option = ref({
  tip: false,
  dialogType: "page",
  border: true,
  index: true,
  stripe: true,
  menu: true,
  header: false,
  searchSpan: 6,
  searchIcon: true,
  searchMenuSpan: 6,
  searchIndex: 3,
  selection: false,
  showTree: false,
  excelBtn: false,
  delBtn: false,
  editBtn: false,
  delBtnsText: "批量删除",
  addBtnText: "新增",
  customEditBtnText: "编辑",
  customDelBtnText: "删除",
  customViewBtnText: "查看",
  column: [{
    label: "整改进度",
    prop: "reformProgress"
  }, {
    label: "提交问题编号",
    prop: "serialNumbers"
  }, {
    label: "提交时间",
    prop: "createTime"
  }, {
    label: "提交人",
    prop: "createName"
  }, {
    label: "审批节点",
    prop: "flowApprovalNodeUserName"
  },{
    label: "审批状态",
    prop: "approvalStatus",
    search: false,
    width: 100,
    align: "center", //对齐方式：居中
    type: 'select',
    dicUrl: "/system/dict/data/type/global_biz_flow_status",
    dicMethod: "get",
    props: {
      label: "dictLabel",
      value: "dictValue"
    },
    html: true,
    formatter: (val, value) => {
      if(value === '1'){
        return `<span style="color:#4871C0">草稿</span>`;
      } else if(value === '2'){
        return `<span style="color:#4871C0">已提交</span>`;
      } else if(value === '4'){
        return `<span style="color:#4871C0">审批中</span>`;
      } else if(value === '3'){
        return `<span style="color:red">上级驳回</span>`;
      } else if(value === '5'){
        return `<span style="color:red">已作废</span>`;
      } else if(value === '6'){
        return `<span style="color:dodgerblue">已审批</span>`;
      } else if(value === '7'){
        return `<span style="color:red">已驳回</span>`;
      }
      return `<p></p>`;
    }
  }]
});
let listData = ref([]);
let formRules = ref({});
let formData = ref(props.data.formData)
function getList() {
  if (!formData.value.id) {
    return
  }
  ProjBizIrPrMApi.list({
    recordId:formData.value.id
  }).then((res) => {
    listData.value = res.data;
  });

}

/**
 * 查看详情
 * @param row
 */
async function viewDetail(row) {
  let rowInfo = await ProjBizIrPrMApi.view(row.id);
  let formData = rowInfo.data;
  let submitQuestions = formData.projBizIrPiSDtoList || []
  submitQuestions.forEach((v) => {
    v.$cellEdit = false
  })
  if (submitQuestions && submitQuestions.length) {
    // 使用 Promise.all 处理异步请求
    const promises = submitQuestions.map(async (v) => {
      if (v.reformImage && v.reformImageInfo?.id) {
        try {
          // 确保 requestImgUrl 返回 Promise
          v.reformImagePreviewUrl = await requestImgUrl(v.reformImageInfo.id);
          v.reformImagePreviewUrlList = [v.reformImagePreviewUrl];
        } catch (e) {
          console.error("图片加载失败", e);
          v.reformImagePreviewUrl = "/fallback-image.jpg"; // 添加备用图片
          v.reformImagePreviewUrlList = [];
        }
      }
      return v;
    });
    // 等待所有图片加载完成
    await Promise.all(promises);
  }
  let recordResult = await ProjBizIrRecordMApi.view(formData.recordId)
  let projBizIrQuestionSDtoList = recordResult.data?.projBizIrQuestionSDtoList || []
  let totalQuestionNumber = projBizIrQuestionSDtoList.length
  let completedQuestions = projBizIrQuestionSDtoList.filter((v) => {
    return v.reformStatus === 'completed'
  })
  let completedQuestionNumber = completedQuestions.length

  let editType = 'view'
  proxy.$DialogForm.show({
    title: "查看",
    type: 'page',
    width: "80%",
    content: EditProjBizIrPrM,
    data: {
      approvalOption: ref({
        isShowApprovalList: true,
        isShowFlowDiagram: true,
        procInstId: formData.flowInstanceId
      }),
      isShowCloseBtn: false,
      formData:{},
      submitQuestions: submitQuestions,
      info: formData,
      type: editType,
      recordInfo:recordResult.data,
      totalQuestionNumber:totalQuestionNumber,
      completedQuestionNumber:completedQuestionNumber,
      createName:formData.createName,
      createOrgName:formData.createOrgName,
      createTime:formData.createTime,
      tips:"view",
    },
    option: {
      submitBtn: false,
      emptyBtn: false,
      submitText: '保存',
      emptyText: '关闭',
      extendButton: [{
        key: 'cancel',
        text: '关闭',
        buttonType: '',
        // icon: 'el-icon-close',
      } ]
    },
    callback: (res) => {
      let vm = res.dialogRefs
      if (vm) {
        switch (res.type) {
            // 关闭
          case 'cancel':
            res.close();
            break;
        }
      }
    },
  });
}

function operateFlow(row, type) {
  let typeName;
  if(type === 'revoke'){
    typeName = '撤回';
  }else if(type === 'stop'){
    typeName = '作废';
  }
  proxy.$modal.confirm("确认要"+typeName+"流程？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    ProjBizIrPrMApi.operateFlow({
      id:row.id,
      operateFlowStatus:type,
    }).then((res) => {
      proxy.$message.success("已"+typeName);
      // 重新请求接口刷新列表
      getList()
    });
  }).catch(() => {});
}


</script>

<style lang="scss" scoped>

</style>
