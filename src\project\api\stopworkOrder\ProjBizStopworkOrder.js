import { request, replaceUrl } from "sn-base-utils";

export default class ProjBizStopworkOrderApi {
    static config = {
        add: {
            url: '/project/stopworkOrder/add',
            method: 'POST'
        },
        remove: {
            url: '/project/stopworkOrder/delete',
            method: 'DELETE'
        },
        update: {
            url: '/project/stopworkOrder/update',
            method: 'PUT'
        },
        view: {
            url: '/project/stopworkOrder/get/{id}',
            method: 'GET'
        },
        pageList: {
            url: '/project/stopworkOrder/page',
            method: "POST"
        },
        list: {
            url: '/project/stopworkOrder/list',
            method: "POST"
        }
    };

    /**
     * 新增停工令
     * @param data
     * @returns {*}
     */
    static add(data) {
        return request({
            url: this.config.add.url,
            method: this.config.add.method,
            data: data
        });
    }

    /**
     * 删除停工令
     * @param data
     * @returns {*}
     */
    static remove(data) {
        return request({
            url: this.config.remove.url,
            method: this.config.remove.method,
            data: data
        });
    }

    /**
     * 更新停工令
     * @param data
     * @returns {*}
     */
    static update(data) {
        return request({
            url: this.config.update.url,
            method: this.config.update.method,
            data: data
        });
    }

    /**
     * 查询停工令详细
     * @param id
     * @returns {*}
     */
    static view(id) {
        return request({
            url: replaceUrl(this.config.view.url, { id }),
            method: this.config.view.method
        });
    }

    /**
     * 分页查询停工令列表
     * @param data
     * @returns {*}
     */
    static pageList(data) {
        return request({
            url: this.config.pageList.url,
            method: this.config.pageList.method,
            data: data
        });
    }

    /**
     * 全部停工令列表
     * @returns {*}
     */
    static list(data) {
        return request({
            url: this.config.list.url,
            method: this.config.list.method,
            data: data
        });
    }
}
