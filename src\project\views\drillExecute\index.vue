<template>
  <div>
    <sn-crud :data="listData" :option="option" v-model:page="queryForm.page" v-model:search="queryForm.filter" @on-load="getPageList" @search-change="onChangeSearch" @search-reset="onResetSearch" @addBtnHandle="onEditData" @row-del="onDelData">
      <template #menu="{ row, index, size }">
        <el-button v-if="!row.state || row.state == '0'" type="primary" :size="size" icon="el-icon-edit" link @click="onEditData(row)">执行</el-button>
        <el-button v-if="!row.state || row.state == '0'" type="danger" :size="size" icon="el-icon-delete" link @click="onDelData([row])">删除</el-button>
        <el-button v-if="row.state && row.state == '1'" type="warning" :size="size" icon="el-icon-refresh-left" link @click="onRefresh(row)">撤回</el-button>
        <el-button v-if="row.state && row.state == '1'" type="primary" :size="size" icon="el-icon-download" link @click="onDownload(row)">下载</el-button>
      </template>
      <template #baseIdSearch = "{ row, size }">
        <el-select v-model="queryForm.filter.baseId" placeholder="请选择计划">
          <el-option v-for="item in planList" :key="item.id" :label="`${item.planYear}-${item.establishmentUnit}`" :value="item.id"/>
        </el-select>
      </template>
      <template #state="{row}">
        <span :style="{color : row.state && row.state==='1' ? 'green' : 'red'}">
          {{row.state && row.state==='1' ? '已发布': '草稿'}}
        </span>
      </template>
      <template #id="{row}">
        <el-button type="text" @click="onRowClick(row)">{{row.id}}</el-button>
      </template>
    </sn-crud>
    <div ref="myRef"></div>
  </div>
</template>

<script>
import ProjBizDrillPlanApi from '@/project/api/drillExecute/ProjBizDrillPlan.js';
import ProjBizDrillPlanBaseApi from '@/project/api/drillPlan/ProjBizDrillPlanBase.js';
  import EditProjBizDrillPlan from "./components/EditProjBizDrillPlan.vue";
  import { getToken } from "sn-base-utils";
  export const routerConfig = [{
    menuType: "C",
    menuName: "应急演练执行",
  }, {
    menuType: "F",
    menuName: "查看",
    perms: "show",
    api: [ProjBizDrillPlanApi.config.pageList,ProjBizDrillPlanBaseApi.config.list],
  }, {
    menuType: "F",
    menuName: "新增",
    perms: "add",
    api: [ProjBizDrillPlanApi.config.add],
  }, {
    menuType: "F",
    menuName: "修改",
    perms: "update",
    api: [ProjBizDrillPlanApi.config.update, ProjBizDrillPlanApi.config.view],
  }, {
    menuType: "F",
    menuName: "删除",
    perms: "del",
    api: [ProjBizDrillPlanApi.config.remove],
  }, {
    menuType: "F",
    menuName: "撤回",
    perms: "refresh",
    api: [ProjBizDrillPlanApi.config.update],
  }, {
    menuType: "F",
    menuName: "下载",
    perms: "download",
    api: [ProjBizDrillPlanApi.config.downloadZip],
  }, {
    menuType: "F",
    menuName: "查看",
    perms: "view",
    api: [ProjBizDrillPlanApi.config.view],
  }];
</script>

<script setup>
import {
  ref,
  getCurrentInstance
} from 'vue';
import dayjs from "dayjs";
const {
  proxy
} = getCurrentInstance()
let option = ref({
  tip: false,
  dialogType: "page",
  border: true,
  index: true,
  stripe: true,
  menu: true,
  header: false,
  height: "auto",
  searchSpan: 6,
  searchIcon: true,
  searchMenuSpan: 6,
  searchIndex: 3,
  selection: true,
  showTree: false,
  excelBtn: false,
  delBtn: false,
  editBtn: false,
  delBtnsText: "批量删除",
  addBtnText: "新增",
  customEditBtnText: "编辑",
  customDelBtnText: "删除",
  customViewBtnText: "查看",
  column: [{
    label: "编码",
    prop: "id"
  }, {
    label: "计划演练月份",
    prop: "drillDate",
    type: "date",
    formatter: (row, column, cellValue) => {
      if (!cellValue) return '';
      return dayjs(cellValue).format('YYYY-MM'); // 使用 dayjs 格式化
    }
  }, {
    label: "演练名称",
    prop: "drillRealName"
  }, {
    label: "演练日期",
    prop: "drillTime"
  }, {
    label: "演练计划",
    prop: "baseId",
    search: true,
    hide: true,
    searchSlot: true
  }, {
    label: "创建人",
    prop: "createName"
  }, {
    label: "创建时间",
    prop: "createTime",
    formatter:(row, column, value) => {
      // 使用dayjs进行时间格式化，确保value存在
      if (!value) return '';
      // 默认格式为YYYY-MM-DD HH:mm:ss，您可以根据需要修改
      return dayjs(value).format('YYYY-MM-DD');
    }
  }, {
    label: "单据状态",
    prop: "state"
  }]
});
let listData = ref([]);
let planList = ref([]);
const myRef = ref(null)
let queryForm = ref({
  page: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  filter: {
    createTime: null,
    baseId: null,
  }
});
let rules = ref({});

function getPageList() {
  if (!planList.value.length) {
    ProjBizDrillPlanBaseApi.list({state:'1'}).then(res => {
      planList.value = res.data
      if (planList.value.length) {
        queryForm.value.filter.baseId = planList.value[0].id
      }
      //查询分页列表
      // 查询前处理参数
      const params = handleQueryForm();
      ProjBizDrillPlanApi.pageList(params).then((res) => {
        listData.value = res.data.dataList;
        queryForm.value.page.total = res.data.totalCount;
      });
    })
  }else {
    //查询分页列表
    // 查询前处理参数
    const params = handleQueryForm();
    ProjBizDrillPlanApi.pageList(params).then((res) => {
      listData.value = res.data.dataList;
      queryForm.value.page.total = res.data.totalCount;
    });
  }
}

function onResetSearch() {
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
}

function handleQueryForm() {
  // 处理参数
  const {
    pageSize,
    pageNum
  } = queryForm.value.page;
  const filter = {}
  for (let key in queryForm.value.filter) {
    if (queryForm.value.filter[key]?.length) {
      // sn-curd搜索select会生成$prop的变量
      if (key.startsWith("$")) continue;
      filter[key] = queryForm.value.filter[key];
    }
  }

  if (!filter.baseId && planList.value.length) {
    filter.baseId = planList.value[0].id
  }
  delete filter.createTime;
  if (Array.isArray(queryForm.value.filter.createTime) && queryForm.value.filter.createTime?.length === 2) {
    filter["beginCreateTime"] = queryForm.value.filter.createTime[0];
    filter["endCreateTime"] = queryForm.value.filter.createTime[1];
  }
  const searchParams = {
    page: {
      pageSize,
      pageNum,
    },
    filter,
  }
  return searchParams
}

function onChangeSearch(params, done) {
  //搜索按钮操作
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
  done && done();
}
async function onEditData(row) {
  //编辑,新增按钮操作
  let editType = row ? "edit" : "add";
  let rowInfo = await (editType !== "add" ? ProjBizDrillPlanApi.view(row.id) : {});
  const formData = editType !== "add" ? rowInfo.data : rowInfo;
  //编辑,新增按钮操作
  proxy.$DialogForm.show({
    title: "应急演练执行",
    type: option.value.dialogType,
    el: myRef.value,
    width: "80%",
    content: EditProjBizDrillPlan,
    data: {
      formData: formData,
      type: editType,
      id:row ? row.id:null,
    },
    option: {
      submitBtn: false,
      emptyBtn: false,
      emptyText:'取消',
      extendButton:[
        {
          key: 'save',
          text: '保存',
          icon: 'el-icon-plus',
          buttonType: 'primary',
        },
        {
          key: 'submit',
          text: '发布',
          icon: 'el-icon-check',
          buttonType: 'primary',
        },
        {
          key: 'close',
          text: '关闭',
          icon: 'el-icon-close',
          buttonType: '',
        },
      ],
    },
    callback: (res) => {
      if (res.type && res.type == 'submit') {
        if (res.dialogRefs) {
          res.dialogRefs.submitData(res.type).then((flag) => {
            if (flag) {
              getPageList();
              res.close();
            }
          });
        }
      } else if (res.type && res.type == 'save') {
        if (res.dialogRefs) {
          res.dialogRefs.submitData(res.type).then((flag) => {
            if (flag) {
              getPageList();
            }
          });
        }
      } else {
        // 当点击关闭按钮且处于编辑模式时，弹出确认对话框
        proxy.$confirm('确认关闭？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          res.close();
        }).catch(() => {
          // 用户点击取消，不关闭弹窗
        });
      }
    }
  });
}

async function onRowClick(row) {
  //编辑,新增按钮操作
  let editType = "view";
  let rowInfo = await (ProjBizDrillPlanApi.view(row.id));
  const formData = rowInfo.data;

  let extendButtons = []
  if (row.state == '0') {
    extendButtons = [{
      key: 'edit',
      text: '编辑',
      icon: 'el-icon-edit',
      buttonType: 'primary',
      click: () => {
        res.close();
        onEditData(row);
      },
    },{
      key: 'close',
      text: '关闭',
      icon: 'el-icon-close',
      buttonType: '',
    }]
  }else {
    extendButtons = [{
      key: 'close',
      text: '关闭',
      icon: 'el-icon-close',
      buttonType: '',
    }]
  }
  //编辑,新增按钮操作
  proxy.$DialogForm.show({
    title: "应急演练执行",
    type: option.value.dialogType,
    el: myRef.value,
    width: "80%",
    content: EditProjBizDrillPlan,
    data: {
      formData: formData,
      type: editType,
      id:row ? row.id:null,
    },
    option: {
      submitBtn: false,
      emptyBtn: false,
      emptyText:'取消',
      extendButton:extendButtons,
    },
    callback: (res) => {
      if (res.type && res.type !== 'close') {
        res.close();
        onEditData(row)
      } else {
        res.close();
      }
    }
  });
}

//撤回
function onRefresh(row) {
  proxy.$modal.confirm("确认撤回该条数据项？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    //编辑操作
    // const formData = getFormData();
    // getPageList();
    row.state = '0'
    ProjBizDrillPlanApi.update(row).then(res =>{
      proxy.$message.success("修改成功");
      getPageList();
    })
  }).catch(() => {});

}

//下载
function onDownload(row) {
  proxy.download(ProjBizDrillPlanApi.config.downloadZip.url, [row.id], 'file_'+row.id+'.zip');
}

function onDelData(rows) {
  proxy.$message.info("请前往应急演练计划中执行！");
  //删除行
  /*if (!rows || rows.length == 0) {
    proxy.$message.info("请勾选数据！");
    return false;
  }
  let ids = rows.map((item) => {
    return item.id;
  });
  proxy.$modal.confirm("确认删除数据项？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    ProjBizDrillPlanApi.remove(ids).then((res) => {
      proxy.$message.success("已删除");
      getPageList();
    });
  }).catch(() => {});*/
}
</script>

<style lang="scss" scoped>

</style>
