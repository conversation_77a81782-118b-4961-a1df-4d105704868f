<template>
  <el-form :model="formData" :rules="formRules" ref="formRef" label-width="100px" label-position="right" :disabled="type == 'view'">
    <el-row :gutter="16" :span="24">
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>基础信息
          </legend>
          <el-row :gutter="16" :span="24">
            <!-- <el-col :span='8'>
              <el-form-item label="项目编号" prop="projectCode">
                <el-input v-model="formData.projectCode" disabled type="text" placeholder="请输入" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="项目名称" prop="projectName">
                <el-input v-model="formData.projectName" disabled type="text" placeholder="请输入" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="项目单位" prop="projectUnit">
                <el-input v-model="formData.projectUnit" disabled type="text" placeholder="请输入" clearable>
                </el-input>
              </el-form-item>
            </el-col> -->
            <el-col :span='8'>
              <el-form-item label="业务编号" prop="code">
                <el-input v-model="formData.code" type="text" placeholder="请输入" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="编制单位" prop="establishmentUnit">
                <el-input v-model="formData.establishmentUnit" disabled type="text" placeholder="请输入" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="计划年度" prop="planYear">
                <el-date-picker type="year" v-model="formData.planYear" format="YYYY" 
                 :picker-options="yearPickerOptions"
                value-format="YYYY" clearable></el-date-picker>
                
              </el-form-item>
            </el-col>
          </el-row>
        </fieldset>
      </el-card>
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>应急计划
          </legend>
          <el-row :gutter="16" :span="24">
            <sn-crud :data="formData.projBizDrillPlanDtoList" :option="projBizDrillPlanOption" @row-save="subRowSave" @row-update="subRowUpdate" @row-del="(row, index) => {subDelRow(row, index, 'projBizDrillPlanDtoList');}">
              <template #empty>
                <div>无数据</div>
              </template>
            </sn-crud>
            <el-button @click="subAddRow('projBizDrillPlanDtoList')" type="primary" plain style="display: block; width: 100%; margin-top: 10px">新增一行数据</el-button>
          </el-row>
        </fieldset>
      </el-card>
            <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>附件信息
          </legend>
          <project-document-storage-ui-table ref="childRef" :type="fileType" :relevantId="formData.id"
            :isPageSearch="false" :isDeleteMinio="false" :isHasAi="false" @on-add-data="onAddData"
            :file-serial-number-builder="fileSerialNumberBuilder" :preview-config="previewConfig" :isShowAddBtn="true"
            :isShowDelBtn="true" :isShowPreviewBtn="true" :isShowDownloadBtn="true" :isShowLinkBtn="false"
            :isShowDownloadBatchBtn="true"></project-document-storage-ui-table>
        </fieldset>
      </el-card>
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>单据信息
          </legend>
          <el-row :gutter="16" :span="24">
            <el-col :span='8'>
              <el-form-item label="创建者人" prop="createName">
                <el-input v-model="formData.createName" disabled :readonly="true" type="text" placeholder="请输入" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="创建时间" prop="createTime">
                   <el-input v-model="formData.createTime" type="text" placeholder="" :disabled="true" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="单据状态" prop="state">
                <el-select v-model="formData.state" disabled clearable placeholder="请选择">
                  <el-option v-for="(item, index) in gh_document_status" disabled :key="index" :label="item.label" :value="item.value"></el-option>
                </el-select> 
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="最近修改人" prop="updateName">
                <el-input v-model="formData.updateName"  disabled  :readonly="true" type="text" placeholder="请输入" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="最近修改时间" prop="updateTime">
          <el-input v-model="formData.updateTime" type="text" placeholder="" :disabled="true" clearable></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </fieldset>
      </el-card>
    </el-row>
  </el-form>
</template>

<script setup>
import ProjBizDrillPlanBaseApi from '@/project/api/drillPlan/ProjBizDrillPlanBase.js' 
import ProjInfoMApi from '@/project/api/projectInfo/ProjInfoM.js'
import {getCurrentFormattedTime} from "@/common/utils/datetime";
import store from "@/store";
import dayjs from 'dayjs'
import {
  nextTick,
  onMounted,
  ref,
  reactive,
  toRef,
  defineProps,
  defineExpose,
  getCurrentInstance
} from 'vue';
import {
  useRoute,
  useRouter
} from 'vue-router'
const {
  proxy
} = getCurrentInstance()
const route = useRoute();
const router = useRouter();
const props = defineProps({
  data: Object,
});
const fileType = ref('drillPlanBase');
const formRef = ref()
let buttonType = ref();
const type = toRef(props.data?.type);
import {
  useDicts
} from "@/common/hooks/useDicts";
const {
  gh_document_status
} = useDicts(["gh_document_status"])

let formData = ref({
  projectCode: "",
  projectName: "",
  projectUnit: "",
  code: "",
  establishmentUnit: "",
  planYear: "",
  projBizDrillPlanDtoList: [],
  createName: "",
  createTime: "",
  state: "0",
  updateName: "",
  updateTime: ""
});
let formRules = ref({
  projectCode: [],
  projectName: [],
  projectUnit: [],
  code: [],
  establishmentUnit: [],
  planYear: [],

    code: [
    { required: true, message: '业务编号不能为空', trigger: 'blur' }
  ],
    establishmentUnit: [
    { required: true, message: '编辑单位不能为空', trigger: 'blur' }
  ],
  planYear: [
    { required: true, message: '计划年度不能为空', trigger: 'blur' }
  ],

  createName: [],
  createTime: [],
  state: [],
  updateName: [],
  updateTime: []
});
let delRowData = ref({});
let projBizDrillPlanOption = ref({
  tip: false,
  border: true,
  index: true,
  stripe: true,
  menu: true,
  header: false,
  menuType: "text",
  addBtn: true,
  addBtnText: "新增",
  editBtn: true,
  editBtnText: "编辑",
  delBtn: true,
  delBtnText: "删除",
  cellBtn: true,
  maxHeight: "200px",
  column: [
     {
    label: "演练名称",
    prop: "drillName",
    type: "input",
    cell: true
  },{
     label: "计划演练月份",
      prop: "drillDate",
      type: "date",
       cell: true,
    
      formatter: (row, column, cellValue) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM') : '';
      }
  }, {
    label: "备注",
    prop: "remark",
    type: "input",
    cell: true
  }]
});

function subRowSave(form, done) {
  //编辑行
  done();
}

function subAddRow(name) {
  //新增一行
  if (formData.value[name]) {
    formData.value[name].push({
      $cellEdit: true,
    });
  } else {
    let arr = new Array();
    arr.push({
      $cellEdit: true
    });
    formData.value[name] = arr
  }
}
function initializer(){

  if(type.value==='add'){
      console.log("执行初始化打印user",store.state.user)

    formData.value.createName =  store.state.user && store.state.user.userInfo.userName ? store.state.user.userInfo.userName : null;//获取到登陆人
    formData.value.createTime = getCurrentFormattedTime();//获取到登录时间
    // formData.value.state = 0
  }
  //判断是否为查看进来，是则禁用输入框及按钮
  // console.log('getPageList.type==',type.value)
  // if(type.value==='show'){
  //   dialogdisabled = true
  // }
}
onMounted(() => {
  nextTick(() => {
  })
  initializer();
   getProjectInfo()
})
function subRowUpdate(form, index, done, loading) {
  //编辑行
  done();
}
const previewConfig = ref({
  // 是否外置预览,必传
  isExternalPreview: false,
  // 内置预览返回的替换字符,akey代表要替换的字符串，avalue代码替换的字符串
  substitute: {
    "akey": "avalue"
  },
  // 内置预览服务地址
  previewServerUrl: "http://10.191.64.191:8012/onlinePreview",
})
function fileSerialNumberBuilder() {
  return "inspection" + Math.floor(Math.random() * 10000);
}
function getListData() {
  if (childRef.value) {
    let list = childRef.value.getListData()
    formData.value.projBizDmStgMDtoList = list
    console.log(list, "组件数据。。。。。。。。。")
  }
}
const childRef = ref(null);
function subDelRow(row, index, name) {

                      proxy.$confirm('确定是否删除', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
         
  //删除行
  if (row[0].id) {
    let data = JSON.parse(JSON.stringify(row[0]));
    if (delRowData.value[name]) {
      delRowData.value[name].push(Object.assign(data, {
        delFlag: 1,
      }));
    } else {
      delRowData.value[name] = [
        Object.assign(data, {
          delFlag: 1,
        }),
      ]
    }
  }
  formData.value[name].splice(index, 1);
        }).catch(() => {
          // 用户点击取消，不关闭弹窗
        });

}

function getFormData() {
  //获取formData数据
  for (let item in delRowData.value) {
    formData.value[item] = formData.value[item].concat(delRowData.value[item]);
  }
  getListData();
   formData.value.projectId = sessionStorage.getItem('projectId');

  return formData.value;
}

function getProjectInfo() {

const keys = Object.keys(localStorage);

 if(localStorage.getItem("orgName") ){
 formData.value.establishmentUnit =    localStorage.getItem("orgName") 
 }else{
 formData.value.establishmentUnit =   '默认单位'

 }
// 遍历并获取每个 Key 的 Value
// keys.forEach(key => {
//   const value = localStorage.getItem(key);
//   console.log(`${key}: ${value}`);
//   localStorage.getItem("orgName")
// });
  ProjInfoMApi.view(sessionStorage.getItem('projectId')).then((resp) => {
    console.log(resp);
    if (!!resp.data) {
      console.log("打印project参数", resp.data)
      if (resp.data.projCode?.trim()) {
        // 覆盖：null、undefined、""、"   "
        formData.value.projectCode = resp.data.projCode
      } else {
        formData.value.projectCode = '默认项目编号'
      }
      if (resp.data.projName?.trim()) {
        // 覆盖：null、undefined、""、"   "

        formData.value.projectName = resp.data.projName
      } else {
        formData.value.projectName = '默认项目名称'
      }
      if (resp.data.projorg?.trim()) {
        // 覆盖：null、undefined、""、"   "
        formData.value.projectUnit = resp.data.projorg
      } else {
        formData.value.projectUnit = '默认项目单位'
      }
    }
  })
}
formData = toRef(Object.keys(props.data?.formData || {}).length > 0 ? {
  ...props.data?.formData
} : toRef(formData.value));



function saveData() {
  //   const formData = getFormData();
  //   if(buttonType.value==='submit'){
  //   formData.state = '1'
  // }else{
  //   formData.state = '0'
  // }
  // //新增操作

  // return ProjBizDrillPlanBaseApi.add(formData).then(() => {
  //   proxy.$message.success("新增成功");
  //   return true;
  // });


   //新增操作
  const formData = getFormData();
  if(buttonType.value==='submit'){
    formData.state = '1'
  }else{
    formData.state = '0'
  }

    if(buttonType.value==='submit'){
  return ProjBizDrillPlanBaseApi.add(formData).then(() => {
    proxy.$message.success("新增成功");
    return true;
  });
    }else{
   

    console.log('formData.id==',formData.id)
    if(formData.id===null || formData.id === undefined){
      return ProjBizDrillPlanBaseApi.add(formData).then((resp) => {
        console.log('resp.data==',resp.data)
        if(resp.data){
          proxy.$message.success("保存成功");
          formData.id = resp.data.id;
          return true;
        }
      });
    }else{
      console.log('进入保存的更新')
      return ProjBizDrillPlanBaseApi.update(formData).then(() => {
        proxy.$message.success("保存成功");
        return true;
      });
    }
 }
}
function submitData(baby) {
  console.log("buttontype",baby)
      buttonType.value = baby
  return new Promise((resolve) => {
    formRef.value.validate((flag) => {
      if (!flag) {
        return false;
      } else {
        if (type.value === "add") {
          resolve(saveData());
        } else {
          resolve(editData());
        }
      }
    });
  });
}
function editData() {

  //编辑操作
  const formData = getFormData();
   if(buttonType.value==='submit'){
    formData.state = '1'
  }else{
    formData.state = '0'
  }
  return ProjBizDrillPlanBaseApi.update(formData).then(() => {
    proxy.$message.success("修改成功");
    return true;
  });
}
defineExpose({
  getFormData,
  submitData,
});
</script>

<style lang="scss" scoped>

</style>
