<template>
  <div style="width: 100%">
    <el-form :model="formData" :rules="formRules" ref="formRef" label-width="180px" label-position="right">
      <el-card class="box-card" style="width: 100%;height:auto">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>工程表单预览
          </legend>
          <el-row :gutter="16" :span="24">
            <div class="mainContent">
              <div class="qualityDom">
                <h3>工程开工令</h3>
                <div class="titletop">
                  <el-row :gutter="16" :span="24">
                    <el-col :span='12'>
                      <el-form-item label="工程名称：" prop="projectName" label-width="100px">
                        <el-input v-model="formData.projectName" type="text" placeholder="请输入" disabled></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span='12'>
                      <el-form-item label="编号：" prop="commandNumber" label-width="100px">
                        <el-input v-model="formData.commandNumber" type="text" placeholder="请输入" clear required
                          :disabled="type == 'view'"></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <div class="form-content">
                    <div class="contents padding-20">致：<span><el-input style="width:100%;"
                          v-model="formData.applicantOrg" type="text" placeholder="请输入施工单位" disabled></el-input></span>
                    </div>
                    <div class="contents textindent padding-auto">
                      经审查，本工程已具备施工合同约定的开工条件，现同意你方开始施工，开工日期为 
                      <span>
                        <el-form-item class="form-item-inline" label-width="0" label=""
                            prop="startDate">
                            <el-date-picker type="date" v-model="formData.startDate" :disabled="type == 'view'" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                              clearable></el-date-picker>
                        </el-form-item>
                      </span>
                    </div>

                    <div class="contents padding-auto">
                      <div class="leftcon pad-right-20">附件：</div>
                      <div class="leftcon file-list">
                        <p v-for="(item, index) in fileList" :key="index">{{ index + 1 }}.{{ item.fileName }}</p>
                      </div>
                      <div class="right-content">
                        <el-form-item class="form-item-inline" label="项目监理机构（盖章）"></el-form-item>
                        <el-form-item class="form-item-inline" label="总监理工程师（签字）：" prop="supervisorEngineerSign"><el-input
                              v-model="formData.supervisorEngineerSign"
                              :disabled="type == 'view'"></el-input></el-form-item>
                        <el-form-item class="form-item-inline" label="日期：" prop="supervisorEngineerSignDate"><el-date-picker type="date"
                                v-model="formData.supervisorEngineerSignDate"
                                :disabled="type == 'view'" format="YYYY-MM-DD"
                                value-format="YYYY-MM-DD" clearable></el-date-picker></el-form-item>
                      </div>
                    </div>
                    <div class="contents padding-20 textline">
                      注：1.本表一式3份，建设单位、项目监理机构、施工单位各1份。<br>
                          &emsp;&emsp;2. 报审中的“口”作为附件附在报审表后，确认后在框内打“ √”。<br>
                          &emsp;&emsp;3. 项目监理机构审查要点：工程开工的各项准备工作是否充分；相关的报审手续是 否已完成；确认是否具备开工条件。

                    </div>

                  </div>
                </div>
              </div>
            </div>
          </el-row>
        </fieldset>
      </el-card>
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>附件信息
          </legend>
          <project-document-storage-ui-table ref="childRef" :type="fileType" :relevantId="relevantId"
            :isPageSearch="false" :isDeleteMinio="isDeleteMinio" :isHasAi="isHasAi" :isShowDelBtn="type !== 'view'"
            :file-serial-number-builder="fileSerialNumberBuilder" :preview-config="previewConfig"
            :isShowAddBtn="type !== 'view'" :isShowLinkBtn="false"
            @onAddData="addData" @onDelete="deleteData"></project-document-storage-ui-table>
        </fieldset>
      </el-card>
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>单据信息
          </legend>
          <el-row :gutter="16" :span="24">
            <el-col :span='8'>
              <el-form-item label="创建人" prop="createName">
                <el-input v-model="formData.createName" type="text" :disabled="true" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="创建时间" prop="createTime">
                <el-input v-model="formData.createTime" type="text" :disabled="true" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="单据状态" prop="publishStatus">
                <el-select v-model="formData.publishStatus" placeholder="请输入" clearable disabled>
                  <el-option label="未发布" value="1"></el-option>
                  <el-option label="已发布" value="2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="最近修改人" prop="updateName">
                <el-input v-model="formData.updateName" type="text" :disabled="true" clearable />
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="最近修改时间" prop="updateTime">
                <el-input v-model="formData.updateTime" type="text" :disabled="true" clearable>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </fieldset>
      </el-card>
    </el-form>
  </div>
</template>

<script setup>
import ProjBizConsStartWorkCommandApi from '@/project/api/constructionManagement/sceneStartWorkCommand/ProjBizConsStartWorkCommand.js'
import { generateWordDocument, download, printDocument } from '@/project/components/downloadWord/word.js';
import {
  nextTick,
  onMounted,
  ref,
  reactive,
  toRef,
  defineProps,
  defineExpose,
  getCurrentInstance
} from 'vue';
import {
  useRoute,
  useRouter
} from 'vue-router'
import store from '@/store'
import { getCurrentFormattedTime } from '@/common/utils/datetime'
import { kkFileViewUrl } from "@/config";
const {
  proxy
} = getCurrentInstance()
const route = useRoute();
const router = useRouter();
const props = defineProps({
  data: Object,
});
const formRef = ref()
const type = toRef(props.data?.type);
let tabsType = ref("YeWu");
let formData = ref({
  reportNumber: "",
  commandNumber: "",
  projectName: "",
  supervisorCompany: "",
  startDate: "",
  publishStatus: "",
  createName: store.state.user && store.state.user.userInfo.userName ? store.state.user.userInfo.userName : '-',
  createTime:getCurrentFormattedTime(),
  reportOrganization: JSON.parse(store.state.user.orgName)?.find((item) => item.id === store.state.user.defaultOrg)?.orgName,
  status: '',
  updateName: '',
  updateTime: '',
  commandNumber: '',
  reportOrganization: '',
  applicantOrg: '',
  supervisorEngineerSign: '',
  supervisorEngineerSignDate: '',

});
let formRules = ref({
  commandNumber: [{ required: true, message: '请输入开工编号', trigger: 'blur'}],
  startDate: [{ required: true, message: '请选择开工日期', trigger: 'blur'}],
  publishStatus: [{ required: true, message: '请选择发布状态', trigger: 'blur'}],
  supervisorEngineerSign: [{ required: true, message: '请输入总监理工程师签名', trigger: 'blur'}],
  supervisorEngineerSignDate: [{ required: true, message: '请选择日期', trigger: 'blur'}],
});
let getHtFlowDrawUrl = ref("");
let approveList = ref([]);
const fileList = ref([])
formData = toRef(Object.keys(props.data?.formData || {}).length > 0 ? {
  ...props.data?.formData
} : toRef(formData.value));
if (formData.value.fileList && formData.value.fileList.length > 0) {
  fileList.value = formData.value.fileList
}
/**
 * 附件上传相关
 */

 // 定义组件参数
const fileType = ref("sceneStartWorkCommand"); // 业务类型标识
const relevantId = ref(props.data?.formData?.id ?? null) // 关联业务ID，新增业务的时候传null（⽰例值）
const isDeleteMinio = ref(true); // 删除Minio⽂件开关
const isHasAi = ref(false); // AI审查功能开关
const childRef = ref(null); // 组件实例引⽤


/**
* 预览配置对象
* - isExternalPreview: 是否使⽤⾃定义预览（false=内置kkfileview）
* - substitute: 替换Minio返回URL中的字符串（⽰例：{"原始字符串":"替换值"}）
* - previewServerUrl: 内置预览服务地址
*/
const previewConfig = ref({
  isExternalPreview: false,
  substitute: { "akey": "avalue" },
  previewServerUrl: kkFileViewUrl,
});

// 获取文件上传列表
function getListData() {
  if (childRef.value) {
    const list = childRef.value.getListData();
    return list
  }
}
// 新增附件回调
function addData(val) {
  setTimeout(() => {
    fileList.value = getListData()
  }, 3000);
}
// 删除附件回调
function deleteData() {
  setTimeout(() => {
    fileList.value = getListData()
  }, 1000);
}
// 文件序列号生成函数
function fileSerialNumberBuilder() {
  return 'TEST' + Math.floor(Math.random() * 10000)
}

//导出word
const generatedFile = ref(null);
// 下载表单（前端生成word）
const startGeneration = async () => {
  try {
    let form = {
      ...formData.value
    }
    generatedFile.value = await generateWordDocument(form, 'projBizConsStartWorkCommand');
    if (generatedFile.value) {
      download(generatedFile.value, '开工令.docx');
      proxy.$message.success("word下载成功！");
    } else {
      console.error('No file generated');
    }
  } catch (error) {
    console.error('Error generating document:', error);
  }
};

// 下载表单(后端)
const downloadWord =  () => {
  if (formData.value.id) {
    let timestamp = proxy.parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')
    proxy.download(`/project/sceneStartWorkCommand/fillTemplateOnlyAndDownload/{${formData.value.id}`, {}, '现场开工令' + '-' + timestamp + '.docx')
  } else {
    proxy.$message.error("请选择先保存数据！");
    return;
  }
}

function getFormData() {
  return formData.value
};

function submitData(publishStatus) {
  return new Promise((resolve) => {
    formRef.value.validate((flag) => {
      if (!flag) {
        return false;
      } else {
        if (type.value === "add") {
          resolve(saveData());
        } else {
          resolve(editData(publishStatus));
        }
      }
    });
  });
}

function saveData() {
  //新增操作
  const formData = getFormData();
  return ProjBizConsStartWorkCommandApi.add(formData).then(() => {
    proxy.$message.success("新增成功");
    return true;
  });
}

function editData(publishStatus = '1') {
  //编辑操作
  const formData = getFormData();
  let form = {
    ...formData,
    publishStatus: publishStatus
  }
  return ProjBizConsStartWorkCommandApi.update(form).then(() => {
    proxy.$message.success("修改成功");
    return true;
  });
}
defineExpose({
  getFormData,
  submitData,
  startGeneration,
  downloadWord,
});
</script>

<style lang="scss" scoped>
.normal-text {
  font-size: 14px !important;
  color: #000;
}
::v-deep label, p, div {
  @extend .normal-text;
}
.mainContent {
  width: 100%;

  .qualityDom {
    width: 56%;
    margin: 0 auto;
  }

  h3 {
    width: 100%;
    text-align: center;
    line-height: 40px;
  }
}

.form-content {
  border: 1px solid #333;
}

.padding-20 {
  padding: 20px;
}

.padding-auto {
  padding: 0 20px;
}

.pad-right-20 {
  padding-left: 20px;
}

.contents {
  span {
    display: inline-block;
    width: 230px;
    padding: 0 5px;
    text-indent: 0ch;
  }
}

.textindent {
  text-indent: 2ch;
}

.leftcon {
  display: inline-block;
  vertical-align: top;
  text-indent: 2ch;
  padding-top: 20px;

  p {
    padding-bottom: 10px;
  }
}

.sp-inline {
  display: inline-block;
}

.right-content {
  // padding-left: 50%;
  padding-left: calc(100% - 400px);
  padding-top: 10px;

  .label {
    text-align: right;
  }
}

.textline {
  margin-top: 20px;
  border-top: 1px solid #333;
}

::v-deep .ep-textarea__inner {
  box-shadow: none;
  outline: none;
}
.preparation {
  padding: 10px 20px;
  ::v-deep .ep-checkbox {
    display: block;
  }
}
.form-item-inline {
  display: inline-block;
  ::v-deep .ep-form-item__label {
    @extend .normal-text;
  }
  ::v-deep .ep-form-item__content {
    display: inline-block;
    width: 220px;
   
  }
}
.file-list {
  min-height: 400px;
}
.bgW {
  height: 100%;
}
</style>