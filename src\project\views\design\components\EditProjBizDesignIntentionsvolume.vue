<template>
  <sn-crud ref="crudRef" :data="listData" :option="option" v-model:page="queryForm.page" v-model:search="queryForm.filter" :selected.sync="selectedData"  @select="handleSelect" @on-load="getPageList" @search-change="onChangeSearch" @search-reset="onResetSearch" @addBtnHandle="onEditData">
  </sn-crud>
</template>

<script setup>
//TODO 引入所需JS
import ProjBizDesignWorkdrawOutPlanMApi from '@/project/api/designManagement/workdrawManager/ProjBizDesignWorkdrawOutPlanM.js'
import {
  nextTick,
  onMounted,
  ref,
  reactive,
  toRef,
  defineProps,
  defineExpose,
  getCurrentInstance
} from 'vue';
import {
  useRoute,
  useRouter
} from 'vue-router'
const {
  proxy
} = getCurrentInstance();
const crudRef = ref(null);//定义一个ref调用默认选中的方法

const route = useRoute();
const router = useRouter();
const props = defineProps({
  data: Object,
});
const formRef = ref()
const type = toRef(props.data?.type);
const existingData = toRef(props.data, 'existingData');//获取到父组件传过来的关联卷册的数据
let option = ref({
  tip: false,
  dialogType: "dialog",
  border: true,
  index: true,
  stripe: true,
  menu: false,
  header: false,
  height: "auto",
  searchSpan: 8,//一行栏为24设置为8就可以一行展示三栏
  searchIcon: false,//是否显示最右侧搜索框展开按钮
  searchMenuSpan: 6,
  searchIndex: 3,
  selection: true,
  showTree: false,
  excelBtn: false,
  delBtn: false,
  editBtn: false,
  delBtnsText: "批量删除",
  addBtnText: "确定",
  cancelBtnText: "取消",
  customEditBtnText: "编辑",
  customDelBtnText: "删除",
  customViewBtnText: "查看",
  column: [{
    label: "专业",
    prop: "major",
    search: true,
    columnSlot: false,
    searchSlot: false,
    dictDatas: "major_type",
    type: "select",
    dicUrl: "/system/dict/data/type/major_type",
    dicMethod: "get",
    props: {
      label: "dictLabel",
      value: "dictValue"
    }
   }, {
    label: "卷册号",
    prop: "volumeNum",
    search: true,
    columnSlot: false,
    searchSlot: false
  }, {
    label: "卷册名称",
    prop: "volumeName",
    search: true,
    columnSlot: false,
    searchSlot: false
  }, {
    label: "计划出图时间",
    prop: "planOutDate",
    columnSlot: false,
    searchSlot: false
  }, {
    label: "实际出图时间",
    prop: "actualOutDate",
    columnSlot: false,
    searchSlot: false
  }, {
    label: "出图状态",
    prop: "drawingStatus",
    search: true,
    searchSlot: true, // 确保这个字段在搜索区域显示
    hide: true,   // 确保这个字段不在表格列中显示
    dictDatas: "drawing_status",
    type: "select",
    dicUrl: "/system/dict/data/type/drawing_status",
    dicMethod: "get",
    props: {
      label: "dictLabel",
      value: "dictValue"
    }
  }
  ]
});
let listData = ref([]);
let queryForm = ref({
  page: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  filter: {
    createTime: null
  }
});
let formRules = ref({});
let selectquery = ref([]);
function getPageList() {
  //查询分页列表
  // 查询前处理参数
  ProjBizDesignWorkdrawOutPlanMApi.detailPageList(queryForm.value).then(resp => {
    const detailData = resp.data.dataList;
    detailData.forEach(item => {
      item["$cellEdit"] = false;
      item["projectId"] = item.projectId ? item.projectId : sessionStorage.getItem('projectId')
    })
    queryForm.value.page.total = resp.data.totalCount;
// // 模拟数据
//   const detailData = [
//     {
//       major: '土木工程',
//       volumeNum: '001',
//       volumeName: '卷册1',
//       planOutDate: '2025-08-01',
//       actualOutDate: '2025-08-05',
//     },
//     {
//       major: '电气工程',
//       volumeNum: '002',
//       volumeName: '卷册2',
//       planOutDate: '2025-08-10',
//       actualOutDate: null,
//     },
//     {
//       major: '机械工程',
//       volumeNum: '003',
//       volumeName: '卷册3',
//       planOutDate: '2025-08-15',
//       actualOutDate: '2025-08-18',
//     }
//   ];
    if(queryForm.value.filter.drawingStatus==='已出图'){
      const filteredData = detailData.filter(item => item.actualOutDate !== null && item.actualOutDate !== ''); // 筛选掉 actualOutDate 不为空的记录
      listData.value = filteredData;
    }else if(queryForm.value.filter.drawingStatus==='未出图'){
      const filteredData = detailData.filter(item => item.actualOutDate === null || item.actualOutDate === ''); // 筛选掉 actualOutDate 为空的记录
      listData.value = filteredData;
    }else{
      listData.value = detailData
    }
    // 根据 existingData 设置选中状态
    nextTick(() => {
      if (existingData.value && existingData.value.length > 0) {
        existingData.value.forEach(item => {
          const row = listData.value.find(row => row.volumeNum === item.volumeNum);
          // if (row) {
          //   proxy.$refs.snCrud.toggleRowSelection(row, true);
          // }
          if (row) {
            toggleRowSelection(row)
          }
        });
      }
    });
  });
}

const toggleRowSelection = (data) => {
  if (crudRef.value) {
    crudRef.value.toggleRowSelection(data,true);
  }
};
let selectedData = ref([]);//存储选中的数据
function handleSelect(selectedItems) {
  selectedData = selectedItems.map(item => {
    // 创建一个新对象，除了 id 属性外，其他属性与原对象相同
    const newItem = { ...item };
    // 将新对象的 id 属性置为空字符串
    newItem.id = '';
    return newItem;
  });
  // selectedData = selectedItems;
}
let sData = ref([])
function submitData() {
  if(selectedData.length>0){
    return sData.value = selectedData
  }else{
    return sData.value = existingData.value
  }
}
function onResetSearch() {
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
}

function handleQueryForm() {
  // 处理参数
  const {
    pageSize,
    pageNum
  } = queryForm.value.page;
  const filter = {}
  for (let key in queryForm.value.filter) {
    if (queryForm.value.filter[key]?.length) {
      // sn-curd搜索select会生成$prop的变量
      if (key.startsWith("$")) continue;
      filter[key] = queryForm.value.filter[key];
    }
  }
  delete filter.createTime;
  if (Array.isArray(queryForm.value.filter.createTime) && queryForm.value.filter.createTime?.length === 2) {
    filter["beginCreateTime"] = queryForm.value.filter.createTime[0];
    filter["endCreateTime"] = queryForm.value.filter.createTime[1];
  }
  const searchParams = {
    page: {
      pageSize,
      pageNum,
    },
    filter,
  }
  return searchParams
}

function onChangeSearch(params, done) {
  //搜索按钮操作
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
  done && done();
}

defineExpose({
  getPageList,
  submitData,
  sData
});
</script>

<style lang="scss" scoped>

</style>