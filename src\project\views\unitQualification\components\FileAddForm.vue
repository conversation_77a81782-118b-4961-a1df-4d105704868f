<template >
          <sn-upload :drag="true" @input="success"/>
</template>

<script setup>
import {

  ref

} from 'vue';

defineExpose({
addFileList

})



  function  success(data){
      for(let item of data){
        // if(item.name.length > 50){
        //     this.$modal.msgWarning('文件名称长度不能超过50!')
        //     return
        // }
  
        addFileList.value.push(item)
      }
  }

</script>
