import { request, replaceUrl } from "sn-base-utils";
import { lifecycleBizServiceCode } from "@/config";
export default class ProjBizDesignFirstoffReportMApi {
    static config = {
        add: {
            url:  '/project/designFirstoffReport/add',
            method: 'POST'
        },
        remove: {
            url:  '/project/designFirstoffReport/delete',
            method: 'DELETE'
        },
        update: {
            url:  '/project/designFirstoffReport/update',
            method: 'PUT'
        },
        view: {
            url:  '/project/designFirstoffReport/get/{id}',
            method: 'GET'
        },
        getOne: {
            url:  '/project/designFirstoffReport/getOne',
            method: "GET"
        },
        pageList: {
            url:  '/project/designFirstoffReport/page',
            method: "POST"
        },
        list: {
            url:  '/project/designFirstoffReport/list',
            method: "POST"
        }
    };

    /**
     * 新增初设收口报备
     * @param data
     * @returns {*}
     */
    static add(data) {
        return request({
            url: this.config.add.url,
            method: this.config.add.method,
            data: data,
            requestPrefix: lifecycleBizServiceCode
        });
    }

    /**
     * 删除初设收口报备
     * @param data
     * @returns {*}
     */
    static remove(data) {
        return request({
            url: this.config.remove.url,
            method: this.config.remove.method,
            data: data,
            requestPrefix: lifecycleBizServiceCode
        });
    }

    /**
     * 更新初设收口报备
     * @param data
     * @returns {*}
     */
    static update(data) {
        return request({
            url: this.config.update.url,
            method: this.config.update.method,
            data: data,
            requestPrefix: lifecycleBizServiceCode
        });
    }

    /**
     * 查询初设收口报备详细
     * @param id
     * @returns {*}
     */
    static view(id) {
        return request({
            url: replaceUrl(this.config.view.url, { id }),
            method: this.config.view.method,
            requestPrefix: lifecycleBizServiceCode
        });
    }

    /**
     * 查询设计管理-初设收口报备详细
     * @param id
     * @returns {*}
     */
    static getOne() {
        return request({
            url: replaceUrl(this.config.getOne.url, {  }),
            method: this.config.getOne.method,
            requestPrefix: lifecycleBizServiceCode
        });
    }



    /**
     * 分页查询初设收口报备列表
     * @param data
     * @returns {*}
     */
    static pageList(data) {
        return request({
            url: this.config.pageList.url,
            method: this.config.pageList.method,
            data: data,
            requestPrefix: lifecycleBizServiceCode
        });
    }

    /**
     * 全部初设收口报备列表
     * @returns {*}
     */
    static list(data) {
        return request({
            url: this.config.list.url,
            method: this.config.list.method,
            data: data,
            requestPrefix: lifecycleBizServiceCode
        });
    }
}
