<template>
  <el-tabs v-model="activeTab">
    <el-tab-pane label="停工申请" name="application">
      <StopworkApplication />
    </el-tab-pane>
    <el-tab-pane label="停工令" name="order">
      <StopworkOrder />
    </el-tab-pane>
  </el-tabs>
</template>

<script setup>
import { ref } from 'vue';
import StopworkOrder from '../stopworkOrder/index.vue';
import StopworkApplication from '../stopworkApplication/index.vue';

// 默认显示 "application" tab
const activeTab = ref('application');
</script>