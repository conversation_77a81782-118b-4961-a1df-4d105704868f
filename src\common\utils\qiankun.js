import {  loadMicroApp, initGlobalState } from "qiankun";
import { theme } from "../../config";
import store from "@/store";
let activeApp = {};
export const actionsQiankun = initGlobalState(() => {
  return {
    theme: localStorage.getItem("theme") || theme,
  };
});
export const startQiankun = () => {
  //解决qiankun路由问题
  window.Vue2 = window.window.Vue;
  window.window.Vue = null;
  actionsQiankun.setGlobalState({
    theme: localStorage.getItem("theme") || theme,
  });
};

export const doQiankun = (to, next) => {
  const conf = store.getters.microApps.find(
    (item) => to.path.indexOf(item.name) !== -1
  );
  if (conf) {
    if (activeApp[conf.activeRule]) {
      next();
      return;
    }

  const microApp = store.getters.microApps
    .map((item) => {
      return {
        ...item,
        container: `#qiankunContainer-${item.name}`,
        props: {
          remoteRouters: store.getters.remoteRouters.filter(
            (i) => i.clientCode == item.name
          ),
          cachedViews: store.state.tagsView.cachedViews,
        },
      };
    })
    .filter((i) => {
      if (to.path.indexOf(i.name) != -1) {
        return i;
      }
    });
  if (microApp.length > 0) {
    activeApp[conf.activeRule] = loadMicroApp({
      ...microApp[0],
    });
    next();
  }
}else{
  next();
}
}
/**
 * @description: 手动卸载微应用
 * @return {*}
 * @Date Changed:
 */
export const unLoadApp = () => {
  console.log('手动卸载微应用')
  // 手动卸载微应用
  for (const key in activeApp) {
    activeApp[key].unmount();
    delete activeApp[key];
  }
}