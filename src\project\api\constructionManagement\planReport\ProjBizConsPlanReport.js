import { request, replaceUrl } from 'sn-base-utils'

export default class ProjBizConsPlanReportApi {
  static config = {
    add: {
      url: '/project/planReport/add',
      method: 'POST'
    },
    remove: {
      url: '/project/planReport/delete',
      method: 'DELETE'
    },
    update: {
      url: '/project/planReport/update',
      method: 'PUT'
    },
    view: {
      url: '/project/planReport/get/{id}',
      method: 'GET'
    },
    pageList: {
      url: '/project/planReport/page',
      method: 'POST'
    },
    list: {
      url: '/project/planReport/list',
      method: 'POST'
    },
    startFlow: {
      url: '/project/planReport/saveAndSubmitProc',
      method: 'POST'
    },
    submitTask: {
      url: '/project/planReport/saveAndSubmitTask',
      method: 'POST'
    },
    operateFlow: {
      url: '/project/planReport/operateFlow',
      method: 'POST'
    },
    printTemplate: {
      url: `/project/planReport/printTemplate`,
      method: 'POST'
    }
  }

  /**
   * 新增施工方案报审
   * @param data
   * @returns {*}
   */
  static add(data) {
    return request({
      url: this.config.add.url,
      method: this.config.add.method,
      data: data
    })
  }

  /**
   * 删除施工方案报审
   * @param data
   * @returns {*}
   */
  static remove(data) {
    return request({
      url: this.config.remove.url,
      method: this.config.remove.method,
      data: data
    })
  }

  /**
   * 更新施工方案报审
   * @param data
   * @returns {*}
   */
  static update(data) {
    return request({
      url: this.config.update.url,
      method: this.config.update.method,
      data: data
    })
  }

  /**
   * 查询施工方案报审详细
   * @param id
   * @returns {*}
   */
  static view(id) {
    return request({
      url: replaceUrl(this.config.view.url, { id }),
      method: this.config.view.method
    })
  }

  /**
   * 分页查询施工方案报审列表
   * @param data
   * @returns {*}
   */
  static pageList(data) {
    return request({
      url: this.config.pageList.url,
      method: this.config.pageList.method,
      data: data
    })
  }

  /**
   * 全部施工方案报审列表
   * @returns {*}
   */
  static list(data) {
    return request({
      url: this.config.list.url,
      method: this.config.list.method,
      data: data
    })
  }

  /**
   * 工作流-启动流程
   * @returns {*}
   */
  static startFlow(data) {
    return request({
      url: this.config.startFlow.url,
      method: this.config.startFlow.method,
      data: data
    })
  }

  /**
   * 工作流-完成任务
   * @returns {*}
   */
  static submitTask(data) {
    return request({
      url: this.config.submitTask.url,
      method: this.config.submitTask.method,
      data: data
    })
  }

  /**
   * 更新变更申请单
   * @param data
   * @returns {*}
   */
  static operateFlow(data) {
    return request({
      url: this.config.operateFlow.url,
      method: this.config.operateFlow.method,
      data: data
    })
  }

  /**
   * 工作流-打印模板
   */
  static printTemplate(data) {
    return request({
      url: this.config.printTemplate.url,
      method: this.config.printTemplate.method,
      data: data
    })
  }
}
