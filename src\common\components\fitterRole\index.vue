<template>
  <el-dropdown
    trigger="click"
    :hide-on-click="false"
    ref="dropdown"
    class="custom-dropdown"
    v-if="delayShow"
  >
    <el-tooltip class="item" effect="dark" content="角色筛选" placement="top">
      <el-button size="mini" circle icon="el-icon-refrigerator" />
    </el-tooltip>
    <el-dropdown-menu slot="dropdown">
      <div
        @click="refData"
        style="
          cursor: pointer;
          margin-top: 5px;
          display: flex;
          justify-content: flex-end;
          font-size: 14px;
          color: dodgerblue;
          margin-right: 10px;
        "
      >
        确定
      </div>
      <div style="margin: 10px 0; height: 1px; background: #dcdfe6"></div>
      <el-dropdown-item v-for="(item, key) in data" :key="key">
        <el-checkbox
          :label="item.uid"
          :key="item.uid"
          :value="item.checked"
          @change="(e) => dataChange(key)"
          >{{ item.roleName }}</el-checkbox
        >
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>
<script>
import { mapMutations } from "vuex";
export default {
  name: "FitterRole",
  props: {
    refresh: {
      default: () => {},
      type: Function,
    },
  },
  data() {
    return {
      data: [],
      delayShow: false, 
    };
  },
  created() {
    // 显隐列初始默认隐藏列
    for (let i in this.$store.getters.meta.menuAuth) {
      this.data.push({
        ...this.$store.getters.meta.menuAuth[i],
        checked: true,
      });
    }
    // 延迟显示
    setTimeout(() => {
      this.delayShow = true;
    }, 200);
  },

  methods: {
    ...mapMutations(["SET_DATA_SCOPE"]),
    // 搜索
    // 刷新
    /**
     * 处理选中的数据，将选中的项的 uid 收集到数组中，并更新数据范围。
     * 隐藏下拉菜单。
     * 
     * @method refData
     * @returns {void}
     */
    refData() {
      const arr = [];
      for (let item of this.data) {
        if (item.checked) {
          arr.push(item.uid);
        }
      }
      this.SET_DATA_SCOPE(arr);
      this.refresh();
      this.$refs.dropdown.hide(); // 隐藏
    },
    // 点击选项
    dataChange(key) {
      let data = this.data;
      const arr = [];
      let checkedNum = 0;
      for (let i = 0; i < data.length; i++) {
        if (data[i].checked) {
          checkedNum++;
        }
      }
      for (let i = 0; i < data.length; i++) {
        if (i == key) {
          if ((data[i].checked && checkedNum > 1) || !data[i].checked) {
            data[i].checked = !data[i].checked;
          }
        }
        arr.push(data[i]);
      }
      this.data = arr;
    },
  },
};
</script>
<style lang="scss" scoped>
:deep(.ep-transfer__button) {
  border-radius: 4px;
  padding: 9px;
  display: block;
  margin-left: 0px;
  font-size: 12px;
  width: 80%;
}
:deep(.ep-transfer__button:first-child) {
  margin-bottom: 10px;
}
:deep(.ep-icon-arrow-right) {
  margin-left: 5px;
}
:deep(.ep-transfer-panel) {
  width: 40%;
}
:deep(.ep-transfer__buttons) {
  width: 20%;
  padding: 0;
  display: inline-flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.custom-dropdown {
  margin-right: -10px;
}
</style>
