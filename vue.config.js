const path = require('path')
const { defineConfig } = require('@vue/cli-service')
const setting = require('./src/config.js')
const CompressionPlugin = require('compression-webpack-plugin')
const NodePolyfillPlugin = require('node-polyfill-webpack-plugin')
// const BundleAnalyzerPlugin = require( 'webpack-bundle-analyzer').BundleAnalyzerPlugin;
// const AutoImport = require('unplugin-auto-import/webpack')
// const Components = require('unplugin-vue-components/webpack')
// const { ElementPlusResolver } = require('unplugin-vue-components/resolvers')

const name = setting.title || '天易开发平台'
function resolve(dir) {
  return path.join(__dirname, dir)
}

module.exports = defineConfig({
  transpileDependencies: ['sn-base-layout-vue3', 'sn-app-system-vue3'],
  publicPath: setting.publicPath,
  outputDir: process.env.VUE_APP_OUTPUT_DIR,
  assetsDir: 'static',
  productionSourceMap: false,
  lintOnSave: false,
  pages: {
    index: {
      // 修改项目入口文件
      entry: 'src/main.js',
      template: 'public/index.html',
      filename: 'index.html'
    }
  },
  devServer: {
    client: {
      overlay: false
    },
    port: process.env.VUE_APP_PORT,
    headers: {
      'Access-Control-Allow-Credentials': true,
      'Access-Control-Allow-Origin': '*'
    },
    open: false,
    allowedHosts: 'all',
    // 接口转发
    proxy: {
      [process.env.VUE_APP_BASE_API]: {
        target: setting.URI,
        changeOrigin: true,
        pathRewrite: {
          ['^' + process.env.VUE_APP_BASE_API]: ''
        }
      },
      '/webMessage': {
        target: setting.isCloud ? setting.URI + '/message' : setting.URI,
        changeOrigin: true
      },
      '/miniofile': {
        target: `http://minio.dev.snpit.com:9000/`,
        changeOrigin: true,
        pathRewrite: {
          ['^/miniofile']: ''
        }
      }
    }
  },
  css: {
    loaderOptions: {
      scss: {
        additionalData: (content, loaderContext) => {
          const { resourcePath } = loaderContext
          if (resourcePath.endsWith('elemet-rest-name-space.scss')) return content
          return `@use "~@/common/styles/elemet-rest-name-space.scss";${content}`
        }
      },
      sass: {
        sassOptions: {
          outputStyle: 'expanded'
        }
      }
    }
  },
  configureWebpack: {
    name: name,
    externals:
      process.env.VUE_APP_OUTPUT_DIR == 'dist'
        ? {}
        : {
            vue: {
              root: 'Vue',
              commonjs2: 'vue',
              commonjs: 'vue',
              amd: 'vue'
            },
            'element-plus': 'element-plus',
            axios: 'axios',
            'sn-base-utils': 'sn-base-utils'
          },
    resolve: {
      alias: {
        '@': resolve('src'),
        '@common': resolve('src/common'),
        // 构建使用
        'sn-base-layout': 'sn-base-layout-vue3',
        '@packLayout': 'sn-base-layout-vue3/packLayout',
      }
    },
    output: {
      library: `${process.env.VUE_APP_APPLATION_NAME}-[name]`,
      libraryTarget: 'umd', // 把微应用打包成 umd 库格式
      chunkLoadingGlobal: `webpackJsonp_${process.env.VUE_APP_APPLATION_NAME}` // webpack 5 需要把 jsonpFunction 替换成 chunkLoadingGlobal
    },
    plugins: [
      // new BundleAnalyzerPlugin(),
      new NodePolyfillPlugin(),
      new CompressionPlugin({
        test: /\.(js|css|html)?$/i, // 压缩文件格式
        filename: '[path][base].gz', // 压缩后的文件名
        algorithm: 'gzip', // 使用gzip压缩
        minRatio: 0.8 // 压缩率小于1才会压缩
      })
      // AutoImport.default({
      //   resolvers: [
      //     ElementPlusResolver({
      //       importStyle: 'sass' // 指示element-plus使用预处理样式
      //     })
      //   ]
      // })
      // Components.default({
      //   resolvers: [
      //     ElementPlusResolver({
      //       importStyle: 'sass' // 指示element-plus使用预处理样式
      //     })
      //   ]
      // })
    ]
  },
  chainWebpack(config) {
    config.module
      .rule('js')
      .test(/\.js$/)
      .include.add(resolve('src'))
      .add(resolve('src'))
      .end()
      .include.add(resolve('node_modules/sn-base-layout-vue3'))
      .end()
      .include.add(resolve('node_modules/sn-app-system-vue3'))
      .end()
      .use('babel')
      .loader('babel-loader')
      .options({
        presets: [
          [
            '@babel/preset-env',
            {
              modules: false
            }
          ]
        ]
      })
    config.module
      .rule('js')
      .test(/\.js$/)
      .include.add(resolve('packSystem'))
      .add(resolve('packSystem'))
      .end()
      .use('babel')
      .loader('babel-loader')
      .options({
        presets: [
          [
            '@babel/preset-env',
            {
              modules: false
            }
          ]
        ]
      })
    config.module
      .rule('js')
      .test(/\.js$/)
      .include.add(resolve('packLayout'))
      .add(resolve('packLayout'))
      .end()
      .use('babel')
      .loader('babel-loader')
      .options({
        presets: [
          [
            '@babel/preset-env',
            {
              modules: false
            }
          ]
        ]
      })
    config.module
      .rule('xml')
      .test(/\.xml$/)
      .include.add(resolve('node_modules'))
      .add(resolve('src'))
      .end()
      .use('xml-loader')
      .loader('xml-loader')
      .end()
    config.module.rule('svg').exclude.add(resolve('src/common/assets/icons')).end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/common/assets/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()
    config.when(
      process.env.NODE_ENV !== 'development' && process.env.NODE_ENV !== 'lib',
      (config) => {
        config.optimization.splitChunks({
          chunks: 'all',
          cacheGroups: {
            libs: {
              //指定chunks名称
              name: 'chunk-libs',
              //符合组的要求就给构建venders
              test: /[\\/]node_modules[\\/]/,
              //priority:优先级：数字越大优先级越高，因为默认值为0，所以自定义的一般是负数形式,决定cacheGroups中相同条件下每个组执行的优先顺序。
              priority: 10,
              // 仅限于最初依赖的第三方
              chunks: 'initial'
            },
            elementUI: {
              // 将elementUI拆分为单个包
              name: 'chunk-elementUI',
              // 重量需要大于libs和app，否则将打包到libs或app中
              priority: 20,
              // 为了适应cnpm
              test: /[\\/]node_modules[\\/]_?elemt-plus(.*)/
            },
            snpitUI: {
              // 将elementUI拆分为单个包
              name: 'chunk-snpitUI',
              // 重量需要大于libs和app，否则将打包到libs或app中
              priority: 20,
              // 为了适应cnpm
              test: /[\\/]node_modules[\\/]_?snpit-plus(.*)/
            },
            system: {
              name: 'chunk-snAppSystem',
              priority: 20,
              test: /[\\/]node_modules[\\/]_?sn-app-system-vue3(.*)/
            },
            layout: {
              name: 'chunk-snBaseLayout',
              priority: 20,
              test: /[\\/]node_modules[\\/]_?sn-base-layout-vue3(.*)/
            },
            pluginModules: {
              name: 'chunk-pluginModules',
              priority: 20,
              test: /[\\/]node_modules[\\/]_?sn-plugin-modules(.*)/
            },
            //公共组件
            commons: {
              name: 'chunk-commons',
              // eslint-disable-next-line no-undef
              test: /[\\/]node_modules[\\/]_?snpit-base-layput-vue3(.*)/,
              minChunks: 3,
              priority: 30,
              //这个的作用是当前的chunk如果包含了从main里面分离出来的模块，则重用这个模块，这样的问题是会影响chunk的名称。
              reuseExistingChunk: true,
              //最大初始化加载次数，一个入口文件可以并行加载的最大文件数量，默认3
              maxInitialRequests: 3,
              //表示在分离前的最小模块大小，默认为0，最小为30000
              minSize: 0
            },
            utils: {
              // split utils libs
              name: 'chunk-utils',
              // eslint-disable-next-line no-undef
              test: resolve('src/common/utils'),
              priority: 70,
              chunks: 'async',
              reuseExistingChunk: true
            }
          }
        })
        config.optimization.runtimeChunk('single')
        config.plugin('define').tap((definitions) => {
          Object.assign(definitions[0], {
            __VUE_OPTIONS_API__: 'true',
            __VUE_PROD_DEVTOOLS__: 'false',
            __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: 'false'
          })
          return definitions
        })
      }
    )
  }
})
