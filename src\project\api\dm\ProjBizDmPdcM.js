// import { request, replaceUrl } from "sn-base-utils";
import {default as request} from '@/project/utils/TestRequest'

export default class ProjBizDmPdcMApi {
    static config = {
        add: {
            url: '/project/dmPdc/add',
            method: 'POST'
        },
        remove: {
            url: '/project/dmPdc/delete',
            method: 'DELETE'
        },
        update: {
            url: '/project/dmPdc/update',
            method: 'PUT'
        },
        all: {
            url: '/project/dmPdc/all',
            method: "GET"
        }
    };

    /**
     * 新增文档管理-文档资料分类
     * @param data
     * @returns {*}
     */
    static add(data) {
        return request({
            url: this.config.add.url,
            method: this.config.add.method,
            data: data
        });
    }

    /**
     * 删除文档管理-文档资料分类
     * @param data
     * @returns {*}
     */
    static remove(data) {
        return request({
            url: this.config.remove.url,
            method: this.config.remove.method,
            data: data
        });
    }

    /**
     * 更新文档管理-文档资料分类
     * @param data
     * @returns {*}
     */
    static update(data) {
        return request({
            url: this.config.update.url,
            method: this.config.update.method,
            data: data
        });
    }

    /**
     * 全部文档管理-文档资料分类列表
     * @returns {*}
     */
    static all() {
        return request({
            url: this.config.all.url,
            method: this.config.all.method
        });
    }
}
