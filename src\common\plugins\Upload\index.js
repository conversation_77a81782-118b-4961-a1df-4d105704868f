import Upload from './main.vue'
import { createVNode, render, Teleport } from 'vue'

let seed = 1
const upload = (options) => {
  let id = 'upload_' + seed++
  const div = document.createElement('div')
  div.setAttribute('id', id)
  document.body.appendChild(div)
  const remove = () => {
    div.remove()
  }
  const content = createVNode(Upload, { ...options, remove })
  content.appContext = window.$SN.Instance._context

  const vnode = createVNode(Teleport, { to: '#' + id }, [content])
  vnode.appContext = window.$SN.Instance._context
  render(vnode, div)
}
export default upload
