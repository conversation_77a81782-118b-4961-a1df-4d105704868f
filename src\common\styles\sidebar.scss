.ep-menu {
  border: none !important;

  .svg-icon {
    width: 16px;
    height: 16px;
    fill: currentColor;
    margin-right: 10px;
    vertical-align: middle;
  }
  .sn-icon i {
    width: 16px;
    height: 16px;
    font-size: 16px;
    vertical-align: baseline;
  }
}

#sideBar {
  width: 220px;
  height: 100%;
  position: relative;
  transition: all 0.3s;

  .Collapse {
    position: absolute;
    height: 60px;
    width: 4px;

    margin-top: -30px;
    top: 50%;
    right: -4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    .icon {
      font-size: 6px;
      color: #ccc;
    }
  }

  .ep-scrollbar {
    height: 100%;
  }
}

// horizantal
#sideBarHorizantal {
  position: relative;
  transition: all 0.3s;

  .Collapse {
    position: absolute;
    height: 60px;
    width: 4px;

    margin-top: -30px;
    top: 50%;
    right: -4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    .icon {
      font-size: 6px;
      color: #ccc;
    }
  }

  .ep-scrollbar {
    height: 100%;
  }
}
.ep-sub-menu__icon-arrow {
  // right: 13px !important;
  font-size: 14px !important;
  position: absolute;
  top: 23px !important;
  // margin-top: -6px;
}
.hideSidebar {
  width: 64px !important;

  .submenu-title-noDropdown {
    padding: 0 !important;
    position: relative;

    .ep-tooltip {
      padding: 0 !important;
      display: inline-flex !important;
      align-items: center;
      justify-content: center;
    }
  }

  .ep-sub-menu {
    overflow: hidden;

    & > .ep-sub-menu__title {
      padding: 0 !important;
      justify-content: center;
    }
  }
  .ep-menu--collapse {
    .ep-sub-menu {
      & > .ep-sub-menu__title {
        display: flex;
        align-items: center;
        justify-content: center;
        & > .title {
          height: 0;
          width: 0;
          overflow: hidden;
          visibility: hidden;
          display: inline-block;
        }
      }
    }
  }
}

.ep-menu--horizontal .ep-menu .ep-menu-item,
.ep-menu--horizontal .ep-sub-menu__title {
  background: var(--theme-color) !important;
}

.ep-sub-menu__title,
.ep-menu-item {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-right: 34px !important;
}
