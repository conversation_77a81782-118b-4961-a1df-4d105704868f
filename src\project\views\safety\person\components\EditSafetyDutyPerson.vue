<template>
  <el-form :model="formData" :rules="formRules" ref="formRef" label-width="100px" label-position="right" :disabled="type == 'view'">
    <el-row :gutter="16" :span="24">
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <el-row :gutter="16" :span="24">
            <el-col :span='8'>
              <el-form-item label="业务编码" prop="code">
                <el-input v-model="formData.code" type="text" placeholder="请输入业务编码" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="甲方单位" prop="clientUnit">
                <el-input v-model="formData.clientUnit" disabled type="text" placeholder="请输入甲方单位" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="甲方签字人" prop="clientSignatory">
                <el-input v-model="formData.clientSignatory" type="text" placeholder="请输入甲方签字人" clearable>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </fieldset>
      </el-card>
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>乙方签订人
          </legend>
          <el-row :gutter="16" :span="24">
            <sn-crud :data="formData.safetySecondSignerDtoList" :option="safetySecondSignerOption" @row-save="subRowSave" @row-update="subRowUpdate" @row-del="(row, index) => {subDelRow(row, index, 'safetySecondSignerDtoList');}">
              <template #empty>
                <div>无数据</div>
              </template>
              <template #unit = "{ row }">
                <div v-permi="formPermi({fieldModelId: 'safetySecondSigner', field:'unit', fieldName: '单位'}, customFormPermi)">
                  <el-select disabled v-model="row.unit" placeholder="请选择" filterable >
                    <el-option v-for="(item, index) in orgData" :key="index" :label="item.name" :value="item.id"></el-option>
                  </el-select>
                </div>
              </template>
            </sn-crud>
            <el-button v-if="type.value != 'view'" @click="addData('safetySecondSignerDtoList')" type="primary" plain style="display: block; width: 100%; margin-top: 10px">添 加</el-button>
          </el-row>
        </fieldset>
      </el-card>
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>安全生产目标
          </legend>
          <el-row :gutter="16" :span="24">
            <sn-crud :data="formData.safetyProductionTargetDtoList" :option="safetyProductionTargetOption" @row-save="subRowSave" @row-update="subRowUpdate" @row-del="(row, index) => {subDelRow(row, index, 'safetyProductionTargetDtoList');}">
              <template #empty>
                <div>无数据</div>
              </template>
            </sn-crud>
            <el-button v-if="type.value != 'view'" @click="subAddRow('safetyProductionTargetDtoList')" type="primary" plain style="display: block; width: 100%; margin-top: 10px">添 加</el-button>
          </el-row>
        </fieldset>
      </el-card>
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>岗位安全生产职责及到位标准
          </legend>
          <el-row :gutter="16" :span="24">
            <el-col :span='24'>
              <el-form-item label="权利与义务" prop="rightDuty">
                <el-input type="textarea" :disabled="type.value && type.value != 'view'" v-model="formData.rightDuty" placeholder="请输入权利与义务" rows="3" clearable></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </fieldset>
      </el-card>
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>检查与考核
          </legend>
          <el-row :gutter="16" :span="24">
            <el-col :span='24'>
              <el-form-item label="检查与考核" prop="checkAssess">
                <el-input type="textarea" :disabled="type.value && type.value != 'view'" v-model="formData.checkAssess" placeholder="请输入检查与考核" rows="3" clearable></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </fieldset>
      </el-card>
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>附则
          </legend>
          <el-row :gutter="16" :span="24">
            <el-col :span='24'>
              <el-form-item label="附则" prop="bylaw">
                <el-input type="textarea" :disabled="type.value && type.value != 'view'" v-model="formData.bylaw" placeholder="请输入附则" rows="3" clearable></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </fieldset>
      </el-card>
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>单据信息
          </legend>
          <el-row :gutter="16" :span="24">
            <el-col :span='8'>
              <el-form-item label="创建人" prop="createName">
                <el-input v-model="formData.createName" type="text" disabled placeholder="" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="创建时间" prop="createTime">
                <el-date-picker type="dateTime" v-model="formData.createTime" style="width: 100%;" disabled format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" clearable></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="单据状态" prop="status">
                <el-select v-model="formData.status" disabled placeholder="请选择单据状态" clearable>
                  <el-option v-for="(item,index) in statusOption" :key="index" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="最近修改人" prop="updateName">
                <el-input v-model="formData.updateName" type="text" disabled placeholder="" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="最近修改时间" prop="updateTime">
                <el-date-picker type="dateTime" v-model="formData.updateTime" style="width: 100%;" disabled format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" clearable></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
        </fieldset>
      </el-card>
    </el-row>
  </el-form>
</template>

<script setup>
import SafetyDutyPersonApi from '@/project/api/safety/SafetyDutyPerson.js'
import projectTeamPerson from "./projectTeamPerson.vue";
import ProjBizProjectTeamOrgApi from "@/project/api/projectTeam/ProjBizProjectTeamOrg";
import {
  nextTick,
  onMounted,
  ref,
  reactive,
  toRef,
  defineProps,
  defineExpose,
  getCurrentInstance
} from 'vue';
import {
  useRoute,
  useRouter
} from 'vue-router'
import store from "@/store";
import {getCurrentFormattedTime} from "@/common/utils/datetime";
const {
  proxy
} = getCurrentInstance()
const route = useRoute();
const router = useRouter();
const props = defineProps({
  data: Object,
});
const formRef = ref()
const type = toRef(props.data?.type);
let formData = ref({
  projectId: sessionStorage.getItem('projectId'),
  projectName: "",
  projectDept: "",
  clientUnit: JSON.parse(store.state.user.orgName)?.find((item) => item.id === store.state.user.defaultOrg)?.orgName,
  clientSignatory: store.state.user && store.state.user.userInfo.userName ? store.state.user.userInfo.userName : null,
  code: "",
  safetySecondSignerDtoList: [],
  safetyProductionTargetDtoList: [],
  rightDuty: "",
  checkAssess: "",
  bylaw: "",
  createName: store.state.user && store.state.user.userInfo.userName ? store.state.user.userInfo.userName : null,
  createTime: getCurrentFormattedTime(),
  status: "0",
  updateName: "",
  updateTime: ""
});
let formRules = ref({
  clientUnit: [{
    required: true,
    message: "甲方单位不能为空"
  }],
  clientSignatory: [{
    required: true,
    message: "甲方签字人不能为空"
  }],
  code: [{
    required: true,
    message: "业务编码不能为空"
  }],
  rightDuty: [{
    required: true,
    message: "权利与义务不能为空"
  }],
  checkAssess: [{
    required: true,
    message: "检查与考核不能为空"
  }],
  bylaw: [{
    required: true,
    message: "附则不能为空"
  }],
  createName: [],
  createTime: [],
  status: [],
  updateName: [],
  updateTime: []
});
let delRowData = ref({});
let statusOption = ref([{
  label: "草稿",
  value: "0"
}, {
  label: "已发布",
  value: "1"
}]);
let safetySecondSignerOption = ref({
  tip: false,
  border: true,
  index: true,
  stripe: true,
  menu: type.value == 'view'? false:true,
  header: false,
  menuType: "text",
  addBtn: true,
  addBtnText: "新增",
  editBtn: false,
  editBtnText: "编辑",
  delBtn: true,
  delBtnText: "删除",
  cellBtn: true,
  maxHeight: "200px",
  column: [{
    label: "姓名",
    prop: "name",
    type: "input",
    cell: true
  }, {
    label: "岗位",
    prop: "post",
    type: "input",
    cell: true
  }, {
    label: "单位",
    prop: "unit",
    type: "input",
    cell: false,
    columnSlot: true
  }]
});
let safetyProductionTargetOption = ref({
  tip: false,
  border: true,
  index: true,
  stripe: true,
  menu: type.value == 'view'? false:true,
  header: false,
  menuType: "text",
  addBtn: true,
  addBtnText: "新增",
  editBtn: true,
  editBtnText: "编辑",
  delBtn: true,
  delBtnText: "删除",
  cellBtn: true,
  maxHeight: "200px",
  column: [{
    label: "生产目标名称",
    prop: "targetName",
    type: "input",
    cell: true
  }]
});

const state = reactive({
  flowData: {
    businessKey: "",
    procInstId: "",
    procDefKey: "",
    taskId: "",
    formConfig: [],
    variableList: [],
    customPropertyConfigList: []
  },
  formAuth: {},
  taskFlag: 1,
  firstAct: false,
  handlerClose: null,
  rootFields: {},
  fullFieldNameMap: {},
  formAuthList: {}
})

let orgData = ref([]);

ProjBizProjectTeamOrgApi.list({projectId : sessionStorage.getItem('projectId')}).then((res) => {
  orgData.value = res.data;
})

function subRowSave(form, done) {
  //编辑行
  done();
}

function subAddRow(name) {
  //新增一行
  if (formData.value[name]) {
    formData.value[name].push({
      $cellEdit: true,
    });
  } else {
    let arr = new Array();
    arr.push({
      $cellEdit: true
    });
    formData.value[name] = arr
  }
}

async function addData(name) {
//编辑,新增按钮操作
  let editType = "view";
  //编辑,新增按钮操作
  proxy.$DialogForm.show({
    title: "乙方签订人",
    type: 'dialog',
    width: "80%",
    content: projectTeamPerson,
    data: {
      type: editType,
    },
    callback: (res) => {
      if (res.type && res.type !== 'close') {
        if (res.dialogRefs) {
          res.dialogRefs.selectData();
          const oldTeamIds = new Set(formData.value[name].map((item) =>item.proTeamId));
          res.dialogRefs.sData.value.forEach(item => {
            if (!oldTeamIds.has(item.proTeamId)) {
              formData.value[name].push({
                name: item.userName,
                proTeamId: item.proTeamId,
                unit: item.orgId,
                post: item.roleName
              })
            }
          })
          res.close();
        }
      } else {
        res.close();
      }
    }
  });
}

function subRowUpdate(form, index, done, loading) {
  //编辑行
  done();
}

function subDelRow(row, index, name) {
  proxy.$confirm('确认删除？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    //删除行
    if (row[0].id) {
      let data = JSON.parse(JSON.stringify(row[0]));
      if (delRowData.value[name]) {
        delRowData.value[name].push(Object.assign(data, {
          delFlag: 1,
        }));
      } else {
        delRowData.value[name] = [
          Object.assign(data, {
            delFlag: 1,
          }),
        ]
      }
    }
    formData.value[name].splice(index, 1);
  }).catch(() => {
    // 用户点击取消，不关闭弹窗
  });
}

function getFormData() {
  //获取formData数据
  for (let item in delRowData.value) {
    formData.value[item] = formData.value[item].concat(delRowData.value[item]);
  }
  return formData.value;
}
formData = toRef(Object.keys(props.data?.formData || {}).length > 0 ? {
  ...props.data?.formData
} : toRef(formData.value));

const buttonType = ref()
function submitData(btnType) {
  return new Promise((resolve) => {
    formRef.value.validate((flag) => {
      if (!flag) {
        return false;
      } else {
        buttonType.value = btnType
        if (type.value === "add") {
          resolve(saveData());
        } else {
          resolve(editData());
        }
      }
    });
  });
}

function saveData() {
  //新增操作
  const formData1 = getFormData();
  if (buttonType.value == 'submit') {
    formData1.status = '1'
  }
  return SafetyDutyPersonApi.add(formData1).then((res) => {
    if(res.data){
      proxy.$message.success("保存成功");
      formData.value.id = res.data.id;
      formData.value.safetyProductionTargetDtoList = res.data.safetyProductionTargetDtoList
      formData.value.safetySecondSignerDtoList = res.data.safetySecondSignerDtoList
      type.value = "edit"
      return true;
    }
  });
}

function editData() {
  //编辑操作
  const formData1 = getFormData();
  if (buttonType.value == 'submit') {
    formData1.status = '1'
  }
  return SafetyDutyPersonApi.update(formData1).then((res) => {
    proxy.$message.success("修改成功");
    delRowData = ref({});
    formData.value.safetyProductionTargetDtoList = res.data.safetyProductionTargetDtoList
    formData.value.safetySecondSignerDtoList = res.data.safetySecondSignerDtoList
    return true;
  });
}

let auth = new Map();
function customFormPermi(obj) {
  /**
   * 工作流表单权限功能：自定义表单权限处理函数，将对应的对象的属性还原成对应的权限值
   */
  let {
    field,
    fieldModelId
  } = obj
  let fullFieldName = `${fieldModelId}_${field}`
  return auth.get(fullFieldName) || 0
};
/**
 * 处理表单权限相关操作, 开发者在template的v-permi设置中必须使用此函数
 * @param {Object} obj - 包含字段名称的对象
 * @param {Function} [callback] - 可选的回调函数，接收 obj 作为参数
 * 如果传入对象的 fieldName 不在 rootFields 中，则将其添加到 rootFields 数组，然后执行回调函数（如果存在）
 */
function formPermi(obj, callback) {
  if (!state.rootFields[obj.fieldModelId + '__' + obj.field]) {
    state.rootFields[obj.fieldModelId + '__' + obj.field] = obj
  }
  return callback && callback(obj)
};

defineExpose({
  getFormData,
  submitData,
});
</script>

<style lang="scss" scoped>

</style>
