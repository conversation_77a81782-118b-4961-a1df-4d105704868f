<template>
  <div>
    <sn-crud :data="listData" :option="option" v-model:page="queryForm.page" v-model:search="queryForm.filter" @on-load="getPageList" @search-change="onChangeSearch" @search-reset="onResetSearch">
      <template #menuLeft="{}">
        <el-button type="primary" :size="size" icon="el-icon-download" @click="onExportData()">列表导出</el-button>
        <el-button type="primary" :size="size" icon="el-icon-download" @click="downloadFormTemplate()">表单模板 </el-button>
      </template>
      <template #reportNumber="scope">
        <el-link type="primary" @click="viewReport(scope.row)">{{ scope.row.reportNumber }}</el-link>
      </template>
      <template #commandNumber="scope">
        <el-link type="primary" @click="onEditData(scope.row, 'view')">{{ scope.row.commandNumber }}</el-link>
      </template>
      <template #menu="{ row, index, size }">
        <!--未发布-->
        <el-button type="primary" :size="size" icon="el-icon-edit" link v-if="row.publishStatus === '1'" @click="onEditData(row)"> 编辑 </el-button>
        <el-button type="danger" :size="size" icon="el-icon-delete" link v-if="row.publishStatus === '1'" @click="onDelData([row])"> 删除 </el-button>
        <!-- <el-button type="primary" :size="size" icon="el-icon-plus" link v-if="row.publishStatus === '1'" @click="onEditData(row)">发布</el-button> -->
        <!--已发布-->
        <!-- <el-button type="primary" :size="size" icon="el-icon-view" link v-if="row.publishStatus === '2'" @click="onEditData(row, 'view')">查看</el-button> -->
        <el-button type="danger" :size="size" icon="el-icon-edit" link v-if="row.publishStatus === '2'" @click="withdraw(row)"> 撤回 </el-button>
        <el-button type="primary" :size="size" icon="el-icon-download" link v-if="row.publishStatus === '2'" @click="onDownloadData(row)"> 下载 </el-button>
      </template>
    </sn-crud>
    <div ref="divDialogRef"></div>
  </div>
</template>

<script>
import ProjBizConsStartWorkCommandApi from '@/project/api/constructionManagement/sceneStartWorkCommand/ProjBizConsStartWorkCommand.js'
import EditProjBizConsStartWorkCommand from '@/project/views/constructionManagement/sceneStartWork/sceneStartWorkCommand/components/EditProjBizConsStartWorkCommand.vue'
import { getToken } from 'sn-base-utils'
import ProjBizConsStartWorkReportApi from '@/project/api/constructionManagement/sceneStartWorkReport/ProjBizConsStartWorkReport'
import { generateWordDocument, download, printDocument } from '@/project/components/downloadWord/word.js'
import EditProjBizConsStartWorkReport from '@/project/views/constructionManagement/sceneStartWork/sceneStartWorkReport/components/EditProjBizConsStartWorkReport.vue'

export const routerConfig = [
  {
    menuType: 'C',
    menuName: '项目开工令'
  },
  {
    menuType: 'F',
    menuName: '查看',
    perms: 'sceneStartWorkCommand:show',
    api: [ProjBizConsStartWorkCommandApi.config.pageList]
  },
  {
    menuType: 'F',
    menuName: '新增',
    perms: 'sceneStartWorkCommand:add',
    api: [ProjBizConsStartWorkCommandApi.config.add]
  },
  {
    menuType: 'F',
    menuName: '修改',
    perms: 'sceneStartWorkCommand:update',
    api: [ProjBizConsStartWorkCommandApi.config.update, ProjBizConsStartWorkCommandApi.config.view]
  },
  {
    menuType: 'F',
    menuName: '删除',
    perms: 'sceneStartWorkCommand:del',
    api: [ProjBizConsStartWorkCommandApi.config.remove]
  },
  {
    menuType: 'F',
    menuName: '发布',
    perms: 'sceneStartWorkCommand:publish',
    api: [ProjBizConsStartWorkCommandApi.config.update]
  },
  {
    menuType: 'F',
    menuName: '取消发布',
    perms: 'sceneStartWorkCommand:unpublish',
    api: [ProjBizConsStartWorkCommandApi.config.update]
  },
  {
    menuType: 'F',
    menuName: '下载',
    perms: 'sceneStartWorkCommand:download',
    api: [ProjBizConsStartWorkReportApi.config.view, ProjBizConsStartWorkReportApi.config.pageList, ProjBizConsStartWorkReportApi.config.download]
  }
]
</script>

<script setup>
import { ref, getCurrentInstance } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getCurrentFormattedTime } from '@/common/utils/datetime'

const route = useRoute()
const router = useRouter()
const { proxy } = getCurrentInstance()
let option = ref({
  tip: false,
  dialogType: 'page',
  border: true,
  index: true,
  stripe: true,
  menu: true,
  header: true,
  height: 'auto',
  searchSpan: 6,
  searchIcon: true,
  searchMenuSpan: 6,
  searchIndex: 3,
  searchLabelWidth: 120,
  selection: true,
  showTree: false,
  excelBtn: false,
  addBtn: false,
  delBtn: false,
  delBtns: false,
  editBtn: false,
  delBtnsText: '批量删除',
  addBtnText: '新增',
  customEditBtnText: '编辑',
  customDelBtnText: '删除',
  customViewBtnText: '查看',
  searchLabelWidth: 120,
  column: [
    {
      label: '开工报审表编号',
      prop: 'reportNumber',
      minWidth: 150,
      search: true
    },
    {
      label: '开工令编号',
      prop: 'commandNumber',
      minWidth: 150,
      search: true
    },
    {
      label: '工程名称',
      prop: 'projectName',
      search: true,
      minWidth: 150,
      overHidden: true,
    },
    {
      label: '施工单位',
      prop: 'applicantOrg',
      search: true,
      // hide: true
    },
    {
      label: '申请人',
      prop: 'applicant',
      search: true,
      // hide: true
    },
    {
      label: '监理单位',
      prop: 'supervisorCompany',
      search: true
    },
    {
      label: '开工日期',
      prop: 'startDate',
      width: 100,
      search: false
    },
    {
      label: '发布状态',
      prop: 'publishStatus',
      search: true,
      type: 'select',
      width: 100,
      dicData: [
        { label: '全部', value: '' },
        { label: '草稿', value: '1' },
        { label: '已发布', value: '2' }
      ],
      formatter: (val, value) => {
        if (value === '1') {
          return '草稿'
        } else {
          return '已发布'
        }
      }
    }
  ]
})
let listData = ref([])
let queryForm = ref({
  page: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  filter: {
    createTime: null
  }
})
const divDialogRef = ref(null)

function getPageList() {
  //查询分页列表
  // 查询前处理参数
  const params = handleQueryForm()
  ProjBizConsStartWorkCommandApi.pageList(params).then((res) => {
    listData.value = res.data.dataList
    queryForm.value.page.total = res.data.totalCount
  })
}

function onResetSearch() {
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20
  }
  getPageList()
}

function handleQueryForm() {
  // 处理参数
  const { pageSize, pageNum } = queryForm.value.page
  const filter = {}
  for (let key in queryForm.value.filter) {
    if (queryForm.value.filter[key]?.length) {
      // sn-curd搜索select会生成$prop的变量
      if (key.startsWith('$')) continue
      filter[key] = queryForm.value.filter[key]
    }
  }
  delete filter.createTime
  if (Array.isArray(queryForm.value.filter.createTime) && queryForm.value.filter.createTime?.length === 2) {
    filter['beginCreateTime'] = queryForm.value.filter.createTime[0]
    filter['endCreateTime'] = queryForm.value.filter.createTime[1]
  }
  const searchParams = {
    page: {
      pageSize,
      pageNum
    },
    filter
  }
  return searchParams
}

function onChangeSearch(params, done) {
  //搜索按钮操作
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20
  }
  getPageList()
  done && done()
}
function generateButtons(editType, row) {
  const buttons = [
    { key: 'cancel', text: '关闭', buttonType: '', icon: 'el-icon-close' },
    { key: 'downloadForm', text: '下载表单', buttonType: 'primary', icon: 'el-icon-download' }
  ]
  if (editType !== 'view') {
    buttons.push({ key: 'save', text: '保存', buttonType: 'primary', icon: 'el-icon-check'},
      { key: 'publish', text: '发布', buttonType: 'primary', icon: 'sn-button-fasong' }
    )
  } else {
    if (row.publishStatus === '1') {
      buttons.push({ key: 'edit', text: '编辑', buttonType: 'primary', icon: 'el-icon-edit' })
    }
  }
  return buttons
}
async function onEditData(row, optionType) {
  //编辑,新增按钮操作
  let editType = row ? 'edit' : 'add'
  if (optionType) editType = optionType
  let rowInfo = await (editType !== 'add' ? ProjBizConsStartWorkCommandApi.view(row.id) : {})
  const formData = editType !== 'add' ? rowInfo.data : rowInfo
  //编辑,新增按钮操作
  proxy.$DialogForm.show({
    title: editType == 'edit' ? '编辑' : editType == 'view' ? '查看' : '新增',
    type: option.value.dialogType,
    width: '80%',
    content: EditProjBizConsStartWorkCommand,
    data: {
      formData: formData,
      type: editType
    },
    el: divDialogRef.value,
    option: {
      submitBtn: false,
      emptyBtn: false,
      submitText: '保存',
      emptyText: '关闭',
      extendButton: generateButtons(editType, row),
    },
    callback: (res) => {
      let vm = res.dialogRefs
      if (vm) {
        if (res.type === 'downloadForm') {
          vm.downloadWord()
        } else if (res.type === 'save') {
          let status = '1'
          res.dialogRefs.submitData(status).then((flag) => {
            // if (flag) {
            //   getPageList()
            // }
          })
        } else if (res.type === 'publish') {
          let status = '2'
          res.dialogRefs.submitData(status).then((flag) => {
            if (flag) {
              getPageList()
              res.close()
            }
          })
        } else if (res.type === 'edit') {
          res.close()
          onEditData(row)
        } else if (res.type === 'cancel') {
          proxy.$modal.confirm('确认关闭？', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            })
            .then(() => {
              res.close()
            })
        } else {
          res.close()
        }
      }
    }
  })
}

// 查看开工报审表
async function viewReport(row) {
  //编辑,新增按钮操作
  let editType = 'view'
  let rowInfo = await ProjBizConsStartWorkReportApi.view(row.reportId)
  const formData = rowInfo.data
  proxy.$DialogForm.show({
    title: '查看',
    type: 'page',
    width: '100%',
    content: EditProjBizConsStartWorkReport,
    el: divDialogRef.value,
    data: {
      formData,
      isShowCloseBtn: false,
      approvalOption: ref({
        isShowApprovalList: true,
        isShowFlowDiagram: true,
        procInstId: rowInfo.data.procInstanceId
      }),
      type: editType
    },
    option: {
      submitBtn: false,
      emptyBtn: false,
      submitText: '保存',
      emptyText: '关闭',
      extendButton: [
        {
          key: 'cancel',
          text: '关闭',
          buttonType: '',
          icon: 'el-icon-close'
        }
      ]
    },
    callback: (res) => {
      res.close()
    }
  })
}

const generatedFile = ref(null)

// 下载表单模板
async function downloadFormTemplate() {
  try {
    generatedFile.value = await generateWordDocument({}, 'projBizConsStartWorkCommandBlank')
    if (generatedFile.value) {
      download(generatedFile.value, '开工令模板.docx')
      proxy.$message.success('word下载成功！')
    } else {
      console.error('No file generated')
    }
  } catch (error) {
    console.error('Error generating document:', error)
  }
}

function onDelData(rows) {
  //删除行
  if (!rows || rows.length == 0) {
    proxy.$message.info('请勾选数据！')
    return false
  }
  let ids = rows.map((item) => {
    return item.id
  })
  proxy.$modal
    .confirm('确认删除数据项？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    .then(() => {
      ProjBizConsStartWorkCommandApi.remove(ids).then((res) => {
        proxy.$message.success('已删除')
        getPageList()
      })
    })
    .catch(() => {})
}

function onDownloadData(row) {
  let params = {
    relevantId: row.id,
    type: 'sceneStartWorkCommand'
  }
  let timestamp = proxy.parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')
  proxy.download(`/project/sceneStartWorkCommand/fillTemplateAndDownload/${row.id}`, params, '项目开工令附件导出-' + row.commandNumber + '-' + timestamp + '.zip')
}
// 撤销发布
function withdraw(row) {
  return proxy.$confirm('确定撤回当前开工令?', '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
      row.publishStatus = '1'
      ProjBizConsStartWorkCommandApi.update(row).then((res) => {
        proxy.$message.success('已撤回')
        getPageList()
      })
    })
}
// 列表导出
function onExportData() {
  const params = handleQueryForm()
  let queryForm = JSON.parse(JSON.stringify(params))
  let timestamp = proxy.parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')
  proxy.download('/project/sceneStartWorkCommand/export', queryForm, '开工令-' + timestamp + '.xlsx')
}
</script>

<style lang="scss" scoped></style>
