<template>
  <sn-crud :data="listData" :option="option" v-model:page="queryForm.page" v-model:search="queryForm.filter" @on-load="getPageList" @search-change="onChangeSearch" @search-reset="onResetSearch" @addBtnHandle="onEditData" @row-del="onDelData">
    <template #menu="{ row, index, size }">
      <!--草稿-->
      <el-button type="primary" :size="size" v-if="row.approvalStatus === '1'" icon="el-icon-edit" link @click="onEditData(row,'edit')">编辑</el-button>
      <el-button type="primary" :size="size" v-if="row.approvalStatus === '1'" icon="el-icon-download" link @click="onDownloadData(row)">下载</el-button>
      <el-button type="danger" :size="size" v-if="row.approvalStatus === '1' && row.processInstanceId" icon="el-icon-edit" link @click="onCancelData(row,'stop')">作废</el-button>
      <el-button type="danger" :size="size" v-if="row.approvalStatus === '1' && !row.processInstanceId" icon="el-icon-delete" link @click="onDelData([row])">删除</el-button>
      <!--已提交-->
      <el-button type="danger" :size="size" v-if="row.approvalStatus === '2'" icon="el-icon-edit" link @click="onCancelData(row,'revoke')">撤回</el-button>
      <el-button type="primary" :size="size" v-if="row.approvalStatus === '2'" icon="el-icon-download" link @click="onDownloadData(row)">下载</el-button>
      <!--驳回上一节点-->
      <el-button type="primary" :size="size" v-if="row.approvalStatus === '3'" icon="el-icon-download" link @click="onDownloadData(row)">下载</el-button>
      <!--审批中-->
      <el-button type="primary" :size="size" v-if="row.approvalStatus === '4'" icon="el-icon-download" link @click="onDownloadData(row)">下载</el-button>
      <!--已作废-->
      <el-button type="primary" :size="size" v-if="row.approvalStatus === '5'" icon="el-icon-download" link @click="onDownloadData(row)">下载</el-button>
      <!--已审批-->
      <el-button type="primary" :size="size" v-if="row.approvalStatus === '6'" icon="el-icon-download" link @click="onDownloadData(row)">下载</el-button>
      <!--已驳回提交人-->
      <el-button type="primary" :size="size" v-if="row.approvalStatus === '7'" icon="el-icon-edit" link @click="onEditData(row,'edit')">编辑</el-button>
      <el-button type="primary" :size="size" v-if="row.approvalStatus === '7'" icon="el-icon-download" link @click="onDownloadData(row)">下载</el-button>
      <el-button type="danger" :size="size" v-if="row.approvalStatus === '7' && row.processInstanceId" icon="el-icon-edit" link @click="onCancelData(row,'stop')">作废</el-button>
    </template>
    <template #applyStopTimeSearch = "{ row, size }">
        <el-date-picker
            v-model="queryForm.filter.applyStopTime"
            type="daterange"
            value-format="YYYY-MM-DD"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 100%;"
        />
    </template>
    <template #applyNo="{ row }">
      <el-link class="image-link" @click="onViewData(row)">{{ row.applyNo }}</el-link>
    </template>
    <template #header>
      <el-button type="primary" icon="el-icon-plus" style="margin-left: 10px;margin-bottom: 10px;" @click="onEditData">新增</el-button>
      <!-- <el-button type="primary" icon="el-icon-document" style="margin-left: 10px;margin-bottom: 10px;" @click="onListTemplate">列表模板</el-button>
      <el-button type="primary" icon="el-icon-upload" style="margin-left: 10px;margin-bottom: 10px;" @click="onListImport">列表导入</el-button> -->
      <el-button type="primary" icon="el-icon-download" style="margin-left: 10px;margin-bottom: 10px;" @click="onListExport">列表导出</el-button>
    </template>
    <template #constructionUnit="{ row }">
      <el-tooltip class="item" effect="dark" :content="row.constructionUnit" placement="top">
        <div class="text-overflow">{{ row.constructionUnit }}</div>
      </el-tooltip>
    </template>
  </sn-crud>
  <div ref="myRef"></div>
</template>

<script>
import ProjBizStopworkApplicationApi from '@/project/api/stopworkApplication/ProjBizStopworkApplication.js'
  import EditProjBizStopworkApplication from "./components/EditProjBizStopworkApplication.vue";
  import { getToken } from "sn-base-utils";
  export const routerConfig = [{
    menuType: "C",
    menuName: "停工申请单",
  }, {
    menuType: "F",
    menuName: "查看",
    perms: "show",
    api: [ProjBizStopworkApplicationApi.config.pageList],
  }, {
    menuType: "F",
    menuName: "新增",
    perms: "add",
    api: [ProjBizStopworkApplicationApi.config.add],
  }, {
    menuType: "F",
    menuName: "修改",
    perms: "update",
    api: [ProjBizStopworkApplicationApi.config.update, ProjBizStopworkApplicationApi.config.view],
  }, {
    menuType: "F",
    menuName: "删除",
    perms: "del",
    api: [ProjBizStopworkApplicationApi.config.remove],
  }];
</script>

<script setup>
import {
  ref,
  getCurrentInstance
} from 'vue';
const {
  proxy
} = getCurrentInstance()
const myRef = ref(null);
let option = ref({
  columnSlot: true,
  tip: false,
  dialogType: "page",
  border: true,
  index: true,
  stripe: true,
  menu: true,
  header: false,
  height: "auto",
  searchSpan: 6,
  searchIcon: true,
  searchLabelWidth: 120,
  searchMenuSpan: 6,
  searchIndex: 3,
  selection: true,
  showTree: false,
  excelBtn: false,
  delBtn: false,
  editBtn: false,
  delBtnsText: "删除",
  addBtnText: "新增",
  customEditBtnText: "编辑",
  customDelBtnText: "删除",
  customViewBtnText: "查看",
  column: [
    {
    label: "申请单编号",
    width: 200,
    prop: "applyNo"
  }, {
    label: "施工单位",
    prop: "constructionUnit",
    width: 200,
    search: true
  }, {
    label: "申请停工时间",
    prop: "applyStopTime",
    width: 200,
    search: true,
    searchSlot: true,
  }, {
    label: "申请人",
    prop: "applicant",
    search: true
  }, {
    label: "申请日期",
    prop: "applyDate"
  }, {
    label: "关联停工令编号",
    prop: "relatedOrderNo",
    width: 200,
    search: true
  }, {
    label: "审批状态",
    prop: "approvalStatus",
    search: true,
    dictDatas: "flow_status",
    type: "select",
    dicUrl: "/system/dict/data/type/flow_status",
    dicMethod: "get",
    props: {
      label: "dictLabel",
      value: "dictValue"
    }
  }]
});
let listData = ref([]);
let queryForm = ref({
  page: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  filter: {
    createTime: null
  }
});
let formRules = ref({});

function getPageList() {
  //查询分页列表
  // 查询前处理参数
  const params = handleQueryForm();
  ProjBizStopworkApplicationApi.pageList(params).then((res) => {
    listData.value = res.data.dataList;
    queryForm.value.page.total = res.data.totalCount;
  });
}

function onResetSearch() {
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
}

function handleQueryForm() {
  // 处理参数
  const {
    pageSize,
    pageNum
  } = queryForm.value.page;
  const filter = {}
  for (let key in queryForm.value.filter) {
    if (queryForm.value.filter[key]?.length) {
      // sn-curd搜索select会生成$prop的变量
      if (key.startsWith("$")) continue;
      filter[key] = queryForm.value.filter[key];
    }
  }
  delete filter.createTime;
  // 处理申请停工时间为起止两个字段
  if (Array.isArray(filter.applyStopTime) && filter.applyStopTime.length === 2) {
    filter.applyStopTimeStart = filter.applyStopTime[0];
    filter.applyStopTimeEnd = filter.applyStopTime[1];
    delete filter.applyStopTime;
  }
  if (Array.isArray(queryForm.value.filter.createTime) && queryForm.value.filter.createTime?.length === 2) {
    filter["beginCreateTime"] = queryForm.value.filter.createTime[0];
    filter["endCreateTime"] = queryForm.value.filter.createTime[1];
  }
  const searchParams = {
    page: {
      pageSize,
      pageNum,
    },
    filter,
  }
  return searchParams
}

function onChangeSearch(params, done) {
  //搜索按钮操作
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
  done && done();
}
async function onEditData(row) {
  //编辑,新增按钮操作
  let editType = row?.procInstanceId ? 'view' : (row?.id ? 'edit' : 'add');
  let rowInfo = await (editType !== "add" ? ProjBizStopworkApplicationApi.view(row.id) : {});
  const formData = editType !== "add" ? rowInfo.data : rowInfo;
  //编辑,新增按钮操作
  proxy.$DialogForm.show({
    title:  editType == "edit" ? "编辑" : (editType == "view" ? "查看" : "新增"),
    type: option.value.dialogType,
    width: "80%",
    el: myRef.value,
    content: EditProjBizStopworkApplication,
    data: editType !== "add" ? {
      formData: rowInfo.data,
      isShowCloseBtn: false,
      approvalOption: ref({
        isShowApprovalList: true,
        isShowFlowDiagram: true,
        procInstId: rowInfo.data.procInstanceId
      }),
      type: editType
    } : {
      approvalOption: ref({
        isShowApprovalList: false,
        isShowFlowDiagram: false,
        procInstId: null
      }),
      type: editType
    },
    option: {
      submitBtn: false,
      emptyBtn: false,
      submitText: '保存',
      emptyText: '取消',
      extendButton: editType !== "view" ? [{
        key: 'cancel',
        text: '取消',
        buttonType: '',
        icon: 'el-icon-close',
      }, {
        key: 'save',
        text: '保存',
        buttonType: 'primary',
        icon: 'el-icon-check',
      }, {
        key: 'afterProcessIsInitiated',
        text: '发起流程',
        buttonType: 'primary',
        icon: 'sn-button-fasong',
      }, ] : [{
        key: 'cancel',
        text: '取消',
        buttonType: '',
        icon: 'el-icon-close',
      }, ],
    },
    callback: (res) => {
      let vm = res.dialogRefs
      if (vm) {
        let formData = vm.getFormData();
        switch (res.type) {
          // 取消
          case 'cancel':
            if (editType === 'edit') {
              proxy.$confirm('确认关闭？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                getPageList();
                res.close();  
              }).catch(() => {});
            } else {
              getPageList();
              res.close();
            }
            break;
            // 保存
          case 'save':
            vm.getSaveFormData(formData).then(resp => {
              proxy.$message.success("保存成功");
              getPageList();
            })
            break;
            // 发起流程
          case 'afterProcessIsInitiated':
            let startProcDto = {
              businessKey: null,
              clientId: null
            };
            vm.getStartFlow(formData, startProcDto).then(resp => {
              proxy.$message.success("流程启动成功");
              getPageList();
              res.close();
            })
            break;
        }
      }
    }
  });
}

async function onViewData(row) {
  //查看操作
  let editType = "view";
  let extendButtons = [];
  if (row.approvalStatus === "1") {
    extendButtons.push({
      key: 'edit',
      text: '编辑',
      icon: 'el-icon-edit',
      buttonType: 'primary',
      click: (res) => {
        res.close();
        onEditData(row);
      },
    });
  }
  extendButtons.push({
    key: 'close',
    text: '关闭',
    icon: 'el-icon-close',
    buttonType: '',
  });
  let rowInfo =  await(ProjBizStopworkApplicationApi.view(row.id));
  const formData = rowInfo.data;
  //查看操作
  proxy.$DialogForm.show({
    title: "查看",
    type: option.value.dialogType,
    width: "80%",
    el: myRef.value,
    content: EditProjBizStopworkApplication,
    data: {
      formData: formData,
      type: editType,
    },
    option: {
      submitBtn: false,
      emptyBtn: false,
      emptyText:'取消',
      extendButton: extendButtons
    },
    callback: (res) => {
      if (res.type === 'edit') {
        res.close();
        onEditData(row);
      } else if (res.type === 'close') {
        if (editType === 'edit') {
          proxy.$confirm('确认关闭？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            res.close();
          }).catch(() => {});
        } else {
          res.close();
        }
      } else if (res.type && res.type !== 'close') {
        if (res.dialogRefs) {
          res.dialogRefs.submitData(res.type).then((flag) => {
            if (flag) {
              getPageList();
              res.close();
            }
          });
        }
      } else {
        res.close();
      }
    }
  });
}


function onDelData(rows) {
  //删除行
  if (!rows || rows.length == 0) {
    proxy.$message.info("请勾选数据！");
    return false;
  }
  let ids = rows.map((item) => {
    return item.id;
  });
  proxy.$modal.confirm("确认删除数据项？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    ProjBizStopworkApplicationApi.remove(ids).then((res) => {
      proxy.$message.success("已删除");
      getPageList();
    });
  }).catch(() => {});
}

/*文件下载*/
function onDownloadData(row) {
  let params = {
    relevantId: row.id,
    type: 'unitQualification'
  }
  let timestamp = proxy.parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')
  proxy.download(ProjBizStopworkApplicationApi.download.url, params, '停工申请-' + row.code + '-' + timestamp + '.zip')
}

/*流程作废*/
function onCancelData(row, type) {
  //删除行
  let formDate = {
    id: row.id,
    operateFlowStatus: type,
  }
  let typeName;
  if (type == 'revoke') {
    typeName = '撤回';
  } else {
    typeName = '作废';
  }
  proxy.$modal.confirm("确认" + typeName + "流程？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    ProjBizStopworkApplicationApi.operateFlow(formDate).then((res) => {
      proxy.$message.success("已" + typeName);
      getPageList();
    });
  }).catch(() => { });
}
</script>

<style lang="scss" scoped>
.image-link {
  color: #1a5cff !important;
  cursor: pointer;
  text-decoration: none;
}
.image-link:hover {
  text-decoration: underline;
}
.text-overflow {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
