<template>
  <el-form :model="formData" :rules="formRules" ref="formRef" label-width="100px" label-position="right"
           :disabled="type == 'show'">
    <el-row :gutter="16" :span="24">
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>基本信息
          </legend>
          <el-row :gutter="16" :span="24">
            <el-col :span='8'>
              <el-form-item label="业务编号" prop="code">
                <el-input v-model="formData.code" type="text" placeholder="请输入"  @blur="checkRepeatNumber()" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='7'>
              <el-form-item label="年月" prop="yearMonth">
                <el-date-picker
                    v-model="formData.yearMonth"
                    type="month"
                    value-format="YYYY-MM"
                />
<!--                <el-input v-model="formData.yearMonth" type="text" placeholder="请输入" clearable>-->
<!--                </el-input>-->
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="本月支出金额" prop="expendAmount" label-width="105px">
                <el-input  v-model="formData.expendAmount" type="text" placeholder="请输入" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='12' style="display: none">
              <el-form-item label="项目编码" prop="projCode">
                <el-input v-model="formData.projCode" type="text" placeholder="请输入" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='12' style="display: none">
              <el-form-item label="项目名称" prop="projName">
                <el-input v-model="formData.projName" type="text" placeholder="请输入" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='12' style="display: none">
              <el-form-item label="项目id" prop="projId">
                <el-input v-model="formData.projId" type="text" placeholder="请输入" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='12' style="display: none">
              <el-form-item label="文件id" prop="fileId" style="display: none">
                <el-input v-model="formData.fileId" type="text" placeholder="请输入" clearable>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </fieldset>
      </el-card>
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>安全措施费投入
          </legend>
          <el-row :gutter="16" :span="24">
            <sn-crud :data="formData.projBizSafefeeInvestSDtoList" :option="projBizSafefeeInvestSOption"
                     @row-save="subRowSave" @row-update="subRowUpdate"
                     @row-del="(row, index) => {subDelRow(row, index, 'projBizSafefeeInvestSDtoList');}">
              <template #empty>
                <div>无数据</div>
              </template>
            </sn-crud>
            <el-button @click="subAddRow('projBizSafefeeInvestSDtoList')" type="primary" plain
                       style="display: block; width: 100%; margin-top: 10px">新增一行数据
            </el-button>
          </el-row>
        </fieldset>
      </el-card>

      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>附件
          </legend>
          <el-row :gutter="16" :span="24">
            <project-document-storage-ui-table
                ref="childRef"
                :type="fileType"
                :relevantId="formData.id"
                :isPageSearch="false"
                :isDeleteMinio="false"
                :isHasAi="false"
                @on-add-data="onAddData"
                :file-serial-number-builder="fileSerialNumberBuilder"
                :preview-config="previewConfig"
                :isShowAddBtn="true"
                :isShowDelBtn="true"
                :isShowPreviewBtn="true"
                :isShowDownloadBtn="true"
                :isShowLinkBtn="false"
                :isShowDownloadBatchBtn="true"
            ></project-document-storage-ui-table>
          </el-row>
        </fieldset>
      </el-card>

      <el-card class="box-card" style="width: 100%;">
        <!--        <fieldset class="fieldset2">-->
        <legend>
          <span class="el-button--primary"></span>单据信息
        </legend>
        <el-row :gutter="16" :span="24">
          <el-col :span='8'>
            <el-form-item label="创建者姓名" prop="createName">
              <el-input v-model="formData.createName" type="text" placeholder="" :disabled="true" clearable>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span='8'>
            <el-form-item label="创建时间" prop="createTime">
              <el-input v-model="formData.createTime" disabled type="text" placeholder="" clearable/>
              <!--              <el-date-picker type="date" v-model="formData.createTime" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD"-->
<!--                              :disabled="true" clearable></el-date-picker>-->
            </el-form-item>
          </el-col>
          <el-col :span='8'>
            <el-form-item label="单据状态" prop="docStatus">
              <el-select v-model="formData.docStatus" disabled placeholder="" clearable>
                <el-option v-for="(item,index) in statusOption" :key="index" :label="item.label"
                           :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span='8'>
            <el-form-item label="最近修改人" prop="lastModifyBy">
              <el-input v-model="formData.updateName" type="text" placeholder="" :disabled="true" clearable>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span='8'>
            <el-form-item label="最近修改时间" prop="lastModifyTime">
              <el-input v-model="formData.updateTime" disabled type="text" placeholder="" clearable/>
              <!--              <el-date-picker type="date" v-model="formData.updateTime" format="YYYY-MM-DD HH:mm:ss"-->
<!--                              value-format="YYYY-MM-DD" :disabled="true" clearable></el-date-picker>-->
            </el-form-item>
          </el-col>
        </el-row>
        <!--        </fieldset>-->
      </el-card>
    </el-row>
  </el-form>
</template>

<script setup>
import ProjBizSafefeeInvestMApi from '@/project/api/safeFeeManage/safeFeeInvest/ProjBizSafefeeInvestM.js'
import ProjInfoMApi from '@/project/api/projectInfo/ProjInfoM.js'
import {getCurrentFormattedTime} from "@/common/utils/datetime";
import store from "@/store";
import {
  nextTick,
  onMounted,
  ref,
  reactive,
  toRef,
  defineProps,
  defineExpose,
  getCurrentInstance
} from 'vue';
import {
  useRoute,
  useRouter
} from 'vue-router'
import ProjBizDesignIntentionMApi from "@/project/api/design/ProjBizDesignIntentionM";

const {
  proxy
} = getCurrentInstance()
const route = useRoute();
const router = useRouter();
const props = defineProps({
  data: Object,
});
const showUpload = ref(true)
const fileType = toRef('safeInvest');
const formRef = ref()
const type = toRef(props.data?.type);
let formData = ref({
  code: "",
  yearMonth: "",
  expendAmount: "",
  projCode: "",
  projName: "",
  projId: "",
  fileId: "",
  docStatus: "0",
  createTime: getCurrentFormattedTime(),
  updateTime: "",
  createName: store.state.user && store.state.user.userInfo.userName ? store.state.user.userInfo.userName : null,
  updateName: "",
  projBizSafefeeInvestSDtoList: []
});
let statusOption = ref([{
  label: "草稿",
  value: "0"
}, {
  label: "已发布",
  value: "1"
}]);
let formRules = ref({
  code: [{ required: true, message: '请输入业务编号', trigger: 'change' }],
  yearMonth: [{ required: true, message: '请输入年月', trigger: 'change' }],
  expendAmount: [{ required: true, message: '请输入本月支出金额', trigger: 'change' }],
  projCode: [],
  projName: [],
  projId: [],
  fileId: [],
  docStatus: []
});
const childRef = ref(null)
let buttonFlag = ref();
let delRowData = ref({});
let projBizSafefeeInvestSOption = ref({
  tip: false,
  border: true,
  index: true,
  stripe: true,
  menu: true,
  header: false,
  menuType: "text",
  addBtn: true,
  addBtnText: "新增",
  editBtn: true,
  editBtnText: "编辑",
  delBtn: true,
  delBtnText: "删除",
  cellBtn: true,
  maxHeight: "200px",
  column: [{
    label: "类别",
    prop: "category",
    type: "input",
    cell: true
  }, {
    label: "主要内容",
    prop: "majorContent",
    type: "input",
    cell: true
  }, {
    label: "支出费用(万元)",
    prop: "expendAmount",
    type: "input",
    cell: true
  }, {
    label: "物品明细数量",
    prop: "itemNumber",
    type: "input",
    cell: true
  }, {
    label: "备注",
    prop: "remark",
    type: "input",
    cell: true
  }, {
    label: "版本号",
    prop: "version",
    hide: true,
    type: "input",
    cell: true
  }, {
    label: "创建人id",
    prop: "createBy",
    hide: true,
    type: "input",
    cell: true
  }, {
    label: "创建人姓名",
    prop: "createName",
    hide: true,
    type: "input",
    cell: true
  }, {
    label: "创建时间",
    prop: "createTime",
    hide: true,
    type: "input",
    cell: true
  }, {
    label: "创建人id",
    prop: "updateBy",
    hide: true,
    type: "input",
    cell: true
  }, {
    label: "更新人姓名",
    prop: "updateName",
    hide: true,
    type: "input",
    cell: true
  }, {
    label: "更新时间",
    prop: "updateTime",
    hide: true,
    type: "input",
    cell: true
  }, {
    label: "删除标识（0-未删 1-已删）",
    prop: "delFlag",
    hide: true,
    type: "input",
    cell: true
  }]
});

function subRowSave(form, done) {
  //编辑行
  done();
}

function fileSerialNumberBuilder() {
  return "safeFeeInvest" + Math.floor(Math.random() * 10000);
}

function subAddRow(name) {
  //新增一行
  if (formData.value[name]) {
    formData.value[name].push({
      $cellEdit: true,
    });
  } else {
    let arr = new Array();
    arr.push({
      $cellEdit: true
    });
    formData.value[name] = arr
  }
}

function subRowUpdate(form, index, done, loading) {
  //编辑行
  done();
}

function subDelRow(row, index, name) {
  //删除行
  if (row[0].id) {
    let data = JSON.parse(JSON.stringify(row[0]));
    if (delRowData.value[name]) {
      delRowData.value[name].push(Object.assign(data, {
        delFlag: 1,
      }));
    } else {
      delRowData.value[name] = [
        Object.assign(data, {
          delFlag: 1,
        }),
      ]
    }
  }
  formData.value[name].splice(index, 1);
}
function getProjectInfo() {
  ProjInfoMApi.view(sessionStorage.getItem('projectId')).then((resp) => {
    formData.value.projCode = resp.data.projCode
    formData.value.projName = resp.data.projName
    formData.value.projId = resp.data.id
    formData.value.projUnit = resp.data.projOrg === null ? "projOrg" : resp.data.projOrg

  })
}
function onAddData(obj) {
  obj.forEach(item => {
    formData.value.fileId += item.id + ','
  })
}
async function checkRepeatNumber() {
  const codeValid = formData.value.code;
  if (codeValid === null || codeValid === '') return
  if (type.value === 'add') {
    // 调用后端接口，查询编号是否存在
    try {
      const params = {
        filter: {
          code: codeValid
        },
        page: {
          pageSize: 1,
          pageNum: 1
        }
      };
      const res = await ProjBizSafefeeInvestMApi.pageList(params); // 确保等待异步请求完成
      if (res.data.dataList && res.data.dataList.length > 0) {
        // 如果编号已存在，提示用户
        proxy.$message.error("业务编号已存在，请重新输入！");
        formData.value.code = "";
      }
    } catch (error) {
      console.error("校验业务编号时出错：", error);
      proxy.$message.error("校验业务编号时出错，请稍后再试！");
    }
  } else {
    try {
      const viewResult = await ProjBizSafefeeInvestMApi.view(formData.value.id); // 确保等待异步请求完成
      const viewData = viewResult.data;
      const existingCode = viewData ? viewData.code : null;
      if (existingCode !== codeValid) {
        const params = {
          filter: {
            code: codeValid
          },
          page: {
            pageSize: 1,
            pageNum: 1
          }
        };
        const res = await ProjBizSafefeeInvestMApi.pageList(params); // 确保等待异步请求完成
        if (res.data.dataList && res.data.dataList.length > 0) {
          // 如果编号已存在，提示用户
          proxy.$message.error("业务编号已存在，请重新输入！");
          formData.value.code = "";
        }
      }
    } catch (error) {
      console.error("校验业务编号时出错：", error);
      proxy.$message.error("校验业务编号时出错，请稍后再试！");
    }
  }
}
function getListData() {
  if (childRef.value) {
    const list = childRef.value.getListData();
    formData.value.projBizDmStgMDtoList = [...list]
  }
}

const previewConfig = ref({
  // 是否外置预览,必传
  isExternalPreview: false,
  // 内置预览返回的替换字符,akey代表要替换的字符串，avalue代码替换的字符串
  substitute: {
    "akey": "avalue"
  },
  // 内置预览服务地址
  previewServerUrl: "http://10.191.64.191:8012/onlinePreview",
})

function getFormData() {
  //获取formData数据
  for (let item in delRowData.value) {
    formData.value[item] = formData.value[item].concat(delRowData.value[item]);
  }
  return formData.value;
}

formData = toRef(Object.keys(props.data?.formData || {}).length > 0 ? {
  ...props.data?.formData
} : toRef(formData.value));

function submitData(buttonType) {
  return new Promise((resolve) => {
    formRef.value.validate((flag) => {
      if (!flag) {
        return false;
      } else {
        buttonFlag.value = buttonType
        if (type.value === "add") {
          resolve(saveData());
        } else {
          resolve(editData());
        }
      }
    });
  });
}

function saveData() {
  //新增操作
  const formData1 = getFormData();
  if (buttonFlag.value === 'submit') {
    formData1.docStatus = '1'
  }
  // 获取文件list
  getListData()
  // 如果当前是提交按钮
  if (buttonFlag.value === 'submit') {
    if (formData.id === null || formData.id === undefined || formData.id === '') {
      return ProjBizSafefeeInvestMApi.add(formData1).then((resp) => {
        if (resp.data) {
          proxy.$message.success("发布成功");
          formData.id = resp.data.id;
          formData.projBizSafefeeInvestSDtoList = resp.data.projBizSafefeeInvestSDtoList
          return true;
        }
      });
    } else {
      return ProjBizSafefeeInvestMApi.update(formData1).then(() => {
        proxy.$message.success("发布成功");
        return true;
      });
    }
  } else if (buttonFlag.value === 'save'){
    if (formData.id === null || formData.id === undefined || formData.id === '') {
      return ProjBizSafefeeInvestMApi.add(formData1).then((resp) => {
        if (resp.data) {
          proxy.$message.success("保存成功");
          formData.id = resp.data.id;
          formData.projBizSafefeeInvestSDtoList = resp.data.projBizSafefeeInvestSDtoList
          return true;
        }
      });
    } else {
      formData1.id = formData.id
      return ProjBizSafefeeInvestMApi.update(formData1).then(() => {
        proxy.$message.success("保存成功");
        return true;
      });
    }
  }
}

function editData() {
  //编辑操作
  const formData1 = getFormData();
  if (buttonFlag.value === 'submit') {
    formData1.docStatus = '1'
    return ProjBizSafefeeInvestMApi.update(formData1).then((res) => {
      formData.value.projBizSafefeeInvestSDtoList = res.data.projBizSafefeeInvestSDtoList
      proxy.$message.success("发布成功");
      return true;
    });
  }
  if (buttonFlag.value === 'revoke') {
    formData1.docStatus = '0'
  }
  return ProjBizSafefeeInvestMApi.update(formData1).then((res) => {
    console.log(res.data, 990)
    formData.value.projBizSafefeeInvestSDtoList = res.data.projBizSafefeeInvestSDtoList
    proxy.$message.success("修改成功");
    return true;
  });
}
onMounted(() => {
  if (type.value === 'show'){
    showUpload.value = false
  }
//获取项目信息
  getProjectInfo();

})
defineExpose({
  getFormData,
  submitData,
});
</script>

<style lang="scss" scoped>

</style>
