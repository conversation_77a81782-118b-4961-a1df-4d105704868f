<template>
  <div class="contents">
    <input ref="importExcel" type="file" @change="handleFileChange" accept=".xls,.xlsx" style="display: none" />
    <div class="top" v-if="isShow">
      <el-card style="width: 100%;">
        <el-button v-if="!formData.procInstanceId" type="primary" @click="saveOrUpdate">保存</el-button>
        <el-button v-if="!formData.procInstanceId" type="primary" @click="submitFlow">发起</el-button>
        <el-button v-if="formData.procInstanceId && formData.procStatus == '7'" type="primary"
          @click="submitFlowTask">提交</el-button>
        <el-button v-if="formData.procInstanceId && formData.procStatus == '2'" type="primary"
          @click="recallTask">撤回</el-button>
      </el-card>
    </div>
    <div class="centent">
      <flow-page-container ref="flowPageContainerRef" @handlerAction="handlerAction" @initData="initData"
        @handlerPrint="handlerPrint" :closeBtn="isShowCloseBtn" :approvalOption="approvalOption"
        @approvalOptionCallback="getApprovalOption">
        <el-form :model="formData" :rules="formRules" ref="formRef" label-width="100px" label-position="right">
          <el-row :gutter="16" :span="24">
            <el-card class="box-card" style="width: 100%;">
              <fieldset class="fieldset2">
                <legend>
                  <span class="el-button--primary"></span>设计信息
                </legend>
                <el-row :gutter="16" :span="24">

                  <el-col :span='8'>

                    <el-form-item label="设计单位" prop="company">
                      <el-input v-model="formData.company" type="text" placeholder="请输入" clearable>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span='8'
                    v-permi="formPermi({ fieldModelId: 'projBizDesignWorkdrawOutPlanM', field: 'liabilityPerson', fieldName: '设计负责人' }, customFormPermi)">
                    <el-form-item label="设计负责人" prop="liabilityPerson">
                      <el-input v-model="formData.liabilityPerson" type="text" placeholder="请输入" clearable>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span='8'
                    v-permi="formPermi({ fieldModelId: 'projBizDesignWorkdrawOutPlanM', field: 'designVersion', fieldName: '设计版本' }, customFormPermi)">
                    <el-form-item label="设计版本" prop="designVersion">
                      <el-input v-model="formData.designVersion" type="text" placeholder="请输入" clearable>
                      </el-input>
                      <!-- <el-date-picker v-model="formData.designVersion" type="date" placeholder="请选择设计版本" /> -->
                    </el-form-item>
                  </el-col>
                </el-row>
              </fieldset>
            </el-card>
           <el-card class="box-card" style="width: 100%; min-height: 400px;">
              <fieldset class="fieldset2">
                <legend>
                  <span class="el-button--primary"></span>施工图信息
                </legend>
                <el-row :gutter="16" :span="24">
                  <sn-crud :data="formData.projBizDesignWorkdrawOutPlanSDtoList" v-model:page="queryForm.page"
                    @search-change="onChangeSearch" @search-reset="onResetSearch" v-model:search="queryForm.filter"
                    :option="projBizDesignWorkdrawOutPlanSOption" @addBtnHandle="subAddRow()" @row-import="uploadData"
                    style="width: 100%;">
                    <template #menuLeft="{ size }">
                      <el-button type="primary" :size="size" @click="downloadTemplate"><sn-icon icon="el-icon-download"
                          class="mr-6"></sn-icon>下载模板</el-button>
                      <el-button type="primary" :size="size" @click="uploadData"><sn-icon icon="el-icon-upload2"
                          class="mr-6"></sn-icon>导入</el-button>
                    </template>

                    <template #menu="{ row, index, size }">
                      <el-button type="primary" :size="size" icon="el-icon-delete" link
                        @click="editDetail(row)">编辑</el-button>
                      <el-button type="danger" :size="size" icon="el-icon-delete" link
                        @click="deletDetail(row, index)">删除</el-button>
                    </template>
                    <template #empty>
                      <div>无数据</div>
                    </template>
                  </sn-crud>
                </el-row>
              </fieldset>
            </el-card>
            <el-card class="box-card" style="width: 100%;">
              <fieldset class="fieldset2">
                <legend>
                  <span class="el-button--primary"></span>单据信息
                </legend>
                <el-row :gutter="16" :span="24">
                  <el-col :span='8'>
                    <el-form-item label="创建人" prop="createName">
                      <el-input v-model="formData.createName" :disabled="true" type="text" placeholder="" clearable>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span='8'>
                    <el-form-item label="创建时间" prop="createTime">
                      <el-input v-model="formData.createTime" :disabled="true" type="text" placeholder="" clearable>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span='8'>
                    <el-form-item label="单据状态" prop="procStatus">
                      <el-select v-model="formData.procStatus" :disabled="true">
                        <el-option v-for="(item, index) in global_biz_flow_status" :key="index" :label="item.label"
                                   :value="item.value"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span='8'>
                    <el-form-item label="最近修改人" prop="updateName">
                      <el-input v-model="formData.updateName" :disabled="true" type="text" placeholder="" clearable>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span='8'>
                    <el-form-item label="最近修改时间" prop="updateTime">
                      <el-input v-model="formData.updateTime" :disabled="true" type="text" placeholder="">
                      </el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
              </fieldset>
            </el-card>
          </el-row>
        </el-form>
      </flow-page-container>
    </div>
  </div>



</template>

<script>

export const routerConfig = [{
  menuType: "C",
  menuName: "施工图出图计划",
}];
</script>

<script setup>
import ProjBizDesignWorkdrawOutPlanMApi from '@/project/api/designManagement/workdrawManager/ProjBizDesignWorkdrawOutPlanM.js'
import FlowPageContainer from '@/common/components/exportFile/FlowPageContainer.vue'
import { useDicts } from '@/common/hooks/useDicts'
import { nextTick, onMounted, ref, reactive, toRef, defineProps, defineExpose, getCurrentInstance } from 'vue';
import { useRoute, useRouter } from 'vue-router'
import {getCurrentFormattedTime} from "@/common/utils/datetime";

import BpmTaskApi from "@/project/api/bpm/bpmTask.js";
import store from "@/store";

const { designManagement_major, global_biz_flow_status } = useDicts(["designManagement_major", "global_biz_flow_status"]);

const importExcel = ref();

const { proxy } = getCurrentInstance()

const route = useRoute();
const router = useRouter();


const flowPageContainerRef = ref();
const type = ref("edit");

const queryForm = ref({
  page: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  filter: {
    major: "",
    volumeNum: "",
    volumeName: ""
  }
})

let auth = new Map();

const props = defineProps({
  data: Object,
});
const formRef = ref()
const isShowCloseBtn = ref(false);
const FlowActionType = ref(proxy.FlowActionType);
let approvalOption = props.data?.approvalOption ? props.data?.approvalOption : route.query?.procInstId ? ref({
  isShowApprovalList: true,
  isShowFlowDiagram: true,
  procInstId: route.query?.procInstId
}) : ref({
  isShowApprovalList: false,
  isShowFlowDiagram: false,
  procInstId: ''
});
const state = reactive({
  flowData: {
    businessKey: "",
    procInstId: "",
    procDefKey: "",
    taskId: "",
    formConfig: [],
    variableList: [],
    customPropertyConfigList: []
  },
  formAuth: {},
  taskFlag: 1,
  firstAct: false,
  handlerClose: null,
  rootFields: {},
  fullFieldNameMap: {},
  formAuthList: {}
})
let formData = ref({
  id: "",
  procInstanceId: "",
  procStatus: "1",
  projectId: "",
  delFlag: "",
  version: "",
  createBy: "",
  createName: store.state.user && store.state.user.userInfo.userName ? store.state.user.userInfo.userName : null,
  createTime: getCurrentFormattedTime(),
  updateBy: "",
  updateName: "",
  updateTime: "",
  company: "",
  liabilityPerson: "",
  designVersion: "",
  projBizDesignWorkdrawOutPlanSDtoList: []
});

formData = toRef(Object.keys(props.data?.formData || {}).length > 0 ? {
  ...props.data?.formData
} : toRef(formData.value));

let formRules = ref({
  company: [{
    required: true,
    message: "请输入设计单位"
  }],
  liabilityPerson: [{
    required: true,
    message: "请输入设计负责人"
  }],
  designVersion: [{
    required: true,
    message: "请输入版本"
  }]
});
let projBizDesignWorkdrawOutPlanSOption = ref({
  tip: false,
  border: true,
  index: true,
  stripe: true,
  menu: true,
  menuWidth: '180px',
  menuHeaderAlign: "center",
  menuAlign: "center",
  header: true,
  height: 'auto',
  searchSpan: 6,
  searchIcon: true,
  searchMenuSpan: 6,
  searchIndex: 3,
  selection: false,
  menuType: "text",
  addBtn: true,
  addBtnText: "新增",
  editBtn: false,
  editBtnText: "编辑",
  delBtn: false,
  delBtnText: "删除",
  delBtns: false,
  cellBtn: false,
  maxHeight: "200px",
  column: [{
    label: "专业",
    prop: "major",
    width: "150px",
    type: "select",
    cell: true,
    search: true,
    dicData: designManagement_major,
    headerAlign: "center"
  }, {
    label: "卷册号",
    prop: "volumeNum",
    width: "150px",
    type: "input",
    cell: true,
    search: true,
    headerAlign: "center"
  }, {
    label: "卷册名称",
    prop: "volumeName",
    type: "input",
    cell: true,
    search: true,
    headerAlign: "center"
  }, {
    label: "计划出图时间",
    width: "180px",
    prop: "planOutDate",
    type: "date",
    cell: true,
    headerAlign: "center"
  }]
});
const isShow = ref(false);


//获取业务数据
function getDataView() {
  ProjBizDesignWorkdrawOutPlanMApi.getOne().then(resp => {
    if (!!resp.data) {
      resp.data["projBizDesignWorkdrawOutPlanSDtoList"] = [];
      formData.value = resp.data;

      if (!!formData.value.procInstanceId) {
        approvalOption.value = {
          isShowApprovalList: true,
          isShowFlowDiagram: true,
          procInstId: formData.value.procInstanceId

        }
        flowPageContainerRef.value.approvalOptionFn(approvalOption.value)
        type.value = "view";
      }


      getDetailpage();
    }
  });

}


//获取进度计划详细
function getDetailpage() {
  const searchParams=handleQueryForm();
  ProjBizDesignWorkdrawOutPlanMApi.detailPageList(searchParams).then(resp => {
    const detailData = resp.data.dataList;
    detailData.forEach(item => {
      item["$cellEdit"] = false;
      item["projectId"] = item.projectId ? item.projectId : sessionStorage.getItem('projectId')
    })
    queryForm.value.page.total = resp.data.totalCount;
    formData.value.projBizDesignWorkdrawOutPlanSDtoList = detailData;
  });
}


function onResetSearch() {
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getDetailpage();
}

//搜索按钮操作
function onChangeSearch(params, done) {
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getDetailpage();
  done && done();
}



// 处理参数
function handleQueryForm() {
  const { pageSize, pageNum } = queryForm.value.page;
  const filter = {}
  for (let key in queryForm.value.filter) {
    if (queryForm.value.filter[key]?.length) {
      // sn-curd搜索select会生成$prop的变量
      if (key.startsWith("$")) continue;
      filter[key] = queryForm.value.filter[key];
    }
  }
  const searchParams = {
    page: {
      pageSize,
      pageNum,
    },
    filter,
  }
  return searchParams
}



//新增一行图纸计划
function subAddRow() {
  if (formData.value['projBizDesignWorkdrawOutPlanSDtoList']) {
    formData.value['projBizDesignWorkdrawOutPlanSDtoList'].push({
      $cellEdit: true,
      projectId: sessionStorage.getItem('projectId')
    });
  }
}

//编辑进度计划
function editDetail(row) {
  row["$cellEdit"] = true
}


//删除记录
function deletDetail(row, index) {
  if (!!row.id) {
    proxy.$confirm('确定删除当前记录?', '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        ProjBizDesignWorkdrawOutPlanMApi.deleteDetail([row.id]).then(resp => {
          proxy.$modal.msgSuccess("操作成功");
          formData.value['projBizDesignWorkdrawOutPlanSDtoList'].splice(index, 1);
          formData.value.page.total = formData.value.page.total > 0 ? formData.value.page.total - 1 : formData.value.page.total;
        });
      })
      .catch(() => { })
  } else {
    formData.value['projBizDesignWorkdrawOutPlanSDtoList'].splice(index, 1);
  }

}


function getFormData() {
  const projectId = sessionStorage.getItem('projectId');
  return formData.value = { ...formData.value, projectId: formData.value.projectId ? formData.value.projectId : projectId };
}


//保存单据
function saveOrUpdate() {
  const formData = getFormData();
  ProjBizDesignWorkdrawOutPlanMApi.add(formData).then(() => {
    proxy.$message.success("操作成功");
    getDataView();
  });
}


//发起流程
function submitFlow() {
  const formData = getFormData();
  ProjBizDesignWorkdrawOutPlanMApi.startFlow({
    busiData: formData,
    startProcDto: {}
  }).then(() => {
    proxy.$message.success("操作成功");
    getDataView();
  });
}

function submitFlowTask() {
  proxy.$confirm('确定提交当前单据?', '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
      const formDatas = getFormData();

      BpmTaskApi.listRuTaskByProcInstId({
        procInstId: formDatas.procInstanceId
      }).then((resp) => {
        console.log(resp);
        if (Array.isArray(resp.data) && resp.data.length > 0) {
          ProjBizDesignWorkdrawOutPlanMApi.submitTask({
            busiDto: formDatas,
            taskActionDto: {
              taskId: resp.data[0].taskId,
              procInstId: formDatas.procInstanceId,
              actionType: "agree",
            }
          }).then(() => {
            proxy.$message.success("操作成功");
            getDataView();
          });
        } else {
          proxy.$message.success("提交失败，未获取到当前任务id");
        }
      })
    })
    .catch(() => { })

  //新增操作

}


function recallTask() {
  proxy.$confirm('确定撤回当前单据?', '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
      ProjBizDesignWorkdrawOutPlanMApi.recallTask({ id: formData.value.id }).then(resp => {
        proxy.$modal.msgSuccess("操作成功");
        getDataView();
      });
    })
    .catch(() => { })

}


//上传出图计划触发
function uploadData() {
  importExcel.value.click()

}

//导入施工图出图计划
function handleFileChange(event) {
  const file = event.target.files[0];
  if (file) {
    const details = formData.value['projBizDesignWorkdrawOutPlanSDtoList'];
    const isEdit = details.find(item => item["$cellEdit"] == true);
    const formDatas = new FormData()
    formDatas.append('file', file)
    formDatas.append('id', isEdit ? "" : formData.value.id)
    importExcel.value.value = null;
    ProjBizDesignWorkdrawOutPlanMApi.importDetail(formDatas).then((resp) => {
      if (Array.isArray(resp.data) && resp.data.length > 0) {
        resp.data.forEach(item => {
          item["$cellEdit"] = true;
          item["projectId"] = sessionStorage.getItem('projectId')
        })
        formData.value['projBizDesignWorkdrawOutPlanSDtoList'] = [...formData.value['projBizDesignWorkdrawOutPlanSDtoList'], ...resp.data]
        proxy.$message.success('导入成功');
      }
      if (!isEdit && !!formData.value.id) {
        getDetailpage();
        proxy.$message.success('导入成功');
      }
    })
  }

}



//下载施工图出图计划模板
function downloadTemplate() {
  proxy.download("/project/workdrawManager/exportDetailTemplate", {}, "施工图计划模板.xlsx");
}






function processAuthFieldForm(fieldList) {
  /**
   * 工作流表单权限功能：在扫描完成之前，业务可对表单字段进行修改。
   */
  //TODO 这里可以对权限表单的字段进行任意的修改,如果不满足业务需求，可以在这里手动添加你想要的字段
  return fieldList
};

function getApprovalOption(fun) {
  /**
   * 工作流表单权限功能(重要)：应对弹窗无法及时相应的问题，特别增加该函数修复该bug
   */
  nextTick(() => {
    fun(approvalOption.value)
  })
};

function customFormPermi(obj) {
  /**
   * 工作流表单权限功能：自定义表单权限处理函数，将对应的对象的属性还原成对应的权限值
   */
  let {
    field,
    fieldModelId
  } = obj
  let fullFieldName = `${fieldModelId}_${field}`
  //TODO 这里可以自定义处理，默认返回0即可
  return auth.get(fullFieldName) || 0
};

function handlerPrint(taskComment) {
  /**
   * 工作流打印模板功能
   */
  ProjBizDesignWorkdrawOutPlanMApi.printTemplate({
    id: formData.value.id,
    templateId: taskComment.templateId,
    formData: formData.value
  }).then(
    (res) => {
      router.push({
        name: "PrintDoc",
        query: {
          fileId: res.data,
        },
      });
      taskComment.close && taskComment.close();
    });
};

function getSubmitTask(taskActionDto) {
  /**
   * 工作流提交任务功能
   */
  return ProjBizDesignWorkdrawOutPlanMApi.submitTask({
    busiDto: formData.value,
    taskActionDto: taskActionDto
  });
};

function getStartFlow(formData, startProcDto) {
  /**
   * 工作流启动流程功能
   */
  return ProjBizDesignWorkdrawOutPlanMApi.startFlow({
    busiData: formData,
    startProcDto: startProcDto
  });
};

function handlerAction(operation, taskComment, handlerClose) {
  /**
   * 工作流操作功能
   */
  // 启动并保存草稿后再次提交
  if (
    (operation.type == FlowActionType.value.SAVESTART || operation.type == FlowActionType.value.SAVE || operation.type == FlowActionType.value.START) && !formData.value.taskId) {
    let startProcDto = {
      procDefKey: formData.value.procDefKey,
      businessKey: null,
      clientId: null,
      ...taskComment?.dialogRefs?.getFormData(),
    };
    let httpCall = null;
    if (operation.type == FlowActionType.value.SAVESTART) {
      httpCall = getStartFlow(formData, startProcDto);
    } else if (operation.type == FlowActionType.value.SAVE) {
      httpCall = getSaveFormData(formData);
    }
    httpCall.then(() => {
      proxy.$modal.msgSuccess("提交成功");
      taskComment.close && taskComment.close();
      handlerClose();
    });
  } else {
    operation.type == FlowActionType.value.START || operation.type == FlowActionType.value.SAVESTART ? (operation.type = FlowActionType.value.AGREE) : operation.type;
    let taskActionDto = {
      taskId: state.flowData.taskId,
      procInstId: state.flowData.procInstId,
      actionType: operation.type,
      ...taskComment?.dialogRefs?.getFormData(),
    };
    getSubmitTask(taskActionDto).then(() => {
      proxy.$modal.msgSuccess("任务办理成功");
      taskComment.close && taskComment.close();
      handlerClose();
    }).catch(() => {
      taskComment.close && taskComment.close();
    });
  }
};

function initBusiForm() {
  /**
   * 工作流初始化表单功能
   */
  let that = proxy
  let routerQueryParams = route.query;
  auth = new Map(Object.entries(state.formAuth));
  if (routerQueryParams) {
    if (routerQueryParams.busiData) {
      formData.value = routerQueryParams.busiData;
    } else {
      if (routerQueryParams.businessKey) {
        let businessKey = route.query.businessKey;
        ProjBizDesignWorkdrawOutPlanMApi.view(businessKey).then(resp => {
          if (!!resp.data) {
            formData.value = resp.data;
            getDetailpage();
          }


        });
      } else {
        that.$message.error("初始化失败,因为工作流未将流程信息传入！")
      }
    }
  }
};

function getSaveFormData(formData) {
  if (formData.id) {
    return ProjBizDesignWorkdrawOutPlanMApi.update(formData);
  } else {
    return ProjBizDesignWorkdrawOutPlanMApi.add(formData);
  }
};

function getFieldForm() {
  let rootFields = {
    ...state.rootFields
  }
  let fieldList = []
  let keys = []
  for (let key in rootFields) {
    keys.push(key)
    fieldList.push(rootFields[key])
  }
  const result = processAuthFieldForm ? processAuthFieldForm(fieldList) : fieldList;
  const message = {
    type: "getTemplateRoot",
    pathname: window.location.pathname,
    actKey: getQueryParams("actKey"),
    content: result
  };
  window.parent.postMessage(JSON.parse(JSON.stringify(message)), "*");
};

function getQueryParams(key) {
  let url = window.location.href;
  // 使用正则表达式解析URL中的查询字符串
  var queryString = url.split('?')[1];
  if (!queryString) {
    return {};
  }
  var params = {};
  // 分割查询字符串成单个参数
  var vars = queryString.split("&");
  for (var i = 0; i < vars.length; i++) {
    var pair = vars[i].split("=");
    params[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);
  }
  return params[key];
};

function initData(urlParams, taskInfo, handlerClose) {
  // 如果用户从流程设计点击启动, 如果是在线表单, 则阻止执行initData, 防止在线表单报错
  if (urlParams.menuId && urlParams.openInStart) {
    return
  }
  state.flowData.procDefKey = taskInfo.procDefKey;
  state.flowData.procInstId = urlParams.procInstId;
  state.flowData.businessKey = urlParams.businessKey;
  state.flowData.taskId = urlParams.taskId;
  state.flowData.fiedPermission = taskInfo.fiedPermission;
  state.flowData.variableList = taskInfo.variableList;
  state.taskFlag = urlParams.taskFlag;
  state.firstAct = taskInfo.firstAct;
  state.handlerClose = handlerClose;
  state.flowData.customPropertyConfigList = taskInfo.customPropertyConfigList
  let fieldPerList = taskInfo.formConfig?.formFieldConfig?.fieldPerList || []
  handleFormAuth(fieldPerList);
  initBusiForm();
};

function handleFormAuth(data) {
  let formAuth = {};
  for (let item of data) {
    let permi = 1;
    if (item.readonly) {
      permi = 2;
    }
    if (item.hidden) {
      permi = 3;
    }
    if (!isCamelCase(item.fieldModelId)) {
      item.fieldModelId = toCamelCase(item.fieldModelId)
    }
    formAuth = {
      ...formAuth,
      [`${item.fieldModelId}_${item.field}`]: permi,
    };
  }
  state.formAuth = formAuth;
  state.formAuthList = JSON.parse(JSON.stringify(formAuth))
};
/**
 * 处理表单权限相关操作, 开发者在template的v-permi设置中必须使用此函数
 * @param {Object} obj - 包含字段名称的对象
 * @param {Function} [callback] - 可选的回调函数，接收 obj 作为参数
 * 如果传入对象的 fieldName 不在 rootFields 中，则将其添加到 rootFields 数组，然后执行回调函数（如果存在）
 */
function formPermi(obj, callback) {
  if (!state.rootFields[obj.fieldModelId + '__' + obj.field]) {
    state.rootFields[obj.fieldModelId + '__' + obj.field] = obj
  }
  return callback && callback(obj)
};

function toCamelCase(s) {
  return s.toLowerCase().replace(/_(.)/g, function (match, group1) {
    return group1.toUpperCase();
  });
};

function isCamelCase(str) {
  return /^[a-z][a-zA-Z0-9]*$/.test(str)
};
defineExpose({
  getFormData,
  getSaveFormData,
  getStartFlow,
});
onMounted(() => {
  nextTick(() => {
    getFieldForm();
    if (!route.query?.procInstId) {
      isShow.value = true;
      //获取信息
      getDataView();
    }
  })
})
</script>
<style>
.lf-graph {
  height: 100vh;
}
</style>
<style lang="scss" scoped>
.contents {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: calc(100vh - 88px);
  overflow: hidden;
}

.top {
  height: 80px;
  width: 100%;
  flex: 0 1 auto;
  padding: 0 15px 0px 0px;
  box-sizing: border-box;
}

.centent {
  height: 100px;
  width: 100%;
  flex: auto;
  overflow-y: auto;
  overflow-x: hidden;
}
</style>
