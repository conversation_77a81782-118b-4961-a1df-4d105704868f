/*
 * @Author: 王云飞
 * @Date: 2025-06-18 11:28:50
 * @LastEditTime: 2025-06-18 13:40:43
 * @LastEditors: 王云飞
 * @Description:
 *
 */
import { useStore } from 'vuex'
import { changeDrakTheme } from '@/common/utils/chageTheme'
import { ref } from 'vue'
import { config, changeElTheme3 } from 'sn-base-utils'
const store = useStore()
export function useDarkTheme() {
  const setDarkMode = () => {
    store.commit('settings/CHANGE_HORIZANTAL', 'side')
    const theme = localStorage.getItem('theme') || config().theme
    changeElTheme3(theme)
    store.dispatch('settings/changeTagsStyle', 'line')
    changeDrakTheme(true)
  }
  return {
    setDarkMode
  }
}
