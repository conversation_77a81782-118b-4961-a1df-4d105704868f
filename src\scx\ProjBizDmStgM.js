// import { request, replaceUrl } from "sn-base-utils";
import {default as request} from './TestRequest'
import {replaceUrl} from "sn-base-utils";
export default class ProjBizDmStgMApi {
    static config = {
        add: {
            url: '/document/storage/add',
            method: 'POST'
        },
        addBatch: {
            url: '/project/document/storage/addBatch',
            method: 'POST'
        },
        view: {
            url: '/project/document/storage/get/{id}',
            method: 'GET'
        },
        pageList: {
            url: '/project/document/storage/page',
            method: "POST"
        },
        pageListHasSize: {
            url: '/project/document/storage/pageHasSize',
            method: "POST"
        },
        remove: {
            url: '/project/document/storage/delete',
            method: 'DELETE'
        },
        deleteAndDeleteMinio: {
            url: '/project/document/storage/deleteAndDeleteMinio',
            method: 'DELETE'
        },
        list: {
            url: '/project/document/storage/list',
            method: "POST"
        },
        aiReview: {
            url: '/project/ai/review/{fileId}',
            method: "POST"
        }
    };

    /**
     * 新增文档管理-文档目录
     * @param data
     * @returns {*}
     */
    static add(data) {
        return request({
            url: this.config.add.url,
            method: this.config.add.method,
            data: data
        });
    }

    /**
     * 新增文档管理-文档目录
     * @param data
     * @returns {*}
     */
    static addBatch(data) {
        return request({
            url: this.config.addBatch.url,
            method: this.config.addBatch.method,
            data: data
        });
    }

    /**
     * 查询文档管理-文档目录详细
     * @param id
     * @returns {*}
     */
    static view(id) {
        return request({
            url: replaceUrl(this.config.view.url, { id }),
            method: this.config.view.method
        });
    }

    /**
     * 分页查询文档管理-文档目录列表
     * @param data
     * @returns {*}
     */
    static pageList(data) {
        return request({
            url: this.config.pageList.url,
            method: this.config.pageList.method,
            data: data
        });
    }

    /**
     * 分页查询文档管理-文档目录列表
     * @param data
     * @returns {*}
     */
    static pageListHasSize(data) {
        return request({
            url: this.config.pageListHasSize.url,
            method: this.config.pageListHasSize.method,
            data: data
        });
    }

    /**
     * 删除文档管理-文档目录
     * @param data
     * @returns {*}
     */
    static remove(data) {
        return request({
            url: this.config.remove.url,
            method: this.config.remove.method,
            data: data
        });
    }

    /**
     * 删除文档管理-文档目录(删除minio文件)
     * @param data
     * @returns {*}
     */
    static deleteAndDeleteMinio(data) {
        return request({
            url: this.config.deleteAndDeleteMinio.url,
            method: this.config.deleteAndDeleteMinio.method,
            data: data
        });
    }

    /**
     * 全部文档管理-文档目录列表
     * @returns {*}
     */
    static list(data) {
        return request({
            url: this.config.list.url,
            method: this.config.list.method,
            data: data
        });
    }

    /**
     * Ai审查
     * @returns {*}
     */
    static aiReview(fileId) {
        return request({
            url: replaceUrl(this.config.aiReview.url, { fileId }),
            method: this.config.aiReview.method,
            data:{}
        });
    }


}
