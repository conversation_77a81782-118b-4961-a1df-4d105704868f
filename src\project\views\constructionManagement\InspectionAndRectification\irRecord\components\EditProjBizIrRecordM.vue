<template>
  <el-form :model="formData" :rules="formRules" ref="formRef" label-width="100px" label-position="right" class="record-container" :disabled="type == 'view'">
    <el-row :gutter="16" :span="24">
      <el-tabs v-model="tabsType" class="" type="" tab-position="top">
        <el-tab-pane label="检查问题" name="tab1">
          <el-row :gutter="16" :span="24">
            <el-card class="box-card" style="width: 100%;">
              <fieldset class="fieldset2">
                <legend>
                  <span class="el-button--primary"></span>基本信息
                </legend>
                <el-row :gutter="16" :span="24">
                  <el-col :span='8'>
                    <el-form-item label="业务编号" prop="businessCode">
                      <el-input v-model="formData.businessCode" :disabled="formData.id != ''" type="text" clearable>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span='8'>
                    <el-form-item label="检查单位" prop="inspectionUnitName">
                      <el-input v-model="formData.inspectionUnitName" :disabled="true" type="text" clearable>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span='8'>
                    <el-form-item label="被检查单位" prop="inspectedUnitName">
                      <el-input v-model="formData.inspectedUnitName"  type="text" :disabled="isDisabled" placeholder="请选择"  @click.native="showSelectOrgDialog" clearable></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span='8'>
                    <el-form-item label="检查负责人" prop="inspectionUserName">
                      <el-input v-model="formData.inspectionUserName"  type="text" :disabled="isDisabled" placeholder="请选择"  @click.native="showSelectUserDialog" clearable></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span='8'>
                    <el-form-item label="检查时间" prop="inspectionDate">
                      <el-date-picker style="width: 100%" type="date" v-model="formData.inspectionDate" :disabled="isDisabled" format="YYYY-MM-DD" value-format="YYYY-MM-DD" clearable></el-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span='8'>
                    <el-form-item label="检查名称" prop="name">
                      <el-input v-model="formData.name" :disabled="isDisabled" type="text" placeholder="请输入" clearable>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span='8'>
                    <el-form-item label="整改期限" prop="reformDeadline">
                      <el-date-picker style="width: 100%"  type="date" v-model="formData.reformDeadline" :disabled="isDisabled" format="YYYY-MM-DD" value-format="YYYY-MM-DD" clearable></el-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span='8'>
                    <el-form-item label="检查类型" prop="inspectionType">
                      <el-select v-model="formData.inspectionType" clearable placeholder="请选择" :disabled="isDisabled">
                        <el-option v-for="(item, index) in inspection_reform_type" :key="index" :label="item.label" :value="item.value"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span='8'>
                    <el-form-item label="整改状态" prop="reformStatus">
                      <el-input :disabled="true" type="text" v-model="formData.reformStatusStr"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span='8'>
                    <el-form-item label="问题数量" prop="questionsNum">
                      <el-input v-model="formData.projBizIrQuestionSDtoList.length" :disabled="true" type="text" placeholder="0" clearable>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span='8'>
                    <el-form-item label="是否超期" prop="isOverdueStr">
                      <el-input v-model="formData.isOverdueStr" :disabled="true" type="text" clearable>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span='8'>
                    <el-form-item label="整体进度" prop="">
                      <el-progress style="width: 100%" :stroke-width="25" :text-inside="true" :percentage="formData.progress" disabled="true"></el-progress>
                    </el-form-item>
                  </el-col>
                </el-row>
              </fieldset>
            </el-card>
            <el-card class="box-card" style="width: 100%;">
              <fieldset class="fieldset2">
                <legend>
                  <span class="el-button--primary"></span>整改文件
                </legend>
                <el-row :gutter="16" :span="24">
                  <el-col :span='24'>
                    <el-form-item style="margin-top: 20px;" label="整改文件" prop="reformFileInfo" v-if="!isDisabled">
                      <div class="reform-file-container">
                        <!--                      accept=".doc,.docx,.pdf"-->
                        <el-upload
                            v-model:file-list="uploadReformFileList"
                            class="upload-demo"
                            action="/add/test"
                            :show-file-list="true"
                            :auto-upload="false"
                            @change="onReformFileUploadChange"
                            :on-remove="onReformFileRemove"
                            :on-preview="handleReformFilePreview"
                            :limit="1"
                        >
                          <el-button type="primary" plain><el-icon><UploadFilled /></el-icon>上传</el-button>
                        </el-upload>
                        <div class="reform-file" v-if="formData.reformFileInfo && formData.reformFileInfo.fileName" @click="previewFile(formData.reformFileInfo)">{{formData.reformFileInfo.fileName}}</div>

                      </div>
                    </el-form-item>
                    <el-form-item label="整改文件" prop="reformFileInfo" v-if="isDisabled">
                      <span class="reform-file" v-if="formData.reformFileInfo && formData.reformFileInfo.fileName" @click="previewFile(formData.reformFileInfo)">{{formData.reformFileInfo.fileName}}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
              </fieldset>
            </el-card>
            <el-card class="box-card" style="width: 100%;">
              <fieldset class="fieldset2">
                <legend>
                  <span class="el-button--primary"></span>问题清单
                </legend>
                <el-row :gutter="16" :span="24">
                  <sn-crud :data="formData.projBizIrQuestionSDtoList" :option="projBizIrQuestionSOption" @row-save="subRowSave" @row-update="subRowUpdate" @row-del="(row, index) => {subDelRow(row, index, 'projBizIrQuestionSDtoList');}">
                    <template #empty>
                      <div>无数据</div>
                    </template>
                    <template #menu="{ row, index, size }">
                      <el-button type="primary" :size="size" icon="el-icon-edit" v-if="!isDisabled && !row.$cellEdit" link @click="editQuestionRow(row)">编辑</el-button>
                      <el-button type="primary" :size="size" icon="el-icon-delete" v-if="!isDisabled  && !row.$cellEdit" link @click="subDelRow(row, index, 'projBizIrQuestionSDtoList')">删除</el-button>
                    </template>
                    <template #source="{row}">
                      {{formData.name}}
                    </template>
                    <template #reformStatus="{row}">
                      <span v-if="row.reformStatus==='in_progress'" class="color-danger">整改中</span>
                      <span v-else-if="row.reformStatus==='not_rectified'">未整改</span>
                      <span v-else class="color-success">已完成</span>
                    </template>
                  </sn-crud>
                  <el-button v-if="!isDisabled" @click="subAddRow('projBizIrQuestionSDtoList')" type="primary" plain style="display: block; width: 100%; margin-top: 10px">新增一行数据</el-button>
                </el-row>
              </fieldset>
            </el-card>
            <el-card class="box-card" style="width: 100%;">
              <fieldset class="fieldset2">
                <legend>
                  <span class="el-button--primary"></span>附件信息
                </legend>
                <el-row :gutter="16" :span="24">
                  <project-document-storage-ui-table
                      ref="childRef"
                      type="inspection_and_rectification"
                      :relevantId="formData.id"
                      :isPageSearch="false"
                      :isDeleteMinio = "false"
                      :isHasAi = "false"
                      :file-serial-number-builder="fileSerialNumberBuilder"
                      :preview-config="previewConfig"
                      :isShowAddBtn="!isDisabled"
                      :isShowDelBtn="!isDisabled"
                      :isShowPreviewBtn="true"
                      :isShowDownloadBtn="true"
                      :isShowLinkBtn="false"
                      :isShowDownloadBatchBtn="true"
                      @on-close-pop="onClosePop"
                  ></project-document-storage-ui-table>
                </el-row>
              </fieldset>
            </el-card>
            <el-card class="box-card" style="width: 100%;">
              <fieldset class="fieldset2">
                <legend>
                  <span class="el-button--primary"></span>单据信息
                </legend>
                <el-row :gutter="16" :span="24">
                  <el-col :span='8'>
                    <el-form-item label="创建人" prop="">
                      <el-input v-model="formData.createName" :disabled="true" type="text" clearable>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span='8'>
                    <el-form-item label="创建时间" prop="">
                      <el-input v-model="formData.createTime" :disabled="true" type="text" clearable>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span='8'>
                    <el-form-item label="单据状态" prop="">
                      <el-input v-model="formData.statusStr" :disabled="true" type="text" clearable>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span='8'>
                    <el-form-item label="最近修改人" prop="">
                      <el-input v-model="formData.updateName" :disabled="true" type="text" clearable>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span='8'>
                    <el-form-item label="最近修改时间" prop="">
                      <el-input v-model="formData.updateTime" :disabled="true" type="text" clearable>
                      </el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
              </fieldset>
            </el-card>
            <div class="check-button-group">
              <el-button type="primary" v-if="formData.status !== 'published'" @click="submitData">保存</el-button>
              <el-button type="primary" v-if="formData.status !== 'published'" @click="publish">发布</el-button>
            </div>
          </el-row>
        </el-tab-pane>
        <el-tab-pane label="问题整改" name="tab2">
          <el-row :gutter="16" :span="24">
            <el-card class="box-card" style="width: 100%;">
              <fieldset class="fieldset2">
                <legend>
                  <span class="el-button--primary"></span>基本信息
                </legend>
                <el-row :gutter="16" :span="24">
                  <el-col :span='8'>
                    <el-form-item label="业务编号" prop="businessCode">
                      <el-input v-model="formData.businessCode" :disabled="true" type="text" clearable>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span='8'>
                    <el-form-item label="检查单位" prop="inspectionUnitName">
                      <el-input v-model="formData.inspectionUnitName" :disabled="true" type="text" clearable>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span='8'>
                    <el-form-item label="被检查单位" prop="inspectedUnitName">
                      <el-input v-model="formData.inspectedUnitName"  type="text" :disabled="true" placeholder="请选择"  @click.native="showSelectOrgDialog" clearable></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span='8'>
                    <el-form-item label="检查负责人" prop="inspectionUserName">
                      <el-input v-model="formData.inspectionUserName"  type="text" :disabled="true" placeholder="请选择"  @click.native="showSelectUserDialog" clearable></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span='8'>
                    <el-form-item label="检查时间" prop="inspectionDate">
                      <el-date-picker style="width: 100%" type="date" v-model="formData.inspectionDate" :disabled="true" format="YYYY-MM-DD" value-format="YYYY-MM-DD" clearable></el-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span='8'>
                    <el-form-item label="检查名称" prop="name">
                      <el-input v-model="formData.name" :disabled="true" type="text" placeholder="请输入" clearable>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span='8'>
                    <el-form-item label="整改期限" prop="reformDeadline">
                      <el-date-picker style="width: 100%"  type="date" v-model="formData.reformDeadline" :disabled="true" format="YYYY-MM-DD" value-format="YYYY-MM-DD" clearable></el-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span='8'>
                    <el-form-item label="检查类型" prop="inspectionType">
                      <el-select v-model="formData.inspectionType" clearable placeholder="请选择" :disabled="true">
                        <el-option v-for="(item, index) in inspection_reform_type" :key="index" :label="item.label" :value="item.value"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span='8'>
                    <el-form-item label="整改状态" prop="reformStatus">
                      <el-input :disabled="true" type="text" v-model="formData.reformStatusStr"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span='8'>
                    <el-form-item label="问题数量" prop="questionsNum">
                      <el-input v-model="formData.projBizIrQuestionSDtoList.length" :disabled="true" type="text" placeholder="0" clearable>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span='8'>
                    <el-form-item label="是否超期" prop="isOverdueStr">
                      <el-input v-model="formData.isOverdueStr" :disabled="true" type="text" clearable>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span='8'>
                    <el-form-item label="整体进度" prop="">
                      <el-progress style="width: 100%" :stroke-width="25" :text-inside="true" :percentage="formData.progress" disabled="true"></el-progress>
                    </el-form-item>
                  </el-col>
                </el-row>
              </fieldset>
            </el-card>
            <el-card class="box-card" style="width: 100%;">
              <fieldset class="fieldset2">
                <legend>
                  <span class="el-button--primary"></span>问题整改
                </legend>
                <el-row :gutter="16" :span="24">
                  <sn-crud :data="formData.projBizIrQuestionSDtoListFilter" :option="projBizIrQuestionSNoAddOption" @select-all="questionRowClick" @select="questionRowClick">
                    <template #empty>
                      <div>无数据</div>
                    </template>
                    <template #reformContent="{row}">
                      <el-input :disabled="row.reformStatus==='completed' || row.submitStatus==='yes' || formData.status === 'draft'"  v-model="row.reformContent" @input="reformContentInput($event,row)"></el-input>
                    </template>
                    <template #reformImage="{row,index}">
                        <div class="remove-reform-image-container" v-if="row.reformImagePreviewUrl">
                          <el-image
                              :preview-teleported="true"
                              :key="index"
                              :src="row.reformImagePreviewUrl"
                              :preview-src-list="row.reformImagePreviewUrlList">
                          </el-image>
                          <el-icon v-if="row.reformStatus!=='completed' && row.submitStatus!=='yes'" color="red" size="20" @click="removeReformImage(row)" class="remove-reform-image-icon"><RemoveFilled /></el-icon>
                        </div>
                        <el-upload
                            :disabled="formData.status === 'draft'"
                            class="remove-reform-image-container"
                            v-if="!row.reformImagePreviewUrl && row.reformStatus!=='completed' && row.submitStatus!=='yes'"
                            drag
                            accept=".jpeg,.jpg,.png"
                            action="/add/test"
                            :show-file-list="true"
                            :auto-upload="false"
                            @change="onReformImageUploadChange($event,row)"
                            :limit="1"
                        >
                          <el-icon class="el-icon--upload" size="30"><upload-filled /></el-icon>
                          <div class="el-upload__text">
                            将文件拖到此处，或点击上传
                          </div>
                        </el-upload>
                    </template>
                    <template #reformStatus="{row}">
                      <span v-if="row && row.reformStatus==='completed'" class="color-success">已完成</span>
                      <span v-else-if="row && row.reformStatus==='not_rectified'">未整改</span>
                      <span v-else class="color-danger">整改中</span>
                    </template>
                    <template #reformStatusSearch="{row}">
                      <el-select v-model="inspection_reform_status_select_value" clearable placeholder="请选择" @change="questionFilterChange">
                        <el-option v-for="(item, index) in inspection_question_reform_status" :key="index" :label="item.label" :value="item.value"></el-option>
                      </el-select>
                    </template>
                  </sn-crud>
                </el-row>
              </fieldset>
            </el-card>
            <div class="check-button-group">
              <el-button @click="recordDialog">审批记录</el-button>
              <el-button type="primary" @click="submitData" v-if="formData.reformStatus !== 'completed'">保存</el-button>
              <el-button type="primary" @click="submitApproval" v-if="formData.status !== 'completed'">提交审批</el-button>
            </div>
          </el-row>
        </el-tab-pane>
      </el-tabs>
    </el-row>
  </el-form>
  <el-dialog
      v-model="dialogVisible"
      title="文件上传"
      width="500"
  >
    <sn-upload :drag="true" :key="componentKey" :limit="1" listType="text" :autoUpload="true" :fileMaxSize="209715200" @input="(fileList) => uploadData(fileList, '')" />
  </el-dialog>
  <div ref="myFlowRef"></div>
  <div ref="mySubmitFlowRef"></div>
</template>

<script setup>
import ProjBizIrRecordMApi from '@/project/api/constructionManagement/InspectionAndRectification/ProjBizIrRecordM.js'
import {getCurrentFormattedTime, isDateInFuture} from '@/common/utils/datetime'
import {formatBytes} from '@/common/utils/Bytes'
import {defineExpose, defineProps, getCurrentInstance, ref, toRef, onBeforeUnmount,inject} from 'vue';
import {useRoute, useRouter} from 'vue-router'
import {useDicts} from "@/common/hooks/useDicts";
import store from "@/store";
import {getAuthHeader} from "@/common/utils/headers";
import {requestImgUrl} from "@/common/api/MinioFile"
import FileApi from "sn-base-layout-vue3/packLayout/api/File";
import {generateHash} from "@/common/utils/md5";
import IrFlow from "../../irFlow/index.vue"
import EditProjBizIrPrM from "@/project/views/constructionManagement/InspectionAndRectification/irFlow/components/EditProjBizIrPrM.vue";
import {calculateIntegerPercentage} from "@/common/utils/math";
import { kkFileViewUrl } from "@/config";
import {previewOpenBlank} from '@/common/utils/general'

const {
  proxy
} = getCurrentInstance()
const route = useRoute();
const router = useRouter();
const props = defineProps({
  data: Object,
});
const formRef = ref()
const type = toRef(props.data?.type || 'add');
let isDisabled = toRef(props.data.isDisabled);
const {
  inspection_reform_status,inspection_reform_type,inspection_question_reform_status
} = useDicts(['inspection_reform_status','inspection_reform_type','inspection_question_reform_status'])
let tabsType = ref("tab1");

let formData = ref(props.data?.formData || {
  id: "",
  projectId: "",
  projectName: "",
  inspectionUnitId: "",
  inspectionUnitName: "",
  inspectedUnitId: "",
  inspectedUnitName: "",
  inspectedUnitParentNames: "",
  inspectionDate: "",
  inspectionUserId: "",
  inspectionUserName: "",
  name: "",
  reformDeadline: "",
  inspectionType: "",
  reformStatus: "",
  questionsNum: "0",
  questionsCompleted: "0",
  projBizIrQuestionSDtoList: [],
  projBizDmStgMDtoList: [],
  isOverdue: "否", // 是否超期
  progress: 0, // 进度
  reformFile: "", // 检查文件ID
  reformFileInfo: {}, // 检查文件信息
})


let formRules = ref({
  projectId: [],
  projectName: [],
  inspectionUnitId: [],
  inspectionUnitName: [],
  inspectedUnitId: [],
  inspectedUnitName: [{
    required: true,
    message: "请选择被检查单位"
  }],
  businessCode: [{
    required: true,
    message: "请输入业务编号"
  }],
  inspectionDate: [{
    required: true,
    message: "请选择检查时间"
  }],
  name: [{
    required: true,
    message: "请输入检查名称"
  }],
  reformDeadline: [{
    required: true,
    message: "请选择整改期限"
  }],
  inspectionType: [{
    required: true,
    message: "请选择检查类型"
  }],
  reformStatus: [],
  reformFileInfo: [{
    required: true,
    message: "请选择整改文件"
  }],
});

let _ = ref([]);
let delRowData = ref({});
let projBizIrQuestionSOption = ref({
  tip: false,
  border: true,
  index: true,
  stripe: true,
  menu: true,
  header: false,
  menuType: "text",
  addBtn: true,
  addBtnText: "新增",
  editBtn: false,
  editBtnText: "编辑",
  delBtn: false,
  delBtnText: "删除",
  cellBtn: true,
  column: [ {
    label: "问题来源",
    prop: "source",
    type: "input",
    cell: false
  },{
    label: "问题描述",
    prop: "describe",
    type: "input",
    cell: true
  }, {
    label: "整改状态",
    prop: "reformStatus",
    type: "input",
    columnSlot: true,
    cell: false
  }]
});
let projBizIrQuestionSNoAddOption = ref({
  tip: false,
  border: true,
  index: false,
  stripe: true,
  selection: true,
  menu: false,
  header: false,
  menuType: "text",
  addBtn: false,
  addBtnText: "新增",
  editBtn: false,
  editBtnText: "编辑",
  selectable: selectable,
  delBtn: false,
  delBtnText: "删除",
  cellBtn: true,
  searchBtn: false,
  emptyBtn: false,
  column: [ {
    label: "序号",
    align: 'center',
    prop: "serialNumber",
    type: "input",
    cell: false
  },{
    label: "问题描述",
    prop: "describe",
    type: "input",
    cell: true
  }, {
    label: "整改内容",
    prop: "reformContent",
    type: "input",
    columnSlot: true,
    cell: false
  }, {
    label: "整改图片",
    prop: "reformImage",
    type: "input",
    columnSlot: true,
    cell: false
  }, {
    label: "整改状态",
    prop: "reformStatus",
    type: "select",
    search: true,
    columnSlot: true,
  }]
});
let inspection_reform_status_select_value = ref("")
let projBizDmStgMOption = ref({
  tip: false,
  border: true,
  index: true,
  stripe: true,
  menu: true,
  header: false,
  menuType: "text",
  addBtn: false,
  addBtnText: "上传附件",
  editBtn: false,
  editBtnText: "编辑",
  delBtn: false,
  delBtnText: "删除",
  delBtns: false,
  columnBtn: false,
  cellBtn: false,
  refreshBtn: false,
  filterBtn: false,
  column: [{
    label: "文件名称",
    prop: "fileName",
    type: "input",
    cell: true
  }, {
    label: "文件大小",
    prop: "fileSizeFormat",
    type: "input",
    cell: true
  }, {
    label: "上传人",
    prop: "createName",
    type: "input",
    cell: true
  }, {
    label: "上传时间",
    prop: "createTime",
    type: "input",
    cell: true
  }]
});

const previewConfig = ref({
  // 是否外置预览,必传
  isExternalPreview: false,
  // 内置预览返回的替换字符,akey代表要替换的字符串，avalue代码替换的字符串
  substitute: {
    "akey":"avalue"
  },
  // 内置预览服务地址
  previewServerUrl: kkFileViewUrl,
})

const childRef = ref(null);

// 是否弹窗上传框
let dialogVisible = ref(false)
// 上传组件key,保证重新渲染
const componentKey = ref(0);
// 选择的问题ID
const checkQuestionIdList = ref([])
// uploadReformFileList
const uploadReformFileList = ref([])
// 流程Ref
const myFlowRef = ref(null)
const mySubmitFlowRef = ref(null)

function subRowSave(form, done) {
  //编辑行
  done();
  if (!form.describe) {
    proxy.$message.info("请填写问题描述！");
    let questions = formData.value['projBizIrQuestionSDtoList']
    questions[form.$index].$cellEdit = true;
    formData.value['projBizIrQuestionSDtoList'] = questions;
  }
}

function subAddRow(name) {
  //新增一行
  if (formData.value[name]) {
    formData.value[name].push({
      $cellEdit: true,
      reformStatus:'not_rectified'
    });
  } else {
    let arr = new Array();
    arr.push({
      $cellEdit: true,
    });
    formData.value[name] = arr
  }
}

function subRowUpdate(form, index, done, loading) {
  //编辑行
  done();
}

function editQuestionRow(row) {
  row.$cellEdit = true
}

function subDelRow(row, index, name) {
  //删除行
  if (row[0].id) {
    let data = JSON.parse(JSON.stringify(row[0]));
    if (delRowData.value[name]) {
      delRowData.value[name].push(Object.assign(data, {
        delFlag: 1,
      }));
    } else {
      delRowData.value[name] = [
        Object.assign(data, {
          delFlag: 1,
        }),
      ]
    }
  }
  formData.value[name].splice(index, 1);
}

function getFormData() {
  //获取formData数据
  for (let item in delRowData.value) {
    formData.value[item] = formData.value[item].concat(delRowData.value[item]);
  }
  return formData.value;
}
formData = toRef(Object.keys(props.data?.formData || {}).length > 0 ? {
  ...props.data?.formData
} : toRef(formData.value));

/**
 * 发布检查
 */
function publish() {
  let myFormData = getFormData();
  if(myFormData.id === null || myFormData.id === undefined || myFormData.id === ''){
    proxy.$message.info("请先保存数据");
    return
  }
  proxy.$modal.confirm("确认要发布数据项？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    ProjBizIrRecordMApi.publish(myFormData.id).then((res) => {
      proxy.$message.success("发布成功");
      props.data.onLink(proxy)
    });
  }).catch(() => {});
}

function submitData() {
  if (tabsType.value === 'tab1') { // tab1
    return new Promise((resolve) => {
      formRef.value.validate((flag) => {
        if (!flag) {
          proxy.$message.info("请填写必要的数据");
          return false;
        } else {
          if (type.value === "add") {
            resolve(saveData());
          } else {
            resolve(editData());
          }
        }
      });
    });
  } else { // tab2
    return new Promise((resolve) => {
      resolve(saveQuestions());
    });
  }
}

/**
 * 提交审批
 */
function submitApproval() {
    let questionIdList = getCheckQuestionIdList()
    if (questionIdList && questionIdList.length) {
      ProjBizIrRecordMApi.questionsUpdate(formData.value.projBizIrQuestionSDtoList).then(() => {
        getNewSubmitQuestions().then((resp) => {
          let submitQuestionsError = []
          let errorMsg = ""
          resp.data.forEach((v) => {
            if (v.submitStatus === 'yes') {
              submitQuestionsError.push(v.serialNumber)
            }
          })
          if (submitQuestionsError.length > 0) {
            errorMsg = '问题编号:[' + submitQuestionsError.join(",") + "]已提审,不可重复提审"
            proxy.$message.success(errorMsg);
            return
          }
          submitTaskDialog(resp.data)
        })
      });
    }else {
      proxy.$message.warning("请选择要提交审批的问题");
    }
}

/**
 * 流程页面
 */
async function submitTaskDialog(submitQuestions) {
  submitQuestions.forEach((v) => {
    v.$cellEdit = true
    v.reInspectionResults = '合格'
  })// 修改父组件的图片处理逻辑
  if (submitQuestions && submitQuestions.length) {
    // 使用 Promise.all 处理异步请求
    const promises = submitQuestions.map(async (v) => {
      if (v.reformImage && v.reformImageInfo?.id) {
        try {
          // 确保 requestImgUrl 返回 Promise
          v.reformImagePreviewUrl = await requestImgUrl(v.reformImageInfo.id);
          v.reformImagePreviewUrlList = [v.reformImagePreviewUrl];
        } catch (e) {
          console.error("图片加载失败", e);
          v.reformImagePreviewUrl = "/fallback-image.jpg"; // 添加备用图片
          v.reformImagePreviewUrlList = [];
        }
      }
      return v;
    });
    // 等待所有图片加载完成
    await Promise.all(promises);
  }

  let rowInfo = await ProjBizIrRecordMApi.view(formData.value.id)
  let projBizIrQuestionSDtoList = rowInfo.data?.projBizIrQuestionSDtoList || []
  let totalQuestionNumber = projBizIrQuestionSDtoList.length
  let completedQuestions = projBizIrQuestionSDtoList.filter((v) => {
    return v.reformStatus === 'completed'
  })
  let completedQuestionNumber = completedQuestions.length
  // console.log("userInfo",store.state.user)

  //编辑,新增按钮操作
  proxy.$DialogForm.show({
    title: "提交审批",
    type: "page",
    width: "80%",
    content: EditProjBizIrPrM,
    el: mySubmitFlowRef.value,
    data: {
      formData: {},
      submitQuestions: submitQuestions,
      recordInfo: rowInfo.data,
      type: 'view',
      totalQuestionNumber:totalQuestionNumber,
      completedQuestionNumber:completedQuestionNumber,
      createName:store.state.user?.userInfo?.userName,
      createOrgName:store.state.user?.userInfo?.userName,
      createTime: getCurrentFormattedTime(),
      tips: "",
      onLinkTwo:onLinkTwo,
      el: mySubmitFlowRef.value,
    },
    option: {
      submitBtn: false,
      emptyBtn: false,
      submitText: '保存',
      emptyText: '关闭',
      extendButton: [ {
        key: 'submit',
        text: '发起流程',
        buttonType: 'primary',
        // icon: 'el-icon-lollipop',
      },{
        key: 'cancel',
        text: '关闭',
        buttonType: '',
        // icon: 'el-icon-close',
      }]
    },
    callback: (res) => {
      let vm = res.dialogRefs
      let submitQuestions = vm.getSubmitQuestions();
      let customData = vm.getCustomData();
      if (vm) {
        switch (res.type) {
            // 关闭
          case 'cancel':
            res.close();
            break;
          case 'submit':
            let startProcDto = {
              businessKey: null,
              clientId: null
            };
            if(customData && customData.procInstId){
              vm.submitFlowTask(res)
            } else{
              vm.getStartFlow(submitQuestions, startProcDto).then(resp => {
                const taskInfo = vm.getTaskInfo();
                vm.handlerOpenConfirm(taskInfo,res);
              })
            }
            break;
        }
      }
    },
  });
}

function onLinkTwo(e) {
  e.close()
  props.data.onLink(proxy)
}

function refreshFormData() {
  if(formData.value.id){
    ProjBizIrRecordMApi.view(formData.value.id).then(async (res) => {
      let myFormData = res.data
      myFormData.isOverdue = isDateInFuture(myFormData.reformDeadline)
      myFormData.isOverdueStr = myFormData.isOverdue ? '是' : "否";
      if (myFormData.status !== 'draft') {
        myFormData.reformStatusStr = myFormData.reformStatus === 'in_progress' ? '整改中' : '已完成'
      } else {
        myFormData.reformStatusStr = '整改中'
      }
      // 处理相关数据
      myFormData.progress = calculateIntegerPercentage(myFormData.questionsCompleted, myFormData.questionsNum)
      myFormData.statusStr = myFormData.status === 'draft' ? '草稿' : "已发布"
      isDisabled.value = myFormData.status !== 'draft'

      // 处理附件数据
      if (myFormData.projBizDmStgMDtoList) {
        myFormData.projBizDmStgMDtoList.forEach((v) => {
          v.fileSizeFormat = formatBytes(v.fileSize)
        })
      } else {
        myFormData.projBizDmStgMDtoList = []
      }
      // 修改父组件的图片处理逻辑
      if (myFormData.projBizIrQuestionSDtoList && myFormData.projBizIrQuestionSDtoList.length) {
        // 使用 Promise.all 处理异步请求
        const promises = myFormData.projBizIrQuestionSDtoList.map(async (v) => {
          if (v.reformImage && v.reformImageInfo?.id) {
            try {
              // 确保 requestImgUrl 返回 Promise
              v.reformImagePreviewUrl = await requestImgUrl(v.reformImageInfo.id);
              v.reformImagePreviewUrlList = [v.reformImagePreviewUrl];
            } catch (e) {
              console.error("图片加载失败", e);
              v.reformImagePreviewUrl = "/fallback-image.jpg"; // 添加备用图片
              v.reformImagePreviewUrlList = [];
            }
          }
          return v;
        });
        // 等待所有图片加载完成
        await Promise.all(promises);
        // 使用响应式方式复制数组
        myFormData.projBizIrQuestionSDtoListFilter = [...myFormData.projBizIrQuestionSDtoList];
      }
      formData.value = myFormData
    })
    // console.log("最新数据表单。。。",formData.value)
  }
}

function saveData() {
  //新增操作
  const saveFormData = getFormData();
  saveFormData.projBizDmStgMDtoList = getAttachmentListData()
  return ProjBizIrRecordMApi.add(saveFormData).then((res) => {
    // console.log(res,"新增数据")
    formData.value.id = res.data.id
    type.value = 'edit'
    proxy.$message.success("新增成功");
    refreshFormData()
    return true;
  });
}

function editData() {
  //编辑操作
  const formData = getFormData();
  return ProjBizIrRecordMApi.update(formData).then(() => {
    proxy.$message.success("修改成功");
    refreshFormData()
    return true;
  });
}

function saveQuestions() {
  return ProjBizIrRecordMApi.questionsUpdate(formData.value.projBizIrQuestionSDtoList).then(() => {
    proxy.$message.success("修改成功");
    return true;
  });
}

function onAddAttachmentData() {
  // 重新计算key,保证上传组件重新渲染
  if (componentKey.value > 100) {
    componentKey.value = 0
  }else{
    componentKey.value += 1
  }
  dialogVisible.value = true
}

/**
 * 上传文件
 * @param e
 */
function uploadData(e){
  if (e.length) {
    let fileId = e[0].id
    let fileName = e[0].fileName
    let fileSize = e[0].fileSize
    formData.value.projBizDmStgMDtoList.push({
      fileId,
      fileName,
      fileSize,
      fileSizeFormat:formatBytes(fileSize),
      createName: store.state.user && store.state.user.userName ? store.state.user.userName : '-',
      createTime:getCurrentFormattedTime()
    })
  }
  dialogVisible.value = false
}

/**
 * 上传检查文件
 * @param e
 */
function uploadReformFile(e) {
  if (e.length) {
    let fileId = e[0].id
    let fileName = e[0].fileName
    let fileSize = e[0].fileSize
    formData.value.reformFileInfo = {
      fileId,
      fileName,
      fileSize
    }
  }
}

function showSelectOrgDialog(e) {
  const projectId = sessionStorage.getItem('projectId')
  let checkData = [];
  if(e.target.value){
    checkData.push({
      id: formData.value.inspectedUnitId,
      orgName: e.target.value,
      parentNames: formData.value.inspectedUnitParentNames,
    })
  }
  proxy.$SelectOrg({
    checkData: checkData,
    multiple: false,
    orgId: projectId,
    onSuccess(res) {
      if (res.data.length > 0) {
        let orgInfo = res.data[0]
        formData.value.inspectedUnitId = orgInfo.id
        formData.value.inspectedUnitName = orgInfo.orgName
        formData.value.inspectedUnitParentNames = orgInfo.parentNames
      }
    }
  })
}

function showSelectUserDialog(e) {
  const projectId = sessionStorage.getItem('projectId')
  let userList = [];
  if(e.target.value){
    userList.push({
      userId: formData.value.inspectionUserId,
      userName: formData.value.inspectionUserName,
    })
  }
  proxy.$SelectUser({
    userList: userList,
    multiple: false,
    orgId: projectId,
    onSuccess(res) {
      // console.log("选择人员",res)
      if (res.data.length > 0) {
        let userInfo = res.data[0]
        formData.value.inspectionUserId = userInfo.userId
        formData.value.inspectionUserName = userInfo.userName
      }
    }
  })
}

/**
 * 预览文件
 * @param data
 */
function previewFile(data) {
  previewOpenBlank(data.id)
}

/**
 * 删除附件元素
 * @param data
 * @param index
 */
function attachmentDeleteData(data, index) {
  formData.value.projBizDmStgMDtoList = formData.value.projBizDmStgMDtoList.filter((v) => {
    return v.id !== data.id
  })
}

/**
 * 附件预览
 * @param data
 */
function attachmentPreviewData(data) {
  // console.log("附件预览")
}

/**
 * 附件下载
 * @param row
 */
function attachmentDownloadData(row) {
  proxy.download("/project/dmStg/exportFile", [row.id], row.fileName);
}

/**
 * 问题清单过滤条件变更
 * @param e
 */
function questionFilterChange(e) {
  if (e) {
    let arr = JSON.parse(JSON.stringify(formData.value.projBizIrQuestionSDtoList));
    arr = arr.filter((v) => {
      return v.reformStatus === e
    })
    formData.value.projBizIrQuestionSDtoListFilter = arr
  } else {
    formData.value.projBizIrQuestionSDtoListFilter = [...formData.value.projBizIrQuestionSDtoList]
  }
}

/**
 * 上传整改图片
 * @param e
 * @param row
 */
async function uploadReformImage(e,row) {
  if (e.length) {
    let fileId = e[0].id
    let fileName = e[0].fileName
    let fileSize = e[0].fileSize
    for (const v of formData.value.projBizIrQuestionSDtoListFilter) {
      if (v.id === row.id) {
        v.reformImage = null
        v.reformImageInfo = {
          id,
          fileName,
          fileSize
        }
        v.reformImagePreviewUrl = await requestImgUrl(v.reformImageInfo.id);
        v.reformImagePreviewUrlList = [v.reformImagePreviewUrl];
      }
    }
    for (const v of formData.value.projBizIrQuestionSDtoList) {
      if (v.id === row.id) {
        v.reformImage = null
        v.reformImageInfo = {
          id,
          fileName,
          fileSize
        }
        v.reformImagePreviewUrl = await requestImgUrl(v.reformImageInfo.id);
        v.reformImagePreviewUrlList = [v.reformImagePreviewUrl];
      }
    }
  }
}

/**
 * 删除对应的整改图片
 * @param row
 */
function removeReformImage(row) {
  if (row.reformStatus === 'completed') {
    proxy.$message.info("整改完成的图片不可删除！");
    return
  }
  // 删除对应的数据
  formData.value.projBizIrQuestionSDtoListFilter.forEach((v) => {
    if (v.id === row.id) {
      v.reformImage = null
      v.reformImagePreviewUrl = null
      v.reformImagePreviewUrlList = []
    }
  })

  // 删除对应的数据
  formData.value.projBizIrQuestionSDtoList.forEach((v) => {
    if (v.id === row.id) {
      v.reformImage = null
      v.reformImagePreviewUrl = null
      v.reformImagePreviewUrlList = []
    }
  })

}

/**
 * 选择要提交的问题
 * @param data
 */
function questionRowClick(data) {
  // console.log("点击数据",data)
  let ids = []
  data.forEach((v) => {
    ids.push(v.id)
  })
  checkQuestionIdList.value = ids
}

/**
 * 整改内容失去焦点的时候触发
 */
function reformContentInput(e,row) {
  // console.log("e",e,row)
  // 删除对应的数据
  formData.value.projBizIrQuestionSDtoListFilter.forEach((v) => {
    if (v.id === row.id) {
      v.reformContent = e
    }
  })

  // 删除对应的数据
  formData.value.projBizIrQuestionSDtoList.forEach((v) => {
    if (v.id === row.id) {
      v.reformContent = e
    }
  })
}

/**
 * 查询要提交的问题
 */
async function getNewSubmitQuestions() {
  return new Promise((resolve) => {
    resolve(ProjBizIrRecordMApi.listQuestionByIdList(checkQuestionIdList.value));
  });
}


function getCheckQuestionIdList() {
  return checkQuestionIdList.value
}

/**
 * 查询刷新后的问题
 * @returns {Promise<unknown>}
 */
async function refreshQuestions() {
  if (formData.value.id) {
    ProjBizIrRecordMApi.listQuestionByRecordId(formData.value.id).then(async (res) => {
      formData.value.projBizIrQuestionSDtoList = res?.data || []
      // 修改父组件的图片处理逻辑
      if (formData.value.projBizIrQuestionSDtoList) {
        // 使用 Promise.all 处理异步请求
        const promises = formData.value.projBizIrQuestionSDtoList.map(async (v) => {
          if (v.reformImage && v.reformImageInfo?.id) {
            try {
              // 确保 requestImgUrl 返回 Promise
              v.reformImagePreviewUrl = await requestImgUrl(v.reformImageInfo.id);
              v.reformImagePreviewUrlList = [v.reformImagePreviewUrl];
            } catch (e) {
              console.error("图片加载失败", e);
              v.reformImagePreviewUrl = "/fallback-image.jpg"; // 添加备用图片
              v.reformImagePreviewUrlList = [];
            }
          }
          return v;
        });
        // 等待所有图片加载完成
        await Promise.all(promises);
        // 使用响应式方式复制数组
        formData.value.projBizIrQuestionSDtoListFilter = [...formData.value.projBizIrQuestionSDtoList];
      }
    })
  }
}


/**
 * 请求图片之前构建header头
 * @param img
 * @returns {boolean}
 */
function beforeReformImageLoad(img) {
  // 添加请求头
  img.setRequestHeader('Authorization', getAuthHeader())
  return true // 必须返回 true 才会继续加载
}

/**
 * 某一行是否可选
 * 如果是草稿才能删除
 * @param row
 */
function selectable(row) {
  return row.submitStatus === 'no';
}


function fileSerialNumberBuilder() {
  return "inspection" + Math.floor(Math.random()*10000);
}

/**
 * 获取附件列表数据
 * @returns {UnwrapRef<*[]>|*|*[]}
 */
function getAttachmentListData() {
  if(childRef.value){
    return childRef.value.getListData()
  } else {
    return []
  }
}

function onReformFileUploadChange(el) {
  // console.log("el",el)
  if (!beforeReformFileUpload(el.raw)) {
    // console.log("不合法！")
    return false;
  }
  const fileFormData = new FormData();
  fileFormData.append('file', el.raw);
  FileApi.fileUpload(generateHash(),'default',fileFormData).then((res) => {
    formData.value.reformFileInfo = res.data
  })
}

function beforeReformFileUpload(file) {
  // let isTrueFile;
  // if (
  //     file.type === 'application/msword' ||
  //     file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
  //     file.type === 'application/pdf'
  // ) {
  //   isTrueFile = true;
  // } else {
  //   isTrueFile = false;
  // }
  // if (!isTrueFile) {
  //   proxy.$message.info("仅限上传doc、docx、pdf格式的文档!")
  // }
  return true;
  // return isTrueFile;
}

function onReformFileRemove() {
  formData.value.reformFileInfo = null
}

function handleReformFilePreview(e) {
  // console.log("111111111111")
}

async function onReformImageUploadChange(el, row) {
  if (!beforeReformImageUpload(el.raw)) {
    return false;
  }
  const fileFormData = new FormData();
  fileFormData.append('file', el.raw);
  FileApi.fileUpload(generateHash(),'default',fileFormData).then(async (res) => {
    if (res.data) {
      for (const v of formData.value.projBizIrQuestionSDtoListFilter) {
        if (v.id === row.id) {
          v.reformImage = res.data.id
          v.reformImageInfo = res.data
          v.reformImagePreviewUrl = await requestImgUrl(v.reformImageInfo.id);
          v.reformImagePreviewUrlList = [v.reformImagePreviewUrl];
        }
      }
      for (const v of formData.value.projBizIrQuestionSDtoList) {
        if (v.id === row.id) {
          v.reformImage = res.data.id
          v.reformImageInfo = res.data
          v.reformImagePreviewUrl = await requestImgUrl(v.reformImageInfo.id);
          v.reformImagePreviewUrlList = [v.reformImagePreviewUrl];
        }
      }
    }
  })
}

function beforeReformImageUpload(file) {
  let isTrueFile;
  if (
      file.type === "image/jpeg" ||
      file.type === "image/jpg" ||
      file.type === "image/png"
  ) {
    isTrueFile = true;
  } else {
    isTrueFile = false;
  }
  if (!isTrueFile) {
    proxy.$message.info("仅限上传jpeg、jpg、png格式的文档!")
  }
  return isTrueFile;
}


/**
 * 记录
 */
function recordDialog() {
  proxy.$DialogForm.show({
    title: "查看审批",
    type: 'page',
    width: "80%",
    content: IrFlow,
    el: myFlowRef.value,
    data: {
      formData: getFormData()
    },
    option: {
      submitBtn: false,
      emptyBtn: false,
      submitText: '保存',
      emptyText: '关闭',
      extendButton: [{
        key: 'cancel',
        text: '关闭',
        buttonType: '',
        // icon: 'el-icon-close',
      }]
    },
    callback: (res) => {
      let vm = res.dialogRefs
      if (vm) {
        switch (res.type) {
            // 关闭
          case 'cancel':
            res.close();
            refreshQuestions()
            break;
        }
      }
    },
  });
}

/**
 * 组件卸载
 */
onBeforeUnmount(() => {
  // 在这里可以执行清理工作，如清除定时器、取消事件监听等
  formData.value.projBizIrQuestionSDtoList.forEach(item => {
    if (item.reformImagePreviewUrl?.startsWith('blob:')) {
      URL.revokeObjectURL(item.reformImagePreviewUrl);
    }
  });
})

function onClosePop(e) {
  // console.log("关闭弹窗触发",e)

}


defineExpose({
  getFormData,
  submitData,
  getNewSubmitQuestions,
  getCheckQuestionIdList
});
</script>

<style lang="scss" scoped>
::v-deep .ep-tabs--top {
  flex-direction: column!important;
}
.record-container{
  padding: 0 20px;
  position: relative;
}
.check-button-group{
  position: fixed;
  top: 101px;
  right: 95px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.reform-file-container{
  display: flex;
  align-items: center;
}
.reform-file{
  cursor: pointer;
  color: #0d8eff;
  margin: 0 0 0 15px;
  text-decoration: underline;
}
.reform-file:hover{
  color: red;
}
.remove-reform-image-container{
  margin: 5px;
  position: relative;
}
.remove-reform-image-icon{
  position: absolute;
  top: 4px;
  right: 4px;
  cursor: pointer;
}
.color-success{
  color: #67C23A;
}
.color-in{
  color: #E6A23C;
}
.color-danger{
  color: #F56C6C;
}
</style>
