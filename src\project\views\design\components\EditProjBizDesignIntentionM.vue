<template>
  <el-form :model="formData" :rules="rules" ref="snForm" label-width="100px" label-position="right" :disabled="type == 'view'">
    <el-row :gutter="16" :span="24">
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>基本信息
          </legend>
          <el-row :gutter="16" :span="24">
            <el-col :span='8'>
              <el-form-item label="交底记录编号" prop="brirecordNumber" style="width: 100%; white-space: nowrap;">
<!--                <el-input v-model="formData.brirecordNumber" type="text" :disabled="dialogdisabled" placeholder="" @blur="checkBrirecordNumber()" clearable >-->
                <el-input v-model="formData.brirecordNumber" type="text" :disabled="dialogdisabled" placeholder="请输入交底记录编号" :maxlength="20" clearable >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="交底人" prop="briefingPerson">
                <el-input v-model="formData.briefingPerson" type="text" placeholder="" :disabled="dialogdisabled"clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="交底时间" prop="briefingTime">
                <el-date-picker type="date" v-model="formData.briefingTime" format="YYYY-MM-DD" value-format="YYYY-MM-DD" style="width: 100%;" :disabled="dialogdisabled" placeholder="请选择" clearable></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16" :span="24">
            <el-col :span='24'>
              <el-form-item label="交底内容" prop="briefingContent" style="margin-bottom: 18px;">
                <el-input type="textarea" v-model="formData.briefingContent" placeholder="请输入交底内容" rows="3" :disabled="dialogdisabled" clearable>
<!--                <el-input v-model="formData.briefingContent" type="text" placeholder="请输入" clearable>-->
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </fieldset>
      </el-card>
      <el-card class="box-card" style="width: 100%;">
<!--        <fieldset class="fieldset2">-->
          <legend>
            <span class="el-button--primary"></span>卷册信息
          </legend>
          <el-row :gutter="16" :span="24">
            <sn-crud :data="formData.projBizDesignIntentionSDtoList" :option="optionj" v-model:search="queryForm.filter" @on-load="getPageList" @search-change="onChangeSearch" @search-reset="onResetSearch" @addBtnHandle="onEditDataj" @row-del="(row, index) => {subDelRow(row, index, 'projBizDesignIntentionSDtoList');}">
              <template #menu="{ row, index, size }">
<!--                <el-button type="primary" :size="size" icon="el-icon-edit" link @click="onEditData(row)">编辑</el-button>-->
<!--                <el-button type="danger" :size="size" icon="el-icon-delete" link @click="onDelData([row])" :disabled="dialogdisabled">删除</el-button>-->
<!--                <el-button type="danger" :size="size" icon="el-icon-delete" link @click="onDelDataj([formData.projBizDesignIntentionSDtoList[index]])" :disabled="dialogdisabled">删除</el-button>-->
              </template>
              <template #empty>
                <div>无数据</div>
              </template>
            </sn-crud>
          </el-row>
<!--        </fieldset>-->
      </el-card>
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>附件
          </legend>
          <el-row :gutter="16" :span="24">
            <project-document-storage-ui-table
                ref="childRef"
                :type="fileType"
                :relevantId="relevantId"
                :isPageSearch="false"
                :isDeleteMinio = "isDeleteMinio"
                :isHasAi = "isHasAi"
                @on-add-data="onAddfileData"
                @on-ai-review="onfileAiReview"
                @on-preview="onfilePreview"
                @on-delete="onfileDelete"
                :file-serial-number-builder="fileSerialNumberBuilder"
                :preview-config="previewConfig"
                :isShowAddBtn="type !== 'show'"
                :isShowDelBtn="type !== 'show'"
                :isShowPreviewBtn="isShowPreviewBtn"
                :isShowDownloadBtn="type !== 'show'"
                :isShowLinkBtn="false"
                :isShowDownloadBatchBtn="type !== 'show'"
            ></project-document-storage-ui-table>
          </el-row>
        </fieldset>
      </el-card>
      <el-card class="box-card" style="width: 100%;">
<!--        <fieldset class="fieldset2">-->
          <legend>
            <span class="el-button--primary"></span>单据信息
          </legend>
          <el-row :gutter="16" :span="24">
            <el-col :span='8'>
              <el-form-item label="创建人" prop="createName">
                <el-input v-model="formData.createName" type="text" placeholder="" :disabled="true" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
<!--              <el-form-item label="创建时间" prop="createTime">-->
<!--                <el-input v-model="formData.createTime" type="text" placeholder="请输入" clearable>-->
<!--                </el-input>-->
<!--              </el-form-item>-->
              <el-form-item label="创建时间" prop="createTime">
                <el-input v-model="formData.createTime" type="text" placeholder="" :disabled="true" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
<!--              <el-form-item label="单据状态" prop="docStatus">-->
<!--                <el-input v-model="formData.docStatus" type="text" placeholder="请输入" clearable>-->
<!--                </el-input>-->
<!--              </el-form-item>-->
              <el-form-item label="单据状态" prop="docStatus">
                <!--                <el-input v-model="formData.docStatus" type="text" placeholder="请输入" :disabled="true" clearable>-->
                <!--                </el-input>-->
                <el-select v-model="formData.docStatus" disabled placeholder="" clearable>
                  <el-option v-for="(item,index) in statusOption" :key="index" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="最近修改人" prop="updateName">
                <el-input v-model="formData.updateName" type="text" placeholder="" :disabled="true" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="最近修改时间" prop="updateTime">
                <el-input v-model="formData.updateTime" type="text" placeholder="" :disabled="true" clearable>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
<!--        </fieldset>-->
      </el-card>
    </el-row>
  </el-form>
</template>

<script setup>
import ProjBizDesignIntentionMApi from '@/project/api/design/ProjBizDesignIntentionM.js'
import EditProjBizDesignIntentionsvolume from "./EditProjBizDesignIntentionsvolume.vue";
import {formatBytes} from "@/scx/Bytes"
import store from "@/store";
import {getCurrentFormattedTime} from "@/common/utils/datetime";
import { kkFileViewUrl } from "@/config";
import {
  nextTick,
  onMounted,
  ref,
  reactive,
  toRef,
  defineProps,
  defineExpose,
  getCurrentInstance
} from 'vue';
import {
  useRoute,
  useRouter
} from 'vue-router'
const {
  proxy
} = getCurrentInstance()
const route = useRoute();
const router = useRouter();

const props = defineProps({
  data: Object,
});
const snForm = ref()
const type = toRef(props.data?.type);

let formData = ref({
  brirecordNumber: "",
  briefingPerson: "",
  briefingTime: "",
  briefingContent: "",
  updateName: "",
  projectId: sessionStorage.getItem('projectId'),
  createName: "",
  createTime: "",
  docStatus: "0",
  updateBy: "",
  updateTime: "",
  volumeNum: "", // 用于存储卷册号的字符串
  major: "", // 用于存储专业的字符串
  volumeName: "", // 用于存储卷册名称的字符串
  fileList: [], // 👈 补上
  projBizDesignIntentionSDtoList: [],
});
let rules = ref({
  brirecordNumber: [
    { required: true, message: '交底记录编号不能为空', trigger: 'blur' }
  ],
  briefingPerson: [
    { required: true, message: '交底人不能为空', trigger: 'blur' }
  ],
  briefingTime: [
    { required: true, message: '请选择交底时间', trigger: 'change' }
  ],
  briefingContent: [
    { required: true, message: '交底内容不能为空', trigger: 'blur' }
  ],
  updateName: [],
  createName: [],
  createTime: [],
  docStatus: [],
  updateBy: [],
  updateTime: []
});
let optionj = ref({
  tip: false,
  dialogType: "dialog",
  border: true,
  index: true,
  stripe: true,
  menu: true,
  header: true,
  height: "auto",
  searchSpan: 6,
  searchIcon: true,
  searchMenuSpan: 6,
  searchIndex: 3,
  selection: true,
  showTree: false,
  excelBtn: false,
  delBtnText: '删除',//管理卷册表格增加删除按钮提示
  delBtn: true,
  delBtns: false,            // 👈 显式隐藏批量删除按钮
  editBtn: false,
  delBtnsText: "批量删除",
  addBtnText: "关联卷册",
  customEditBtnText: "编辑",
  customDelBtnText: "删除",
  customViewBtnText: "查看",
  column: [{
    label: "专业",
    prop: "major",
    columnSlot: false,
    searchSlot: false
  }, {
    label: "卷册号",
    prop: "volumeNum",
    columnSlot: false,
    searchSlot: false
  }, {
    label: "卷册名称",
    prop: "volumeName",
    columnSlot: false,
    searchSlot: false
  }]
});
//输入框是否禁止输入
let dialogdisabled = ref(false)
let listData = ref([]);
let listvolumeData = ref([]);
let queryForm = ref({
  page: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  filter: {
    createTime: null
  }
});
let statusOption = ref([{
  label: "草稿",
  value: "0"
}, {
  label: "已发布",
  value: "1"
}]);

//以下为文件的方法及其定义=======
//处理附件上传相关方法---------begin
const fileType = ref("designIntention")//文件类型记得在后台配置-表名
const relevantId = toRef(props.data?.id)

const previewConfig = ref({
  // 是否外置预览,必传
  isExternalPreview: false,
  // 内置预览返回的替换字符,akey代表要替换的字符串，avalue代码替换的字符串
  substitute: {
    "akey":"avalue"
  },
  // 内置预览服务地址
  // previewServerUrl: "http://10.191.64.191:8012/onlinePreview",
  // 内置预览服务地址
  previewServerUrl: kkFileViewUrl,
})
function onAddfileData(list) {
  console.log(list,"onAddData-sssss")
  console.log(childRef.value.getListData(),"onAddData-ccccccc")
}

function onAifileReview(row) {
  console.log(row,"onAiReview-sssss")
}

function onfilePreview(row) {
  console.log(row,"onPreview-sssss")
}

function onfileDelete(list) {
  console.log(list,"onDelete-sssss")
}
const childRef = ref(null);
function getfileListData() {
  if(childRef.value){
    let list = childRef.value.getListData()
    console.log(list,"组件数据。。。。。。。。。")
  }
}
// 文件序列号生成函数
function fileSerialNumberBuilder() {
  return "file" + Math.floor(Math.random()*10000)
}
const getAttachmentList = () => {
  // 假设子组件有 getListData 方法
  return childRef.value?.getListData() || [];
};
//处理附件上传相关方法---------end
// =======  文件方法至此结束

//校验交底记录编号是否重复   目前后端校验了
async function checkBrirecordNumber() {
  const brirecordNumber = formData.value.brirecordNumber;
  if(type.value==='add') {
    // 调用后端接口，查询编号是否存在
    try {
      const params = {
        filter: {
          brirecordNumber: brirecordNumber
        },
        page: {
          pageSize: 1,
          pageNum: 1
        }
      };
      const res = await ProjBizDesignIntentionMApi.pageList(params); // 确保等待异步请求完成
      if (res.data.dataList && res.data.dataList.length > 0) {
        // 如果编号已存在，提示用户
        proxy.$message.error("交底记录编号已存在，请重新输入！");
        formData.value.brirecordNumber = "";
      }

    } catch (error) {
    console.error("校验交底记录编号时出错：", error);
    proxy.$message.error("校验交底记录编号时出错，请稍后再试！");
   }
  } else{
    try {
      const viewResult = await ProjBizDesignIntentionMApi.view(formData.value.id); // 确保等待异步请求完成
      const viewData = viewResult.data;
      const existingBrirecordNumber = viewData ? viewData.brirecordNumber : null;
      if(existingBrirecordNumber!==brirecordNumber){
        const params = {
          filter: {
            brirecordNumber: brirecordNumber
          },
          page: {
            pageSize: 1,
            pageNum: 1
          }
        };
        const res = await ProjBizDesignIntentionMApi.pageList(params); // 确保等待异步请求完成
        if (res.data.dataList && res.data.dataList.length > 0) {
          // 如果编号已存在，提示用户
          proxy.$message.error("交底记录编号已存在，请重新输入！");
          formData.value.brirecordNumber = "";
        }
      }
    } catch (error) {
      console.error("校验交底记录编号时出错：", error);
      proxy.$message.error("校验交底记录编号时出错，请稍后再试！");
    }
  }
}

function getPageList() {
  //查询分页列表
  // 查询前处理参数
  const params = handleQueryForm();
  ProjBizDesignIntentionMApi.pageList(params).then((res) => {
    listData.value = res.data.dataList;
    queryForm.value.page.total = res.data.totalCount;
  });
  if(type.value==='show'){
    dialogdisabled = true
    optionj.value.addBtn = false//如果是查看进来则隐藏这个按钮 关联卷册
  }
}

function onResetSearch() {
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
}

function handleQueryForm() {
  // 处理参数
  const {
    pageSize,
    pageNum
  } = queryForm.value.page;
  const filter = {}
  for (let key in queryForm.value.filter) {
    if (queryForm.value.filter[key]?.length) {
      // sn-curd搜索select会生成$prop的变量
      if (key.startsWith("$")) continue;
      filter[key] = queryForm.value.filter[key];
    }
  }
  delete filter.createTime;
  if (Array.isArray(queryForm.value.filter.createTime) && queryForm.value.filter.createTime?.length === 2) {
    filter["beginCreateTime"] = queryForm.value.filter.createTime[0];
    filter["endCreateTime"] = queryForm.value.filter.createTime[1];
  }
  const searchParams = {
    page: {
      pageSize,
      pageNum,
    },
    filter,
  }
  return searchParams
}

function onChangeSearch(params, done) {
  //搜索按钮操作
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
  done && done();
}
//关联卷册按钮操作
//定义一个数组用于接收关联卷册返回的数据
let existingData = []
async function onEditDataj(row) {
  //编辑,新增按钮操作
  let editType = row ? "edit" : "add";
  let rowInfo = await (editType !== "add" ? ProjBizDesignIntentionMApi.view(row.id) : {});
  const formData = editType !== "add" ? rowInfo.data : rowInfo;
  existingData =  getFormData().projBizDesignIntentionSDtoList
  proxy.$DialogForm.show({
    title: editType == "edit" ? "编辑" : "关联卷册",
    type: optionj.value.dialogType,
    width: "80%",
    content: EditProjBizDesignIntentionsvolume,
    data: {
      formData: formData,
      type: editType,
      existingData: existingData // 当前已关联的卷册数据
      },
    callback: (res) => {
      if (res.type && res.type !== 'close') {
      res.dialogRefs.submitData();
        //获取formdata
      const formData = getFormData();
      if(type.value==='add'){
        listvolumeData.value = res.dialogRefs.sData;
        formData.projBizDesignIntentionSDtoList=listvolumeData.value
      }else{
        const oldTeamIds = new Set(formData.projBizDesignIntentionSDtoList.map((item) =>item.volumeNum));
        res.dialogRefs.sData.value.forEach(item => {
          if (!oldTeamIds.has(item.volumeNum)) {
            formData.projBizDesignIntentionSDtoList.push({
              major: item.major,
              volumeNum: item.volumeNum,
              volumeName: item.volumeName
            })
          }
        })
        listvolumeData.value = formData.projBizDesignIntentionSDtoList;
      }
      // 获取已关联的卷册数据
      existingData = formData.projBizDesignIntentionSDtoList
      res.close();
    } else {
      // formData.projBizDesignIntentionSDtoList =  existingData
      res.close();
    }
    }
  });
}


async function onEditData(row) {
  //编辑,新增按钮操作
  let editType = row ? "edit" : "add";
  let rowInfo = await (editType !== "add" ? ProjBizDesignIntentionMApi.view(row.id) : {});
  const formData = editType !== "add" ? rowInfo.data : rowInfo;
  //编辑,新增按钮操作
  proxy.$DialogForm.show({
    title: editType == "edit" ? "编辑" : "新增",
    type: option.value.dialogType,
    width: "80%",
    content: EditProjBizDesignIntentionM,
    data: {
      formData: formData,
      type: editType,
    },
    callback: (res) => {
      if (res.type && res.type !== 'close') {
        if (res.dialogRefs) {
          res.dialogRefs.submitData().then((flag) => {
            if (flag) {
              getPageList();
              res.close();
            }
          });
        }
      } else {
        res.close();
      }
    }
  });
}

function onDelData(rows) {
  //删除行
  if (!rows || rows.length == 0) {
    proxy.$message.info("请勾选数据！");
    return false;
  }
  let ids = rows.map((item) => {
    return item.id;
  });
  proxy.$modal.confirm("确认删除数据项？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    ProjBizDesignIntentionMApi.remove(ids).then((res) => {
      proxy.$message.success("已删除");
      getPageList();
    });
  }).catch(() => {});
}

formData = toRef(Object.keys(props.data?.formData || {}).length > 0 ? {
  ...props.data?.formData
} : toRef(formData.value));

function getFormData() {
  //获取formData数据
  for (let item in delRowData.value) {
    formData.value[item] = formData.value[item].concat(delRowData.value[item]);
  }
  formData.value.fileList = getAttachmentList();
  return formData.value
};

function submitData(buttontype) {
  return new Promise((resolve) => {
    snForm.value.validate((flag) => {
      if (!flag) {
        return false;
      } else {
        buttonType.value = buttontype
        if (type.value === "add") {
          resolve(saveData());
        } else {
          resolve(editData());
        }
      }
    });
  });
}
let buttonType = ref();
function saveData() {
  //新增操作
  const formData = getFormData();
  if(listvolumeData.value.length !== 0){
    formData.volumeNum = convertListToString(listvolumeData.value, 'volumeNum'); // 假设你想用卷册号
    formData.major = convertListToString(listvolumeData.value, 'major'); // 假设你想用专业
    formData.volumeName = convertListToString(listvolumeData.value, 'volumeName'); // 假设你想用卷册名称
    formData.projBizDesignIntentionSDtoList = listvolumeData.value;
  }
  if(buttonType.value==='submit'){
    formData.docStatus = '1'
  }
  if(buttonType.value==='submit'){
    if(formData.id===null || formData.id === undefined){
      return ProjBizDesignIntentionMApi.add(formData).then((resp) => {
        if(resp.data){
          proxy.$message.success("发布成功");
          formData.id = resp.data.id;
          formData.projBizDesignIntentionSDtoList = resp.data.projBizDesignIntentionSDtoList
          return true;
        }
      });
    }else{
      return ProjBizDesignIntentionMApi.update(formData).then(() => {
        proxy.$message.success("发布成功");
        return true;
      });
    }
  }else{
    if(formData.id===null || formData.id === undefined){
      return ProjBizDesignIntentionMApi.add(formData).then((resp) => {
        if(resp.data){
          proxy.$message.success("保存成功");
          formData.id = resp.data.id;
          if(resp.data.projBizDesignIntentionSDtoList!==null){
            formData.projBizDesignIntentionSDtoList = resp.data.projBizDesignIntentionSDtoList
          }
          return true;
        }
      });
    }else{
      return ProjBizDesignIntentionMApi.update(formData).then(() => {
        proxy.$message.success("保存成功");
        return true;
      });
    }
  }
}
function convertListToString(list, field) {
  if(type.value==='add'){
    return list.value.map(item => item[field]).join(',');
  }else{
    return list.map(item => item[field]).join(',');
  }
}
const dialogVisible = ref(false); // 控制弹窗显示
const componentKey = ref(Date.now()); // 用于重置上传组件

/**
 * 删除文件数据
 * @param rows
 * @returns {boolean}
 */
function onDelFileData(rows) {
  //删除行
  if (!rows || rows.length == 0) {
    proxy.$message.info("请选择要删除的数据！");
    return false;
  }
  let ids = rows.map((item) => {
    return item.id;
  });
  proxy.$modal.confirm("确认删除数据项？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    rows.forEach((v) => {
      if (v.id) {
        ProjBizDmStgMApi.remove(ids).then((res) => {
          proxy.$message.success("已删除");
          getPageList();
        });
      } else {
        listData.value = listData.value.filter((value) => {
          value.fileId = v.fileId
        })
      }
    })
    proxy.$emit("onDelete",rows)

  }).catch(() => {
  });
}

function editData() {
  //编辑操作
  const formData = getFormData();
  if(buttonType.value==='submit'){
    formData.docStatus = '1'
  }
  if(buttonType.value==='revoke'){
    formData.docStatus = '0'
  }
  if(listvolumeData.value.length !== 0){
    formData.volumeNum = convertListToString(existingData, 'volumeNum'); // 假设你想用卷册号
    formData.major = convertListToString(existingData, 'major'); // 假设你想用专业
    formData.volumeName = convertListToString(existingData, 'volumeName'); // 假设你想用卷册名称
    return ProjBizDesignIntentionMApi.update(formData).then((resp) => {
      if(buttonType.value==='save'){
        proxy.$message.success("保存成功");
        formData.projBizDesignIntentionSDtoList = resp.data.projBizDesignIntentionSDtoList
        delRowData = ref({});
      }else{
        proxy.$message.success("发布成功");
      }
      return true;
    });
  }else{
    // 如果没有选中的卷册数据，使用formData中已有的数据，但需要过滤掉delFlag为1的项
    formData.volumeNum = formData.projBizDesignIntentionSDtoList
        .filter(item => item.delFlag !== 1) // 过滤掉delFlag为1的项
        .map(item => item.volumeNum)
        .join(',');
    formData.major = formData.projBizDesignIntentionSDtoList
        .filter(item => item.delFlag !== 1) // 过滤掉delFlag为1的项
        .map(item => item.major)
        .join(',');
    formData.volumeName = formData.projBizDesignIntentionSDtoList
        .filter(item => item.delFlag !== 1) // 过滤掉delFlag为1的项
        .map(item => item.volumeName)
        .join(',');
    return ProjBizDesignIntentionMApi.update(formData).then((resp) => {
      if(buttonType.value==='save'){
        proxy.$message.success("保存成功");
        formData.projBizDesignIntentionSDtoList = resp.data.projBizDesignIntentionSDtoList
        delRowData = ref({});
      }else{
        proxy.$message.success("发布成功");
      }
      return true;
    });
  }

}

let delRowData = ref({});//逻辑删除存储data
function subDelRow(row, index, name) {
  //删除行
  if (row[0].id) {
    let data = JSON.parse(JSON.stringify(row[0]));
    if (delRowData.value[name]) {
      delRowData.value[name].push(Object.assign(data, {
        delFlag: 1,
      }));
    } else {
      delRowData.value[name] = [
        Object.assign(data, {
          delFlag: 1,
        }),
      ]
    }
  }
  formData.value[name].splice(index, 1);
}
//卷册信息删除按钮
function onDelDataj(rows) {
  // 删除行
  // 确认删除操作
  proxy.$modal.confirm("确认删除数据项？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    // 从 formData 中移除选中的行
    formData.value.projBizDesignIntentionSDtoList = formData.value.projBizDesignIntentionSDtoList.filter(
        item => !rows.some(row => row.id === item.id)
    );

    proxy.$message.success("已删除");
  }).catch(() => {
    proxy.$message.info("取消删除");
  });
}
//初始化表单填充数据
function initializer(){
  if(type.value==='add'){
    formData.value.briefingPerson =  store.state.user && store.state.user.userInfo.userName ? store.state.user.userInfo.userName : null;//获取到登陆人
    formData.value.createName =  store.state.user && store.state.user.userInfo.userName ? store.state.user.userInfo.userName : null;//获取到登陆人
    formData.value.createTime = getCurrentFormattedTime();//获取到登录时间
  }
  //判断是否为查看进来，是则禁用输入框及按钮
  // console.log('getPageList.type==',type.value)
  // if(type.value==='show'){
  //   dialogdisabled = true
  // }
}
defineExpose({
  getFormData,
  submitData,
});
//初始化
onMounted(() => {
  initializer()
});
</script>

<style lang="scss" scoped>

</style>
