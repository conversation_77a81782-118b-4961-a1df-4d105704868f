import {request} from "sn-base-utils";
import { bpmCode } from "@/config";
export default class HandelUserListApi {
  static config = {

     // 获取角色id
    getNextIdentityConfig: {
      url: `/flow/action/getNextIdentityConfig`,
      method: "GET",
    },

    // 根据角色id获取当前用户组织下的人员
    getUserListByRoleId: {
      url: `/project/projectTeam/getUserListByRoleId`,
      method: "POST",
    },
    

  };

  static getNextIdentityConfig(data) {
    return request({
      ...this.config.getNextIdentityConfig,
      params: data,
      requestPrefix: bpmCode
    });
  }

  static getUserListByRoleId(data) {
    return request({
      ...this.config.getUserListByRoleId,
      data,
    });
  }
}