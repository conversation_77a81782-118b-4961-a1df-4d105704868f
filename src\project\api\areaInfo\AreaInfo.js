import { request, replaceUrl } from "sn-base-utils";

import { projectManagerServiceCode } from "@/config";

export default class AreaInfApi {
    static config = {
        view: {
            url: '/project/mdgArea/get/{id}',
            method: 'GET'
        },
        list: {
            url: '/project/mdgArea/list',
            method: "POST"
        },
        selectChildren: {
            url: '/project/mdgArea/selectChildren',
            method: "POST"
        }
    };

    /**
     * 查询地区信息详细
     * @param id
     * @returns {*}
     */
    static view(id) {
        return request({
            url: replaceUrl(this.config.view.url, { id }),
            method: this.config.view.method,
            requestPrefix: projectManagerServiceCode
        });
    }

    /**
     * 全部地区信息列表
     * @returns {*}
     */
    static list(data) {
        return request({
            url: this.config.list.url,
            method: this.config.list.method,
            data: data,
            requestPrefix: projectManagerServiceCode
        });
    }

    /**
     * 全部地区信息列表
     * @returns {*}
     */
    static selectChildren(data) {
        return request({
            url: this.config.list.url,
            method: this.config.list.method,
            data: data,
            requestPrefix: projectManagerServiceCode
        });
    }
}
