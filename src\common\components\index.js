/*
 * @Author: 王云飞
 * @Date: 2024-11-11 14:45:04
 * @LastEditTime: 2024-12-30 10:57:55
 * @LastEditors: 王云飞
 * @Description: 注册公共组件和优先给sn-base-utils传入基本配置
 *
 */
import config from "@/config.js";
import { setConfig } from "sn-base-utils";
import Crontab from "./crontab";
import SvgIcon from "@common/components/svgIcon"; 

// sn-base-utils传入基本配置,提升注册时机
setConfig(config);

const components = {
  Crontab, // sn-base-system中有使用
  SvgIcon
}
// 注册全局组件函数
export function registerComponents(app) {
  Object.keys(components).forEach(key => {
    app.component(key, components[key]);
  });
}