<template>
  <el-form :model="formData" :rules="formRules" ref="formRef" label-width="100px" label-position="right" :disabled="type == 'view'">
    <el-card class="box-card" style="width: 100%">
      <fieldset class="">
        <legend><span class="el-button--primary"></span>交底记录</legend>
        <el-row :gutter="16" :span="24">
          <el-col :span="12">
            <el-form-item label="交底内容" prop="tellContent" label-width="130px">
              <el-input v-model="formData.tellContent" type="text" placeholder="请输入" clearable maxlength="120"> </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="交底日期" prop="tellDate" label-width="130px">
              <el-date-picker v-model="formData.tellDate" type="date" value-format="YYYY-MM-DD" placeholder="请选择日期" clearable> </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="业务编号" prop="businessNumber" label-width="130px">
              <el-input v-model="formData.businessNumber" type="text" placeholder="请输入" clearable maxlength="120"> </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </fieldset>
    </el-card>
    <el-card class="box-card" style="width: 100%">
      <fieldset class="">
        <legend><span class="el-button--primary"></span>附件信息</legend>

        <project-document-storage-ui-table
          ref="childRef"
          :type="fileType"
          :relevantId="formData.id"
          :isPageSearch="false"
          :isDeleteMinio="isDeleteMinio"
          :isHasAi="isHasAi"
          :disabled="type == 'view'"
          :isShowAddBtn="type !== 'view'"
          :isShowDelBtn="type !== 'view'"
          @on-add-data="onAddData"
          @on-ai-review="onAiReview"
          @on-preview="onPreview"
          @on-delete="onDelete"
          :file-serial-number-builder="fileSerialNumberBuilder"
          :preview-config="previewConfig"
        ></project-document-storage-ui-table>
      </fieldset>
    </el-card>
    <el-card class="box-card" style="width: 100%">
      <fieldset class="">
        <legend><span class="el-button--primary"></span>单据信息</legend>
        <el-row :gutter="16" :span="24">
          <el-col :span="12">
            <el-form-item label="创建人" prop="createName" label-width="130px">
              <el-input v-model="formData.createName" type="text" placeholder="请输入" clearable disabled="true"> </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="创建时间" prop="createTime" label-width="130px">
              <el-input v-model="formData.createTime" type="text" placeholder="请输入" clearable disabled="true"> </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发布状态" prop="publishStatus" label-width="130px">
              <el-select v-model="formData.publishStatus" placeholder="请选择" disabled>
                <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item> </el-col
          ><el-col :span="12">
            <el-form-item label="最近修改人" prop="updateName" label-width="130px">
              <el-input v-model="formData.updateName" type="text" placeholder="请输入" clearable disabled="true"> </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最近修改时间" prop="updateTime" label-width="130px">
              <el-input v-model="formData.updateTime" type="text" placeholder="请输入" clearable disabled="true"> </el-input>
            </el-form-item>
          </el-col>
        </el-row></fieldset
    ></el-card>
  </el-form>
</template>

<script setup>
import ProjBizConsPlanTellApi from '@/project/api/constructionManagement/planTell/ProjBizConsPlanTell.js'
import { nextTick, onMounted, ref, reactive, toRef, defineProps, defineExpose, getCurrentInstance } from 'vue'
import { useRoute, useRouter } from 'vue-router'
const { proxy } = getCurrentInstance()
const fileType = ref('planTell')
const isDeleteMinio = ref(true)
const isHasAi = ref(true)
const route = useRoute()
const router = useRouter()
const props = defineProps({
  data: Object
})
const formRef = ref()
const type = toRef(props.data?.type)
import { useDicts } from '@/common/hooks/useDicts'
import ProjInfoMApi from '@/project/api/projectInfo/ProjInfoM'
import { kkFileViewUrl } from '@/config'
import store from '@/store'
import { getCurrentFormattedTime } from '@/common/utils/datetime'
const { projectCategories } = useDicts(['projectCategories'])

const statusOptions = [
  { label: '全部', value: '' },
  { label: '草稿', value: '1' },
  { label: '已发布', value: '2' }
]

let formData = ref({
  businessNumber: '',
  tellContent: '',
  tellDate: '',
  tellDoc: '',
  organizationUniter: '',
  organizationUnit: '',

  publishStatus: '1',
  updateName: '  ',
  updateTime: '  ',
  createTime: getCurrentFormattedTime(),
  createName: store.state.user && store.state.user.userInfo.userName ? store.state.user.userInfo.userName : '-',
  organizationUnit: store.state.user && store.state.user.userInfo.userName ? store.state.user.userInfo.userName : '-',
  organizationUniter: JSON.parse(store.state.user.orgName)?.find((item) => item.id === store.state.user.defaultOrg)?.orgName,
  fileList: []
})
let form = ref({
  createTime: getCurrentFormattedTime(),
  updateName: '',
  updateTime: ''
})

let formRules = ref({
  businessNumber: [
    {
      required: true,
      message: '请输入业务编号'
    },
    {
      pattern: /^(?=.*[a-zA-Z])(?=.*\d)[A-Za-z0-9]+$/,
      trigger: ['blur', 'change'],
      message: '业务编号格式不正确，需包含字母和数字'
    }
  ],
  tellContent: [
    {
      required: true,
      message: '请输入交底内容'
    },
    {
      trigger: ['blur', 'change'],
      message: '请输入交底内容'
    }
  ],
  tellDate: [
    {
      required: true,
      message: '请选择交底日期'
    },
    {
      trigger: ['blur', 'change'],
      message: '请选择交底日期'
    }
  ],
  tellDoc: [
    {
      required: true,
      message: '请上传交底文档'
    },
    {
      trigger: ['blur', 'change'],
      message: '请上传交底文档'
    }
  ],
  organizationUniter: [
    {
      required: true,
      message: '请输入编制人'
    },
    {
      trigger: ['blur', 'change'],
      message: '请输入编制人'
    }
  ],
  organizationUnit: [],
  submitDate: [],
  publishStatus: [
    {
      required: true,
      message: '请选择发布状态'
    },
    {
      trigger: ['blur', 'change'],
      message: '请选择发布状态'
    }
  ]
})
let getHtFlowDrawUrl = ref('')
let approveList = ref([])
formData = toRef(
  Object.keys(props.data?.formData || {}).length > 0
    ? {
        ...props.data?.formData
      }
    : toRef(formData.value)
)

function getFormData() {
  return formData.value
}
const childRef = ref(null)
const relevantId = ref(props.data?.formData?.id ?? null)
const previewConfig = ref({
  // 是否外置预览,必传
  isExternalPreview: false,
  // 内置预览返回的替换字符,akey代表要替换的字符串，avalue代码替换的字符串
  substitute: {
    akey: 'avalue'
  },
  // 内置预览服务地址
  previewServerUrl: kkFileViewUrl
})

function onAddData(list) {}

function onAiReview(row) {}

function onPreview(row) {}

function onDelete(list) {}
function getListData() {
  if (childRef.value) {
    let list = childRef.value.getListData()

    formData.value.fileList = [...list]
  }
}

// 文件序列号生成函数
function fileSerialNumberBuilder() {
  return 'TEST' + Math.floor(Math.random() * 10000)
}

function submitData(publishStatus) {
  return new Promise((resolve) => {
    formRef.value.validate((flag) => {
      if (!flag) {
        return false
      } else {
        if (type.value === 'add') {
          resolve(saveData(publishStatus))
        } else {
          resolve(editData(publishStatus))
        }
      }
    })
  })
}
// 下载表单(后端)
function downloadWord() {
  if (formData.value.id) {
    let params = {
      id: formData.value.id,
      type: 'planTell'
    }
    let timestamp = proxy.parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')
    proxy.download('/project/sceneStartWorkReport/export/attachments', params, '施工技术交底-' + formData.value.businessNumber + '-' + timestamp + '.zip')
  } else {
    proxy.$message.error('请选择先保存数据！')
    return
  }
}

function saveData(publishStatus) {
  getListData()
  //新增操作
  getListData()
  // 获取当前表单数据
  const data = getFormData()
  // 创建新对象而不是尝试修改响应式引用
  const formDataToSubmit = {
    ...data,
    publishStatus: publishStatus,
    updateName: '',
    updateTime: ''
  }
  return ProjBizConsPlanTellApi.add(formDataToSubmit).then(() => {
    proxy.$message.success('新增成功')
    return true
  })
}

function editData(publishStatus) {
  //编辑操作
  const formData = getFormData()
  let form = {
    ...formData,
    publishStatus: publishStatus
  }
  return ProjBizConsPlanTellApi.update(form).then(() => {
    if (publishStatus == '2') {
      proxy.$message.success('发布成功')
      return true
    }
    proxy.$message.success('修改成功')
    return
  })
}
defineExpose({
  getFormData,
  submitData
})
</script>

<style lang="scss" scoped>
/* 添加表格容器限制 */
::v-deep .sn-crud {
  width: 100%;
}

::v-deep .ep-date-editor {
  width: 100% !important;
}

::v-deep .ep-input-number {
  width: 100% !important;
}

::v-deep .ep-date-editor {
  --ep-date-editor-width: 100%;
}
</style>
