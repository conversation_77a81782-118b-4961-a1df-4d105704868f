import { request, replaceUrl } from "sn-base-utils";
import { bpmCode } from "@/config";
export default class BpmTaskApi {
    static config = {
        listRuTaskByProcInstId: {
            url: '/flow/task/listRuTaskByProcInstId',
            method: 'GET'
        },
        
    };

    /**
     * 新增初设审查
     * @param data
     * @returns {*}
     */
    static listRuTaskByProcInstId(data) {
        return request({
            url: this.config.listRuTaskByProcInstId.url,
            method: this.config.listRuTaskByProcInstId.method,
            params: data,
            requestPrefix: bpmCode
        });
    }

    
}
