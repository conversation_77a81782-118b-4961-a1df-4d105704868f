<template>
  <el-form class="page-content" :model="formData" :rules="formRules" ref="formRef" label-width="100px" label-position="right" :disabled="type == 'view'">
    <el-card class="box-card" style="width: 100%;">
      <fieldset class="fieldset2">
        <legend>
          <span class="el-button--primary"></span>施工影像记录
        </legend>
        <el-row :gutter="24" :span="24">
          <el-col :span='12'>
            <el-form-item label="业务编号" prop="workId">
              <el-input v-model="formData.workId" type="text" placeholder="请输入" clearable>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span='12'>
            <el-form-item label="影像描述" prop="imageDescription">
              <el-input v-model="formData.imageDescription" type="text" placeholder="请输入" clearable>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span='12'>
            <el-form-item label="影像分类" prop="imageCategory">
              <el-select v-model="formData.imageCategory" clearable  placeholder="请选择">
                <el-option v-for="(item, index) in image_category" :key="index" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </fieldset>
    </el-card>
    <el-card class="box-card" style="width: 100%;">
      <fieldset class="fieldset2">
        <legend>
          <span class="el-button--primary"></span>施工影像列表
        </legend>
        <el-row :gutter="16" :span="24">
          <sn-crud :data="formData.projBizConstructionImageDetailSDtoList" :option="projBizConstructionImageDetailSOption" @row-save="subRowSave"  @row-update="subRowUpdate"  @addBtnHandle="addRow" @row-del="(row, index) => {subDelRow(row, index, 'projBizConstructionImageDetailSDtoList');}">
            <template #empty>
              <div>无数据</div>
            </template>
            <!-- 影像文件列自定义插槽 -->
            <template #attachmentUrl="{ row, index }">
              <div style="display: flex; align-items: center; justify-content: center; min-height: 60px;">
                <el-image
                  v-if="row.attachmentUrl"
                  :src="row.attachmentUrl"
                  style="width: 120px; height: 90px; margin-left: 8px;"
                  fit="cover"
                  :preview-src-list="[row.attachmentUrl]"
                />
                <el-button v-else type="primary" plain @click="onUpload(row, index)">
                  <el-icon><UploadFilled /></el-icon>上传附件
                </el-button>
              </div>
            </template>
            <template #menu="{ row, index }">
              <el-button type="primary" link @click="onUpload(row, index)" :disabled="type == 'view'">替换</el-button>
              <!-- <el-button type="danger" link @click="onDelRow(row, index)" :disabled="type == 'view'">删除</el-button> -->
            </template>
          </sn-crud>
          <el-button @click="addRow" type="primary" plain style="display: block; width: 100%; margin-top: 10px">新增一行数据</el-button>
        </el-row>
      </fieldset>
    </el-card>
    <el-card class="box-card" style="width: 100%;">
      <fieldset class="fieldset2">
        <legend>
          <span class="el-button--primary"></span>单据信息
        </legend>
        <el-row :gutter="16" :span="24">
          <el-col :span='8'>
            <el-form-item label="创建人" prop="createName">
              <el-input v-model="formData.createName" :readonly="true" type="text" placeholder="请输入" clearable>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span='8'>
            <el-form-item label="创建时间" prop="createTime">
              <el-date-picker type="datetime" v-model="formData.createTime" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" clearable disabled style="width: 100%;"></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span='8'>
            <el-form-item label="单据状态" prop="publishStatus">
              <el-input v-if="type === 'view' || type === 'edit'" :value="getPublishStatusLabel(formData.publishStatus)" readonly />
              <el-select v-else v-model="formData.publishStatus" clearable placeholder="请选择">
                <el-option v-for="item in publishStatusOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span='8'>
            <el-form-item label="最近修改人" prop="updateName">
              <el-input v-model="formData.updateName" :readonly="true" type="text" placeholder="请输入" clearable>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span='8'>
            <el-form-item label="最近修改时间" prop="updateTime">
              <el-date-picker type="datetime" v-model="formData.updateTime" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" clearable disabled style="width: 100%;"></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </fieldset>
    </el-card>
  </el-form>
  <el-dialog
      v-model="dialogVisible"
      title="文件上传"
      width="500"
  >
    <sn-upload :drag="true" :key="componentKey" :limit="1" listType="text" :autoUpload="true" :fileMaxSize="209715200"
               @input="(fileList) => uploadData(fileList)"/>
  </el-dialog>
</template>

  <script setup>
  import store from "@/store";
  import {requestImgUrl} from "@/common/api/MinioFile"
  import FileApi from "sn-base-layout-vue3/packLayout/api/File";
  import {getCurrentFormattedTime} from "@/common/utils/datetime";
  import ProjBizConstructionImageMApi from '@/project/api/constructionImage/ProjBizConstructionImageM.js' 
  import {
    nextTick,
    onMounted,
    ref,
    reactive,
    toRef,
    defineProps,
    defineExpose,
    getCurrentInstance,
    watch,
  } from 'vue';
  import {
    useRoute,
    useRouter
  } from 'vue-router'
  const {
    proxy
  } = getCurrentInstance()
  const route = useRoute();
  const router = useRouter();
  const props = defineProps({
    data: Object,
  });
  const formRef = ref()
  const type = toRef(props.data?.type);
  import {
    useDicts
  } from "@/common/hooks/useDicts";
  const {
    image_category
} = useDicts(["image_category"]);
  const publishStatusOptions = [
    { label: '草稿', value: 0 },
    { label: '已发布', value: 1 }
  ];
  let formData = ref({
    workId:"",
    imageDescription: "",
    imageCategory: "",
    projBizConstructionImageDetailSDtoList: [],
    createName: store.state.user && store.state.user.userInfo.userName ? store.state.user.userInfo.userName : null,
    createTime: getCurrentFormattedTime(),
    publishStatus: 0,
    updateName: "",
    updateTime:"",
  });
  let formRules = ref({
    workId:  [{
      required: true,
      message: ""
    }],
    imageDescription:  [{
      required: true,
      message: ""
    }],
    imageCategory:  [{
      required: true,
      message: ""
    }],
    createName: [],
    createTime: [],
    publishStatus: [],
    updateName: [],
    updateTime: []
  });
  let delRowData = ref({});
  let projBizConstructionImageDetailSOption = ref({
    tip: false,
    border: true,
    index: true,
    stripe: true,
    menu: true,
    header: true,
    menuType: "text",
    addBtn: true,
    addBtnText: "新增",
    editBtn: false,
    editBtnText: "替换",
    delBtn: true,
    delBtnText: "删除",
    delBtns: false,
    cellBtn: true,
    maxHeight: "200px",
    headerAlign: "center",
    column: [
      { label: "描述", prop: "description", type: "input", cell: true },
      { label: "影像文件id", prop: "attachment", type: "input", cell: true, hide: true },
      { label: "影像文件", prop: "attachmentUrl", cell: false }
    ]
  });
  const dialogVisible = ref(false); // 控制弹窗显示
  const componentKey = ref(Date.now()); // 用于重置上传组件
  const currentRow = ref(null); // 记录当前上传的行
  function subRowSave(form, done) {
    //编辑行
    done();
  }

  // watch(
  //   () => formData.value.projBizConstructionImageDetailSDtoList,
  //   async (newList) => {
  //     if (!Array.isArray(newList)) return;
  //     for (const row of newList) {
  //       if (row.attachment) {
  //         try {
  //           row.attachmentUrl = await requestImgUrl(row.attachment);
  //         } catch (e) {
  //           row.attachmentUrl = '';
  //         }
  //       }
  //     }
  //   },
  //   { immediate: true, deep: true }
  // );

  onMounted(async () => {
    const list = formData.value.projBizConstructionImageDetailSDtoList || [];
    for (const row of list) {
      if (row.attachment) {
        try {
          row.attachmentUrl = await requestImgUrl(row.attachment);
        } catch (e) {
          row.attachmentUrl = '';
        }
      }
    }
  });

  // 新增一行方法，供按钮和外部调用
  function addRow() {
    if (formData.value.projBizConstructionImageDetailSDtoList) {
      formData.value.projBizConstructionImageDetailSDtoList.push({
        $cellEdit: true,
        description: '',
        attachment: '',
        attachmentUrl: '',
        tempRelevantId: fileSerialNumberBuilder()
      });
    } else {
      formData.value.projBizConstructionImageDetailSDtoList = [{
        $cellEdit: true,
        description: '',
        attachment: '',
        attachmentUrl: '',
        tempRelevantId: fileSerialNumberBuilder()
      }];
    }
  }
  // 修改原有subAddRow调用为addRow
  function subAddRow() {
    addRow();
  }

  function subRowUpdate(form, index, done, loading) {
    //编辑行
    done();
  }

  function onUpload(row, index) {
  // 打开上传弹窗并记录当前行
  currentRow.value = row;
  dialogVisible.value = true;
  componentKey.value = Date.now(); // 重置上传组件
}

  function subDelRow(row, index, name) {
    //删除行
    if (row[0].id) {
      let data = JSON.parse(JSON.stringify(row[0]));
      if (delRowData.value[name]) {
        delRowData.value[name].push(Object.assign(data, {
          delFlag: 1,
        }));
      } else {
        delRowData.value[name] = [
          Object.assign(data, {
            delFlag: 1,
          }),
        ]
      }
    }
    formData.value[name].splice(index, 1);
  }

  function getFormData() {
    //获取formData数据
    for (let item in delRowData.value) {
      formData.value[item] = formData.value[item].concat(delRowData.value[item]);
    }
    // 保证publishStatus默认为0
    if (formData.value.publishStatus === undefined || formData.value.publishStatus === "" || formData.value.publishStatus === null) {
      formData.value.publishStatus = 0;
    }
    return formData.value;
  }
  formData = toRef(Object.keys(props.data?.formData || {}).length > 0 ? {
    ...props.data?.formData
  } : toRef(formData.value));

  function submitData(buttonType) {
    return new Promise((resolve) => {
      formRef.value.validate((flag) => {
        if (!flag) {
          return false;
        } else {
          if (type.value === "add") {
            resolve(saveData(buttonType));
          } else {
            resolve(editData(buttonType));
          }
        }
      });
    });
  }

  function saveData(buttonType) {
    //新增操作
    const formData = getFormData();
    if(buttonType==='submit'){
      formData.publishStatus = 1
    }
    if(formData.id===null || formData.id === undefined){
      return ProjBizDesignIntentionMApi.add(formData).then((resp) => {
        if(resp.data){
          proxy.$message.success("保存成功");
          formData.id = resp.data.id;
          return true;
        }
      });
    }else{
      return ProjBizDesignIntentionMApi.update(formData).then(() => {
        proxy.$message.success("保存成功");
        return true;
      });
    }
  }

  function editData(buttonType) {
    //编辑操作
    const formData = getFormData();
    if(buttonType==='submit'){
      formData.publishStatus = 1
    }
    return ProjBizConstructionImageMApi.update(formData).then((resp) => {
      formData.projBizConstructionImageDetailSDtoList = resp.data.projBizConstructionImageDetailSDtoList;
      delRowData = ref({});
      proxy.$message.success("修改成功");
      return true;
    });
  }

  function getPublishStatusLabel(val) {
    const found = publishStatusOptions.find(item => item.value === val || String(item.value) === String(val));
    return found ? found.label : val;
  }

  function onUploadSuccess(res, row, index) {
    // 假设后端返回的图片地址为res.url
    row.attachment = res.url;
  }

  function onReplace(row, index) {
  // 触发上传逻辑，上传成功后赋值 row.attachment
  }
  // function onDelRow(row, index) {
  //   // 删除formData.value.projBizConstructionImageDetailSDtoList中的对应行
  //   formData.value.projBizConstructionImageDetailSDtoList.splice(index, 1);
  // }

/**
 * 上传文件
 * @param e
 */
async function uploadData(fileList) {
  if (!fileList || !fileList.length || !currentRow.value) {
    dialogVisible.value = false;
    return;
  }
  const file = fileList[0];
  const relevantId = currentRow.value.id || currentRow.value.tempRelevantId;
  try {
    await ProjBizConstructionImageMApi.upload({
      relevantId,
      fileId: file.id,
      fileName: file.fileNameOld,
      fileSize: file.fileSize,
      fileSerialNumber: file.uid,
      type: 'constructionImage'
    });
    // 获取图片url
    const imgUrl = await requestImgUrl(file.id);

    currentRow.value.attachment = file.id;
    currentRow.value.attachmentUrl = imgUrl;
    proxy.$message.success("上传成功");
  } catch (e) {
    proxy.$message.error("上传失败");
  }
  dialogVisible.value = false;
}
const uploadReformFileList = ref([])

function onReformFileUploadChange(el) {
  console.log("el",el)
  if (!beforeReformFileUpload(el.raw)) {
    console.log("不合法！")
    return false;
  }
  const fileFormData = new FormData();
  fileFormData.append('file', el.raw);
  FileApi.fileUpload(generateHash(),'default',fileFormData).then((res) => {
    formData.value.reformFileInfo = {
      fileId: res.data.id,
      fileName: res.data.fileNameOld,
      fileSize: res.data.fileSize
    }
  })
}
function onReformFileRemove() {
  
}

function handleReformFilePreview(e) {
  console.log("预览")
}

// 文件序列号生成函数
function fileSerialNumberBuilder() {
  return "TEST" + Math.floor(Math.random()*10000)
}
  
  defineExpose({
    getFormData,
    submitData,
  });
  </script>

<style scoped>

</style>



