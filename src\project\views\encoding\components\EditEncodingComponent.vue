<template>
  <el-form :model="formData" :rules="formRules" ref="formRef" label-width="120px">
    <el-row :gutter="16">
      <el-col :span="12">
        <el-form-item label="类型编号" prop="projectTypeCode">
          <el-input v-model="formData.projectTypeCode" type="text" placeholder="请输入类型编号" clearable>
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="类型名称" prop="projectName">
          <el-input v-model="formData.projectName" type="text" placeholder="请输入类型名称" clearable>
          </el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="16">
      <el-col :span="12">
        <el-form-item label="项目类型" prop="projectType">
          <el-input v-model="formData.projectType" type="text" placeholder="请输入项目类型" clearable>
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="项目ID" prop="projectId">
          <el-input v-model="formData.projectId" type="text" placeholder="请输入项目ID" clearable>
          </el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="16">
      <el-col :span="12">
        <el-form-item label="租户ID" prop="tenantId">
          <el-input v-model="formData.tenantId" type="text" placeholder="请输入租户ID" clearable>
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="formData.sort" :min="0" :max="999999999999" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
import { ref, defineProps, defineExpose, getCurrentInstance, onMounted } from 'vue';
import EncodingApi from '@/project/api/encoding/encoding';
const { proxy } = getCurrentInstance();

const props = defineProps({
  data: Object,
});

const formRef = ref();
const formData = ref({
  projectTypeCode: '',
  projectName: '',
  projectType: '',
  projectId: '',
  tenantId: 'T001',
  sort: 1
});

const formRules = ref({
  projectTypeCode: [
    { required: true, message: '请输入类型编号', trigger: 'blur' }
  ],
  projectName: [
    { required: true, message: '请输入类型名称', trigger: 'blur' }
  ],
  projectType: [
    { required: true, message: '请输入项目类型', trigger: 'blur' }
  ],
  projectId: [
    { required: true, message: '请输入项目ID', trigger: 'blur' }
  ],
  sort: [
    { required: true, message: '请输入排序', trigger: 'blur' }
  ]
});

onMounted(() => {
  if (props.data && props.data.formData && props.data.type === 'edit') {
    // 编辑模式，填充数据
    formData.value = {
      ...formData.value,
      ...props.data.formData
    };
  }
});

function submitData() {
  return formRef.value.validate().then(() => {
    const submitData = {
      ...formData.value,
      projBizTenderFlowManagementSDtoList: []
    };

    if (props.data.type === 'edit') {
      // 编辑模式
      submitData.id = props.data.id;
      return EncodingApi.update(submitData).then((res) => {
        if (res.code === 200) {
          proxy.$message.success('编辑成功');
          return true;
        } else {
          proxy.$message.error(res.msg || '编辑失败');
          return false;
        }
      }).catch(error => {
        console.error('编辑失败:', error);
        proxy.$message.error('编辑失败');
        return false;
      });
    } else {
      // 新增模式
      return EncodingApi.add(submitData).then((res) => {
        if (res.code === 200) {
          proxy.$message.success('新增成功');
          return true;
        } else {
          proxy.$message.error(res.msg || '新增失败');
          return false;
        }
      }).catch(error => {
        console.error('新增失败:', error);
        proxy.$message.error('新增失败');
        return false;
      });
    }
  }).catch(error => {
    console.error('表单验证失败:', error);
    return false;
  });
}

defineExpose({
  submitData
});
</script>