<template>
  <el-form :model="formData" :rules="formRules" ref="formRef">
    <el-row :gutter="16" :span="20">
      <el-col :span="12">
        <el-form-item label="类型编号" prop="typeCode">
          <el-input v-model="formData.typeCode" type="text" placeholder="请输入类型名称" clearable>
          </el-input>
        </el-form-item>
        <el-form-item label="类型名称" prop="typeName">
          <el-input v-model="formData.typeName" type="text" placeholder="请输入类型名称" clearable>
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="20">
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="formData.sort" :min="0" :max="999999999999" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
import { ref, defineProps, defineExpose } from 'vue';
import EncodingApi from '@/project/api/encoding/encoding';
const { proxy } = getCurrentInstance();

const props = defineProps({
  data: Object,
});

const formRef = ref();
const formData = ref({
  typeCode: '',
  typeName: '',
  sort: 0
});

const formRules = ref({
  typeCode: [
    { required: true, message: '请输入类型编号', trigger: 'blur' }
  ],
  typeName: [
    { required: true, message: '请输入类型名称', trigger: 'blur' }
  ],
  sort: [
    { required: true, message: '请输入排序', trigger: 'blur' }
  ]
});

function submitData() {
  return formRef.value.validate().then(() => {
    // 提交逻辑
    return true;
  });
}

defineExpose({
  submitData
});
</script>