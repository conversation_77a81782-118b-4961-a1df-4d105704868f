import {request} from "sn-base-utils";

export default class HandelFlowTask {
  static config = {
    // 获取首环节任务信息
    viewInitialTaskInfo: {
      url: `/flow/action/viewInitialTaskInfo`,
      method: "GET",
    },
    // 获取节点扩展信息, 在代办,已办, 历史任务, 传阅的详情/办理中调用
    viewActDefExtInfo: {
      url: `/flow/action/viewActDefExtInfo`,
      method: "GET",
    },
    // 获取流程运行时指定任务的信息
    viewRuTaskInfo: {
      url: `/flow/action/viewRuTaskInfo`,
      method: "GET",
    },
    // 获取首环节表单信息
    viewInitialFormInfo: {
      url: `/flow/action/viewInitialFormInfo`,
      method: "GET",
    },
    // 获取流程运行时表单信息
    viewRuFormInfo: {
      url: `/flow/action/viewRuFormInfo`,
      method: "GET",
    },
    // 获取流程历史任务的信息
    viewHiTaskInfo: {
      url: `/flow/action/viewHiTaskInfo`,
      method: "GET",
    },
    
    viewArchHiTaskInfo: {
      url: `/flow/action/viewArchHiTaskInfo`,
      method: "GET",
    },
    // 获取流程历史任务表单信息
    viewHiFormInfo: {
      url: `/flow/action/viewHiFormInfo`,
      method: "GET",
    },
    // 提交流程
    submitProc: {
      url: `/flow/action/submitProc`,
      method: "POST",
    },

    // 启动流程
    startProc: {
      url: `/flow/action/startProc`,
      method: "POST",
    },

    // 终止流程
    stopProc: {
      url: `/flow/action/stopProc`,
      method: "POST",
    },
    // 撤销流程
    revokeProc: {
      url: `/flow/action/revokeProc`,
      method: "POST",
    },
    //挂起流程实例
    suspendProcInst: {
      url: `/flow/action/suspendProcInst`,
      method: "POST",
    },
    //激活流程实例
    activateProcInst: {
      url: `/flow/action/activateProcInst`,
      method: "POST",
    },
    // 提交任务
    submitTask: {
      url: `/flow/action/submitTask`,
      method: "POST",
    },
    // 替换办理人
    replaceTaskAssignee: {
      url: `/flow/action/replaceTaskAssignee`,
      method: "POST",
    },
    // 自由跳转
    freeJumpTo: {
      url: `/flow/action/freeJumpTo`,
      method: "POST",
    },
    // 转办任务
    turnTask: {
      url: `/flow/action/turnTask`,
      method: "POST",
    },

    // 根据实例Id查询任务办理列表
    listTaskCommentByInstId: {
      url: `/flow/taskComment/listTaskCommentByInstId`,
      method: "GET",
    },
    listArchTaskCommentByInstId: {
      url: "/flow/taskComment/listArchTaskCommentByInstId",
      method: "GET",
    },
    // 获取全部用户任务节点
    getAllActInfoList: {
      url: `/flow/action/getAllActInfoList`,
      method: "GET",
    },
    // 获取可驳回节点
    getRejectActInfoList: {
      url: `/flow/action/getRejectActInfoList`,
      method: "GET",
    },
    // 获取后续节点
    getNextActInfoList: {
      url: `/flow/action/getNextActInfoList`,
      method: "GET",
    },
    // 获取下一环节预置人员
    getNextUserInfoList: {
      url: `/flow/action/getNextUserInfoList`,
      method: "GET",
    },
    // 提交阅知记录
    submitReadComment: {
      url: `/flow/action/submitReadComment`,
      method: "GET",
    },
    // 获取常用语列表
    commentItemList: {
      url: "/flow/commentItem/listByOwner",
      method: "POST",
    },
    // 获取加签列表
    listRuTaskUserByProcInstId: {
      url: "/flow/task/listRuTaskUserByProcInstId",
      method: "GET",
    },
     // 获取打印列表
     getPrintTemplateList: {
      url: "/flow/action/getPrintTemplateList",
      method: "GET",
    },
    //查询在线表单详情
    getByMenuId: {
      url: "/fdOnline/getByMenuId/{menuId}",
      method: "GET",
    },

  };
  static getByMenuId(menuId) {
    return request({
      method: "GET",
      url: `/fdOnline/getByMenuId/${menuId || "headers"}`,
    });
  }
  static getPrintTemplateList(params) {
    return request({
      ...this.config.getPrintTemplateList,
      params,
    });
  }
  static viewInitialTaskInfo(params) {
    return request({
      ...this.config.viewInitialTaskInfo,
      params,
    });
  }
  static viewActDefExtInfo(params) {
    return request({
      ...this.config.viewActDefExtInfo,
      params,
    });
  }
  static viewInitialFormInfo(params) {
    return request({
      ...this.config.viewInitialFormInfo,
      params,
    });
  }

  static viewRuTaskInfo(params) {
    return request({
      ...this.config.viewRuTaskInfo,
      params,
    });
  }

  static viewRuFormInfo(params) {
    return request({
      ...this.config.viewRuFormInfo,
      params,
    });
  }

  static viewHiTaskInfo(params) {
    return request({
      ...this.config.viewHiTaskInfo,
      params,
    });
  }
  static viewArchHiTaskInfo(params) {
    return request({
      ...this.config.viewArchHiTaskInfo,
      params,
    });
  }
  static viewHiFormInfo(params) {
    return request({
      ...this.config.viewHiFormInfo,
      params,
    });
  }

  static submitProc(data) {
    return request({
      ...this.config.submitProc,
      data,
    });
  }

  static startProc(data) {
    return request({
      ...this.config.startProc,
      data,
    });
  }

  static stopProc(data) {
    return request({
      ...this.config.stopProc,
      data,
    });
  }

  static revokeProc(data) {
    return request({
      ...this.config.revokeProc,
      data,
    });
  }

  static suspendProcInst(data) {
    return request({
      ...this.config.suspendProcInst,
      data,
    });
  }

  static activateProcInst(data) {
    return request({
      ...this.config.activateProcInst,
      data,
    });
  }

  static submitTask(data) {
    return request({
      ...this.config.submitTask,
      data,
    });
  }

  static turnTask(data) {
    return request({
      ...this.config.turnTask,
      data,
    });
  }

  static replaceTaskAssignee(data) {
    return request({
      ...this.config.replaceTaskAssignee,
      data,
    });
  }

  static freeJumpTo(data) {
    return request({
      ...this.config.freeJumpTo,
      data,
    });
  }

  static listTaskCommentByInstId(params,urlParams) {
    let obj={...this.config.listTaskCommentByInstId,params}
    if(urlParams.taskFlag==5){
      obj={
        ...this.config.listArchTaskCommentByInstId,
        params:{...params,year:urlParams.year}
      }
    }
    return request({
      ...obj,
    });
  }

  static getAllActInfoList(params) {
    return request({
      ...this.config.getAllActInfoList,
      params,
    });
  }

  static getRejectActInfoList(params) {
    return request({
      ...this.config.getRejectActInfoList,
      params,
    });
  }

  static getNextActInfoList(params) {
    return request({
      ...this.config.getNextActInfoList,
      params,
    });
  }

  static getNextUserInfoList(params) {
    return request({
      ...this.config.getNextUserInfoList,
      params,
    });
  }
  static listRuTaskUserByProcInstId(params) {
    return request({
      ...this.config.listRuTaskUserByProcInstId,
      params,
    });
  }

  static submitReadComment(params) {
    return request({
      ...this.config.submitReadComment,
      params,
    });
  }
  static commentItemList(data) {
    return request({
      ...this.config.commentItemList,
      data,
    });
  }
}