import { request, replaceUrl } from "sn-base-utils";

export default class SafetyWorkRiskControlApi {
    static config = {
        add: {
            url: '/project/risk/add',
            method: 'POST'
        },
        remove: {
            url: '/project/risk/delete',
            method: 'DELETE'
        },
        update: {
            url: '/project/risk/update',
            method: 'PUT'
        },
        view: {
            url: '/project/risk/get/{id}',
            method: 'GET'
        },
        pageList: {
            url: '/project/risk/page',
            method: "POST"
        },
        list: {
            url: '/project/risk/list',
            method: "POST"
        }
    };

    /**
     * 新增作业风险管控
     * @param data
     * @returns {*}
     */
    static add(data) {
        return request({
            url: this.config.add.url,
            method: this.config.add.method,
            data: data
        });
    }

    /**
     * 删除作业风险管控
     * @param data
     * @returns {*}
     */
    static remove(data) {
        return request({
            url: this.config.remove.url,
            method: this.config.remove.method,
            data: data
        });
    }

    /**
     * 更新作业风险管控
     * @param data
     * @returns {*}
     */
    static update(data) {
        return request({
            url: this.config.update.url,
            method: this.config.update.method,
            data: data
        });
    }

    /**
     * 查询作业风险管控详细
     * @param id
     * @returns {*}
     */
    static view(id) {
        return request({
            url: replaceUrl(this.config.view.url, { id }),
            method: this.config.view.method
        });
    }

    /**
     * 分页查询作业风险管控列表
     * @param data
     * @returns {*}
     */
    static pageList(data) {
        return request({
            url: this.config.pageList.url,
            method: this.config.pageList.method,
            data: data
        });
    }

    /**
     * 全部作业风险管控列表
     * @returns {*}
     */
    static list(data) {
        return request({
            url: this.config.list.url,
            method: this.config.list.method,
            data: data
        });
    }
}
