<template>
  <flow-page-container
    ref="flowPageContainerRef"
    @handlerAction="handlerAction"
    @initData="initData"
    @handlerPrint="handlerPrint"
    :closeBtn="isShowCloseBtn"
    :approvalOption="approvalOption"
    @approvalOptionCallback="getApprovalOption"
  >
    <el-form :model="formData" :rules="formRules" ref="formRef" label-width="165px" label-position="right" :disabled="type == 'view'">
      <el-row :gutter="16" :span="24">
        <el-card class="box-card" style="width: 100%">
          <fieldset class="">
            <legend><span class="el-button--primary"></span>施工方案报审</legend>
            <el-row :gutter="16" :span="24">
              <el-col
                :span="12"
                v-permi="
                  formPermi(
                    {
                      fieldModelId: 'projBizConsPlanReport',
                      field: 'constructionPlanType',
                      fieldName: '施工方案类型'
                    },
                    customFormPermi
                  )
                "
              >
                <el-form-item label="施工方案类型" prop="constructionPlanType" label-width="180px">
                  <el-select v-model="formData.constructionPlanType" placeholder="请选择施工方案类型" clearable>
                    <el-option label="一般施工方案" value="1"></el-option>
                    <el-option label="专项施工方案" value="2"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col
                :span="12"
                v-permi="
                  formPermi(
                    {
                      fieldModelId: 'projBizConsPlanReport',
                      field: 'hugeFlag',
                      fieldName: '是否危大及以上工程，1是0否'
                    },
                    customFormPermi
                  )
                "
              >
                <el-form-item label="是否危大及以上工程" prop="hugeFlag" label-width="180px">
                  <el-select v-model="formData.hugeFlag" placeholder="请选择" clearable>
                    <el-option label="是" :value="1"></el-option>
                    <el-option label="否" :value="0"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col
                :span="12"
                v-permi="
                  formPermi(
                    {
                      fieldModelId: 'projBizConsPlanReport',
                      field: 'expertCheckFlag',
                      fieldName: '是否经过专家评估论证，1是0否'
                    },
                    customFormPermi
                  )
                "
              >
                <el-form-item label="是否经过专家评估/论证" prop="expertCheckFlag" label-width="180px">
                  <el-select v-model="formData.expertCheckFlag" placeholder="请选择" clearable>
                    <el-option label="是" :value="1"></el-option>
                    <el-option label="否" :value="0"></el-option>
                  </el-select>
                </el-form-item> </el-col
              ><el-col
                :span="12"
                v-permi="
                  formPermi(
                    {
                      fieldModelId: 'projBizConsPlanReport',
                      field: 'projectName',
                      fieldName: '工程名称'
                    },
                    customFormPermi
                  )
                "
              >
                <el-form-item label="工程名称" prop="projectName" label-width="180px">
                  <el-input v-model="formData.projectName" type="text" placeholder="请输入" clearable> </el-input>
                </el-form-item>
              </el-col>

              <el-col
                :span="12"
                v-permi="
                  formPermi(
                    {
                      fieldModelId: 'projBizConsPlanReport',
                      field: 'organizationUnit',
                      fieldName: '编制单位'
                    },
                    customFormPermi
                  )
                "
              >
                <el-form-item label="编制单位" prop="organizationUnit" label-width="180px">
                  <el-input v-model="formData.organizationUnit" type="text" placeholder="请输入" clearable disabled="true"> </el-input>
                </el-form-item>
              </el-col>
              <el-col
                :span="12"
                v-permi="
                  formPermi(
                    {
                      fieldModelId: 'projBizConsPlanReport',
                      field: 'businessNumber',
                      fieldName: '报审表编号'
                    },
                    customFormPermi
                  )
                "
              >
                <el-form-item label="报审表编号" prop="businessNumber" label-width="180px">
                  <el-input v-model="formData.businessNumber" type="text" placeholder="请输入业务编号" clearable> </el-input>
                </el-form-item> </el-col
              ><el-col
                :span="12"
                v-permi="
                  formPermi(
                    {
                      fieldModelId: 'projBizConsPlanReport',
                      field: 'supervisorCompany',
                      fieldName: '监理单位'
                    },
                    customFormPermi
                  )
                "
              >
                <el-form-item label="监理单位" prop="supervisorCompany" label-width="180px">
                  <el-input v-model="formData.supervisorCompany" type="text" placeholder="请输入" clearable> </el-input>
                </el-form-item>
              </el-col>
            </el-row></fieldset
        ></el-card>
        <el-card class="box-card" style="width: 100%">
          <fieldset class="">
            <el-row :gutter="16" :span="24">
              <legend><span class="el-button--primary"></span>附件信息</legend>
              <project-document-storage-ui-table
                ref="childRef"
                :type="fileType"
                :relevantId="relevantId"
                :isPageSearch="false"
                :isDeleteMinio="isDeleteMinio"
                :isHasAi="isHasAi"
                :file-serial-number-builder="fileSerialNumberBuilder"
                :preview-config="previewConfig"
                :isShowAddBtn="type !== 'view'"
                :isShowDelBtn="type !== 'view'"
              ></project-document-storage-ui-table>
            </el-row></fieldset
        ></el-card>
        <el-card class="box-card" style="width: 100%">
          <fieldset class="">
            <legend><span class="el-button--primary"></span>单据信息</legend>
            <el-row :gutter="16" :span="24">
              <el-col
                :span="12"
                v-permi="
                  formPermi(
                    {
                      fieldModelId: 'projBizConsPlanReport',
                      field: 'createName',
                      fieldName: '创建人'
                    },
                    customFormPermi
                  )
                "
              >
                <el-form-item label="创建人" prop="createName" label-width="180px">
                  <el-input v-model="formData.createName" type="text" placeholder="请输入" clearable disabled="true"> </el-input>
                </el-form-item>
              </el-col>
              <el-col
                :span="12"
                v-permi="
                  formPermi(
                    {
                      fieldModelId: 'projBizConsPlanReport',
                      field: 'formData.createTime',
                      fieldName: '创建时间'
                    },
                    customFormPermi
                  )
                "
              >
                <el-form-item label="创建时间" prop="formData.createTime" label-width="180px">
                  <el-input v-model="formData.createTime" type="text" placeholder="请输入" clearable disabled="true"> </el-input>
                </el-form-item>
              </el-col>
              <el-col
                :span="12"
                v-permi="
                  formPermi(
                    {
                      fieldModelId: 'projBizConsPlanReport',
                      field: 'formData.examineStatus',
                      fieldName: '审批状态'
                    },
                    customFormPermi
                  )
                "
              >
                <el-form-item label="审批状态" prop="formData.examineStatus" label-width="180px">
                  <el-select v-model="formData.examineStatus" placeholder="请选择" disabled>
                    <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item> </el-col
              ><el-col
                :span="12"
                v-permi="
                  formPermi(
                    {
                      fieldModelId: 'projBizConsPlanReport',
                      field: 'formData.updateName',
                      fieldName: '最近修改人'
                    },
                    customFormPermi
                  )
                "
              >
                <el-form-item label="最近修改人" prop="formData.updateName" label-width="180px">
                  <el-input v-model="formData.updateName" type="text" placeholder="请输入" clearable disabled="true"> </el-input>
                </el-form-item>
              </el-col>
              <el-col
                :span="12"
                v-permi="
                  formPermi(
                    {
                      fieldModelId: 'projBizConsPlanReport',
                      field: 'formData.updateTime',
                      fieldName: '最近修改时间'
                    },
                    customFormPermi
                  )
                "
              >
                <el-form-item label="最近修改时间" prop="updateTime" label-width="180px">
                  <el-input v-model="formData.updateTime" type="text" placeholder="请输入" clearable disabled="true"> </el-input>
                </el-form-item>
              </el-col>
            </el-row></fieldset
        ></el-card> </el-row
    ></el-form>
  </flow-page-container>
</template>

<script setup>
import ProjBizConsPlanReportApi from '@/project/api/constructionManagement/planReport/ProjBizConsPlanReport.js'
import FlowPageContainer from '@/common/components/exportFile/FlowPageContainer.vue'
import { kkFileViewUrl } from '@/config'
let auth = new Map()
import { nextTick, onMounted, ref, reactive, toRef, defineProps, defineExpose, getCurrentInstance } from 'vue'
import { useRoute, useRouter } from 'vue-router'
const { proxy } = getCurrentInstance()

const route = useRoute()
const router = useRouter()
const props = defineProps({
  data: Object
})
const formRef = ref()
const isShowCloseBtn = ref(false)
const FlowActionType = ref(proxy.FlowActionType)
const fileType = ref('planReport')
const isDeleteMinio = ref(true)
const isShowAddBtn = ref(false) //上传
const isShowDelBtn = ref(false) //下载
const isHasAi = ref(true)
const previewConfig = ref({
  // 是否外置预览,必传
  isExternalPreview: false,
  // 内置预览返回的替换字符,akey代表要替换的字符串，avalue代码替换的字符串
  substitute: {
    akey: 'avalue'
  },
  // 内置预览服务地址
  previewServerUrl: kkFileViewUrl
})
const relevantId = ref(props.data?.formData?.id ?? null)
let approvalOption = props.data?.approvalOption
  ? props.data?.approvalOption
  : route.query?.procInstId
    ? ref({
        isShowApprovalList: true,
        isShowFlowDiagram: true,
        procInstId: route.query?.procInstId
      })
    : ref({
        isShowApprovalList: false,
        isShowFlowDiagram: false,
        procInstId: ''
      })
const state = reactive({
  flowData: {
    businessKey: '',
    procInstId: '',
    procDefKey: '',
    taskId: '',
    formConfig: [],
    variableList: [],
    customPropertyConfigList: []
  },
  formAuth: {},
  taskFlag: 1,
  firstAct: false,
  handlerClose: null,
  rootFields: {},
  fullFieldNameMap: {},
  formAuthList: {}
})
const type = toRef(props.data?.type)
const res = ref()
const flowPageContainerRef = ref(null)
import { useDicts } from '@/common/hooks/useDicts'
import ProjInfoMApi from '@/project/api/projectInfo/ProjInfoM'

import store from '@/store'
import BpmTaskApi from '@/project/api/bpm/bpmTask'
import { getCurrentFormattedTime } from '@/common/utils/datetime'
const { projectCategories } = useDicts(['projectCategories'])
let formData = ref({
  businessNumber: '',
  constructionPlan: '',
  constructionPlanType: '',
  hugeFlag: '',
  expertCheckFlag: '',
  supervisorCompany: '',
  projectName: '',
  examineStatus: '1',
  createName: store.state.user && store.state.user.userInfo.userName ? store.state.user.userInfo.userName : '-',

  organizationUnit: JSON.parse(store.state.user.orgName)?.find((item) => item.id === store.state.user.defaultOrg)?.orgName,
  createTime: getCurrentFormattedTime(),
  updateName: '  ',
  updateTime: '  ',
  fileList: []
})
let form = ref({
  createTime: getCurrentFormattedTime(),
  updateName: formData.value.updateName ? formData.value.updateName : '-',
  updateTime: formData.value.updateTime ? formData.value.updateTime : '-'
})

let formRules = ref({
  businessNumber: [
    {
      required: true,
      message: '请输入报审表编号'
    },
    {
      pattern: /^(?=.*[a-zA-Z])(?=.*\d)[A-Za-z0-9]+$/,
      trigger: ['blur', 'change'],
      message: '报审表编号必须包含字母和数字'
    }
  ],

  constructionPlanType: [
    {
      required: true,
      message: '请选择施工方案类型'
    }
  ],
  hugeFlag: [
    {
      required: true,
      message: '请选择是否危大及以上工程'
    }
  ],
  expertCheckFlag: [
    {
      required: true,
      message: '请选择是否经过专家评估/论证'
    }
  ],
  supervisorCompany: [
    {
      required: true,
      message: '请输入监理单位'
    }
  ],
  projectName: [
    {
      required: true,
      message: '请输入工程名称'
    }
  ]
})
let isShowFlow = ref(true)
let getHtFlowDrawUrl = ref('')
let approveList = ref([])
formData = toRef(Object.keys(props.data?.formData || {}).length > 0 ? { ...props.data?.formData } : toRef(formData.value))

function getFormData() {
  return formData.value
}

function processAuthFieldForm(fieldList) {
  /**
   * 工作流表单权限功能：在扫描完成之前，业务可对表单字段进行修改。
   */
  //TODO 这里可以对权限表单的字段进行任意的修改,如果不满足业务需求，可以在这里手动添加你想要的字段
  return fieldList
}

function getApprovalOption(fun) {
  /**
   * 工作流表单权限功能(重要)：应对弹窗无法及时相应的问题，特别增加该函数修复该bug
   */
  nextTick(() => {
    fun(approvalOption.value)
  })
}

function customFormPermi(obj) {
  /**
   * 工作流表单权限功能：自定义表单权限处理函数，将对应的对象的属性还原成对应的权限值
   */
  let { field, fieldModelId } = obj
  let fullFieldName = `${fieldModelId}_${field}`
  //TODO 这里可以自定义处理，默认返回0即可
  return auth.get(fullFieldName) || 0
}

function handlerPrint(taskComment) {
  /**
   * 工作流打印模板功能
   */
  ProjBizConsPlanReportApi.printTemplate({
    id: formData.value.id,
    templateId: taskComment.templateId,
    formData: formData.value
  }).then((res) => {
    router.push({
      name: 'PrintDoc',
      query: {
        fileId: res.data
      }
    })
    taskComment.close && taskComment.close()
  })
}

function getSubmitTask(taskActionDto) {
  /**
   * 工作流提交任务功能
   */
  return ProjBizConsPlanReportApi.submitTask({
    busiDto: formData.value,
    taskActionDto: taskActionDto
  })
}

//打开确认框
function handlerOpenConfirm(taskInfo, resp) {
  ProjBizConsPlanReportApi.view(taskInfo.businessKey).then((resp) => {
    if (resp.data) {
      formData.value = resp.data
      relevantId.value = resp.data.id
      isShowAddBtn.value = true
      isShowDelBtn.value = true
    }
  })
  res.value = resp
  flowPageContainerRef.value.handlerActionSubmit(taskInfo, 1, isHugeFlag.value)
}

function getStartFlow(formData, startProcDto) {
  if (formData.hugeFlag === 1) {
    isHugeFlag.value = 'Activity_e27d04e'
  } else if (formData.hugeFlag === 0) {
    isHugeFlag.value = 'Activity_cf2b0ba'
  }
  /**
   * 工作流启动流程功能
   */
  return new Promise((resolve) => {
    formRef.value.validate((flag) => {
      if (!flag) {
        return false
      } else {
        /*工作流启动流程功能*/
        formData.fileList = getListData()
        formData.fileList.map((item) => {
          item.type = fileType.value
        })
        formData.updateName = ''
        formData.updateTime = ''
        resolve(
          ProjBizConsPlanReportApi.startFlow({
            busiData: formData,
            startProcDto: startProcDto
          }).then((respon) => {
            state.flowData.procDefKey = respon.data[0].procDefKey
            state.flowData.procInstId = respon.data[0].procInstId
            state.flowData.businessKey = respon.data[0].businessKey
            state.flowData.taskId = respon.data[0].taskId
            taskInfo.value = respon.data[0]
            nextTick(() => {
              btnHandle(props.data.el.lastChild)
            })
          })
        )
      }
    })
  })
}

let isHugeFlag = ref('')

function submitFlowTask(resp) {
  res.value = resp
  if (resp.hugeFlag === 1) {
    isHugeFlag.value = 'Activity_e27d04e'
  } else if (resp.hugeFlag === 0) {
    isHugeFlag.value = 'Activity_cf2b0ba'
  }
  return proxy
    .$confirm('确定提交当前单据?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    .then(() => {
      const formDatas = getFormData()
      return BpmTaskApi.listRuTaskByProcInstId({
        procInstId: formDatas.procInstanceId
      }).then((params) => {
        flowPageContainerRef.value.handlerActionSubmit(params.data[0], 1, isHugeFlag.value)
      })
    })
    .catch(() => {
      return true
    })
}

function handlerAction(operation, taskComment, handlerClose) {
  /**
   * 工作流操作功能
   */
  // 启动并保存草稿后再次提交
  if ((operation.type == FlowActionType.value.SAVESTART || operation.type == FlowActionType.value.SAVE || operation.type == FlowActionType.value.START) && !formData.value.taskId) {
    let startProcDto = {
      procDefKey: formData.value.procDefKey,
      businessKey: null,
      clientId: null,
      ...taskComment?.dialogRefs?.getFormData()
    }
    let httpCall = null
    if (operation.type == FlowActionType.value.SAVESTART) {
      httpCall = getStartFlow(formData, startProcDto)
    } else if (operation.type == FlowActionType.value.SAVE) {
      httpCall = getSaveFormData(formData)
    }
    httpCall.then(() => {
      proxy.$modal.msgSuccess('提交成功')
      taskComment.close && taskComment.close()
      handlerClose()
    })
  } else {
    operation.type == FlowActionType.value.START || operation.type == FlowActionType.value.SAVESTART ? (operation.type = FlowActionType.value.AGREE) : operation.type
    let taskActionDto = {
      taskId: state.flowData.taskId,
      procInstId: state.flowData.procInstId,
      actionType: operation.type,
      ...taskComment?.dialogRefs?.getFormData(),
      taskAssignees: taskComment?.dialogRefs?.getFormData()?.taskAssignees ? taskComment.dialogRefs.getFormData().taskAssignees.join(',') : ''
    }
    getSubmitTask(taskActionDto)
      .then(() => {
        proxy.$modal.msgSuccess('任务办理成功')
        taskComment.close && taskComment.close()
        if (res) {
          props.data.closeDialog(proxy)
        } else {
          handlerClose()
        }
      })
      .catch(() => {
        taskComment.close && taskComment.close()
      })
  }
}

function initBusiForm() {
  /**
   * 工作流初始化表单功能
   */
  let that = proxy
  let routerQueryParams = route.query
  auth = new Map(Object.entries(state.formAuth))
  if (routerQueryParams) {
    if (routerQueryParams.busiData) {
      formData.value = routerQueryParams.busiData
    } else {
      if (routerQueryParams.businessKey) {
        let businessKey = route.query.businessKey
        ProjBizConsPlanReportApi.view(businessKey).then((resp) => {
          if (!resp.data) {
            formData.value = { ...formData.value, projectId: sessionStorage.getItem('projectId') }
          } else {
            formData.value = resp.data
            relevantId.value = resp.data.id
          }
        })
      } else {
        that.$message.error('初始化失败,因为工作流未将流程信息传入！')
      }
    }
  }
}

function getSaveFormData(formData) {
  return new Promise((resolve) => {
    formRef.value.validate((flag) => {
      if (!flag) {
        return false
      } else {
        if (formData.id) {
          resolve(ProjBizConsPlanReportApi.update(formData))
        } else {
          formData.fileList = getListData()
          formData.fileList.map((item) => {
            item.type = fileType.value
          })
          formData.updateName = ''
          formData.updateTime = ''
          resolve(ProjBizConsPlanReportApi.add(formData))
        }
      }
    })
  })
}

function getFieldForm() {
  let rootFields = {
    ...state.rootFields
  }
  let fieldList = []
  let keys = []
  for (let key in rootFields) {
    keys.push(key)
    fieldList.push(rootFields[key])
  }
  const result = processAuthFieldForm ? processAuthFieldForm(fieldList) : fieldList
  const message = {
    type: 'getTemplateRoot',
    pathname: window.location.pathname,
    actKey: getQueryParams('actKey'),
    content: result
  }
  window.parent.postMessage(JSON.parse(JSON.stringify(message)), '*')

  if (type.value != 'view' && (formData.value.examineStatus == '1' || !formData.value.examineStatus)) {
    isShowAddBtn.value = true
    isShowDelBtn.value = true
  }
}

function getQueryParams(key) {
  let url = window.location.href
  // 使用正则表达式解析URL中的查询字符串
  var queryString = url.split('?')[1]
  if (!queryString) {
    return {}
  }
  var params = {}
  // 分割查询字符串成单个参数
  var vars = queryString.split('&')
  for (var i = 0; i < vars.length; i++) {
    var pair = vars[i].split('=')
    params[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1])
  }
  return params[key]
}

function initData(urlParams, taskInfo, handlerClose) {
  // 如果用户从流程设计点击启动, 如果是在线表单, 则阻止执行initData, 防止在线表单报错
  if (urlParams.menuId && urlParams.openInStart) {
    return
  }
  state.flowData.procDefKey = taskInfo.procDefKey
  state.flowData.procInstId = urlParams.procInstId
  state.flowData.businessKey = urlParams.businessKey
  state.flowData.taskId = urlParams.taskId
  state.flowData.fiedPermission = taskInfo.fiedPermission
  state.flowData.variableList = taskInfo.variableList
  state.taskFlag = urlParams.taskFlag
  state.firstAct = taskInfo.firstAct
  state.handlerClose = handlerClose
  state.flowData.customPropertyConfigList = taskInfo.customPropertyConfigList
  let fieldPerList = taskInfo.formConfig?.formFieldConfig?.fieldPerList || []
  handleFormAuth(fieldPerList)
  initBusiForm()
}

function handleFormAuth(data) {
  let formAuth = {}
  for (let item of data) {
    let permi = 1
    if (item.readonly) {
      permi = 2
    }
    if (item.hidden) {
      permi = 3
    }
    if (!isCamelCase(item.fieldModelId)) {
      item.fieldModelId = toCamelCase(item.fieldModelId)
    }
    formAuth = {
      ...formAuth,
      [`${item.fieldModelId}_${item.field}`]: permi
    }
  }
  state.formAuth = formAuth
  state.formAuthList = JSON.parse(JSON.stringify(formAuth))
}
/**
 * 处理表单权限相关操作, 开发者在template的v-permi设置中必须使用此函数
 * @param {Object} obj - 包含字段名称的对象
 * @param {Function} [callback] - 可选的回调函数，接收 obj 作为参数
 * 如果传入对象的 fieldName 不在 rootFields 中，则将其添加到 rootFields 数组，然后执行回调函数（如果存在）
 */
function formPermi(obj, callback) {
  if (!state.rootFields[obj.fieldModelId + '__' + obj.field]) {
    state.rootFields[obj.fieldModelId + '__' + obj.field] = obj
  }
  return callback && callback(obj)
}

function toCamelCase(s) {
  return s.toLowerCase().replace(/_(.)/g, function (match, group1) {
    return group1.toUpperCase()
  })
}

function isCamelCase(str) {
  return /^[a-z][a-zA-Z0-9]*$/.test(str)
}
const childRef = ref(null)
function getListData() {
  if (childRef.value) {
    return childRef.value.getListData()
  }
}

// 文件序列号生成函数
function fileSerialNumberBuilder() {
  return 'TEST' + Math.floor(Math.random() * 10000)
}
const statusOptions = [
  { label: '全部', value: '' },
  { label: '草稿', value: '1' },
  { label: '已提交', value: '2' },
  { label: '上级驳回', value: '3' },
  { label: '审批中', value: '4' },
  { label: '已作废', value: '5' },
  { label: '已审批', value: '6' },
  { label: '已驳回', value: '7' }
]
const taskInfo = ref()
function getTaskInfo() {
  return taskInfo.value
}
defineExpose({
  getFormData,
  getSaveFormData,
  getStartFlow,
  submitFlowTask,
  handlerOpenConfirm,
  getTaskInfo
})
onMounted(() => {
  nextTick(() => {
    getFieldForm()
    getListData()
  })
})
</script>

<style lang="scss" scoped>
.bgW {
  height: 100%;
}
</style>
