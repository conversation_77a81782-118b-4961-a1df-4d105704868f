<template>
  <div class="project-header">
    <div class="project-name">检查与整改</div>
    <div class="project-meta">
      <span>项目编码：XXXXXXXX</span>
      <span>项目经理：XXXXXX</span>
    </div>
  </div>
  <div class="project-dashboard">
    <!-- 右侧导航栏 -->
    <div class="sidebar-nav">
      <el-menu
        mode="vertical"
        :default-active="activeTab.toString()"
        @select="scrollToSection"
        class="sidebar-menu"
      >
        <el-menu-item v-for="(tab, index) in tabs" :key="index" :index="index.toString()">
          {{ tab.label }}
        </el-menu-item>
      </el-menu>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
      <!-- 概览卡片 -->
      <section :ref="(el) => setSectionRef(el, 0)" div class="progress-card">
        <div class="card-header">
          <span class="title">概览</span>
        </div>
        <div class="card-body">
          <el-steps :active="activeStep" finish-status="success" align-center>
            <el-step
              v-for="(step, index) in steps"
              :key="index"
              :title="step.title"
              :status="step.status"
            />
          </el-steps>
        </div>
      </section>

      <!-- 设计卡片 -->

      <section :ref="(el) => setSectionRef(el, 1)" class="design-card">
        <div class="card-header">
          <span class="title">设计</span>
        </div>
        <div class="card-body">
          <div class="design-container">
            <!-- 表格部分 -->
            <div class="design-table">
              <sn-crud
                :data="designlistData"
                :option="designoption"
                v-model:page="queryForm.page"
                v-model:search="queryForm.filter"
                @on-load="getPageList"
              ></sn-crud>
            </div>

            <!-- 分隔竖线 -->
            <div class="design-divider"></div>

            <!-- 圆圈指示器部分 -->
            <div class="design-indicators">
              <div class="indicator-item">
                <div class="indicator-label">施工图图纸</div>

                <div class="indicator-circle blue"></div>
                <div class="indicator-value">{{ designStats.total }}</div>
              </div>
              <div class="indicator-item">
                <div class="indicator-label">已提交施工图</div>
                <div class="indicator-circle green"></div>

                <div class="indicator-value">30</div>
              </div>
              <div class="indicator-item">
                <div class="indicator-label">已延期施工图</div>
                <div class="indicator-circle red"></div>

                <div class="indicator-value">3</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 进度卡片 -->
      <section :ref="(el) => setSectionRef(el, 2)" class="card">
        <div class="card-header">
          <span class="title">进度</span>
        </div>
        <div class="progress-comparison">
          <!-- 计划进度条 -->
          <div class="progress-item">
            <div class="progress-title">计划完成百分比</div>
            <el-progress
              :percentage="40"
              :stroke-width="16"
              :text-inside="true"
              status="primary"
              class="custom-progress"
            />
          </div>

          <!-- 实际进度条 -->
          <div class="progress-item">
            <div class="progress-title">实际完成百分比</div>
            <el-progress
              :percentage="30"
              :stroke-width="16"
              :text-inside="true"
              status="success"
              class="custom-progress"
            />
          </div>
        </div>
        <div class="card-body">
          <sn-crud
            :data="progresslistData"
            :option="progressoption"
            v-model:page="queryForm.page"
            v-model:search="queryForm.filter"
          >
          </sn-crud>
        </div>
      </section>

      <!-- 费控卡片 -->
      <section :ref="(el) => setSectionRef(el, 3)" class="card">
        <div class="card-header">
          <span class="title">费控</span>
        </div>
        <div class="card-body">
          <sn-crud :data="costlistData" :option="costoption">
            <!-- 自定义设备购置费列 -->
            <template #equipmentCostValue="{ row }">
              <span>
                {{ row.equipmentCostValue }}
                <span :style="{ color: row.equipmentCostTrend ? 'red' : 'green' }">
                  {{ row.equipmentCostTrend ? '↑' : '↓' }}
                </span>
              </span>
            </template>

            <!-- 建安工程费 - 材料费 -->
            <template #materialCostValue="{ row }">
              <span>
                {{ row.constructionCost.materialCostValue }}
                <span :style="{ color: row.constructionCost.materialCostTrend ? 'red' : 'green' }">
                  {{ row.constructionCost.materialCostTrend ? '↑' : '↓' }}
                </span>
              </span>
            </template>

            <!-- 建安工程费 - 安装费 -->
            <template #installationCostValue="{ row }">
              <span>
                {{ row.constructionCost.installationCostValue }}
                <span
                  :style="{ color: row.constructionCost.installationCostTrend ? 'red' : 'green' }"
                >
                  {{ row.constructionCost.installationCostTrend ? '↑' : '↓' }}
                </span>
              </span>
            </template>

            <!-- 建安工程费 - 建筑工程费 -->
            <template #buildingCostValue="{ row }">
              <span>
                {{ row.constructionCost.buildingCostValue }}
                <span :style="{ color: row.constructionCost.buildingCostTrend ? 'red' : 'green' }">
                  {{ row.constructionCost.buildingCostTrend ? '↑' : '↓' }}
                </span>
              </span>
            </template>
          </sn-crud>
        </div>
      </section>

      <!-- 合同卡片 -->
      <section :ref="(el) => setSectionRef(el, 4)" class="card">
        <div class="card-header">
          <span class="title">合同</span>
        </div>
        <div class="card-body">
          <sn-crud
            :data="contractlistData"
            :option="contractoption"
            v-model:page="queryForm.page"
            v-model:search="queryForm.filter"
          >
          </sn-crud>
        </div>
      </section>
      <!-- 物资卡片 -->
      <section :ref="(el) => setSectionRef(el, 5)" class="card">
        <div class="card-header">
          <span class="title">物资</span>
        </div>
        <div class="flow-chart">
          <!-- 每个步骤和箭头的容器 -->
          <div class="stepwz-container" v-for="(step, index) in stepswz" :key="index">
            <!-- 步骤卡片 -->
            <div class="stepwz">
              <div class="stepwz-header">{{ step.title }}</div>
              <div class="stepwz-content">
                <div v-for="(item, i) in step.items" :key="i" class="stepwz-item">
                  <div class="itemwz-title">{{ item.title }}</div>
                  <div class="itemwz-value">{{ item.value }}</div>
                  <div v-if="item.sub" class="itemwz-sub">{{ item.sub }}</div>
                </div>
              </div>
            </div>
            <!-- 箭头连接线 -->
            <div class="arrow-container" v-if="index < stepswz.length - 1">
              <div class="arrow"></div>
            </div>
          </div>
        </div>
      </section>
      <!-- 质量卡片 -->
      <section :ref="(el) => setSectionRef(el, 6)" class="card">
        <div class="card-header">
          <span class="title">质量</span>
        </div>
        <div class="card-body">
          <sn-crud
            :data="qualitylistData"
            :option="qualityoption"
            v-model:page="queryForm.page"
            v-model:search="queryForm.filter"
          >
          </sn-crud>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { defineOptions } from 'vue'

defineOptions({
  name: 'ProjectBoard'
})
const stepswz = ref([
  {
    title: '需求计划',
    items: [{ title: '需求计划单', value: '10个' }]
  },
  {
    title: '采购',
    items: [{ title: '采购订单', value: '4个' }]
  },
  {
    title: '设备监造',
    items: [
      { title: '监造设备', value: '30个' },
      { title: '已具备发货条件', value: '10个' }
    ]
  },
  {
    title: '项目现场验收',
    items: [
      { title: '乙供材入场报审单', value: '10个' },
      { title: '甲供材现场验收', value: '10个' },
      { title: '验收不通过物资', value: '10个' }
    ]
  },
  {
    title: '项目现场领料',
    items: [
      { title: '领料单', value: '10个' },
      { title: '退料单', value: '1个' }
    ]
  },
  {
    title: '代保管库盘点',
    items: [
      { title: '乙供材', value: '330个' },
      { title: '甲供材', value: '300个' }
    ]
  }
])
const arrows = ref([1, 1, 1, 1, 1]) // 5个箭头

// 导航标签数据
const tabs = [
  { label: '概览', key: 'overview' },
  { label: '设计', key: 'design' },
  { label: '进度', key: 'progress' },
  { label: '费控', key: 'cost' },
  { label: '合同', key: 'contract' }, // 合同管理
  { label: '物资', key: 'materials' }, // 物资管理
  { label: '质量', key: 'quality' } // 质量管理
]

// 响应式数据
const sectionRefs = ref([])
const activeTab = ref(0)

// 动态设置卡片引用
const setSectionRef = (el, index) => {
  if (el) {
    sectionRefs.value[index] = el
  }
}

// 滚动到指定卡片
const scrollToSection = (index) => {
  activeTab.value = index
  const targetEl = sectionRefs.value[index]

  if (targetEl) {
    targetEl.scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    })
  } else {
    console.warn('未找到对应的卡片元素')
  }
}

// 滚动监听处理
const handleScroll = () => {
  tabs.forEach((_, index) => {
    const el = sectionRefs.value[index]
    if (el) {
      const rect = el.getBoundingClientRect()
      const isInViewport = rect.top <= 100 && rect.bottom >= 100
      if (isInViewport) activeTab.value = index
    }
  })
}

// 步骤数据
const steps = ref([
  { title: '储备', status: 'success' },
  { title: '立项', status: 'success' },
  { title: '复合优选', status: 'success' },
  { title: '投决', status: 'success' },
  { title: '开工批复', status: 'success' },
  { title: '现场开工', status: '' },
  { title: '投产', status: '' },
  { title: '达标', status: '' },
  { title: '竣工', status: '' },
  { title: '关闭', status: '' }
])

// 当前激活步骤（已完成步骤数+1）
const activeStep = ref(5) // 5表示前5个已完成

/* //动态更新进度
  const updateProgress = (completedSteps) => {
    activeStep.value = completedSteps;

    // 更新所有步骤状态
    steps.value.forEach((step, index) => {
      step.status = index < completedSteps ? 'success' : '';
    });
  };

  //3秒后更新进度
  setTimeout(() => {
    updateProgress(7); // 完成前7个步骤
  }, 3000); */
//设计数据

let designlistData = ref([
  {
    designtype: '可研',
    isKeyIndicator: '23-NA04921K黑龙江省大兴安岭地区呼玛县电投100MW风电项目23.1.15 (收口).pdf'
  },
  {
    designtype: '初设',
    isKeyIndicator: '23-NA04921K黑龙江省大兴安岭地区呼玛县电投100MW风电项目23.1.15 (收口).pdf'
  }
])
const designStats = ref({
  total: 45,
  reviewed: 30,
  delayed: 3
})
let designoption = ref({
  tip: false,
  dialogType: 'dialog',
  border: true,
  index: false,
  stripe: true,
  menu: false,
  header: false,
  //height: 'auto',
  searchSpan: 6,
  searchIcon: true,

  selection: false,
  showTree: false,
  excelBtn: false,
  importBtn: false,

  column: [
    {
      label: '类型',
      prop: 'designtype',
      width: 80
    },
    {
      label: '收口板',
      prop: 'isKeyIndicator'
    }
  ]
})
//进度数据

// 进度条
const planProgress = ref(40)
const actualProgress = ref(30)
//表格
let progresslistData = ref([])

let progressoption = ref({
  tip: false,
  dialogType: 'dialog',
  border: true,
  index: false,
  stripe: true,
  menu: false,
  header: false,
  height: 'auto',
  searchSpan: 6,
  searchIcon: true,

  selection: false,
  showTree: false,
  excelBtn: false,
  importBtn: false,

  column: [
    {
      label: '里程碑名称',
      prop: 'indicatorName'
    },
    {
      label: '计划日期',
      prop: 'isKeyIndicator'
    },
    {
      label: '实际日期',
      prop: 'isKeyIndicator'
    },
    {
      label: '状态',
      prop: 'isKeyIndicator'
    }
  ]
})

//费控数据
let costlistData = ref([])
costlistData = ref([
  {
    indicatorName: '初设概算',
    equipmentCost: '3010,90↑', // 这里可以直接包含箭头，但推荐分开存储

    equipmentCostValue: '3010,90',
    equipmentCostTrend: true, // 上升
    constructionCost: {
      materialCostValue: '3010,90',
      materialCostTrend: true,
      installationCostValue: '3010,90',
      installationCostTrend: true,
      buildingCostValue: '3010,90',
      buildingCostTrend: true
    },
    otherCost: '3010,90',
    otherCostTrend: true,
    totalInvestment: '3010,90',
    totalInvestmentTrend: true,
    staticInvestment: '3010,90',
    staticInvestmentTrend: true,
    unitKwStaticInvestment: '3010,90',
    unitKwStaticInvestmentTrend: true,
    unitKwDynamicInvestment: '3010,90',
    unitKwDynamicInvestmentTrend: true
  },
  {
    indicatorName: '执行概算',
    equipmentCostValue: '3010,90',
    equipmentCostTrend: true,
    constructionCost: {
      materialCostValue: '3010,90',
      materialCostTrend: false, // 下降
      installationCostValue: '3020,90',
      installationCostTrend: true,
      buildingCostValue: '3010,90',
      buildingCostTrend: true
    },
    otherCost: '3010,90',
    otherCostTrend: true,
    totalInvestment: '3009,90', // 总投资下降
    totalInvestmentTrend: false,
    staticInvestment: '3010,90',
    staticInvestmentTrend: true,
    unitKwStaticInvestment: '3010,90',
    unitKwStaticInvestmentTrend: false, // 下降
    unitKwDynamicInvestment: '3010,90',
    unitKwDynamicInvestmentTrend: true
  },
  {
    indicatorName: '竣工决算',
    equipmentCostValue: '3000,90',
    equipmentCostTrend: false,
    constructionCost: {
      materialCostValue: '3000,90',
      materialCostTrend: false,
      installationCostValue: '3000,90',
      installationCostTrend: false,
      buildingCostValue: '3000,90',
      buildingCostTrend: false
    },
    otherCost: '3000,90',
    otherCostTrend: false,
    totalInvestment: '3000,90',
    totalInvestmentTrend: false,
    staticInvestment: '3000,90',
    staticInvestmentTrend: false,
    unitKwStaticInvestment: '3000,90',
    unitKwStaticInvestmentTrend: false,
    unitKwDynamicInvestment: '3000,90',
    unitKwDynamicInvestmentTrend: false
  }
])

let costoption = ref({
  tip: false,
  dialogType: 'dialog',
  border: true,
  index: false,
  stripe: true,
  menu: false,
  header: false,
  headerAlign: 'center',
  height: 'auto',
  searchSpan: 6,
  searchIcon: true,
  align: 'center',
  selection: false,
  showTree: false,
  excelBtn: false,
  importBtn: false,

  column: [
    {
      label: '',
      prop: 'indicatorName'
    },
    {
      label: '设备购置费',
      prop: 'equipmentCostValue'
    },

    {
      label: '建安工程费',
      children: [
        {
          label: '材料费',
          prop: 'materialCostValue'
        },
        {
          label: '安装费',
          prop: 'installationCostValue'
        },
        {
          label: '建筑工程费',
          prop: 'buildingCostValue'
        }
      ]
    },
    {
      label: '其他费用',
      prop: 'otherCost'
    },
    {
      label: '项目总投资',
      prop: 'totalInvestment'
    },
    {
      label: '静态投资',
      prop: 'staticInvestment'
    },
    {
      label: '单位千瓦静态投资',
      prop: 'unitKwStaticInvestment'
    },
    {
      label: '单位千瓦动态投资',
      prop: 'unitKwDynamicInvestment'
    }
  ]
})

//合同数据
let contractlistData = ref([])

let contractoption = ref({
  tip: false,
  dialogType: 'dialog',
  border: true,
  index: true,
  stripe: true,
  menu: false,
  header: false,
  height: 'auto',
  searchSpan: 6,
  searchIcon: true,

  selection: false,
  showTree: false,
  excelBtn: false,
  importBtn: false,

  column: [
    {
      label: '合同类型',
      prop: 'indicatorName'
    },
    {
      label: '合同编号',
      prop: 'isKeyIndicator'
    },

    {
      label: '合同名称',
      prop: 'isKeyIndicator'
    },
    {
      label: '供应商',
      prop: 'isKeyIndicator'
    },
    {
      label: '合同金额',
      prop: 'isKeyIndicator'
    },
    {
      label: '实际支付金额',
      prop: 'isKeyIndicator'
    },
    {
      label: '付款进度',
      prop: 'isKeyIndicator'
    }
  ]
})
//物资数据
// 流程数据
const processStages = ref([
  {
    name: '需求计划',
    items: [{ type: '需求计划单', count: '10个' }]
  },
  {
    name: '采购',
    items: [
      { type: '采购订单', count: '4个' },
      { type: '已具备发货条件', count: '10个' },
      { type: '验收不通过物资', count: '10个' }
    ]
  },
  {
    name: '设备监造',
    items: [{ type: '监造设备', count: '30个' }]
  },
  {
    name: '项目现场验收',
    items: [
      { type: '乙供材入场报审单', count: '10个' },
      { type: '甲供材现场验收', count: '10个' }
    ]
  },
  {
    name: '项目现场领料',
    items: [
      { type: '领料单', count: '10个' },
      { type: '退料单', count: '1个' }
    ]
  },
  {
    name: '代保管库盘点',
    items: [
      { type: '乙供材', count: '330个' },
      { type: '甲供材', count: '300个' }
    ]
  }
])

//质量数据
let qualitylistData = ref([])

let qualityoption = ref({
  tip: false,
  dialogType: 'dialog',
  border: true,
  index: true,
  stripe: true,
  menu: false,
  header: false,
  height: 'auto',
  searchSpan: 6,
  searchIcon: true,

  selection: false,
  showTree: false,
  excelBtn: false,
  importBtn: false,

  column: [
    {
      label: '单位/子单位工程',
      prop: 'indicatorName'
    },
    {
      label: '质检点数',
      prop: 'isKeyIndicator'
    },
    {
      label: '已检点数',
      prop: 'isKeyIndicator'
    },
    {
      label: '合格数',
      prop: 'isKeyIndicator'
    },
    {
      label: '合格率',
      prop: 'isKeyIndicator'
    },
    {
      label: '验收状态',
      prop: 'isKeyIndicator'
    }
  ]
})

let queryForm = ref({
  page: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  filter: {
    createTime: null
  }
})
let formRules = ref({})

function getPageList() {
  //查询分页列表
  // 查询前处理参数
  const params = handleQueryForm()

  /*  IndustryIndicatorApi.pageList(params).then((res) => {
    listData.value = res.data.dataList
    queryForm.value.page.total = res.data.totalCount
  }) */

  queryForm.value.page.total = 10
}

function handleQueryForm() {
  // 处理参数
  const { pageSize, pageNum } = queryForm.value.page
  const filter = {}
  for (let key in queryForm.value.filter) {
    if (queryForm.value.filter[key]?.length) {
      // sn-curd搜索select会生成$prop的变量
      if (key.startsWith('$')) continue
      filter[key] = queryForm.value.filter[key]
    }
  }
  delete filter.createTime
  if (
    Array.isArray(queryForm.value.filter.createTime) &&
    queryForm.value.filter.createTime?.length === 2
  ) {
    filter['beginCreateTime'] = queryForm.value.filter.createTime[0]
    filter['endCreateTime'] = queryForm.value.filter.createTime[1]
  }
  const searchParams = {
    page: {
      pageSize,
      pageNum
    },
    filter
  }
  return searchParams
}

// 生命周期钩子
onMounted(() => {
  window.addEventListener('scroll', handleScroll)
  // 初始化时确保第一个卡片可见
  /*  if (sectionRefs.value[0]) {
    sectionRefs.value[0].scrollIntoView({ block: 'start' })
  } */
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped>
/*=========================顶部=======================================================================*/
/* 项目信息容器样式 */
.project-header {
  top: 0; /* 固定在顶部 */
  width: 100%; /* 使其宽度充满整个屏幕 */
  z-index: 1000; /* 确保其在其他内容之上 */
  padding: 16px 20px; /* 内边距 */

  background-color: #ffffff; /* 白色背景 */
  margin-bottom: 16px; /* 与下方内容的间距 */
}

/* 项目名称样式 */
.project-name {
  font-size: 18px; /* 较大字号 */
  font-weight: 600; /* 中等加粗 */
  color: #000; /* 黑色文字 */
  margin-bottom: 8px; /* 与下方项目的间距 */
  line-height: 1.4; /* 行高 */
}

/* 项目编码和项目经理样式 */
.project-meta {
  color: #000; /* 黑色文字 */
  line-height: 1.5; /* 行高 */
}

/* 项目编码和项目经理之间的间隔 */
.project-meta span {
  margin-right: 20px; /* 右侧间距 */
}

/* 响应式调整 */
@media (max-width: 768px) {
  .project-header {
    padding: 12px 16px;
  }

  .project-name {
    font-size: 16px;
  }
}
/*=========================下部=======================================================================*/
.project-dashboard {
  position: relative;
  margin-top: 0;
}

.content-area {
  margin-right: 100px; /* 留出侧边栏空间 */
}
/*=========================卡片样式=======================================================================*/
.card {
  background-color: #fff;

  padding: 20px;
  margin-bottom: 20px;
}

.progress-card {
  background-color: #fff;

  padding: 20px;
  margin-bottom: 20px;
}
.design-card {
  background-color: #fff;

  padding: 20px;
  margin-bottom: 20px;
}

.card-header {
  margin-bottom: 20px;
}

.title {
  font-weight: 600;
  color: #000;
}
/*=========================概览step=======================================================================*/
/* 调整步骤条样式 */
:deep(.ep-steps) {
  padding: 0 20px;
}

/* 已完成步骤的样式 */
:deep(.ep-step__head.is-success) {
  color: #67c23a;
  border-color: #67c23a;
}

:deep(.ep-step__title.is-success) {
  color: #67c23a;
}
/* 进行中步骤的样式 */
:deep(.ep-step__head.is-process) {
  color: #bfbf00;
  border-color: #bfbf00;
}

:deep(.ep-step__title.is-process) {
  color: #bfbf00;
}

/* 未完成步骤的样式 */
:deep(.ep-step__head) {
  color: #c0c4cc;
  border-color: #c0c4cc;
}

:deep(.ep-step__title) {
  color: #606266;
}

/* 连接线样式 */
:deep(.ep-step__line) {
  background-color: #c0c4cc;
  top: 15px;
}

/*=========================设计卡片样式=======================================================================*/
/* 设计卡片容器 */
.design-container {
  display: flex;
  gap: 20px;
}

/* 表格部分 */
.design-table {
  flex: 1;
}

/* 分隔竖线 */
.design-divider {
  width: 1px;
  background-color: #e8e8e8;
  margin: 0 10px;
}

/* 指示器容器 - 水平排列 */
.design-indicators {
  display: flex;
  gap: 30px; /* 调整圆圈之间的间距 */
  align-items: center;
  padding: 10px 0;
}

/* 单个指示器项 */
.indicator-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px; /* 调整标签和圆圈之间的间距 */
  width: 100px; /* 固定宽度确保对齐 */
}

/* 彩色圆圈 - 确保数字居中 */
.indicator-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  margin: 0 auto; /* 水平居中 */
}

.blue {
  background-color: #409eff;
  box-shadow: 0 0 10px rgba(64, 158, 255, 0.5);
}

.green {
  background-color: #67c23a;
  box-shadow: 0 0 10px rgba(103, 194, 58, 0.5);
}

.red {
  background-color: #f56c6c;
  box-shadow: 0 0 10px rgba(245, 108, 108, 0.5);
}

/* 数字样式 - 已经居中，无需额外样式 */
.indicator-value {
  font-size: 18px;
  font-weight: bold;
  color: white;
  position: absolute;
  padding-top: 53px;
}

/* 标签样式 - 圆圈正上方 */
.indicator-label {
  font-size: 14px;
  color: #606266;
  text-align: center;
  white-space: nowrap;
  margin-top: 8px; /* 调整标签和圆圈之间的间距 */
}

/*=========================进度卡片样式=======================================================================*/
.progress-comparison {
  display: flex;
  gap: 30px; /* 两个进度条之间的间距 */
  padding: 20px;
}

.progress-item {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.progress-title {
  font-size: 14px;
  color: #000;
  margin-bottom: 8px;
  font-weight: 500;
  margin-right: 20px;
}

.custom-progress {
  width: 60%;
  margin-bottom: 4px;
}

/* 自定义进度条样式 */
:deep(.ep-progress-bar__outer) {
  background-color: #f5f5f5 !important; /* 浅灰色背景 */
  border-radius: 8px !important;
}

:deep(.ep-progress-bar__inner) {
  border-radius: 8px !important;
}

/* 计划进度条颜色 */
:deep(.ep-progress--primary .ep-progress-bar__inner) {
  background-color: #409eff !important; /* 蓝色 */
}

/* 实际进度条颜色 */
:deep(.ep-progress--success .ep-progress-bar__inner) {
  background-color: #67c23a !important; /* 绿色 */
}

/*=========================物资数据=======================================================================*/
.flow-chart {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
}

/* 新增：每个步骤和箭头的容器 */
.stepwz-container {
  width: 100%;
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px; /* 每个步骤之间的间距 */
  z-index: 1;
}

.stepwz {
  width: 100%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
  padding: 10px;
}

.stepwz-header {
  font-weight: bold;
  color: white;
  background: #409eff;
  padding: 5px;
  border-radius: 4px;
  margin-bottom: 10px;
}

.stepwz-content {
  font-size: 14px;
}

.stepwz-item {
  padding: 5px;
  border-bottom: 1px solid #eee;
}

.stepwz-item:last-child {
  border-bottom: none;
}

.itemwz-title {
  color: #666;
}

.itemwz-value {
  color: #333;
  font-weight: bold;
}

.itemwz-sub {
  color: #999;
  font-size: 12px;
}

/* 箭头容器，确保箭头在两个步骤中间 */
.arrow-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px; /* 箭头容器的宽度，与箭头宽度一致 */
}

.arrow {
  width: 30px;
  height: 2px;
  background: #ccc;
  position: relative;
  top: 20px;
}

.arrow::after {
  content: '';
  position: absolute;
  right: -5px;
  top: -3px;
  border-top: 5px solid transparent;
  border-bottom: 5px solid transparent;
  border-left: 5px solid #ccc;
}
/*=========================右侧导航栏=======================================================================*/
.sidebar-nav {
  position: fixed;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  background: #ffffff00;

  padding: 10px 0;
  z-index: 100;
}

.sidebar-menu {
  border: none;
}

.ep-menu-item {
  height: 40px;
  background-color: #f1f2f6 !important;
}
/* 激活状态样式 */
.ep-menu-item.is-active {
  background-color: #e8e8e8 !important;
  color: rgb(0, 0, 0) !important;
  border-left: 3px solid #2196f3; /* 添加3px宽的蓝色左侧边框 */
}

/* 左侧竖线 - 所有菜单项都有 */
.ep-menu-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 1px; /* 竖线宽度 */
  background-color: #b9b8b8;
}
</style>
