<template>
    <div ref="myRef" style="height: 100%;">
        <!-- 左侧菜单 -->
        <el-row :gutter="14" :span="24" style="height: 100%">
            <el-col :span="4" class="tree-container">
                <div>
                    <el-row :span="24">
                        <el-col :span='24' style="margin-top: 10px;">
                            <el-form-item label="" prop="">
                                <el-input v-model="treeSearchKeyword" type="text" placeholder="根据目录名称过滤" clearable>
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span='24'>
                            <el-row :gutter="0">
                                <el-col :span="12" style="display: flex; align-items: center; justify-content: center;">
                                    <span>类型</span>
                                </el-col>
                                <el-col :span="12" style="display: flex; justify-content: right;">
                                    <el-button @click="changeItem(data, 'add')" type="primary">新增</el-button>
                                </el-col>
                            </el-row>
                        </el-col>
                        <el-col :span='24'>
                            <div style="height: calc(100vh - 215px);">
                                <el-scrollbar style="height: calc(100% - 40px);overflow-y: auto;width: 100%;">
                                    <el-tree :data="filteredTreeData" node-key="id" default-expand-all
                                        :props="defaultProps" highlight-current @node-click="handleNodeClick">
                                        <template #default="{ node, data }">
                                            <span class="custom-tree-node">
                                                <template v-if="!data.isEdit">
                                                    <span>{{ node.label }}</span>
                                                    <el-popover placement="bottom" width="100" trigger="click">
                                                        <div class="popover-content">
                                                            <el-button type="text" @click="changeItem(data, 'edit')"
                                                                style="font-size: 14px;  margin-left: 0px !important">编辑</el-button>
                                                            <el-button type="text" @click="deleteItem(data)"
                                                                style="font-size: 14px; margin-left: 0px !important">删除</el-button>
                                                        </div>
                                                        <template #reference>
                                                            <!-- <el-button v-if="data.roleType === 'owner'" type="text"style="margin-left: auto; font-weight: 3000;">···</el-button> -->
                                                            <i class="el-icon-more" style="margin-left: auto;"></i>
                                                        </template>
                                                    </el-popover>
                                                </template>
                                                <!-- <el-input v-else v-model="data.name" @blur="handleInputBlur(data)"
                                                    @keyup.enter="handleInputBlur(data)" :autofocus="data.isEdit"
                                                    ref="inputRef" /> -->
                                            </span>
                                        </template>
                                    </el-tree>
                                </el-scrollbar>
                            </div>
                            <!-- <el-input style="margin-bottom: 15px;"
                        v-model="menuInput" placeholder="请输入新增菜单名称" @change="handleAddInputBlur(menuInput)" /> -->
                        </el-col>
                    </el-row>
                </div>
            </el-col>
            <!-- 右侧-->
            <el-col :span="20" style="height: 100%;">
                <el-form :model="formData" :rules="formRules" ref="formRef" label-width="0px" label-position="right"
                    style="height: 100%;">
                    <el-card shadow="never" style="height: 100%; position: relative; ">
                        <div style="height: 100%; display: flex; flex-direction: column;">
                            <!-- 表单内容 -->
                            <div style="flex: 1; overflow-y: auto; padding-right: 10px;">
                                <el-row :gutter="16" class="title">
                                    <el-col :span="10" style="display: flex; justify-content: center;">
                                        <span>编码内容</span>
                                    </el-col>
                                    <el-col :span="10" style="display: flex; justify-content: center;">
                                        <span>段间分隔</span>
                                    </el-col>
                                    <el-col :span="4" style="display: flex; justify-content: right;">
                                        <el-button @click="addRow" type="primary">
                                            新增
                                        </el-button>
                                    </el-col>
                                </el-row>
                                <el-row v-for="(item, index) in formData" :key="index" :gutter="16">
                                    <el-col :span="10">
                                        <el-form-item label="" prop="codeName">
                                            <el-input v-model="item.codeName" placeholder="请输入编码内容" clearable />
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="10">
                                        <el-form-item label="" prop="codeType">
                                            <el-input v-model="item.codeType" placeholder="请输入段间分隔" clearable />
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="16" style="margin-top: 20px;">
                                    <el-col :span="4" class="number">
                                        <span>数字序列(位数)</span>
                                    </el-col>
                                    <el-col :span="4">
                                        <el-input-number v-model="num" :min="4" :max="7" @change="handleChange" />
                                    </el-col>
                                    <el-col :span="4">
                                        <el-input v-model="numberSequence" disabled />
                                    </el-col>
                                </el-row>
                            </div>

                            <!-- 底部按钮 -->
                            <div class="foot">
                                <el-button @click="resetForm" plain>重置</el-button>
                                <el-button @click="confirmForm" type="primary">确定</el-button>
                            </div>
                        </div>
                    </el-card>
                </el-form>
            </el-col>
        </el-row>
    </div>
</template>


<script>
import EditEncodingComponent from "./components/EditEncodingComponent.vue";
import EncodingApi from '@/project/api/encoding/encoding.js'
import { getToken } from "sn-base-utils";

</script>

<script setup>
import {
    ref,
    getCurrentInstance,
    nextTick,
    onMounted,
    computed
} from 'vue';
const {
    proxy
} = getCurrentInstance();

// let formData = ref({
//     createName: '',

//     createTime: ''
// });
// let treeData = ref([]);
let treeData = ref([
    {
        id: "1",
        name: "项目编码"
    },
    {
        id: "2",
        name: "文档编码"
    },
    {
        id: "3",
        name: "质量管理"
    },
    {
        id: "4",
        name: "安全操作"
    },
    {
        id: "5",
        name: "技术规范"
    },
    {
        id: "6",
        name: "施工标准"
    }
]);

const defaultProps = {
    children: 'children',
    label: 'name'
};

let formData = ref([
    {
        codeName: '',
        codeType: ''
    }
]);
const myRef = ref();
let treeSearchKeyword = ref('');
const numberSequence = ref('9999'); // 默认4位数的最大值
const num = ref(4)

onMounted(() => {
  addMenu()
});

function addMenu() {
  EncodingApi.page().then((res) => {

  });
}

// 过滤菜单名称
const filteredTreeData = computed(() => {
    if (!treeSearchKeyword.value.trim()) {
        return treeData.value;
    }
    const keyword = treeSearchKeyword.value.trim().toLowerCase();
    return treeData.value.filter(node =>
        node.name.toLowerCase().includes(keyword)
    );
});

// 新增编辑类型弹框
async function changeItem(row, option) {
    console.log("option", option)
    console.log("row", row)
    const editType = option;
    // 编辑,新增按钮操作
    //   let editType = row ? "edit" : "add";
    //   let rowInfo = await (editType !== "add" ? EncodingApi.view(row.id) : {});
    //   const formData = editType !== "add" ? rowInfo.data : rowInfo;
    proxy.$DialogForm.show({
        title: editType == "edit" ? "编辑" : "新增",
        type: "dialog",
        width: "60%",
        content: EditEncodingComponent,
        // data: {
        //   formData: formData,
        //   type: editType,
        //   id: row ? row.id : null,
        // },
        callback: (res) => {
            if (res.type && res.type !== 'close') {
                if (res.dialogRefs) {
                    //   res.dialogRefs.submitData().then((flag) => {
                    //     if (flag) {
                    //       // 刷新树形数据
                    //       getTreeData();
                    //       res.close();
                    //     }
                    //   });
                }
            } else {
                res.close();
            }
        }
    });
}

// 新增表单行
function addRow() {
    formData.value.push({
        codeName: '',
        codeType: ''
    });
}

// 数字序列
function handleChange(value) {
    if (value) {
        // 根据位数生成对应的最大数值
        numberSequence.value = '9'.repeat(value);
    }
}

// 重置表单
function resetForm() {
    formData.value = [
        {
            codeName: '',
            codeType: ''
        }
    ];
    num.value = 4;
    numberSequence.value = '9999';
    proxy.$message.success('重置成功');
}

// 确定提交
function confirmForm() {
    // 验证表单数据
    const hasEmptyData = formData.value.some(item =>
        !item.codeName.trim() || !item.codeType.trim()
    );

    if (hasEmptyData) {
        proxy.$message.warning('请填写完整的编码内容和段间分隔');
        return;
    }

    // 提交数据
    const submitData = {
        codeList: formData.value,
        numberDigits: num.value,
        maxNumber: numberSequence.value
    };

    console.log('提交数据:', submitData);
    proxy.$message.success('保存成功');
}

</script>

<style lang="scss" scoped>
.tree-container {
    background-color: #fff;
    border-radius: 4px;
}

.number {
    display: flex;
    align-items: center;
}

.custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
}

.popover-content {
    display: flex;
    flex-direction: column;
    align-items: center;

}

.title {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
}

.foot {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #ebeef5;
}

::v-deep .ep-row {
    margin-left: 0px !important;
}
</style>
