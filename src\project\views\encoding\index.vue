<template>
    <div ref="myRef" style="height: 100%;">
        <!-- 左侧菜单 -->
        <el-row :gutter="14" :span="24" style="height: 100%">
            <el-col :span="4" class="tree-container">
                <div>
                    <el-row :span="24">
                        <el-col :span='24' style="margin-top: 10px;">
                            <el-form-item label="" prop="">
                                <el-input v-model="treeSearchKeyword" type="text" placeholder="根据目录名称过滤" clearable>
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span='24'>
                            <el-row :gutter="0">
                                <el-col :span="12" style="display: flex; align-items: center; justify-content: center;">
                                    <span>类型</span>
                                </el-col>
                                <el-col :span="12" style="display: flex; justify-content: right;">
                                    <el-button @click="changeItem(null, 'add')" type="primary">新增</el-button>
                                </el-col>
                            </el-row>
                        </el-col>
                        <el-col :span='24'>
                            <div style="height: calc(100vh - 215px);">
                                <el-scrollbar style="height: calc(100% - 40px);overflow-y: auto;width: 100%;">
                                    <el-tree :data="filteredTreeData" node-key="id" default-expand-all
                                        :props="defaultProps" highlight-current @node-click="handleNodeClick">
                                        <template #default="{ node, data }">
                                            <span class="custom-tree-node">
                                                <template v-if="!data.isEdit">
                                                    <span>{{ node.label }}</span>
                                                    <el-popover placement="bottom" width="100" trigger="click">
                                                        <div class="popover-content">
                                                            <el-button type="text" @click="changeItem(data, 'edit')"
                                                                style="font-size: 14px;  margin-left: 0px !important">编辑</el-button>
                                                            <el-button type="text" @click="deleteItem(data)"
                                                                style="font-size: 14px; margin-left: 0px !important">删除</el-button>
                                                        </div>
                                                        <template #reference>
                                                            <!-- <el-button v-if="data.roleType === 'owner'" type="text"style="margin-left: auto; font-weight: 3000;">···</el-button> -->
                                                            <i class="el-icon-more" style="margin-left: auto;"></i>
                                                        </template>
                                                    </el-popover>
                                                </template>
                                                <!-- <el-input v-else v-model="data.name" @blur="handleInputBlur(data)"
                                                    @keyup.enter="handleInputBlur(data)" :autofocus="data.isEdit"
                                                    ref="inputRef" /> -->
                                            </span>
                                        </template>
                                    </el-tree>
                                </el-scrollbar>
                            </div>
                            <!-- <el-input style="margin-bottom: 15px;"
                        v-model="menuInput" placeholder="请输入新增菜单名称" @change="handleAddInputBlur(menuInput)" /> -->
                        </el-col>
                    </el-row>
                </div>
            </el-col>
            <!-- 右侧-->
            <el-col :span="20" style="height: 100%;">
                <el-form :model="formData" :rules="formRules" ref="formRef" label-width="0px" label-position="right"
                    style="height: 100%;">
                    <el-card shadow="never" style="height: 100%; position: relative; ">
                        <div style="height: 100%; display: flex; flex-direction: column;">
                            <!-- 表单内容 -->
                            <div style="flex: 1; overflow-y: auto; padding-right: 10px;">
                                <el-row :gutter="16" class="title">
                                    <el-col :span="10" style="display: flex; justify-content: center;">
                                        <span>编码内容</span>
                                    </el-col>
                                    <el-col :span="10" style="display: flex; justify-content: center;">
                                        <span>段间分隔</span>
                                    </el-col>
                                    <el-col :span="4" style="display: flex; justify-content: right;">
                                        <el-button @click="addRow" type="primary">
                                            新增
                                        </el-button>
                                    </el-col>
                                </el-row>
                                <el-row v-for="(item, index) in formData" :key="index" :gutter="16">
                                    <el-col :span="10">
                                        <el-form-item label="" prop="content">
                                            <el-input v-model="item.content" placeholder="请输入编码内容" clearable />
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="10">
                                        <el-form-item label="" prop="paragraphSplitter">
                                            <el-input v-model="item.paragraphSplitter" placeholder="请输入段间分隔" clearable />
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="4" style="display: flex; align-items: center;">
                                        <el-button @click="removeRow(index)" type="danger" size="small" v-if="formData.length > 1">
                                            删除
                                        </el-button>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="16" style="margin-top: 20px;">
                                    <el-col :span="4" class="number">
                                        <span>数字序列(位数)</span>
                                    </el-col>
                                    <el-col :span="4">
                                        <el-input-number v-model="num" :min="4" :max="7" @change="handleChange" />
                                    </el-col>
                                    <el-col :span="4">
                                        <el-input v-model="numberSequence" disabled />
                                    </el-col>
                                </el-row>
                            </div>

                            <!-- 底部按钮 -->
                            <div class="foot">
                                <el-button @click="resetForm" plain>重置</el-button>
                                <el-button @click="confirmForm" type="primary">确定</el-button>
                            </div>
                        </div>
                    </el-card>
                </el-form>
            </el-col>
        </el-row>
    </div>
</template>


<script>
import EditEncodingComponent from "./components/EditEncodingComponent.vue";
import EncodingApi from '@/project/api/encoding/encoding.js'
import { getToken } from "sn-base-utils";

</script>

<script setup>
import {
    ref,
    getCurrentInstance,
    nextTick,
    onMounted,
    computed
} from 'vue';
const {
    proxy
} = getCurrentInstance();

let treeData = ref([]);

const defaultProps = {
    children: 'children',
    label: 'name'
};

let formData = ref([
    {
        paragraphName: '',
        content: '',
        paragraphSplitter: '',
        type: '文本',
        size: ''
    }
]);
const myRef = ref();
let treeSearchKeyword = ref('');
const numberSequence = ref('9999'); // 默认4位数的最大值
const num = ref(4);
const currentSelectedItem = ref(null); // 当前选中的左侧项目
const isEditMode = ref(false); // 是否为编辑模式

onMounted(() => {
  getTreeData();
});

// 获取左侧树形数据
function getTreeData() {
  const params = {
    page: {
      pageNum: 1,
      pageSize: 100
    },
    filter: {
      createName: ""
    }
  };

  EncodingApi.page(params).then((res) => {
    if (res.code === 200 && res.data && res.data.dataList) {
      treeData.value = res.data.dataList.map(item => ({
        id: item.id,
        name: item.projectName,
        projectType: item.projectType,
        projectTypeCode: item.projectTypeCode,
        projectId: item.projectId,
        tenantId: item.tenantId,
        sort: item.sort,
        version: item.version
      }));
    }
  }).catch(error => {
    console.error('获取数据失败:', error);
    proxy.$message.error('获取数据失败');
  });
}

// 过滤菜单名称
const filteredTreeData = computed(() => {
    if (!treeSearchKeyword.value.trim()) {
        return treeData.value;
    }
    const keyword = treeSearchKeyword.value.trim().toLowerCase();
    return treeData.value.filter(node =>
        node.name.toLowerCase().includes(keyword)
    );
});

// 节点点击事件
function handleNodeClick(data) {
    currentSelectedItem.value = data;
    // 根据ID获取详细信息
    EncodingApi.getById(data.id).then((res) => {
        if (res.code === 200 && res.data) {
            const detail = res.data;
            isEditMode.value = true;

            // 更新右侧表单数据
            if (detail.projBizTenderFlowManagementSDtoList && detail.projBizTenderFlowManagementSDtoList.length > 0) {
                formData.value = detail.projBizTenderFlowManagementSDtoList.map(item => ({
                    id: item.id,
                    paragraphName: item.paragraphName || '',
                    content: item.content || '',
                    paragraphSplitter: item.paragraphSplitter || '',
                    type: item.type || '文本',
                    size: item.size || ''
                }));

                // 查找数字序列项
                const digitalItem = detail.projBizTenderFlowManagementSDtoList.find(item => item.type === '数字序列');
                if (digitalItem) {
                    num.value = parseInt(digitalItem.size) || 4;
                    numberSequence.value = digitalItem.content || '9999';
                }
            } else {
                // 如果没有详细数据，重置表单
                resetForm();
            }
        }
    }).catch(error => {
        console.error('获取详细信息失败:', error);
        proxy.$message.error('获取详细信息失败');
    });
}

// 删除项目
function deleteItem(data) {
    proxy.$confirm('确定要删除这个项目吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        EncodingApi.delete([data.id]).then((res) => {
            if (res.code === 200) {
                proxy.$message.success('删除成功');
                getTreeData(); // 刷新列表
                // 如果删除的是当前选中项，清空右侧表单
                if (currentSelectedItem.value && currentSelectedItem.value.id === data.id) {
                    resetForm();
                    currentSelectedItem.value = null;
                    isEditMode.value = false;
                }
            } else {
                proxy.$message.error('删除失败');
            }
        }).catch(error => {
            console.error('删除失败:', error);
            proxy.$message.error('删除失败');
        });
    }).catch(() => {
        // 用户取消删除
    });
}

// 新增编辑类型弹框
async function changeItem(row, option) {
    const editType = option;
    let formData = {};

    if (editType === "edit" && row) {
        // 编辑模式，获取当前数据
        formData = {
            id: row.id,
            projectName: row.name,
            projectType: row.projectType,
            projectTypeCode: row.projectTypeCode,
            projectId: row.projectId,
            tenantId: row.tenantId,
            sort: row.sort,
            version: row.version
        };
    }

    proxy.$DialogForm.show({
        title: editType == "edit" ? "编辑类型" : "新增类型",
        type: "dialog",
        width: "60%",
        content: EditEncodingComponent,
        data: {
            formData: formData,
            type: editType,
            id: row ? row.id : null,
        },
        callback: (res) => {
            if (res.type && res.type !== 'close') {
                if (res.dialogRefs) {
                    res.dialogRefs.submitData().then((flag) => {
                        if (flag) {
                            // 刷新树形数据
                            getTreeData();
                            res.close();
                        }
                    });
                }
            } else {
                res.close();
            }
        }
    });
}

// 新增表单行
function addRow() {
    formData.value.push({
        paragraphName: '',
        content: '',
        paragraphSplitter: '',
        type: '文本',
        size: ''
    });
}

// 数字序列
function handleChange(value) {
    if (value) {
        // 根据位数生成对应的最大数值
        numberSequence.value = '9'.repeat(value);
    }
}

// 重置表单
function resetForm() {
    formData.value = [
        {
            paragraphName: '',
            content: '',
            paragraphSplitter: '',
            type: '文本',
            size: ''
        }
    ];
    num.value = 4;
    numberSequence.value = '9999';
    currentSelectedItem.value = null;
    isEditMode.value = false;
    proxy.$message.success('重置成功');
}

// 确定提交
function confirmForm() {
    if (!currentSelectedItem.value) {
        proxy.$message.warning('请先选择一个项目类型');
        return;
    }

    // 验证表单数据
    const hasEmptyData = formData.value.some(item =>
        !item.content.trim() || !item.paragraphSplitter.trim()
    );

    if (hasEmptyData) {
        proxy.$message.warning('请填写完整的编码内容和段间分隔');
        return;
    }

    // 构建提交数据
    const projBizTenderFlowManagementSDtoList = [...formData.value];

    // 添加数字序列项
    projBizTenderFlowManagementSDtoList.push({
        paragraphName: "数字序列",
        size: num.value.toString(),
        type: "数字序列",
        content: numberSequence.value,
        paragraphSplitter: "-"
    });

    const submitData = {
        id: currentSelectedItem.value.id,
        projectType: currentSelectedItem.value.projectType,
        projectTypeCode: currentSelectedItem.value.projectTypeCode,
        projectId: currentSelectedItem.value.projectId,
        projectName: currentSelectedItem.value.name,
        tenantId: currentSelectedItem.value.tenantId,
        sort: currentSelectedItem.value.sort,
        projBizTenderFlowManagementSDtoList: projBizTenderFlowManagementSDtoList
    };

    // 调用更新接口
    EncodingApi.update(submitData).then((res) => {
        if (res.code === 200) {
            proxy.$message.success('保存成功');
            getTreeData(); // 刷新列表
        } else {
            proxy.$message.error('保存失败');
        }
    }).catch(error => {
        console.error('保存失败:', error);
        proxy.$message.error('保存失败');
    });
}

</script>

<style lang="scss" scoped>
.tree-container {
    background-color: #fff;
    border-radius: 4px;
}

.number {
    display: flex;
    align-items: center;
}

.custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
}

.popover-content {
    display: flex;
    flex-direction: column;
    align-items: center;

}

.title {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
}

.foot {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #ebeef5;
}

::v-deep .ep-row {
    margin-left: 0px !important;
}
</style>
