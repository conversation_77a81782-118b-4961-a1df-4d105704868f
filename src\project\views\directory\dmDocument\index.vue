<template>
  <el-form :model="formData" :rules="formRules" ref="formRef" label-width="0px" label-position="right">
    <el-row :gutter="14" :span="24">
      <el-col :span="4" class="tree-container">
        <div style="height: calc(100vh - 105px);">
          <!-- 添加筛选输入框 -->
            <el-col :span='24' style="margin-top: 10px;">
              <el-form-item label="" prop="">
                <el-input v-model="treeSearchKeyword" type="text" placeholder="根据目录名称过滤" clearable>
                </el-input>
              </el-form-item>
            </el-col>
          <el-scrollbar style="height: calc(100% - 40px);overflow-y: auto;width: 100%;">

            <el-tree :data="filteredTreeData"  :highlight-current="true" :props="treeProps" :show-checkbox="false" node-key="id"
              @node-click="handleNodeClick" v-loading="treeLoading" style="margin-top: 10px;">
              <template #reference>
                <el-button type="text" style="margin-left: auto; font-weight: 3000;">···</el-button>
              </template>
            </el-tree>
          </el-scrollbar>
        </div>
      </el-col>
      <el-col :span="20">
        <sn-crud :data="listData" @node-click="onSelect" :option="option" v-model:page="queryForm.page"
          v-model:search="queryForm.filter" @on-load="getPageList" @search-change="onChangeSearch"
          @search-reset="onResetSearch" @row-del="onDelData" @row-excel="onExportData"
          @selection-change="onSelectionChange" @addBtnHandle="onAddData">
        </sn-crud>
      </el-col>
    </el-row>
  </el-form>

<!--  <el-dialog v-model="dialogVisible" title="文件上传" width="500" @close="handleDialogClose">-->
<!--    <sn-upload :drag="true" :key="componentKey" :limit="20" listType="text" :autoUpload="true" :fileMaxSize="209715200"-->
<!--      @input="handleFileInput" />-->
<!--  </el-dialog>-->
</template>

<script>
import getRoutersApi from "@/project/api/getRouter/getRouter";
import ProjBizDmStgMApi from '@/project/api/dm/ProjBizDmStgM.js'
export const routerConfig = [{
  menuType: "C",
  menuName: "文档目录",
  menuSort: 1,
}, {
  menuType: "F",
  menuName: "查看",
  perms: "show",
  api: [ProjBizDmStgMApi.config.pageList],
}, {
  menuType: "F",
  menuName: "上传文件",
  perms: "add",
  api: [ProjBizDmStgMApi.config.add],
},{
  menuType: "F",
  menuName: "删除",
  perms: "del",
  api: [ProjBizDmStgMApi.config.remove],
}];
</script>

<script setup>
import {
  ref,
  getCurrentInstance,
  onMounted,
  computed
} from 'vue';
const {
  proxy
} = getCurrentInstance()
let treeData = ref([])
const treeRef = ref(null)
let treeProps = ref({
  children: "children",
  label: "label"
});
let option = ref({
  tip: false,
  dialogType: "dialog",
  border: true,
  index: true,
  stripe: true,
  menu: false,
  header: true,
  height: "auto",
  searchSpan: 6,
  searchIcon: true,
  searchMenuSpan: 6,
  searchIndex: 3,
  selection: true,
  showTree: false,
  excelBtn: true,
  delBtn: false,
  editBtn: false,
  addBtn: false,
  delBtns: false,
  delBtnsText: "批量删除",
  addBtnText: "上传文件",
  excelBtnText: "下载",
  customEditBtnText: "编辑",
  customDelBtnText: "删除",
  customViewBtnText: "查看",
  column: [{
    label: "文件编号",
    prop: "fileSerialNumber",
    columnSlot: false,
    searchSlot: false,
    overHidden:true,
    width: 200
  }, {
    label: "文件名称",
    prop: "fileName",
    search: true,
    columnSlot: false,
    searchSlot: false,
    queryType: "LIKE",
    overHidden:true,
  }, {
    label: "提交人",
    prop: "createName",
    columnSlot: false,
    searchSlot: false,
    overHidden:true,
    width: 150
  }, {
    label: "提交单位",
    prop: "createOrgName",
    search: true,
    columnSlot: false,
    searchSlot: false,
    queryType: "LIKE",
    overHidden:true,
  }, {
    label: "提交时间",
    prop: "createTime",
    columnSlot: false,
    searchSlot: false,
    overHidden:true,
    width: 200
  }]
});
// 是否弹窗上传框
let dialogVisible = ref(false)
// 上传组件key,保证重新渲染
const componentKey = ref(0);
let listData = ref([]);
let queryForm = ref({
  page: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  filter: {
    type: "",
    relevantId: null,
    createTime: null
  }
});
// 筛选关键词变量
const treeSearchKeyword = ref('');
// 加载状态
const treeLoading = ref(false);
// 选中的行
let selectedRows = ref([]);
// 保存当前上传的文件列表
const currentFileList = ref([]);

// 目录过滤计算属性
const filteredTreeData = computed(() => {
  const keyword = treeSearchKeyword.value.trim().toLowerCase();
  if (!keyword) {
    return treeData.value;
  }

  // 递归过滤树形节点
  const filterNode = (nodes) => {
    return nodes
      .map(node => {
        const copyNode = { ...node };
        const isMatch = copyNode.label?.toLowerCase().includes(keyword);

        // 如果有子节点，递归过滤
        if (copyNode.children && copyNode.children.length > 0) {
          const filteredChildren = filterNode(copyNode.children);
          if (filteredChildren.length > 0 || isMatch) {
            copyNode.children = filteredChildren;
            return copyNode;
          }
        } else if (isMatch) {
          return copyNode;
        }
        return null;
      })
      .filter(Boolean);
  };

  return filterNode(treeData.value);
});

// 选择某一行
function onSelectionChange(rows) {
  selectedRows.value = rows;
}

// 点击左侧树形节点
function handleNodeClick(e) {
  // console.log("e",e)
  queryForm.value.filter.relevantId = e.id;
  onResetSearch()
}
onMounted(() => {
  getTreeData();
});

// 获取左侧树形数据
function getTreeData() {
  treeLoading.value = true;
  let projectId = sessionStorage.getItem('projectId');
  const params = {
    funcType: "MENU",
    structType: "0",
    extendParams: `{"projectId":"${projectId}"}`
  }
  getRoutersApi.getRouters(params).then((res) => {
    if (res.code === 200 && res.data) {
      // 处理数据为树形结构
      const processedTree = res.data.map(firstLevel => {
        return {
          id: firstLevel.id,
          label: firstLevel.meta.title,  // 一级菜单标题
          type:'', // 文档类型,占位
          // 处理二级菜单（如果有children）
          children: firstLevel.children?.map(secondLevel => ({
            id: secondLevel.id,
            label: secondLevel.meta.title,  // 二级菜单标题
            type:'', // 文档类型,占位
          })) || []  // 若无children则为空数组
        };
      });
      // 赋值给treeData
      treeData.value = processedTree;
    };
  }).catch(err => {
    proxy.$message.error('菜单加载失败');
  }).finally(() => {
    treeLoading.value = false;
  });
}

// 上传文件
function uploadData(e) {
  if (e.length) {
    let addList = [];
    e.forEach((file) => {
      addList.push({
        type: queryForm.value.filter.type,
        relevantId: queryForm.value.filter.relevantId,
        fileId: file.id,
        fileName: file.fileName,
        fileSize: file.fileSize
      });
    });
    ProjBizDmStgMApi.addBatch(addList).then((res) => {
      proxy.$message.success("上传成功");
      // componentKey.value += 1
      getPageList();
    }).catch((err) => {
      proxy.$message.error("上传失败");
    });
  }
  // dialogVisible.value = false
}

// 获取分页列表
function getPageList() {
  //查询分页列表
  // 查询前处理参数
  const params = handleQueryForm();
  if(params?.filter?.type){
    ProjBizDmStgMApi.pageList(params).then((res) => {
      listData.value = res.data.dataList;
      queryForm.value.page.total = res.data.totalCount;
    });
  }else{
    console.log("类型为空")
    listData.value = []
  }

}

// 重置
function onResetSearch() {
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
}

// 构建查询条件
function handleQueryForm() {
  // 处理参数
  const {
    pageSize,
    pageNum
  } = queryForm.value.page;
  const filter = {}
  for (let key in queryForm.value.filter) {
    if (queryForm.value.filter[key]?.length) {
      // sn-curd搜索select会生成$prop的变量
      if (key.startsWith("$")) continue;
      filter[key] = queryForm.value.filter[key];
    }
  }
  delete filter.createTime;
  if (Array.isArray(queryForm.value.filter.createTime) && queryForm.value.filter.createTime?.length === 2) {
    filter["beginCreateTime"] = queryForm.value.filter.createTime[0];
    filter["endCreateTime"] = queryForm.value.filter.createTime[1];
  }
  const searchParams = {
    page: {
      pageSize,
      pageNum,
    },
    filter,
  }
  return searchParams
}

// 点击搜索
function onChangeSearch(params, done) {
  //搜索按钮操作
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
  done && done();
}

// 处理文件输入
function handleFileInput(fileList) {
  currentFileList.value = fileList;
}

// 处理对话框关闭
function handleDialogClose() {
  if (currentFileList.value.length > 0) {
    uploadData(currentFileList.value);
    currentFileList.value = []; // 清空文件列表
  }
}

// 上传文件
function onAddData() {
  if (!queryForm.value.filter.relevantId) {
    proxy.$message.info("请先选择左侧菜单");
    return
  }
  // 重新计算key,保证上传组件重新渲染
  if (componentKey.value > 100) {
    componentKey.value = 0
  } else {
    componentKey.value += 1
  }
  currentFileList.value = []; // 清空之前的文件列表
  dialogVisible.value = true
}

// 批量下载
function onExportData() {
  if (selectedRows.value.length == 0) {
    proxy.$message.info("请勾选要下载的文件！");
    return false;
  }
  let ids = selectedRows.value.map((item) => {
    return item.id;
  });
  proxy.download("/project/dmStg/exportZip", ids, '项目文档.zip');
}
</script>

<style lang="scss" scoped>
.tree-container {
  background-color: #fff;
  border-radius: 4px;
}

::v-deep .ep-row {
  margin-left: 0px !important;
}
</style>
