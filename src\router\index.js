// 测试静态路由
import layoutIndex from '@/common/layoutView/LayoutIndex.vue'

export default [
  // {
  //   path: '/dm/document',
  //   component: () => import('@/project/views/dmDocument/index.vue'),
  //   hidden: false
  // },
  // {
  //   path: '/dm/material',
  //   component: () => import('@/project/views/dmMaterial/index.vue'),
  //   hidden: false
  // },
  // {
  //   path: '/knowledge/laws',
  //   component: () => import('@/project/views/knowledge/laws/index.vue'),
  //   hidden: false
  // },
  // {
  //   path: '/knowledge/institution',
  //   component: () => import('@/project/views/knowledge/institution/index.vue'),
  //   hidden: false
  // },
  // {
  //   path: '/knowledge/standard',
  //   component: () => import('@/project/views/knowledge/standard/index.vue'),
  //   hidden: false
  // },
  {
    path: '/knowledge/experience',
    component: () => import('@/project/views/knowledge/experience/index.vue'),
    hidden: false
  },
  // {
  //   path: '/knowledge/template',
  //   component: layoutIndex,
  //   redirect:"/test",
  //   hidden: true,
  //   children: [
  //     {
  //       path: '/test',
  //       component: () => import('@/project/views/knowledge/template/index.vue'),
  //     },
  //   ]
  // },
  // {
  //   path: '/knowledge/template',
  //   component: () => import('@/project/views/knowledge/template/index.vue'),
  //   hidden: false
  // },
  {
    path: '/scx/test',
    component: () => import('@/project/views/test/test.vue'),
    hidden: false
  },
  {
    path: '/scx/ir/record',
    component: () => import('@/project/views/constructionManagement/InspectionAndRectification/irRecord'),
    hidden: false
  },
  {
    path: '/scx/ir/add',
    component: () => import('@/project/views/constructionManagement/InspectionAndRectification/irRecord/components/EditProjBizIrRecordM.vue'),
    hidden: false
  },
  {
    path: '/sceneStartWork',
    component: layoutIndex,
    hidden: true,
    children: [
      {
        path: '/sceneStartWork/sceneStartWorkReport:reportNumber',
        component: () => import('@/project/views/constructionManagement/sceneStartWork/sceneStartWorkReport/components/EditProjBizConsStartWorkReport.vue')
      },
    ]
  },
  {
    path: '/sceneStartWork',
    component: layoutIndex,
    hidden: true,
    children: [
      {
        path: '/sceneStartWork/sceneStartWorkCommand:commandNumber',
        component: () => import('@/project/views/constructionManagement/sceneStartWork/sceneStartWorkCommand/components/EditProjBizConsStartWorkCommand.vue')
      }
    ]
  }
]
