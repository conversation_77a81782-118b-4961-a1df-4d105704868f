<template>
  <el-form :model="formData" :rules="formRules" ref="formRef" label-width="0px" label-position="right">
    <el-row :gutter="14" :span="24">
      <el-col :span="4" class="tree-container">
        <div>
          <el-tree :data="treeData" :highlight-current="true" :props="treeProps" :show-checkbox="false" node-key="id" @node-click="handleNodeClick">
          </el-tree>
        </div>
      </el-col>
      <el-col :span="20">
        <sn-crud :data="listData" :option="option" v-model:page="queryForm.page" v-model:search="queryForm.filter" @on-load="getPageList" @search-change="onChangeSearch" @search-reset="onResetSearch" @addBtnHandle="onAddData" @row-del="onDelData" @row-excel="onExportData" @selection-change="onSelectionChange">
          <template #menu="{ row, size }">
            <el-button type="primary" :size="size" icon="el-icon-download" link @click="onDownData(row)">下载</el-button>
            <el-button type="danger" :size="size" icon="el-icon-delete" link @click="onDelData([row])">删除</el-button>
          </template>
          <template #secondaryUnitIdSearch = "{ row, size }">
            <el-select v-model="queryForm.filter.secondaryUnitId" clearable placeholder="请选择">
              <el-option v-for="(item, index) in secondaryUnit" :key="index" :label="item.label" :value="item.id"></el-option>
            </el-select>
          </template>
        </sn-crud>
      </el-col>
    </el-row>
  </el-form>

  <el-dialog
      v-model="dialogVisible"
      title="文件上传"
      width="500"
  >
    <sn-upload :drag="true" :key="componentKey" :limit="1" listType="text" :autoUpload="true" :fileMaxSize="209715200" @input="(fileList) => uploadData(fileList, '')" />
  </el-dialog>
</template>

<script>
import OtherApi from "@/project/api/other/Other";
import ProjBizDmStgMApi from "@/project/api/dm/ProjBizDmStgM";
import {formatBytes} from "@/common/utils/Bytes"

export const routerConfig = [{
    menuType: "C",
    menuName: "模板",
  }, {
    menuType: "F",
    menuName: "查看",
    perms: "show",
    api: [ProjBizDmStgMApi.config.pageList],
  }, {
    menuType: "F",
    menuName: "新增",
    perms: "add",
    api: [ProjBizDmStgMApi.config.add],
  }, {
    menuType: "F",
    menuName: "修改",
    perms: "update",
    api: [ProjBizDmStgMApi.config.update, ProjBizDmStgMApi.config.view],
  }, {
    menuType: "F",
    menuName: "删除",
    perms: "del",
    api: [ProjBizDmStgMApi.config.remove],
  }];
</script>

<script setup>
import {
  ref,
  getCurrentInstance
} from 'vue';

const {
  proxy
} = getCurrentInstance()
let treeData = ref([{
  id: "T0010",
  label: "通用",
  children: []
}, {
  id: "T0020",
  label: "风电",
  children: [
    {
      id: "T0021",
      label: "路上风电",
      children: []
    },
    {
      id: "T0022",
      label: "海上风电",
      children: []
    }
  ]
}, {
  id: "T0030",
  label: "光伏",
  children: []
}]);
let treeProps = ref({
  children: "children",
  label: "label"
});
let option = ref({
  tip: false,
  dialogType: "dialog",
  border: true,
  index: true,
  stripe: true,
  menu: true,
  header: true,
  height: "auto",
  searchSpan: 6,
  searchIcon: true,
  searchMenuSpan: 6,
  searchIndex: 3,
  selection: true,
  excelBtn: true,
  excelBtnText:"下载",
  delBtn: false,
  editBtn: false,
  delBtns: true,
  // importBtn:true,
  // importBtnText:"上传文件",
  delBtnsText: "批量删除",
  addBtnText: "上传文件",
  customEditBtnText: "编辑",
  customDelBtnText: "删除",
  customViewBtnText: "查看",
  column: [{
    label: "文件名称",
    prop: "fileName",
    search: true,
    columnSlot: false,
    searchSlot: false,
    queryType: "LIKE"
  },{
    label: "提交单位",
    prop: "createOrgName",
    search: true,
    columnSlot: false,
    searchSlot: false,
    queryType: "LIKE"
  },{
    label: "二级单位",
    prop: "secondaryUnitId",
    search: true,
    columnSlot: false,
    searchSlot: true,
    hide: true
  },{
    label: "提交时间",
    prop: "createTime",
    columnSlot: false,
    searchSlot: false
  }, {
    label: "文件大小",
    prop: "fileSize",
    columnSlot: false,
    searchSlot: false
  }]
});
let listData = ref([]);
let queryForm = ref({
  page: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  filter: {
    type: "template",
    relevantId: null,
    createTime: null
  }
});
// form规则
let formRules = ref({});
// 是否弹窗上传框
let dialogVisible = ref(false)
// form数据
let formData = ref({})
// 上传组件key,保证重新渲染
const componentKey = ref(0);
// 选中的行
let selectedRows = ref([]);
// 二级单位
let secondaryUnit = ref([])

/**
 * 选择某一行
 * @param rows
 */
function onSelectionChange(rows){
  selectedRows.value = rows;
}

/**
 * 上传文件
 * @param e
 */
function uploadData(e){
  if (e.length) {
    let fileId = e[0].id
    let fileName = e[0].fileName
    let fileSize = e[0].fileSize
    ProjBizDmStgMApi.add({
      type:queryForm.value.filter.type,
      relevantId:queryForm.value.filter.relevantId,
      fileId:fileId,
      fileName:fileName,
      fileSize:fileSize
    }).then((res)=>{
      getPageList()
    })
  }
  // dialogVisible.value = false
}

/**
 * 查询二级单位数据
 */
OtherApi.getSecondaryUnit().then((res) => {
  if (res.code === 200 && res.data) {
    let newArr = []
    res.data.forEach((v) => {
      v.lable = v.orgName
      newArr.push({
        id:v.id,
        label:v.orgName,
        children:[]
      })
    })
    secondaryUnit.value = newArr
  }
})

/**
 * 点击左侧树形节点
 * @param e
 */
function handleNodeClick(e){
  if (e.children === undefined || e.children.length === 0) {
    let filter = queryForm.value.filter
    filter.relevantId = e.id
    queryForm.value.filter = filter
    onResetSearch()
  }
}

function getPageList() {
  const params = handleQueryForm();
  ProjBizDmStgMApi.pageList(params).then((res) => {
    listData.value = res.data.dataList;
    listData.value.forEach((v) => {
      v.fileSize = formatBytes(v.fileSize)
    })
    queryForm.value.page.total = res.data.totalCount;
  });
}

/**
 * 重置
 */
function onResetSearch() {
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
}

/**
 * 构建查询条件
 * @returns {{filter: {}, page: {pageSize: number, pageNum: number}}}
 */
function handleQueryForm() {
  // 处理参数
  const {
    pageSize,
    pageNum
  } = queryForm.value.page;
  const filter = {}
  for (let key in queryForm.value.filter) {
    if (queryForm.value.filter[key]?.length) {
      // sn-curd搜索select会生成$prop的变量
      if (key.startsWith("$")) continue;
      filter[key] = queryForm.value.filter[key];
    }
  }
  delete filter.createTime;
  if (Array.isArray(queryForm.value.filter.createTime) && queryForm.value.filter.createTime?.length === 2) {
    filter["beginCreateTime"] = queryForm.value.filter.createTime[0];
    filter["endCreateTime"] = queryForm.value.filter.createTime[1];
  }
  return {
    page: {
      pageSize,
      pageNum,
    },
    filter,
  }
}

/**
 * 点击搜索
 * @param params
 * @param done
 */
function onChangeSearch(params, done) {
  //搜索按钮操作
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
  done && done();
}

/**
 * 上传文件
 */
function onAddData() {
  if (!queryForm.value.filter.relevantId) {
    proxy.$message.info("请先选择左侧菜单");
    return
  }
  // 重新计算key,保证上传组件重新渲染
  if (componentKey.value > 100) {
    componentKey.value = 0
  }else{
    componentKey.value += 1
  }
  dialogVisible.value = true
}

/**
 * 下载文件
 * @param row
 */
function onDownData(row){
  proxy.download("/project/dmStg/exportFile", [row.id], row.fileName);
}

/**
 * 批量下载
 * @returns {boolean}
 */
function onExportData() {
  if (selectedRows.value.length == 0) {
    proxy.$message.info("请勾选要下载的文件！");
    return false;
  }
  let ids = selectedRows.value.map((item) => {
    return item.id;
  });
  proxy.download("/project/dmStg/exportZip", ids, '资料.zip');
}

/**
 * 删除数据
 * @param rows
 * @returns {boolean}
 */
function onDelData(rows) {
  //删除行
  if (!rows || rows.length == 0) {
    proxy.$message.info("请勾选数据！");
    return false;
  }
  let ids = rows.map((item) => {
    return item.id;
  });
  proxy.$modal.confirm("确认删除数据项？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    ProjBizDmStgMApi.remove(ids).then((res) => {
      proxy.$message.success("已删除");
      getPageList();
    });
  }).catch(() => {});
}


</script>

<style lang="scss" scoped>

.tree-container {
  background-color: #fff;
  border-radius: 4px;
  max-height: 100vh;
  overflow-y: auto;
}
</style>
