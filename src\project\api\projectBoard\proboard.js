import { request, replaceUrl } from 'sn-base-utils'
import { huizhiServiceCode, screenServiceCode } from '@/config'
export default class ProjBoardApi {
  static config = {
    view: {
      url: '/project/designManagement/getDesignFileList?projectId={id}',
      method: 'GET'
    },
    getContractList: {
      url: '/template/contract/getContractList/{id}',
      method: 'GET'
    },
    getmaterialList: {
      url: '/material/project/board/{id}',
      method: 'GET'
    },
    getmanufacturingList: {
      url: '/template/manufacturing/list',
      method: 'POST'
    },
    getlistQualityProjectSum: {
      url: '/lituo/qualityAcceptance/listQualityProjectSum/{id}',
      method: 'GET'
    },
    getProjectPhase: {
      url: '/screen/qualityAcceptance/getProjectPhase/{id}',
      method: 'GET'
    }
  }

  static view(id) {
    return request({
      url: replaceUrl(this.config.view.url, { id }),
      method: this.config.view.method
    })
  }

  static getContractList(id) {
    return request({
      url: replaceUrl(this.config.getContractList.url, { id }),
      method: this.config.getContractList.method,
      requestPrefix: huizhiServiceCode
    })
  }

  static getmaterialList(id) {
    return request({
      url: replaceUrl(this.config.getmaterialList.url, { id }),
      method: this.config.getmaterialList.method,
      requestPrefix: huizhiServiceCode
    })
  }

  static getmanufacturingList(id) {
    return request({
      url: this.config.getmanufacturingList.url,
      method: this.config.getmanufacturingList.method,
      data: { projectId: id },
      requestPrefix: huizhiServiceCode
    })
  }

  static getlistQualityProjectSum(id) {
    return request({
      url: replaceUrl(this.config.getlistQualityProjectSum.url, { id }),
      method: this.config.getlistQualityProjectSum.method
    })
  }

  static getProjectPhasem(id) {
    return request({
      url: replaceUrl(this.config.getProjectPhase.url, { id }),
      method: this.config.getProjectPhase.method,
      requestPrefix: screenServiceCode
    })
  }
}
