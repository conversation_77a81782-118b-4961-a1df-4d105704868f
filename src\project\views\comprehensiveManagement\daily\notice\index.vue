<template>
  <div>
  <sn-crud :data="listData" :option="option" v-model:page="queryForm.page" v-model:search="queryForm.filter" @on-load="getPageList" @search-change="onChangeSearch" @search-reset="onResetSearch" @addBtnHandle="onEditData">
    <template #menu="{ row, index, size }">
      <el-button type="primary" :size="size" icon="el-icon-edit" link @click="onEditData(row, 'edit')" v-if="(row.approvalStatus === 1 || row.approvalStatus === 7)">编辑</el-button>
      <el-button type="danger" :size="size" icon="el-icon-edit" link @click="operateFlow(row,'revoke')" v-if="row.approvalStatus === 2">撤回</el-button>
      <el-button type="primary" :size="size" icon="el-icon-download" link @click="onDownData(row)">下载</el-button>
      <el-button type="danger" :size="size" icon="el-icon-delete" link @click="onDelData([row])" v-if="row.approvalStatus === 1 && !row.procInstanceId">删除</el-button>
      <el-button type="danger" :size="size" icon="el-icon-edit" link @click="operateFlow(row,'stop')" v-if="(row.approvalStatus === 1 || row.approvalStatus === 7) && row.procInstanceId">作废</el-button>
    </template>
    <template #menuLeft="{ row, index, size }">
      <el-button type="primary" :size="size" icon="el-icon-plus" v-if="hasPermi('add')" @click="onEditData">新增</el-button>
    </template>
    <template #bizNum="{ row }">
      <el-tooltip class="item" effect="dark" :content="row.bizNum" placement="top">
        <el-link type="primary" @click="onEditData(row,'view')" class="text-overflow">{{row.bizNum}}</el-link>
      </el-tooltip>
    </template>
    <template #title="{ row }">
      <el-tooltip class="item" effect="dark" :content="row.title" placement="top">
        <div class="text-overflow">{{ row.title }}</div>
      </el-tooltip>
    </template>
  </sn-crud>
    <div ref="myRef"></div>
  </div>
</template>

<script>
import ProjBizDailyNoticeMApi from '@/project/api/comprehensiveManagement/daily/ProjBizDailyNoticeM.js'
  import EditProjBizDailyNoticeM from "./components/EditProjBizDailyNoticeM.vue";
  import { getToken } from "sn-base-utils";
  export const routerConfig = [{
    menuType: "C",
    menuName: "通知公告",
    menuSort: 100, // 菜单排序, 初始化菜单使用
  }, {
    menuType: "F",
    menuName: "查看",
    perms: "show",
    api: [ProjBizDailyNoticeMApi.config.pageList],
  }, {
    menuType: "F",
    menuName: "新增",
    perms: "add",
    api: [ProjBizDailyNoticeMApi.config.add],
  }, {
    menuType: "F",
    menuName: "修改",
    perms: "update",
    api: [ProjBizDailyNoticeMApi.config.update, ProjBizDailyNoticeMApi.config.view],
  }, {
    menuType: "F",
    menuName: "删除",
    perms: "del",
    api: [ProjBizDailyNoticeMApi.config.remove],
  },{
    menuType: "F",
    menuName: "撤回",
    perms: "retract",
    api: [ProjBizDailyNoticeMApi.config.retract],
  },{
    menuType: "F",
    menuName: "作废",
    perms: "cancel",
    api: [ProjBizDailyNoticeMApi.config.cancel],
  }];
</script>

<script setup>
import {
  ref,
  getCurrentInstance
} from 'vue';
import { hasPermi } from 'sn-base-utils'
const {
  proxy
} = getCurrentInstance()
let option = ref({
  tip: false,
  dialogType: "page",
  border: true,
  index: true,
  stripe: true,
  menu: true,
  header: true,
  height: "auto",
  searchSpan: 6,
  searchIcon: true,
  searchMenuSpan: 6,
  searchIndex: 3,
  selection: true,
  showTree: false,
  excelBtn: false,
  delBtn: false,
  editBtn: false,
  addBtn: false,
  delBtns: false,
  delBtnsText: "批量删除",
  addBtnText: "新增",
  customEditBtnText: "编辑",
  customDelBtnText: "删除",
  customViewBtnText: "查看",
  column: [{
    label: "业务编号",
    prop: "bizNum",
    solt: true,
    width: 200
  }, {
    label: "标题",
    prop: "title",
    search: true,
    queryType: "LIKE"
  }, {
    label: "类型",
    prop: "type",
    search: true,
    width: 100,
    queryType: "EQ",
    dictDatas: "noticeType",
    type: "select",
    dicUrl: "/system/dict/data/type/noticeType",
    dicMethod: "get",
    props: {
      label: "dictLabel",
      value: "dictValue"
    }
  }, {
    label: "审批状态",
    prop: "approvalStatus",
    search: true,
    width: 100,
    align: "center", //对齐方式：居中
    type: 'select',
    dicUrl: "/system/dict/data/type/global_biz_flow_status",
    dicMethod: "get",
    props: {
      label: "dictLabel",
      value: "dictValue"
    },
    html: true,
    formatter: (val, value) => {
      value = value + ''
      if(value === '1'){
        return `<span style="color:#4871C0">草稿</span>`;
      } else if(value === '2'){
        return `<span style="color:#4871C0">已提交</span>`;
      } else if(value === '4'){
        return `<span style="color:#4871C0">审批中</span>`;
      } else if(value === '3'){
        return `<span style="color:red">上级驳回</span>`;
      } else if(value === '5'){
        return `<span style="color:red">已作废</span>`;
      } else if(value === '6'){
        return `<span style="color:dodgerblue">已审批</span>`;
      } else if(value === '7'){
        return `<span style="color:red">已驳回</span>`;
      }
      return `<p></p>`;
    }
  }]
});
let listData = ref([]);
let queryForm = ref({
  page: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  filter: {
    createTime: null
  }
});
const myRef = ref(null);
let formRules = ref({});

function getPageList() {
  //查询分页列表
  // 查询前处理参数
  const params = handleQueryForm();
  ProjBizDailyNoticeMApi.pageList(params).then((res) => {
    listData.value = res.data.dataList;
    queryForm.value.page.total = res.data.totalCount;
  });
}

function onResetSearch() {
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
}

function handleQueryForm() {
  // 处理参数
  const {
    pageSize,
    pageNum
  } = queryForm.value.page;
  const filter = {}
  for (let key in queryForm.value.filter) {
    if (queryForm.value.filter[key]?.length) {
      // sn-curd搜索select会生成$prop的变量
      if (key.startsWith("$")) continue;
      filter[key] = queryForm.value.filter[key];
    }
  }
  filter.projectId = sessionStorage.getItem('projectId')
  delete filter.createTime;
  if (Array.isArray(queryForm.value.filter.createTime) && queryForm.value.filter.createTime?.length === 2) {
    filter["beginCreateTime"] = queryForm.value.filter.createTime[0];
    filter["endCreateTime"] = queryForm.value.filter.createTime[1];
  }
  const searchParams = {
    page: {
      pageSize,
      pageNum,
    },
    filter,
  }
  return searchParams
}

function onChangeSearch(params, done) {
  //搜索按钮操作
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
  done && done();
}
async function onEditData(row,option) {
  //编辑,新增按钮操作
  let editType = row?.procInstanceId ? 'view' : (row?.id ? 'edit' : 'add');
  if (option) {
    editType = option;
  }
  let rowInfo = await (editType !== "add" ? ProjBizDailyNoticeMApi.view(row.id) : {});
  const formData = editType !== "add" ? rowInfo.data : rowInfo;
  //编辑,新增按钮操作
  proxy.$DialogForm.show({
    title: editType == "edit" ? "编辑" : (editType == "view" ? "查看" : "新增"),
    type: 'page',
    width: "80%",
    el: myRef.value,
    content: EditProjBizDailyNoticeM,
    data: editType !== "add" ? {
      formData: rowInfo.data,
      isShowCloseBtn: false,
      approvalOption: ref({
        isShowApprovalList: true,
        isShowFlowDiagram: true,
        procInstId: rowInfo.data.procInstanceId
      }),
      type: editType,
      el: myRef.value,
      closeDialog: closeDialog,
    } : {
      approvalOption: ref({
        isShowApprovalList: false,
        isShowFlowDiagram: false,
        procInstId: null
      }),
      type: editType,
      el: myRef.value,
      closeDialog: closeDialog,
    },
    option: {
      submitBtn: false,
      emptyBtn: false,
      submitText: '保存',
      emptyText: '关闭',
      extendButton: generateButtons(editType, row)
    },
    callback: (res) => {
      let vm = res.dialogRefs
      if (vm) {
        let formData = vm.getFormData();
        switch (res.type) {
          //直接取消
          // 取消
          case 'cancelDirect':
            getPageList();
            res.close();
            break;
          // 取消
          case 'cancel':
            proxy.$modal.confirm("确认关闭？", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }).then(() => {
              getPageList();
              res.close();
            }).catch(() => {});
            break;
          // 保存
          case 'save':
            if(!formData.approvalStatus){
              formData.approvalStatus = '1'
            }
            vm.getSaveFormData(formData).then(() => {
              proxy.$message.success("保存成功");
              getPageList();
              // res.close();
            })
            break;
          // 编辑
          case 'edit':
            res.close();
            onEditData(row)
            break;
          // 发起流程
          case 'afterProcessIsInitiated':
            let startProcDto = {
              businessKey: null,
              clientId: null
            };
            if(formData.procInstanceId){
              let taskInfo = vm.getTaskInfo();
              vm.handlerOpenConfirm(taskInfo,res);
              getPageList();
            }else {
              vm.getStartFlow(formData, startProcDto).then(() => {
                let taskInfo = vm.getTaskInfo();
                vm.handlerOpenConfirm(taskInfo,res);
                getPageList();
              })
            }
            break;
          //提交流程
          //提交流程
          case 'submitFlowTaskProcess':
            vm.submitFlowTask()
            break;
        }
      }
    }
  });
}
function closeDialog(e) {
  getPageList();
  e.close()
}
function generateButtons(editType, row) {
  if (editType !== "view") {
    const hasProcId = row?.procInstanceId;
    const buttons = [{ key: 'cancel', text: '关闭', buttonType: '', icon: 'el-icon-close' }];
    if (hasProcId) {
      buttons.push({
        key: 'save',
        text: '保存',
        buttonType: 'primary',
        icon: 'el-icon-plus'
      }, {
        key: 'submitFlowTaskProcess',
        text: '提交',
        buttonType: 'primary',
        icon: 'el-icon-check'
      });
    } else {
      buttons.push({
        key: 'save',
        text: '保存',
        buttonType: 'primary',
        icon: 'el-icon-plus'
      }, {
        key: 'afterProcessIsInitiated',
        text: '发起流程',
        buttonType: 'primary',
        icon: 'el-icon-check'
      });
    }
    return buttons;
  }
  return row.approvalStatus != '1' ? [
    { key: 'cancelDirect', text: '关闭', buttonType: '', icon: 'el-icon-close' }
  ] : [
    { key: 'cancelDirect', text: '关闭', buttonType: '', icon: 'el-icon-close' },
    { key: 'edit', text: '编辑', buttonType: 'primary', icon: 'el-icon-edit' }
  ];
}
function operateFlow(row, type) {
  let typeName;
  if(type === 'revoke'){
    typeName = '撤回';
  }else if(type === 'stop'){
    typeName = '作废';
  }
  proxy.$modal.confirm("确认要"+typeName+"流程？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    ProjBizDailyNoticeMApi.operateFlow({
      id:row.id,
      operateFlowStatus:type,
      approvalStatus: row.approvalStatus
    }).then((res) => {
      proxy.$message.success("已"+typeName);
      // 重新请求接口刷新列表
      getPageList()
    });
  }).catch(() => {});
}
function onDelData(rows) {
  //删除行
  if (!rows || rows.length == 0) {
    proxy.$message.info("请勾选数据！");
    return false;
  }

  let ids = rows.map((item) => {
    return item.id;
  });
  proxy.$modal.confirm("确认删除数据项？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    ProjBizDailyNoticeMApi.remove(ids).then((res) => {
      proxy.$message.success("已删除");
      getPageList();
    });
  }).catch(() => {});
}
/**
 * 下载文件
 * @param row
 */
function onDownData(row){
  proxy.download("/daily/notice/exportZip", [row.id], "通知公告"+".zip");
}
</script>

<style lang="scss" scoped>
.text-overflow {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>

