.ep-picker-panel {
  border: 1px solid #01333d;
  background: #01333d;
}

.ep-tabs--card > .ep-tabs__header .ep-tabs__nav {
  border-radius: 0;
}
.ep-tabs__item {
  height: 40px;
  line-height: 40px;
  color: #dbe9f1;
}
.ep-tabs__item.is-active {
  font-weight: normal;
  color: #fff;
}

.ep-tree {
  background: transparent;
  .ep-tree-node__content {
    border-bottom: 1px solid rgba(216, 216, 216, 0.15);
  }
  .ep-tree-node.is-current > .ep-tree-node__content {
    color: #dbe9f1;
    background-color: #00a7db;
  }
}
.ep-radio__inner::after {
  width: 8px;
  height: 8px;
  background: #00a7db;
}
.ep-radio__input.is-checked .ep-radio__inner {
  background: #fff;
  box-shadow: none;
}

.ep-table {
  background: transparent;
}
.ep-table .ep-table__row {
  background: rgba(1, 51, 61, 0.4);
}
.ep-table .ep-table__row--striped {
  background: rgba(1, 51, 61, 0.7);
}
.ep-table--striped .ep-table__body tr.ep-table__row--striped td.ep-table__cell {
  background: transparent;
}
.ep-table th.ep-table__cell.is-leaf,
.ep-table td.ep-table__cell {
  border: none;
}
.ep-table::before,
.ep-table--group::after,
.ep-table--border::after {
  background: none;
}
.ep-table__fixed::before,
.ep-table__fixed-right::before {
  display: none !important;
}

.ep-cascader-menu {
  border: none;
}
.ep-cascader__dropdown {
  background: #01333d;
  border: 1px solid #01333d;
}

.ep-cascader-node:not(.is-disabled):hover,
.ep-cascader-node:not(.is-disabled):focus {
  background: #06475a;
}
.ep-pagination .btn-prev,
.ep-pagination .btn-next {
  background: #01333d;
  color: #fff;
}
.ep-pager li {
  background: #01333d;
  color: #fff;
  margin: 0 5px;
}
.btn-next:disabled {
  background: #01333d;
}
.ep-message-box {
  background-color: #01333d;
  border: 1px solid #01333d;
}
.ep-dialog__header {
  background: #0a5d74;
  border-bottom: 1px solid #0a5d74;
}
.ep-dialog__title {
  color: #fff;
  position: relative;
  padding-left: 10px;
}
.ep-dialog__title::after {
  content: '';
  width: 3px;
  height: 14px;
  margin-top: -7px;
  background: #00a7db;
  position: absolute;
  left: 0;
  top: 50%;
}
.ep-dialog__footer {
  border-top: 1px solid #0a5d74;
  background: #0a5d74;
}
.ep-dialog {
  background: #06404e;
}

.ep-form-item__label {
  padding: 0;
  padding-right: 10px;
}

.ep-select {
  width: 100%;
}

.ep-tree-node__content {
  height: 40px;
}

.sp-dialog__footer {
  .ep-button + .ep-button {
    margin-left: 16px !important;
  }
}

.ep-submenu .ep-submenu__icon-arrow {
  font-size: 14px;
}
#menu-container .org-tree {
  background-color: transparent !important;
}
#menu-container .org-contain {
  background-color: transparent !important;
  border: 1px solid #0a5d74 !important;
}
.goback-block {
  background: #01333d !important;
  border: 1px solid #01333d !important;
  border-radius: 4px;
  padding: 0 10px;
  margin-right: 10px;
  cursor: pointer;
}
.goback-block .ep-button {
  color: #fff !important;
}
.ep-drawer__header {
  border-bottom: 1px solid #dcdfe6 !important;
}
.ep-pagination__total,
.ep-form-item__label,
.ep-pagination__jump {
  color: #fff;
}
.sp-dialog .ep-dialog__header,
.sp-dialog .ep-drawer__header {
  border: none;
}
.sp-dialog .ep-dialog__title,
.sp-dialog .ep-drawer__header {
  color: #fff !important;
}
.tags-view-container .tags-view-item {
  border: 1px solid rgba(1, 51, 61, 0.56) !important;
  color: #a1c0ca !important;
  background: rgba(1, 51, 61, 0.56) !important;
}
.tags-view-container .tags-view-item.active {
  background-color: rgba(1, 51, 61, 0.8) !important;
  border-color: rgba(1, 51, 61, 0.8) !important;
}

.app-main {
  background: url('./bj.png');
  background-size: 100% 100%;
  width: 100%;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  height: 100%;
  overflow: hidden;
}
.main_contation {
  width: 100%;
}

#qiankunContainer > div {
  height: 100%;
}

.ep-descriptions .is-bordered .ep-descriptions-item__cell {
  border: 1px solid #305e6a !important;
  background: rgba(1, 51, 61, 1) !important;
}

.sp-dialog-ui .ep-dialog__header,
.sp-dialog-ui-full .ep-dialog__header {
  background: rgba(1, 51, 61, 1) !important;
  border-bottom: 1px solid rgba(1, 51, 61, 1) !important;
  color: #ffffff !important;
}

.sp-dialog-ui .ep-dialog__title,
.sp-dialog-ui .ep-drawer__header,
.sp-dialog-ui-full .ep-dialog__title,
.sp-dialog-ui-full .ep-drawer__header {
  color: #ffffff !important;
}

.ep-descriptions__body {
  background: rgba(1, 51, 61, 1) !important;
  color: #ffffff !important;
}

.sp-dialog-ui-full__footer,
.sp-dialog-ui__footer {
  background-color: rgba(1, 51, 61, 1) !important;
  border-top: 1px solid rgba(1, 51, 61, 1) !important;
}

.ep-descriptions-item__label.is-bordered-label {
  color: #ffffff !important;
}

.ep-drawer__header {
  background-color: rgba(1, 51, 61, 1) !important;
  border-bottom: 1px solid rgba(1, 51, 61, 1) !important;
}

.ep-drawer__body {
  background-color: #06404e !important;
}

.ep-tabs--border-card,
.ep-tabs--border-card > .ep-tabs__header,
org-tab-box {
  background: transparent !important;
}

.ep-tabs--border-card > .ep-tabs__header {
  border: none;
}

#main-org-container .org-cont .card-fix-bg {
  .ep-tabs__content {
    background: transparent !important;
  }
}

.org-tab-box,
.ep-card {
  background: transparent !important;
  border: none;
  color: #fff;
}

.org-tab-box {
  padding: 0 !important;
}

.ep-tabs__content .rowTitle {
  color: #fff;
}

.ep-tabs--border-card > .ep-tabs__header .ep-tabs__item {
  color: #fff !important;
}

.upload-file {
  div {
    background: transparent !important;
  }
}

.ep-tabs__content .submitRow {
  background: transparent !important;
}

.ep-table--group,
.ep-table--border {
  border: none !important;
}

.border-w-1 {
  background: rgba(1, 51, 61, 0.7) !important;
  border: none !important;
}

.sp-crud .ep-table th.ep-table__cell {
  background: #01333d !important;
  color: #c6d8e0 !important;
}
.ep-table__header-wrapper tr td.ep-table-fixed-column--left,
.ep-table__header-wrapper tr td.ep-table-fixed-column--right,
.ep-table__header-wrapper tr th.ep-table-fixed-column--left,
.ep-table__header-wrapper tr th.ep-table-fixed-column--right,
.ep-table__body-wrapper tr td.ep-table-fixed-column--left,
.ep-table__body-wrapper tr td.ep-table-fixed-column--right,
.ep-table__body-wrapper tr th.ep-table-fixed-column--left,
.ep-table__body-wrapper tr th.ep-table-fixed-column--right,
.ep-table__footer-wrapper tr td.ep-table-fixed-column--left,
.ep-table__footer-wrapper tr td.ep-table-fixed-column--right,
.ep-table__footer-wrapper tr th.ep-table-fixed-column--left,
.ep-table__footer-wrapper tr th.ep-table-fixed-column--right {
  background-color: transparent;
}
.ep-pagination .btn-prev,
.ep-pagination .btn-next {
  background: #01333d;
  color: #fff;
}

.ep-pager li {
  background: #01333d;
  color: #fff;
  margin: 0 5px;
}

.sp-crud,
.crud-wrapper,
.sp-crud__menu,
.ep-card {
  background: transparent !important;
}

.ep-input-group__append,
.ep-input-group__prepend {
  background: #01333d !important;
}

.ep-pagination.is-background .btn-prev,
.ep-pagination.is-background .btn-next,
.ep-pagination.is-background .ep-pager li {
  background: rgba(1, 51, 61, 0.7);
  border: 1px solid rgba(0, 167, 219, 0.7);
}

.select-dialog .right .addOrg_title {
  color: #fff !important;
}

.select-dialog .right .org_item {
  color: rgba(0, 167, 219, 0.7) !important;
  border-bottom: 1px solid rgba(4, 130, 167, 1) !important;
}

.sp-dialog-ui .ep-dialog__header,
.sp-dialog-ui-full .ep-dialog__header,
.sp-dialog-ui-full__footer,
.sp-dialog-ui__footer,
.ep-drawer__header {
  background: #0a5d74 !important;
  border-bottom: 1px solid #0a5d74 !important;
  color: #fff;
}
.sp-crud .ep-table {
  color: #fff;
}
.sp-dialog-ui-full__footer,
.sp-dialog-ui__footer {
  border-top: 1px solid #0a5d74 !important;
}

.sp-dialog-ui .ep-dialog__title,
.sp-dialog-ui .ep-drawer__header,
.sp-dialog-ui-full .ep-dialog__title,
.sp-dialog-ui-full .ep-drawer__header,
.sp-dialog-ui-full__footer,
.sp-dialog-ui__footer {
  color: #fff !important;
}

.ep-drawer__body {
  background: #06404e !important;
}

.ep-table__fixed-right-patch {
  background-color: #01333d;
  border-bottom: 1px solid #01333d;
}

.ep-descriptions__body,
.ep-descriptions-item__label.is-bordered-label {
  background: #223136;
  color: #fff;
}

.ep-descriptions .is-bordered .ep-descriptions-item__cell {
  border: 1px solid #305e6a;
}

#main-org-container .org-tree-cont {
  background: rgba(1, 51, 61, 0.7) !important;
}

#main-org-container .org-cont {
  background: rgba(1, 51, 61, 0.7) !important;
  border: none !important;
}

.mt20 {
  color: #fff;
}

.ep-input-group__append,
.ep-input-group__prepend {
  background: #01333d !important;
}

.ep-tabs--border-card,
.ep-tabs--border-card > .ep-tabs__header,
org-tab-box {
  background: transparent !important;
}

.ep-tabs--border-card > .ep-tabs__header {
  border: none;
}

#main-org-container .org-cont .card-fix-bg {
  .ep-tabs__content {
    background: transparent !important;
  }
}

.org-tab-box,
.ep-card {
  background: transparent !important;
  border: none;
  color: #fff;
}

.org-tab-box {
  padding: 0 !important;
}

.ep-tabs__content .rowTitle {
  color: #fff;
}

.ep-tabs--border-card > .ep-tabs__header .ep-tabs__item {
  color: #fff !important;
}

.upload-file {
  div {
    background: transparent !important;
  }
}

.ep-tabs__content .submitRow {
  background: transparent !important;
}
.ep-input__inner {
  color: #fff;
}
.ep-loading-mask {
  background: transparent;
}

.bg-white {
  background: transparent !important;
}
.ep-step__title.is-process {
  color: rgb(0, 167, 219);
}
.footer,
.ep-input-number__increase,
.ep-input-number__decrease {
  background-color: #0a5d74 !important;
}
.ep-dropdown-menu__item--divided:before {
  background-color: transparent;
}
.ep-dropdown-menu__item--divided {
  border-top: 1px solid #fff;
  margin-top: 0;
}
#__qiankun_microapp_wrapper_for_system__ .p16 {
  background-color: transparent !important;
}

.pagination-container {
  background: transparent !important;
}

.selectPost .item {
  border: 1px solid #0088a8 !important;
}

.selectPost .select_org_table .filter .addOrg_title,
.selectPost .right .addOrg_title {
  color: #fff !important;
}
.tags-view-container {
  border-left: none !important;
}
.ep-scrollbar .ep-menu-item.is-active:after {
  position: absolute;
  content: '';
  width: 4px;
  height: 23px;
  background: #0a6efa;
  right: 5px;
  border-radius: 2.5px;
  top: 50%;
  transform: translateY(-50%);
}
.ep-scrollbar .ep-menu-item.is-active {
  background-color: #141319 !important;
}
.sp-crud .ep-button .icon.el-icon-refresh-right {
  color: #fff !important;
}
.vue-treeselect__control {
  background: var(--ep-fill-color-blank);
  border-color: var(--ep-color-primary);
}
.vue-treeselect__single-value {
  color: var(--ep-input-text-color, var(--ep-text-color-regular));
}
.vue-treeselect__menu {
  background: var(--ep-fill-color-blank);
  border: 1px solid var(--ep-color-primary);
}
.vue-treeselect--single .vue-treeselect__option--selected {
  background: var(--ep-color-primary);
  color: var(--ep-color-white);
}
.vue-treeselect__option--highlight {
  background: var(--ep-color-primary);
  color: var(--ep-color-white);
}
.vue-treeselect--single .vue-treeselect__option--selected:hover {
  background: var(--ep-color-primary);
  color: var(--ep-color-white);
}
.ep-input-group__append .icon.snMenuIcon {
  color: #fff !important;
}
.ep-input-group__append .icon.snButtonIcon {
  color: #fff !important;
}
.iconBox .icon.snMenuIcon {
  color: #fff !important;
}
.iconBox .icon.snButtonIcon {
  color: #fff !important;
}