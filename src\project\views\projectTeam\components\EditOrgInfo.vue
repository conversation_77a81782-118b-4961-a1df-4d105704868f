<template>
    <el-form :model="formData" :rules="rules" ref="orgForm" label-width="100px" label-position="right"
        :disabled="type == 'view'" style="overflow-x: hidden;">
        <el-row :gutter="16" :span="24">
            <el-col :span='24'>
                <el-form-item label="上级组织">
                    <el-input v-model="props.data.upOrgName" type="text" placeholder="请输入名称" :disabled="true">
                    </el-input>
                </el-form-item>
            </el-col>
            <el-col :span='24'>
                <el-form-item label="名称" prop="name">
                    <el-input v-model="formData.name" type="text" placeholder="请选择单位" :disabled="formData.type != 'dept'">
                        <template #append>
                            <el-button :icon="Search" @click="selectOrgDialog"></el-button>
                        </template>
                    </el-input>
                </el-form-item>
            </el-col>
            <el-col :span='24'>
                <el-form-item label="编码" prop="code">
                    <el-input v-model="formData.code" type="text" placeholder="" :disabled="formData.type != 'dept'">
                    </el-input>
                </el-form-item>
            </el-col>
            <el-col :span='24'>
                <el-form-item label="组织类型" prop="type">
                    <el-radio-group v-model="formData.type">
                        <el-radio v-for="dict in sys_org_type" :key="dict.value" :label="dict.value">{{ dict.label }}
                        </el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-col>
            <el-col :span='24' v-if="formData.type == 'company'">
                <el-form-item label="单位性质" prop="orgNature">
                    <el-radio-group v-model="formData.orgNature">
                        <el-radio v-for="dict in projectTeam_orgNature" :key="dict.value" :label="dict.value"> {{
                            dict.label }}
                        </el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-col>
            <el-col :span='24'>
                <el-form-item label="排序码" prop="sort">
                    <el-input-number v-model="formData.sort" :disabled="type == 'view'" :min="0" :max="999999999999"
                        style="width: 100%;" />
                </el-form-item>
            </el-col>
        </el-row>
    </el-form>
</template>

<script setup>
import ProjBizProjectTeamOrgApi from '@/project/api/projectTeam/ProjBizProjectTeamOrg.js'
import { ref, toRef, watch, defineProps, defineExpose, getCurrentInstance } from 'vue';
import { useDicts } from '@/common/hooks/useDicts'

const { projectTeam_orgNature, sys_org_type } = useDicts(["projectTeam_orgNature", "sys_org_type"])
const { proxy } = getCurrentInstance()
const props = defineProps({
    data: Object,
});
const orgForm = ref()
const type = toRef(props.data?.type);
let formData = ref({
    code: "",
    name: "",
    referred: "",
    type: "",
    fullPathId: "",
    fullPathName: "",
    parentId: "",
    nodeType: "",
    state: "",
    sort: "",
    orgNature: "",
    projectId: "",
});
let rules = ref({
    code: [],
    name: [],
    referred: [],
    type: [],
    fullPathId: [],
    fullPathName: [],
    parentId: [],
    nodeType: [],
    state: [],
    sort: [],
    orgNature: [],
    projectId: []
});

watch(props.data?.rowId, (value) => {
    if (props.data?.type == "edit") {
        getDataById(props.data?.rowId);
    } else {
        formData.value = { parentId: props.data?.rowId }
    }
}, { immediate: true })


function getDataById(id) {
    ProjBizProjectTeamOrgApi.view(id).then((resp) => {
        formData.value = resp.data;
    });
}

function getFormData() {
    return { ...formData.value, projectId: formData.value.projectId ? formData.value.projectId : sessionStorage.getItem('projectId') };
}

function submitData() {
    return new Promise((resolve) => {
        orgForm.value.validate((flag) => {
            if (!flag) {
                return false;
            } else {
                if (type.value === "add") {
                    resolve(saveData());
                } else {
                    resolve(saveData());
                }
            }
        });
    });
}

//新增操作
function saveData() {
    const formDatas = getFormData();
    return ProjBizProjectTeamOrgApi.add(formDatas).then(() => {
        proxy.$message.success("操作成功");
        return true;
    });
}

//选择组织对话框
function selectOrgDialog() {
    proxy.$SelectOrg({
        multiple: false,
        orgTypeList: ["company"],
        onSuccess(res) {
            if (Array.isArray(res.data) && res.data.length) {
                const orgInfo = res.data[0];
                formData.value = {
                    ...formData.value,
                    code: sessionStorage.getItem('projectId') + "-" + orgInfo.orgCode,
                    name: orgInfo.orgName,
                    type: orgInfo.orgType,
                    nodeType: 0,
                }
            }
        }
    })
}


defineExpose({
    getFormData,
    submitData,
});
</script>

<style lang="scss" scoped></style>
