<!--
 * @Author: 王云飞
 * @Date: 2025-01-17 10:12:44
 * @LastEditTime: 2025-02-18 13:45:58
 * @LastEditors: 王云飞
 * @Description: 
 * 
-->
<template>
  <div class="theme_setting">
    <div class="themePicker">
      <div
        class="theme_color"
        v-for="(item, index) in themeColor"
        :key="index"
        :style="{ background: item }"
        @click="changeTheme(item)"
      ></div>
    </div>
  </div>
</template>

<script>
import { changeElTheme3 } from 'sn-base-utils'

export default {
  name: 'ThemeView',
  computed: {
    defaultTheme() {
      return this.$store.state.settings.theme
    }
  },
  data() {
    return {
      themeColor: [
        '#3C3DFA',
        '#448EF7',
        '#6A32C9',
        '#5ABFC1',
        '#5EAA35',
        '#EFBE46',
        '#E6873A',
        '#E13C39'
      ]
    }
  },
  methods: {
    changeTheme(item) {
      this.$store.dispatch('changeSetting', item)
      changeElTheme3(`http://127.0.0.1:8912/${item.replace('#', '%23')}.css`)
    }
  }
}
</script>

<style lang="scss" scoped>
.theme_setting {
  display: flex;
  height: 100%;

  .themePicker {
    width: 100%;
    height: 100%;
    border-left: 1px solid #f1ecec;
    float: left;
    padding: 10px 10px 10px 20px;
    .theme_color {
      margin: 10px calc((100% - 120px) / 8);
      float: left;
      width: 30px;
      height: 30px;
      border-radius: 4px;
      cursor: pointer;
    }
  }
}
</style>
