<template>
  <el-form :model="formData" :rules="rules" ref="snForm" label-width="100px" label-position="right" :disabled="type == 'view'">
    <el-row :gutter="16" :span="24">
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>基本信息
          </legend>
          <el-row :gutter="16" :span="24">
            <el-col :span='8'>
              <el-form-item label="编码" prop="encoding">
                <el-input v-model="formData.encoding" type="text" placeholder="" :disabled="true" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="编制单位" prop="organizationUnit">
                <el-input v-model="formData.organizationUnit" type="text" :disabled="true"clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="编制日期" prop="compileDate" :disabled="dialogdisabled">
                <el-date-picker type="date" v-model="formData.compileDate" format="YYYY-MM-DD" value-format="YYYY-MM-DD" style="width: 100%;" :disabled="dialogdisabled"clearable></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>

            <el-row :gutter="16" :span="24">
            <el-col :span='8'>
              <el-form-item label="预案类型" prop="planType" :disabled="dialogdisabled">
                <el-select v-model="formData.planType" :disabled="dialogdisabled" clearable>
                    <el-option v-for="(item,index) in plan_type" :key="index" :label="item.label" :value="item.value" :disabled="dialogdisabled"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span='16'>
              <el-form-item label="预案名称" prop="planName" :disabled="dialogdisabled">
                <el-input v-model="formData.planName" type="text" placeholder="请输入预案名称" :disabled="dialogdisabled" clearable>
                </el-input>
              </el-form-item>
            </el-col>

          </el-row>

      <el-row :gutter="16" :span="24">
        <el-col :span='24'>
          <el-form-item label="预案描述" prop="planRemark">
            <el-input type="textarea" v-model="formData.planRemark" placeholder="请输入预案描述" rows="3" :disabled="dialogdisabled" clearable></el-input>
          </el-form-item>
          </el-col>
       </el-row>
        </fieldset>
      </el-card>

      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>附件
          </legend>
          <el-row :gutter="16" :span="24">
            <project-document-storage-ui-table
                ref="childRef"
                :type="fileType"
                :relevantId="relevantId"
                :isPageSearch="false"
                :isDeleteMinio = "isDeleteMinio"
                :isHasAi = "isHasAi"
                @on-add-data="onAddfileData"
                @on-ai-review="onfileAiReview"
                @on-preview="onfilePreview"
                @on-delete="onfileDelete"
                :file-serial-number-builder="fileSerialNumberBuilder"
                :preview-config="previewConfig"
                :isShowAddBtn="type !== 'show'"
                :isShowDelBtn="type !== 'show'"
                :isShowPreviewBtn="isShowPreviewBtn"
                :isShowDownloadBtn="type !== 'show'"
                :isShowLinkBtn="false"
                :isShowDownloadBatchBtn="type !== 'show'"
            ></project-document-storage-ui-table>
          </el-row>
        </fieldset>
      </el-card>

      <el-card class="box-card" style="width: 100%;">
        <!--        <fieldset class="fieldset2">-->
        <legend>
          <span class="el-button--primary"></span>单据信息
        </legend>
        <el-row :gutter="16" :span="24">
          <el-col :span='8'>
            <el-form-item label="创建人" prop="createName">
              <el-input v-model="formData.createName" type="text" placeholder="" :disabled="true" clearable>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span='8'>
            <el-form-item label="创建时间" prop="createTime">
              <el-input v-model="formData.createTime" type="text" placeholder="" :disabled="true" clearable>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span='8'>
            <el-form-item label="单据状态" prop="docStatus">
              <el-select v-model="formData.docStatus" disabled placeholder="" clearable>
                <el-option v-for="(item,index) in statusOption" :key="index" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span='8'>
            <el-form-item label="最近修改人" prop="updateName">
              <el-input v-model="formData.updateName" type="text" placeholder="" :disabled="true" clearable>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span='8'>
            <el-form-item label="最近修改时间" prop="updateTime">
              <el-input v-model="formData.updateTime" type="text" placeholder="" :disabled="true" clearable>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <!--        </fieldset>-->
      </el-card>
    </el-row>
  </el-form>
</template>

<script setup>
import ProjBizEmergencyContingencyPlanMApi from '@/project/api/emergency/ProjBizEmergencyContingencyPlanM.js';
import { kkFileViewUrl } from "@/config";
import {
  nextTick,
  onMounted,
  ref,
  reactive,
  toRef,
  defineProps,
  defineExpose,
  getCurrentInstance
} from 'vue';
import {
  useRoute,
  useRouter
} from 'vue-router'
const {
  proxy
} = getCurrentInstance()
const route = useRoute();
const router = useRouter();

const props = defineProps({
  data: Object,
});
const snForm = ref()
const type = toRef(props.data?.type);
import {
  useDicts
} from "@/common/hooks/useDicts";
import store from "@/store";
import {getCurrentFormattedTime} from "@/common/utils/datetime";
import ProjBizDesignIntentionMApi from "@/project/api/design/ProjBizDesignIntentionM";
//获取到字典中定义的数据下拉选择
const {
  plan_type
} = useDicts([ "plan_type"])
let formData = ref({
  encoding: "",
  organizationUnit: "",
  compileDate: "",
  planType: "",
  planName: "",
  projectId: sessionStorage.getItem('projectId'),
  planRemark: "",
  createName: "",
  createTime: "",
  docStatus: "0",
  updateName: "",
  updateTime: "",
  fileList: [], // 👈 文件列表
});
let rules = ref({
  encoding: [
    { required: true, message: '编码不能为空', trigger: 'blur' }
  ],
  organizationUnit: [
    { required: true, message: '编制单位不能为空', trigger: 'blur' }
  ],
  compileDate: [
    { required: true, message: '编制日期不能为空', trigger: 'blur' }
  ],
  planType: [
    { required: true, message: '预案类型不能为空', trigger: 'change' }
  ],
  planName: [
    { required: true, message: '预案名称不能为空', trigger: 'blur' }
  ],
  planRemark: [
    { required: true, message: '预案描述不能为空', trigger: 'blur' }
  ],
  createName: [],
  createTime: [],
  docStatus: [],
  updateName: [],
  updateTime: [],
  fileList:[]   //文件列表
});

//初始化借据状态
let statusOption = ref([{
  label: "草稿",
  value: "0"
}, {
  label: "已发布",
  value: "1"
}]);

//输入框是否禁止输入
let dialogdisabled = ref(false)
let listData = ref([]);
let listvolumeData = ref([]);

let queryForm = ref({
  page: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  filter: {
    createTime: null
  }
});
//以下为文件的方法及其定义=======
//处理附件上传相关方法---------begin
const fileType = ref("emergencyContingencyPlan")//文件类型记得在后台配置-表名
const relevantId = toRef(props.data?.id)

const previewConfig = ref({
  // 是否外置预览,必传
  isExternalPreview: false,
  // 内置预览返回的替换字符,akey代表要替换的字符串，avalue代码替换的字符串
  substitute: {
    "akey":"avalue"
  },
  // 内置预览服务地址
  // previewServerUrl: "http://10.191.64.191:8012/onlinePreview",
  // 内置预览服务地址
  previewServerUrl: kkFileViewUrl,
})
function onAddfileData(list) {
  console.log(list,"onAddData-sssss")
  console.log(childRef.value.getListData(),"onAddData-ccccccc")
}

function onAifileReview(row) {
  console.log(row,"onAiReview-sssss")
}

function onfilePreview(row) {
  console.log(row,"onPreview-sssss")
}

function onfileDelete(list) {
  console.log(list,"onDelete-sssss")
}
const childRef = ref(null);
function getfileListData() {
  if(childRef.value){
    let list = childRef.value.getListData()
    console.log(list,"组件数据。。。。。。。。。")
  }
}
// 文件序列号生成函数
function fileSerialNumberBuilder() {
  return "file" + Math.floor(Math.random()*10000)
}
const getAttachmentList = () => {
  // 假设子组件有 getListData 方法
  return childRef.value?.getListData() || [];
};
//处理附件上传相关方法---------end

function getPageList() {
  //查询分页列表
  // 查询前处理参数
  const params = handleQueryForm();
  ProjBizEmergencyContingencyPlanMApi.pageList(params).then((res) => {
    listData.value = res.data.dataList;
    queryForm.value.page.total = res.data.totalCount;
  });
}

function onResetSearch() {
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
}

function handleQueryForm() {
  // 处理参数
  const {
    pageSize,
    pageNum
  } = queryForm.value.page;
  const filter = {}
  for (let key in queryForm.value.filter) {
    if (queryForm.value.filter[key]?.length) {
      // sn-curd搜索select会生成$prop的变量
      if (key.startsWith("$")) continue;
      filter[key] = queryForm.value.filter[key];
    }
  }
  delete filter.createTime;
  if (Array.isArray(queryForm.value.filter.createTime) && queryForm.value.filter.createTime?.length === 2) {
    filter["beginCreateTime"] = queryForm.value.filter.createTime[0];
    filter["endCreateTime"] = queryForm.value.filter.createTime[1];
  }
  const searchParams = {
    page: {
      pageSize,
      pageNum,
    },
    filter,
  }
  return searchParams
}

function onChangeSearch(params, done) {
  //搜索按钮操作
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
  done && done();
}
async function onEditData(row) {
  //编辑,新增按钮操作
  let editType = row ? "edit" : "add";
  let rowInfo = await (editType !== "add" ? ProjBizEmergencyContingencyPlanMApi.view(row.id) : {});
  const formData = editType !== "add" ? rowInfo.data : rowInfo;
  //编辑,新增按钮操作
  proxy.$DialogForm.show({
    title: editType == "edit" ? "编辑" : "新增",
    type: option.value.dialogType,
    width: "80%",
    content: EditProjBizEmergencyContingencyPlanM,
    data: {
      formData: formData,
      type: editType,
    },
    callback: (res) => {
      if (res.type && res.type !== 'close') {
        if (res.dialogRefs) {
          res.dialogRefs.submitData().then((flag) => {
            if (flag) {
              getPageList();
              res.close();
            }
          });
        }
      } else {
        res.close();
      }
    }
  });
}

function onDelData(rows) {
  //删除行
  if (!rows || rows.length == 0) {
    proxy.$message.info("请勾选数据！");
    return false;
  }
  let ids = rows.map((item) => {
    return item.id;
  });
  proxy.$modal.confirm("确认删除数据项？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    ProjBizEmergencyContingencyPlanMApi.remove(ids).then((res) => {
      proxy.$message.success("已删除");
      getPageList();
    });
  }).catch(() => {});
}
formData = toRef(Object.keys(props.data?.formData || {}).length > 0 ? {
  ...props.data?.formData
} : toRef(formData.value));

function getFormData() {
  formData.value.fileList = getAttachmentList();
  return formData.value
};

function submitData(buttontype) {
  return new Promise((resolve) => {
    snForm.value.validate((flag) => {
      if (!flag) {
        return false;
      } else {
        buttonType.value = buttontype
        if (type.value === "add") {
          resolve(saveData());
        } else {
          resolve(editData());
        }
      }
    });
  });
}
let buttonType = ref();
function saveData() {
  //新增操作
  const formData = getFormData();
  if(buttonType.value==='submit'){
    formData.docStatus = '1'
  }
  if(buttonType.value==='submit'){
    if(formData.id===null || formData.id === undefined){
      return ProjBizEmergencyContingencyPlanMApi.add(formData).then((resp) => {
        if(resp.data){
          proxy.$message.success("发布成功");
          formData.id = resp.data.id;
          return true;
        }
      });
    }else{
      return ProjBizEmergencyContingencyPlanMApi.update(formData).then(() => {
        proxy.$message.success("发布成功");
        return true;
      });
    }
  }else{
    if(formData.id===null || formData.id === undefined){
      return ProjBizEmergencyContingencyPlanMApi.add(formData).then((resp) => {
        if(resp.data){
          proxy.$message.success("保存成功");
          formData.id = resp.data.id;
          return true;
        }
      });
    }else{
      return ProjBizEmergencyContingencyPlanMApi.update(formData).then(() => {
        proxy.$message.success("保存成功");
        return true;
      });
    }
  }
  // return ProjBizEmergencyContingencyPlanMApi.add(formData).then(() => {
  //   if(buttonType.value==='save'){
  //     proxy.$message.success("保存成功");
  //   }else{
  //     proxy.$message.success("发布成功");
  //   }
  //   return true;
  // });
}

function editData() {
  //编辑操作
  const formData = getFormData();
  if(buttonType.value==='submit'){
    formData.docStatus = '1'
  }
  if(buttonType.value==='revoke'){
    formData.docStatus = '0'
  }
  return ProjBizEmergencyContingencyPlanMApi.update(formData).then(() => {
    if(buttonType.value==='save'){
      proxy.$message.success("保存成功");
    }else{
      proxy.$message.success("发布成功");
    }
    return true;
  });
}
//初始化表单填充数据
function initializer(){
  if(type.value==='add'){
    formData.value.createName =  store.state.user && store.state.user.userInfo.userName ? store.state.user.userInfo.userName : null;//获取到登陆人
    formData.value.createTime = getCurrentFormattedTime();//获取到登录时间
    formData.value.organizationUnit =  JSON.parse(store.state.user.orgName)?.find((item) => item.id === store.state.user.defaultOrg)?.orgName;
    // 自动生成编码
    const currentDate = new Date();
    const year = String(currentDate.getFullYear());
    const month = String(currentDate.getMonth() + 1).padStart(2, '0');
    const day = String(currentDate.getDate()).padStart(2, '0');
    const hours = String(currentDate.getHours()).padStart(2, '0');
    const minutes = String(currentDate.getMinutes()).padStart(2, '0');
    const seconds = String(currentDate.getSeconds()).padStart(2, '0');
    const dateTimeStr = `${year}${month}${day}${hours}${minutes}${seconds}`;
    formData.value.encoding = dateTimeStr
  }
  //判断是否为查看进来，是则禁用输入框及按钮
  if(type.value==='show'){
    dialogdisabled = true
  }
}
defineExpose({
  getFormData,
  submitData,
});
//初始化
onMounted(() => {
  initializer()
});
</script>

<style lang="scss" scoped>

</style>
