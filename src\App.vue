<template>
  <div id="app">
    <el-config-provider namespace="ep">
      <!-- <router-view></router-view> -->
      <router-view v-slot="{ Component, route }">
        <!-- <transition appear name="el-zoom-in-center" mode="out-in"> -->
        <keep-alive :include="cachedViews">
          <component :is="Component" :key="route.path" />
        </keep-alive>
        <!-- </transition> -->
      </router-view>
      <el-dialog
        v-model="isFirstLogin"
        title="修改密码"
        :show-close="false"
        class="update-pwd-dialog"
      >
        <el-form ref="form" :model="form" :rules="rules" :form="form" label-width="80px">
          <el-form-item label="旧密码" prop="oldPassword">
            <el-input
              show-password
              v-model="form.oldPassword"
              placeholder="请输入旧密码"
            ></el-input>
          </el-form-item>
          <el-form-item label="新密码" prop="newPassword">
            <el-input
              show-password
              v-model="form.newPassword"
              placeholder="请输入新密码"
            ></el-input>
          </el-form-item>
          <el-form-item label="确认密码" prop="repeatPassword">
            <el-input
              show-password
              v-model="form.repeatPassword"
              placeholder="请确认新密码"
            ></el-input>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" size="mini" @click="logout">退出登录</el-button>
          <el-button type="primary" size="mini" @click="UpdateUserPwd">修改</el-button>
        </span>
      </el-dialog>
    </el-config-provider>
  </div>
</template>

<script>
import { CommonUserApi } from 'sn-base-layout'
import { mapState } from 'vuex'
import { sha256 } from 'js-sha256'
import { rsaEncrypt, changeElTheme3 } from 'sn-base-utils'
import { useDark } from '@vueuse/core'
import config from '@/config'
import { changeDrakTheme } from '@/common/utils/chageTheme'
export default {
  name: 'AppView',
  setup() {
    const isDark = useDark()
    return {
      isDark
    }
  },
  watch: {
    isDark: {
      handler(newVal) {
        changeDrakTheme(newVal)
      }
    }
  },
  data() {
    return {
      form: {
        oldPassword: '',
        newPassword: '',
        repeatPassword: ''
      },
      rules: {
        oldPassword: [
          {
            required: true,
            validator: this.checkOldPassword,
            trigger: 'blur'
          }
        ],
        newPassword: [
          {
            required: true,
            validator: this.passwordRule,
            trigger: 'blur'
          }
        ],
        repeatPassword: [
          {
            required: true,
            validator: this.repeatPassword,
            trigger: 'blur'
          }
        ]
      }
    }
  },
  computed: {
    isFirstLogin() {
      return this.$store.state.user.isFirstLogin
    },
    ...mapState({
      cachedViews: (state) => {
        console.log(state.tagsView)
        return state.tagsView.cachedViews
      },
      safeConfig: (state) => state.settings.safeConfig,
      publicKey: (state) => state.settings.publicKey
    })
  },
  created() {
    this.$store.commit('settings/INIT_SETTING')
    const theme = localStorage.getItem('theme') || config.theme
    if (!useDark().value) {
      changeElTheme3(theme)
    }
    setTimeout(() => {
      changeDrakTheme(useDark().value)
    }, 300)
    this.$store.dispatch('settings/getAppConfig')
  },
  methods: {
    UpdateUserPwd() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          let oldPass = this.form.oldPassword
          let newPass = this.form.newPassword
          if (this.publicKey) {
            oldPass = rsaEncrypt(this.form.oldPassword, this.publicKey)
            newPass = rsaEncrypt(this.form.newPassword, this.publicKey)
          }
          CommonUserApi.updateUserPwd(
            oldPass,
            newPass,
            sha256(this.form.oldPassword + this.form.newPassword)
          ).then((res) => {
            if (res.code == 200) {
              this.$store.dispatch('IsFirstLogin', false)
              this.$modal.msgSuccess('修改成功')
            }
          })
        }
      })
    },
    checkPassword(rule, value, callback) {
      if (!value) {
        callback(new Error('新密码不能为空'))
      }
      this.passwordRule(value, callback)
      callback()
    },
    passwordRule(rlue, value, callback) {
      // if (JSON.stringify(this.safe) === '{}') {
      //   callback()
      //   return
      // }
      if (this.safeConfig.pwdLenCheckEnabled === true) {
        let pwdLen = value?.length
        let pwd_len_min = this.safeConfig.pwdLenValue.split(',')[0]
        let pwd_len_max = this.safeConfig.pwdLenValue.split(',')[1]
        if (pwdLen < pwd_len_min) {
          callback(new Error('密码最小长度为' + pwd_len_min + '位'))
        } else if (pwdLen > pwd_len_max) {
          callback(new Error('密码最大长度为' + pwd_len_max + '位'))
        }
      }
      if (this.safeConfig.pwdRuleCheckEnabled === true) {
        const pwdRuleValue = JSON.parse(this.safeConfig.pwdRuleValue)
        if (pwdRuleValue.key == 0) {
          callback()
        } else {
          const reg = new RegExp(pwdRuleValue.reg)
          if (reg.test(value)) {
            callback()
          } else {
            callback(new Error(`${pwdRuleValue.desc}`))
          }
        }
      }
      callback()
    },
    checkOldPassword(rule, value, callback) {
      if (!value) {
        callback(new Error('旧密码不能为空'))
      } else {
        callback()
      }
    },
    repeatPassword(rule, value, callback) {
      if (!value) {
        callback(new Error('确认密码不能为空'))
      } else if (value !== this.form.newPassword) {
        callback(new Error('两次输入的密码不一致'))
      } else {
        callback()
      }
    },
    async logout() {
      this.$confirm('确定注销并退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$store.dispatch('LogOut')
        })
        .catch(() => {})
    }
  }
}
</script>

<style lang="scss">
#app {
  /* width: 1920px;
  transform-origin: left top; */
  display: flex;
  position: relative;
  width: 100%;
  height: 100vh;
  flex-direction: column;
}

.layui-layer-content {
  padding: 20px !important;
}

.ep-form-item__label {
  line-height: 32px;
}

// .ep-textarea__inner {
//   font-family: Ariale !important;
// }
.update-pwd-dialog {
  .ep-dialog__body {
    padding-bottom: 48px;
    min-height: 50vh !important;
  }
}

input[aria-hidden='true'] {
  display: none !important;
}
</style>
