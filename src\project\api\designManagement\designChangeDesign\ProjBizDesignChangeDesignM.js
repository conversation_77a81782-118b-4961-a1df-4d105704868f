import { request, replaceUrl } from "sn-base-utils";

export default class ProjBizDesignChangeDesignMApi {
    static config = {
        add: {
            url: '/project/designChangeDesign/add',
            method: 'POST'
        },
        remove: {
            url: '/project/designChangeDesign/delete',
            method: 'DELETE'
        },
        update: {
            url: '/project/designChangeDesign/update',
            method: 'PUT'
        },
        view: {
            url: '/project/designChangeDesign/get/{id}',
            method: 'GET'
        },
        pageList: {
            url: '/project/designChangeDesign/page',
            method: "POST"
        },
        list: {
            url: '/project/designChangeDesign/list',
            method: "POST"
        },
        startFlow: {
            url: '/project/designChangeDesign/saveAndSubmitProc',
            method: "POST"
        },
        submitTask: {
            url: '/project/designChangeDesign/saveAndSubmitTask',
            method: "POST"
        },
        printTemplate: {
            url: `/project/designChangeDesign/printTemplate`,
            method: "POST"
        },
        operateFlow: {
            url: `/project/designChangeDesign/operateFlow`,
            method: "POST"
        }
    };

    /**
     * 新增设计变更单
     * @param data
     * @returns {*}
     */
    static add(data) {
        return request({
            url: this.config.add.url,
            method: this.config.add.method,
            data: data
        });
    }

    /**
     * 删除设计变更单
     * @param data
     * @returns {*}
     */
    static remove(data) {
        return request({
            url: this.config.remove.url,
            method: this.config.remove.method,
            data: data
        });
    }

    /**
     * 更新设计变更单
     * @param data
     * @returns {*}
     */
    static update(data) {
        return request({
            url: this.config.update.url,
            method: this.config.update.method,
            data: data
        });
    }

    /**
     * 更新变更申请单
     * @param data
     * @returns {*}
     */
    static operateFlow(data) {
        return request({
            url: this.config.operateFlow.url,
            method: this.config.operateFlow.method,
            data: data
        });
    }

    /**
     * 查询设计变更单详细
     * @param id
     * @returns {*}
     */
    static view(id) {
        return request({
            url: replaceUrl(this.config.view.url, { id }),
            method: this.config.view.method
        });
    }

    /**
     * 分页查询设计变更单列表
     * @param data
     * @returns {*}
     */
    static pageList(data) {
        return request({
            url: this.config.pageList.url,
            method: this.config.pageList.method,
            data: data
        });
    }


    /**
     * 全部设计变更单列表
     * @returns {*}
     */
    static list(data) {
        return request({
            url: this.config.list.url,
            method: this.config.list.method,
            data: data
        });
    }

    /**
     * 工作流-启动流程
     * @returns {*}
     */
    static startFlow(data) {
        return request({
            url: this.config.startFlow.url,
            method: this.config.startFlow.method,
            data: data
        });
    }

    /**
     * 工作流-完成任务
     * @returns {*}
     */
    static submitTask(data) {
        return request({
            url: this.config.submitTask.url,
            method: this.config.submitTask.method,
            data: data
        });
    }

    /**
     * 工作流-打印模板
     */
    static printTemplate(data) {
        return request({
            url: this.config.printTemplate.url,
            method: this.config.printTemplate.method,
            data: data
        });
    }
}
