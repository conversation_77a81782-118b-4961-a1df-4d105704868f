import { request, replaceUrl } from "sn-base-utils";

export default class ProjBizIrRecordMApi {
    static config = {
        add: {
            url: '/project/irRecord/add',
            method: 'POST'
        },
        remove: {
            url: '/project/irRecord/delete',
            method: 'DELETE'
        },
        update: {
            url: '/project/irRecord/update',
            method: 'PUT'
        },
        view: {
            url: '/project/irRecord/get/{id}',
            method: 'GET'
        },
        pageList: {
            url: '/project/irRecord/page',
            method: "POST"
        },
        list: {
            url: '/project/irRecord/list',
            method: "POST"
        },
        listQuestionByRecordId: {
            url: '/project/irRecord/listQuestionByRecordId/{id}',
            method: "GET"
        },
        listQuestionByIdList: {
            url: '/project/irRecord/listQuestionByIdList',
            method: "POST"
        },
        publish: {
            url: '/project/irRecord/publish/{id}',
            method: 'GET'
        },
        revoke: {
            url: '/project/irRecord/revoke/{id}',
            method: 'GET'
        },
        questionsUpdate: {
            url: '/project/irRecord/questions/update',
            method: 'PUT'
        }
    };

    /**
     * 新增检查整改记录
     * @param data
     * @returns {*}
     */
    static add(data) {
        return request({
            url: this.config.add.url,
            method: this.config.add.method,
            data: data
        });
    }

    /**
     * 删除检查整改记录
     * @param data
     * @returns {*}
     */
    static remove(data) {
        return request({
            url: this.config.remove.url,
            method: this.config.remove.method,
            data: data
        });
    }

    /**
     * 更新检查整改记录
     * @param data
     * @returns {*}
     */
    static update(data) {
        return request({
            url: this.config.update.url,
            method: this.config.update.method,
            data: data
        });
    }

    /**
     * 查询检查整改记录详细
     * @param id
     * @returns {*}
     */
    static view(id) {
        return request({
            url: replaceUrl(this.config.view.url, { id }),
            method: this.config.view.method
        });
    }

    /**
     * 分页查询检查整改记录列表
     * @param data
     * @returns {*}
     */
    static pageList(data) {
        return request({
            url: this.config.pageList.url,
            method: this.config.pageList.method,
            data: data
        });
    }

    /**
     * 全部检查整改记录列表
     * @returns {*}
     */
    static list(data) {
        return request({
            url: this.config.list.url,
            method: this.config.list.method,
            data: data
        });
    }

    /**
     * 查询检查整改记录详细
     * @param id
     * @returns {*}
     */
    static listQuestionByRecordId(id) {
        return request({
            url: replaceUrl(this.config.listQuestionByRecordId.url, { id }),
            method: this.config.listQuestionByRecordId.method
        });
    }

    /**
     * 全部检查整改记录列表
     * @returns {*}
     */
    static listQuestionByIdList(data) {
        return request({
            url: this.config.listQuestionByIdList.url,
            method: this.config.listQuestionByIdList.method,
            data: data
        });
    }

    /**
     * 发布
     * @param id
     * @returns {*}
     */
    static publish(id) {
        return request({
            url: replaceUrl(this.config.publish.url, { id }),
            method: this.config.publish.method
        });
    }

    /**
     * 撤回
     * @param id
     * @returns {*}
     */
    static revoke(id) {
        return request({
            url: replaceUrl(this.config.revoke.url, { id }),
            method: this.config.revoke.method
        });
    }

    /**
     * 更新检查整改记录问题
     * @param data
     * @returns {*}
     */
    static questionsUpdate(data) {
        return request({
            url: this.config.questionsUpdate.url,
            method: this.config.questionsUpdate.method,
            data: data
        });
    }
}
