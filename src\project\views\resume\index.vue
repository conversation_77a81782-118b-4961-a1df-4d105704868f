<template>
  <el-tabs v-model="activeTab">
    <el-tab-pane label="复工申请" name="application">
      <ResumeApply />
    </el-tab-pane>
    <el-tab-pane label="复工令" name="order">
      <ResumeOrder />
    </el-tab-pane>
  </el-tabs>
</template>

<script setup>
import { ref } from 'vue';
import ResumeOrder from '../resumeOrder/index.vue';
import ResumeApply from '../resumeApply/index.vue';

// 默认显示 "application" tab
const activeTab = ref('application');
</script>