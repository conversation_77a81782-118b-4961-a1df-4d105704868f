<template>
  <flow-page-container ref="flowPageContainerRef" @handlerAction="handlerAction" @initData="initData" @handlerPrint="handlerPrint" :closeBtn="isShowCloseBtn" :approvalOption="approvalOption" @approvalOptionCallback="getApprovalOption">
    <el-form :model="formData" :rules="formRules" ref="snForm" label-width="100px" label-position="right" :disabled="type == 'view'">
      <el-row :gutter="16" :span="24">
        <el-card class="box-card" style="width: 100%;" v-if="props.data?.tips !== ''">
          <div>
            <span class="special-text">{{recordInfo.inspectedUnitName}}</span>于<span class="special-text">{{recordInfo.inspectionDate}}</span>进行了<span class="special-text">{{recordInfo.name}}</span>。
            问题共计<span class="special-text">{{totalQuestionNumber}}</span>条,已整改完成<span class="special-text">{{completedQuestionNumber}}</span>条,本次整改提交<span class="special-text">{{submitQuestions.length}}</span>
            条,请予以复查确认。
          </div>
          <div class="commit-info-container">
            <span>提交人: <span class="special-text" style="margin-right: 30px">{{createName}}</span></span>
            <span>提交单位: <span class="special-text" style="margin-right: 30px">{{createOrgName}}</span></span>
            <span>提交时间: <span class="special-text">{{createTime}}</span></span>
          </div>
        </el-card>
      </el-row>
      <el-row :gutter="16" :span="24">
        <el-card class="box-card" style="width: 100%;">
            <el-row :gutter="16" :span="24">
              <sn-crud :data="submitQuestions" :option="projBizIrPiSOption" @row-save="subRowSave" @row-update="subRowUpdate">
                <template #empty>
                  <div>无数据</div>
                </template>
                <template #reformImage = "{ row,index }">
                  <div v-permi="formPermi({fieldModelId: 'projBizIrPiS', field:'reformImage', fieldName: '整改图片'}, customFormPermi)">
                    <div class="remove-reform-image-container" v-if="row.reformImagePreviewUrl">
                      <el-image
                          :preview-teleported="true"
                          :key="index"
                          :src="row.reformImagePreviewUrl"
                          :preview-src-list="row.reformImagePreviewUrlList">
                      </el-image>
                    </div>
                  </div>
                </template>
                <template #reInspectionResults = "{ row }">
                  <div v-permi="formPermi({fieldModelId: 'projBizIrPiS', field:'reInspectionResults', fieldName: '复验结果'}, customFormPermi)">
                    <span v-if="type == 'view'">{{row.reInspectionResults}}</span>
                    <el-select v-else v-model="row.reInspectionResults" placeholder="请选择">
                      <el-option v-for="(item, index) in inspection_reform_result" :key="index" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                  </div>
                </template>
                <template #opinion = "{ row }">
                  <span v-if="type == 'view'">{{row.opinion}}</span>
                  <div v-else v-permi="formPermi({fieldModelId: 'projBizIrPiS', field:'opinion', fieldName: '审批意见'}, customFormPermi)">
                    <el-input v-model="row.opinion" @input="opinionInput($event,row)"></el-input>
                  </div>
                </template>
              </sn-crud>
            </el-row>
<!--          </fieldset>-->
        </el-card>
      </el-row>
    </el-form>
  </flow-page-container>
</template>

<script setup>
import ProjBizIrPrMApi from '@/project/api/constructionManagement/InspectionAndRectification/ProjBizIrPrM.js'
import FlowPageContainer from '@/common/components/exportFile/FlowPageContainer.vue'

let auth = new Map();
import {
  nextTick,
  onMounted,
  ref,
  reactive,
  toRef,
  defineProps,
  defineExpose,
  getCurrentInstance
} from 'vue';
import {
  useRoute,
  useRouter
} from 'vue-router'
import {useDicts} from "@/common/hooks/useDicts";
import {requestImgUrl} from "@/common/api/MinioFile";
import ProjBizIrRecordMApi from "@/project/api/constructionManagement/InspectionAndRectification/ProjBizIrRecordM";
import {btnHandle} from "@/project/components/hooks/buttonChangeName";
import BpmTaskApi from "@/project/api/bpm/bpmTask";

const {
  inspection_reform_result,
} = useDicts(['inspection_reform_result'])
const {
  proxy
} = getCurrentInstance()
const route = useRoute();
const router = useRouter();
const props = defineProps({
  data: Object,
});

let formRules = ref({
  reInspectionResults: [{
    required: true,
    message: "请选择检查结果"
  }],
});
const customData = ref({})
const parentResp = ref(null)
const flowPageContainerRef = ref(null)
const snForm = ref()
const isShowCloseBtn = ref(false);
const FlowActionType = ref(proxy.FlowActionType);
let approvalOption = props.data?.approvalOption ? props.data?.approvalOption : route.query?.procInstId ? ref({
  isShowApprovalList: true,
  isShowFlowDiagram: true,
  procInstId: route.query?.procInstId
}) : ref({
  isShowApprovalList: false,
  isShowFlowDiagram: false,
  procInstId: ''
});
const state = reactive({
  flowData: {
    businessKey: "",
    procInstId: "",
    procDefKey: "",
    taskId: "",
    formConfig: [],
    variableList: [],
    customPropertyConfigList: []
  },
  formAuth: {},
  taskFlag: 1,
  firstAct: false,
  handlerClose: null,
  rootFields: {},
  fullFieldNameMap: {},
  formAuthList: {}
})
const type = toRef(props.data?.type);
let delRowData = ref({});
let projBizIrPiSOption = ref({
  tip: false,
  border: true,
  index: true,
  stripe: true,
  menu: false,
  header: false,
  menuType: "text",
  addBtn: true,
  addBtnText: "新增",
  editBtn: true,
  editBtnText: "编辑",
  delBtn: true,
  delBtnText: "删除",
  cellBtn: true,
  column: [{
    label: "问题编号",
    prop: "serialNumber",
    type: "input",
  },{
    label: "描述",
    prop: "describe",
    type: "input",
  }, {
    label: "整改内容",
    prop: "reformContent",
    type: "input",
  }, {
    label: "整改图片",
    prop: "reformImage",
    columnSlot: true,
    cell: false
  }, {
    label: "复验结果",
    prop: "reInspectionResults",
    type: "select",
    hide: props.data?.tips === '',
    columnSlot: true,
    cell: false
  }, {
    label: "审批意见",
    prop: "opinion",
    type: "input",
    hide: props.data?.tips === '',
    columnSlot: true,
    cell: true
  }]
});

let formData = ref({
  projBizIrPiSDtoList: [{
    $cellEdit: true
  }]
});
const taskInfo = ref()
let submitQuestions = ref(props.data?.submitQuestions || [])
let totalQuestionNumber = ref(props.data?.totalQuestionNumber)
let completedQuestionNumber = ref(props.data?.completedQuestionNumber)
let info = ref(props.data?.info || {})
let recordInfo = ref(props.data?.recordInfo || {})
let createName = ref(props.data?.createName)
let createOrgName = ref(props.data?.createOrgName)
let createTime = ref(props.data?.createTime)

function subRowSave(form, done) {
  //编辑行
  done();
}

function subRowUpdate(form, index, done, loading) {
  //编辑行
  done();
}

function getSubmitQuestions(){
  let list = []
  submitQuestions.value.forEach((v) => {
    v.reformImageInfo = undefined
    list.push(v)
  })
  return list
}

function getCustomData() {
  return customData.value
}

// function getFormData() {
//   //获取formData数据
//   for (let item in delRowData.value) {
//     formData.value[item] = formData.value[item].concat(delRowData.value[item]);
//   }
//   return formData.value;
// }
formData = toRef(Object.keys(props.data?.formData || {}).length > 0 ? {
  ...props.data?.formData
} : toRef(formData.value));

function processAuthFieldForm(fieldList) {
  /**
   * 工作流表单权限功能：在扫描完成之前，业务可对表单字段进行修改。
   */
  //TODO 这里可以对权限表单的字段进行任意的修改,如果不满足业务需求，可以在这里手动添加你想要的字段
  return fieldList
};

function getApprovalOption(fun) {
  /**
   * 工作流表单权限功能(重要)：应对弹窗无法及时相应的问题，特别增加该函数修复该bug
   */
  nextTick(() => {
    fun(approvalOption.value)
  })
};

function customFormPermi(obj) {
  /**
   * 工作流表单权限功能：自定义表单权限处理函数，将对应的对象的属性还原成对应的权限值
   */
  let {
    field,
    fieldModelId
  } = obj
  let fullFieldName = `${fieldModelId}_${field}`
  //TODO 这里可以自定义处理，默认返回0即可
  return auth.get(fullFieldName) || 0
};

function handlerPrint(taskComment) {
  /**
   * 工作流打印模板功能
   */
  ProjBizIrPrMApi.printTemplate({
    id: formData.value.id,
    templateId: taskComment.templateId,
    formData: formData.value
  }).then(
    (res) => {
      router.push({
        name: "PrintDoc",
        query: {
          fileId: res.data,
        },
      });
      taskComment.close && taskComment.close();
    });
};

function getSubmitTask(taskActionDto) {
  /**
   * 工作流提交任务功能
   */
  let submitInfo = info.value
  submitInfo.projBizIrPiSDtoList = submitQuestions.value
  return ProjBizIrPrMApi.submitTask({
    busiDto: submitInfo,
    taskActionDto: taskActionDto
  });
};

function getStartFlow(formData, startProcDto) {
  return new Promise((resolve) => {
    resolve(
        ProjBizIrPrMApi.startFlow({
          busiData: getSubmitQuestions(),
          startProcDto: startProcDto
        }).then((respon) => {
          state.flowData.procDefKey = respon.data[0].procDefKey
          state.flowData.procInstId = respon.data[0].procInstId
          state.flowData.businessKey = respon.data[0].businessKey
          state.flowData.taskId = respon.data[0].taskId
          taskInfo.value = respon.data[0];
          customData.value = respon.data[0]
          nextTick(() => {
            console.log("执行",props.data)
            btnHandle(props.data.el.lastChild)
          })
        })
    )
  })
};

function submitFlowTask(resp) {
  parentResp.value = resp
  return proxy.$confirm('确定提交当前单据?', '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
    return BpmTaskApi.listRuTaskByProcInstId({
      procInstId: customData.value.procInstId
    }).then((params) => {
      flowPageContainerRef.value.handlerActionSubmit(params.data[0],1);
    })
  }).catch(() => {
    return true;
  })
}

function handlerAction(operation, taskComment, handlerClose) {
  /**
   * 工作流操作功能
   */
  // 启动并保存草稿后再次提交
  if (
    (operation.type == FlowActionType.value.SAVESTART || operation.type == FlowActionType.value.SAVE || operation.type == FlowActionType.value.START) && !formData.value.taskId) {
    let startProcDto = {
      procDefKey: formData.value.procDefKey,
      businessKey: null,
      clientId: null,
      ...taskComment?.dialogRefs?.getSubmitQuestions(),
    };
    let httpCall = null;
    if (operation.type == FlowActionType.value.SAVESTART) {
      httpCall = getStartFlow(formData, startProcDto);
    } else if (operation.type == FlowActionType.value.SAVE) {
      httpCall = getSaveFormData(formData);
    }
    httpCall.then(() => {
      proxy.$modal.msgSuccess("提交成功");
      taskComment.close && taskComment.close();
      handlerClose();
    });
  } else {
    operation.type == FlowActionType.value.START || operation.type == FlowActionType.value.SAVESTART ? (operation.type = FlowActionType.value.AGREE) : operation.type;
    let taskActionDto = {
      taskId: state.flowData.taskId,
      procInstId: state.flowData.procInstId,
      actionType: operation.type,
    };
    getSubmitTask(taskActionDto).then(() => {
      proxy.$modal.msgSuccess("任务办理成功");
      taskComment.close && taskComment.close();
      if (parentResp) {
        parentResp.value.close()
        props.data.onLinkTwo(proxy)
      }else{
        handlerClose();
      }
    }).catch(() => {
      taskComment.close && taskComment.close();
    });
  }
};

function initBusiForm() {
  /**
   * 工作流初始化表单功能
   */
  if (type.value === null || type.value === '' || type.value === undefined) {
    type.value = 'edit'
  }
  let that = proxy
  let routerQueryParams = route.query;
  auth = new Map(Object.entries(state.formAuth));
  if (routerQueryParams) {
    if (routerQueryParams.busiData) {
      formData.value = routerQueryParams.busiData;
    } else {
      if (routerQueryParams.businessKey) {
        let businessKey = route.query.businessKey;
        ProjBizIrPrMApi.view(businessKey).then(async resp => {
          info.value = resp?.data
          createName.value = info.value.createName
          createOrgName.value = info.value.createOrgName
          createTime.value = info.value.createTime
          submitQuestions.value = resp?.data?.projBizIrPiSDtoList || []
          // 修改父组件的图片处理逻辑
          if (submitQuestions.value) {
            // 使用 Promise.all 处理异步请求
            const promises = submitQuestions.value.map(async (v) => {
              if (v.reformImage && v.reformImageInfo?.id) {
                try {
                  // 确保 requestImgUrl 返回 Promise
                  v.reformImagePreviewUrl = await requestImgUrl(v.reformImageInfo.id);
                  v.reformImagePreviewUrlList = [v.reformImagePreviewUrl];
                } catch (e) {
                  console.error("图片加载失败", e);
                  v.reformImagePreviewUrl = "/fallback-image.jpg"; // 添加备用图片
                  v.reformImagePreviewUrlList = [];
                }
              }
              return v;
            });
            // 等待所有图片加载完成
            await Promise.all(promises);
          }
          // 处理记录问题
          if(info.value.recordId){
            ProjBizIrRecordMApi.view(info.value.recordId).then((resp) => {
              let record = resp.data
              recordInfo.value = record
              let projBizIrQuestionSDtoList = resp.data?.projBizIrQuestionSDtoList || []
              let totalQuestionNumberNew = projBizIrQuestionSDtoList.length
              let completedQuestions = projBizIrQuestionSDtoList.filter((v) => {
                return v.reformStatus === 'completed'
              })
              let completedQuestionNumberNew = completedQuestions.length
              totalQuestionNumber.value = totalQuestionNumberNew
              completedQuestionNumber.value = completedQuestionNumberNew
            })
          }
        });
      } else {
        that.$message.error("初始化失败,因为工作流未将流程信息传入！")
      }
    }
  }
};

function getSaveFormData(formData) {
  if (formData.id) {
    return ProjBizIrPrMApi.update(formData);
  } else {
    return ProjBizIrPrMApi.add(formData);
  }
};

function getFieldForm() {
  let rootFields = {
    ...state.rootFields
  }
  let fieldList = []
  let keys = []
  for (let key in rootFields) {
    keys.push(key)
    fieldList.push(rootFields[key])
  }
  const result = processAuthFieldForm ? processAuthFieldForm(fieldList) : fieldList;
  const message = {
    type: "getTemplateRoot",
    pathname: window.location.pathname,
    actKey: getQueryParams("actKey"),
    content: result
  };
  window.parent.postMessage(JSON.parse(JSON.stringify(message)), "*");
};

function getQueryParams(key) {
  let url = window.location.href;
  // 使用正则表达式解析URL中的查询字符串
  var queryString = url.split('?')[1];
  if (!queryString) {
    return {};
  }
  var params = {};
  // 分割查询字符串成单个参数
  var vars = queryString.split("&");
  for (var i = 0; i < vars.length; i++) {
    var pair = vars[i].split("=");
    params[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);
  }
  return params[key];
};

function initData(urlParams, taskInfo, handlerClose) {
  // 如果用户从流程设计点击启动, 如果是在线表单, 则阻止执行initData, 防止在线表单报错
  if (urlParams.menuId && urlParams.openInStart) {
    return
  }
  state.flowData.procDefKey = taskInfo.procDefKey;
  state.flowData.procInstId = urlParams.procInstId;
  state.flowData.businessKey = urlParams.businessKey;
  state.flowData.taskId = urlParams.taskId;
  state.flowData.fiedPermission = taskInfo.fiedPermission;
  state.flowData.variableList = taskInfo.variableList;
  state.taskFlag = urlParams.taskFlag;
  state.firstAct = taskInfo.firstAct;
  state.handlerClose = handlerClose;
  state.flowData.customPropertyConfigList = taskInfo.customPropertyConfigList
  let fieldPerList = taskInfo.formConfig?.formFieldConfig?.fieldPerList || []
  handleFormAuth(fieldPerList);
  initBusiForm();
};

function handleFormAuth(data) {
  let formAuth = {};
  for (let item of data) {
    let permi = 1;
    if (item.readonly) {
      permi = 2;
    }
    if (item.hidden) {
      permi = 3;
    }
    if (!isCamelCase(item.fieldModelId)) {
      item.fieldModelId = toCamelCase(item.fieldModelId)
    }
    formAuth = {
      ...formAuth,
      [`${item.fieldModelId}_${item.field}`]: permi,
    };
  }
  state.formAuth = formAuth;
  state.formAuthList = JSON.parse(JSON.stringify(formAuth))
};
/**
 * 处理表单权限相关操作, 开发者在template的v-permi设置中必须使用此函数
 * @param {Object} obj - 包含字段名称的对象
 * @param {Function} [callback] - 可选的回调函数，接收 obj 作为参数
 * 如果传入对象的 fieldName 不在 rootFields 中，则将其添加到 rootFields 数组，然后执行回调函数（如果存在）
 */
function formPermi(obj, callback) {
  if (!state.rootFields[obj.fieldModelId + '__' + obj.field]) {
    state.rootFields[obj.fieldModelId + '__' + obj.field] = obj
  }
  return callback && callback(obj)
};

/**
 * 审批意见
 */
function opinionInput(e,row) {
  submitQuestions.value.forEach((v) => {
    if (v.id === row.id) {
      v.opinion = e
    }
  })
}

function toCamelCase(s) {
  return s.toLowerCase().replace(/_(.)/g, function(match, group1) {
    return group1.toUpperCase();
  });
};

function handlerOpenConfirm(taskInfo,resp) {
  parentResp.value = resp
  ProjBizIrPrMApi.view(taskInfo.businessKey).then(async myRes => {
    info.value = myRes?.data
    flowPageContainerRef.value.handlerActionSubmit(taskInfo,1);
  })

}

function getTaskInfo() {
  return taskInfo.value
}

function isCamelCase(str) {
  return /^[a-z][a-zA-Z0-9]*$/.test(str)
};
defineExpose({
  getSubmitQuestions,
  getSaveFormData,
  getStartFlow,
  handlerOpenConfirm,
  getTaskInfo,
  getCustomData,
  submitFlowTask
});
onMounted(() => {
  nextTick(() => {
    getFieldForm()
  })
})
</script>

<style lang="scss" scoped>
::v-deep .lf-graph{
  height: 700px!important;
  overflow-y: auto;
}
.special-text{
  color: red;
}
.commit-info-container{
  margin-top: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
