<template>
  <div>
    <sn-crud :data="listData" :option="option" v-model:page="queryForm.page" v-model:search="queryForm.filter" @on-load="getPageList" @search-change="onChangeSearch" @search-reset="onResetSearch">
      <template #receiptNumber="scope">
        <el-link type="primary" @click="onEditData(scope.row, 'view')">{{ scope.row.receiptNumber }}</el-link>
      </template>
      <template #menuLeft="{ row, index, size }">
        <el-button type="primary" :size="size" icon="el-icon-plus" @click="onEditData()">新增</el-button>
        <el-button type="primary" :size="size" icon="el-icon-download" @click="onExportData(row)">列表导出</el-button>
        <el-button type="primary" :size="size" icon="el-icon-download" @click="downloadFormTemplate()">表单模板 </el-button>
        <!-- <el-button type="primary" :size="size" icon="el-icon-upload" @click="onImportData(row)">列表导入</el-button>
        <el-button type="primary" :size="size" icon="el-icon-download" @click="downloadTemplate(row)">列表模版</el-button> -->
      </template>
      <template #menu="{ row, index, size }">
        <!--未提交-->
        <el-button type="primary" :size="size" v-if="row.receiptStatus === '1'" icon="el-icon-edit" link @click="onEditData(row, 'edit')"> 编辑 </el-button>
        <!--          <el-button type="primary" :size="size" v-if="row.receiptStatus === '1'" icon="el-icon-upload" link @click="onEditData(row)">上传</el-button>-->
        <el-button type="primary" :size="size" v-if="row.receiptStatus === '1'" icon="el-icon-download" link @click="onDownloadData(row)"> 下载 </el-button>

        <el-button type="danger" :size="size" v-if="row.receiptStatus === '1' && row.procInstanceId" icon="el-icon-edit" link @click="onCancelData(row, 'stop')"> 作废 </el-button>
        <el-button type="danger" :size="size" v-if="row.receiptStatus === '1' && !row.procInstanceId" icon="el-icon-delete" link @click="onDelData([row])"> 删除 </el-button>

        <!--已提交-->
        <el-button type="danger" :size="size" v-if="row.receiptStatus === '2'" icon="el-icon-edit" link @click="onCancelData(row, 'revoke')"> 撤回 </el-button>
        <el-button type="primary" :size="size" v-if="row.receiptStatus === '2'" icon="el-icon-download" link @click="onDownloadData(row)"> 下载 </el-button>
        <!--已驳回到上一步-->
        <el-button type="primary" :size="size" v-if="row.receiptStatus === '3'" icon="el-icon-download" link @click="onDownloadData(row)"> 下载 </el-button>
        <!--审批中-->
        <!--跳到编辑页-->
        <el-button type="primary" :size="size" v-if="row.receiptStatus === '4'" icon="el-icon-download" link @click="onDownloadData(row)"> 下载 </el-button>
        <!--已作废-->
        <el-button type="primary" :size="size" v-if="row.receiptStatus === '5'" icon="el-icon-download" link @click="onDownloadData(row)"> 下载 </el-button>
        <!--已审批-->
        <el-button type="primary" :size="size" v-if="row.receiptStatus === '6'" icon="el-icon-download" link @click="onDownloadData(row)"> 下载 </el-button>
        <!--已驳回提交人-->
        <el-button type="primary" :size="size" v-if="row.receiptStatus === '7'" icon="el-icon-edit" link @click="onEditData(row, 'edit')"> 编辑 </el-button>
        <!--          <el-button type="primary" :size="size" v-if="row.receiptStatus === '7'" icon="el-icon-upload" link @click="onEditData(row)">上传</el-button>-->
        <el-button type="primary" :size="size" v-if="row.receiptStatus === '7'" icon="el-icon-download" link @click="onDownloadData(row)"> 下载 </el-button>
        <el-button type="danger" :size="size" v-if="row.receiptStatus === '7' && row.procInstanceId" icon="el-icon-edit" link @click="onCancelData(row, 'stop')"> 作废 </el-button>
      </template>
    </sn-crud>
    <div ref="divDialogRef"></div>
  </div>
</template>

<script>
import ProjBizConsStartWorkReportApi from '@/project/api/constructionManagement/sceneStartWorkReport/ProjBizConsStartWorkReport.js'
import EditProjBizConsStartWorkReport from './components/EditProjBizConsStartWorkReport.vue'
import importView from './components/import.vue'
import { generateWordDocument, download, printDocument } from '@/project/components/downloadWord/word.js'

export const routerConfig = [
  {
    menuType: 'C',
    menuName: '现场开工报审'
  },
  {
    menuType: 'F',
    menuName: '查看',
    perms: 'sceneStartWorkReport:show',
    api: [ProjBizConsStartWorkReportApi.config.pageList]
  },
  {
    menuType: 'F',
    menuName: '新增',
    perms: 'sceneStartWorkReport:add',
    api: [ProjBizConsStartWorkReportApi.config.add]
  },
  {
    menuType: 'F',
    menuName: '修改',
    perms: 'sceneStartWorkReport:update',
    api: [ProjBizConsStartWorkReportApi.config.update, ProjBizConsStartWorkReportApi.config.view]
  },
  {
    menuType: 'F',
    menuName: '删除',
    perms: 'sceneStartWorkReport:del',
    api: [ProjBizConsStartWorkReportApi.config.remove]
  },
  {
    menuType: 'F',
    menuName: '上传',
    perms: 'sceneStartWorkReport:upload',
    api: [ProjBizConsStartWorkReportApi.config.view, ProjBizConsStartWorkReportApi.config.upload]
  },
  {
    menuType: 'F',
    menuName: '下载',
    perms: 'sceneStartWorkReport:download',
    api: [ProjBizConsStartWorkReportApi.config.view, ProjBizConsStartWorkReportApi.config.pageList, ProjBizConsStartWorkReportApi.config.download]
  },
  {
    menuType: 'F',
    menuName: '撤回',
    perms: 'sceneStartWorkReport:revoke',
    api: [
      ProjBizConsStartWorkReportApi.config.view,
      ProjBizConsStartWorkReportApi.config.startFlow,
      ProjBizConsStartWorkReportApi.config.submitTask,
      ProjBizConsStartWorkReportApi.config.printTemplate
    ]
  },
  {
    menuType: 'F',
    menuName: '作废',
    perms: 'sceneStartWorkReport:stop',
    api: [
      ProjBizConsStartWorkReportApi.config.view,
      ProjBizConsStartWorkReportApi.config.startFlow,
      ProjBizConsStartWorkReportApi.config.submitTask,
      ProjBizConsStartWorkReportApi.config.printTemplate
    ]
  }
]
</script>

<script setup>
import { ref, getCurrentInstance } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getCurrentFormattedTime } from '@/common/utils/datetime'
// import { download } from 'sn-base-utils';
const { proxy } = getCurrentInstance()
const route = useRoute()
const router = useRouter()
let option = ref({
  tip: false,
  dialogType: 'page',
  border: true,
  index: true,
  stripe: true,
  menu: true,
  header: true,
  height: 'auto',
  searchSpan: 6,
  searchIcon: true,
  searchMenuSpan: 6,
  searchIndex: 3,
  selection: true,
  showTree: false,
  // importBtn: true,
  // excelBtn: true,
  addBtn: false,
  delBtn: false,
  delBtns: false,
  editBtn: false,
  // delBtnsText: "批量删除",
  // addBtnText: "新增",
  // customEditBtnText: "编辑",
  // customDelBtnText: "删除",
  // customViewBtnText: "查看",
  // excelBtnText: '批量导出',
  // importBtnText: '批量导入',
  column: [
    {
      label: '单据编号',
      prop: 'receiptNumber',
      search: true,
      overHidden: true,
    },
    {
      label: '工程名称',
      prop: 'projectName',
      search: true,
      overHidden: true,
    },
    {
      label: '监理单位',
      prop: 'supervisorCompany',
      search: true,
      overHidden: true,
    },
    {
      label: '开工日期',
      prop: 'startDate',
      search: false,
      width: 100,
    },
    {
      label: '申请人',
      prop: 'applicant',
      search: true
    },
    {
      label: '申请单位',
      prop: 'applicantOrg',
      search: true,
      overHidden: true,
    },
    {
      label: '审批状态',
      prop: 'receiptStatus',
      search: true,
      type: 'select',
      dicUrl: '/system/dict/data/type/global_biz_flow_status',
      dicMethod: 'get',
      props: {
        label: 'dictLabel',
        value: 'dictValue'
      },
      width: 100,
      html: true,
      formatter: (val, value) => {
        if (value === '1') {
          return `<span style="color:#4871C0">草稿</span>`
        } else if (value === '2') {
          return `<span style="color:#4871C0">已提交</span>`
        } else if (value === '4') {
          return `<span style="color:#4871C0">审批中</span>`
        } else if (value === '3') {
          return `<span style="color:red">上级驳回</span>`
        } else if (value === '5') {
          return `<span style="color:red">已作废</span>`
        } else if (value === '6') {
          return `<span style="color:#4871C0">已审批</span>`
        } else if (value === '7') {
          return `<span style="color:red">已驳回</span>`
        }
        return `<p></p>`
      }
    }
  ]
})
let listData = ref([])
let queryForm = ref({
  page: {
    pageSize: 20,
    pageNum: 1,
    total: 10
  },
  filter: {
    createTime: null
  }
})
let formRules = ref({})
const divDialogRef = ref(null)

function getPageList() {
  //查询分页列表
  // 查询前处理参数
  const params = handleQueryForm()
  ProjBizConsStartWorkReportApi.pageList(params).then((res) => {
    listData.value = res.data.dataList
    queryForm.value.page.total = res.data.totalCount
  })
}

function onResetSearch() {
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20
  }
  getPageList()
}

function handleQueryForm() {
  // 处理参数
  const { pageSize, pageNum } = queryForm.value.page
  const filter = {}
  for (let key in queryForm.value.filter) {
    if (queryForm.value.filter[key]?.length) {
      // sn-curd搜索select会生成$prop的变量
      if (key.startsWith('$')) continue
      filter[key] = queryForm.value.filter[key]
    }
  }
  delete filter.createTime
  if (Array.isArray(queryForm.value.filter.createTime) && queryForm.value.filter.createTime?.length === 2) {
    filter['beginCreateTime'] = queryForm.value.filter.createTime[0]
    filter['endCreateTime'] = queryForm.value.filter.createTime[1]
  }
  const searchParams = {
    page: {
      pageSize,
      pageNum
    },
    filter
  }
  return searchParams
}

function onChangeSearch(params, done) {
  //搜索按钮操作
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20
  }
  getPageList()
  done && done()
}

function generateButtons(editType, row) {
  if (editType !== 'view') {
    const hasProcId = row?.procInstanceId
    const buttons = [
      { key: 'cancelDirect', text: '关闭', buttonType: '', icon: 'el-icon-close' },
      { key: 'downloadForm', text: '下载表单', buttonType: 'primary', icon: 'el-icon-download' }
    ]

    if (hasProcId) {
      buttons.push(
        {
          key: 'save',
          text: '保存',
          buttonType: 'primary',
          icon: 'el-icon-check'
        },
        {
          key: 'submitFlowTaskProcess',
          text: '提交',
          buttonType: 'primary',
          icon: 'sn-button-fasong'
        }
      )
    } else {
      buttons.push(
        {
          key: 'save',
          text: '保存',
          buttonType: 'primary',
          icon: 'el-icon-check'
        },
        {
          key: 'afterProcessIsInitiated',
          text: '发起流程',
          buttonType: 'primary',
          icon: 'sn-button-fasong'
        }
      )
    }
    return buttons
  }
  return row.receiptStatus != '1'
    ? [{ key: 'cancelDirect', text: '关闭', buttonType: '', icon: 'el-icon-close' },
      { key: 'downloadForm', text: '下载表单', buttonType: 'primary', icon: 'el-icon-download' }
    ]
    : [
      { key: 'cancelDirect', text: '关闭', buttonType: '', icon: 'el-icon-close' },
        { key: 'edit', text: '编辑', buttonType: 'primary', icon: 'el-icon-edit' },
        { key: 'downloadForm', text: '下载表单', buttonType: 'primary', icon: 'el-icon-download' }
      ]
}

async function onEditData(row, option) {
  //编辑,新增按钮操作
  // let editType = row?.id ? 'edit' : 'add';
  let editType = row?.procInstanceId ? 'view' : row?.id ? 'edit' : 'add'
  if (option) {
    editType = option
  }
  let rowInfo = await (editType !== 'add' ? ProjBizConsStartWorkReportApi.view(row.id) : {})
  const formData = editType !== 'add' ? rowInfo.data : rowInfo
  delete formData.beginCreateTime
  // delete formData.createTime;
  delete formData.endCreateTime
  proxy.$DialogForm.show({
    title: editType == 'edit' ? '编辑' : editType == 'view' ? '查看' : '新增',
    type: 'page',
    width: '100%',
    content: EditProjBizConsStartWorkReport,
    el: divDialogRef.value,
    data:
      editType !== 'add'
        ? {
            formData: rowInfo.data,
            isShowCloseBtn: false,
            approvalOption: ref({
              isShowApprovalList: true,
              isShowFlowDiagram: true,
              procInstId: rowInfo.data.procInstanceId
            }),
            type: editType,
            el: divDialogRef.value,
            closeDialog: closeDialog,
          }
        : {
            approvalOption: ref({
              isShowApprovalList: false,
              isShowFlowDiagram: false,
              procInstId: null
            }),
            type: editType,
            el: divDialogRef.value,
            closeDialog: closeDialog,
          },
    option: {
      submitBtn: false,
      emptyBtn: false,
      submitText: '保存',
      emptyText: '关闭',
      extendButton: generateButtons(editType, row)
      //   extendButton: editType !== "view" ? [{
      //     key: 'cancel',
      //     text: '关闭',
      //     buttonType: '',
      //     icon: 'el-icon-close',
      //   },{
      //     key: 'downloadForm',
      //     text: '下载表单',
      //     buttonType: 'primary',
      //     icon: 'el-icon-download',
      //   }, {
      //     key: 'save',
      //     text: '保存',
      //     buttonType: 'primary',
      //     icon: 'el-icon-check',
      //   }, {
      //     key: 'afterProcessIsInitiated',
      //     text: '发起流程',
      //     buttonType: 'primary',
      //     icon: 'sn-button-fasong',
      //   }, ] : [{
      //     key: 'cancel',
      //     text: '关闭',
      //     buttonType: '',
      //     icon: 'el-icon-close',
      //   }, {
      //     key: 'downloadForm',
      //     text: '下载表单',
      //     buttonType: 'primary',
      //     icon: 'el-icon-download',
      //   }],
    },
    callback: (res) => {
      let vm = res.dialogRefs
      if (vm) {
        let formData = vm.getFormData()
        switch (res.type) {
          // 取消
          case 'cancelDirect':
            proxy.$modal.confirm('确认关闭？', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            })
            .then(() => {
              res.close()
            })
            break
          // 保存
          case 'save':
            vm.getSaveFormData(formData).then((resp) => {
              proxy.$message.success('保存成功')
              getPageList()
              // res.close();
            })
            break
          // 发起流程
          case 'afterProcessIsInitiated':
            let startProcDto = {
              businessKey: null,
              clientId: null
            }
            if(formData.procInstanceId){
              let taskInfo = vm.getTaskInfo();
              vm.handlerOpenConfirm(taskInfo,res);
              getPageList();
            }else {
              vm.getStartFlow(formData, startProcDto).then(() => {
                let taskInfo = vm.getTaskInfo();
                vm.handlerOpenConfirm(taskInfo,res);
                getPageList();
              })
            }
            break
          case 'downloadForm':
            vm.downloadWord()
            break
          // 编辑
          case 'edit':
            res.close()
            onEditData(row)
            break
          //提交流程
          case 'submitFlowTaskProcess':
            vm.submitFlowTask()
            // vm.submitFlowTask().then((resp) => {
            //   if (!resp) {
            //     getPageList()
            //     res.close()
            //   }
            // })
            break
        }
      }
    }
  })
}
// 关闭新增/修改弹窗
function closeDialog(e) {
  getPageList();
  e.close()
}
const generatedFile = ref(null)

// 下载表单模板
async function downloadFormTemplate() {
  try {
    generatedFile.value = await generateWordDocument({}, 'projBizConsStartWorkReportBlank')
    if (generatedFile.value) {
      download(generatedFile.value, '工程开工报审表模板.docx')
      proxy.$message.success('word下载成功！')
    } else {
      console.error('No file generated')
    }
  } catch (error) {
    console.error('Error generating document:', error)
  }
}

function onImportData(row) {
  proxy.$DialogForm.show({
    title: '上传',
    type: 'dialog',
    width: '80%',
    content: importView,
    data: {},
    option: {
      submitBtn: true,
      emptyBtn: true,
      submitText: '确定',
      emptyText: '取消'
    },
    callback: (res) => {
      let vm = res.dialogRefs
      if (vm) {
        res.close()
      } else {
        res.close()
      }
    }
  })
}

function onDelData(rows) {
  //删除行
  if (!rows || rows.length == 0) {
    proxy.$message.info('请勾选数据！')
    return false
  }
  let ids = rows.map((item) => {
    return item.id
  })
  proxy.$modal
    .confirm('确认删除数据项？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    .then(() => {
      ProjBizConsStartWorkReportApi.remove(ids).then((res) => {
        proxy.$message.success('已删除')
        getPageList()
      })
    })
    .catch(() => {})
}

//下载现场开工报审模板
function downloadTemplate() {
  proxy.download('/project/sceneStartWorkReport/import/template', {}, '现场开工报审模板.xlsx')
}

function onExportData() {
  const params = handleQueryForm()
  let queryForm = JSON.parse(JSON.stringify(params))
  let timestamp = proxy.parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')
  proxy.download('/project/sceneStartWorkReport/export', queryForm, '现场开工报审-' + timestamp + '.xlsx')
}

function toReportDetail(row) {
  router.push({
    path: '/lifecycleBiz/views/constructionManagement/sceneStartWorkReport/components/EditProjBizConsStartWorkReport',
    query: {
      receiptNumber: row.receiptNumber,
      businessKey: row.id
    }
  })
}

/*流程作废*/
function onCancelData(row, type) {
  //删除行
  let formDate = {
    id: row.id,
    actionType: type
  }
  let typeName
  if (type == 'revoke') {
    typeName = '撤回'
  } else {
    typeName = '作废'
  }
  proxy.$modal
    .confirm('确认' + typeName + '流程？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    .then(() => {
      ProjBizConsStartWorkReportApi.operateFlow(formDate).then((res) => {
        proxy.$message.success('已' + typeName)
        getPageList()
      })
    })
    .catch(() => {})
}

function onEditFlowData(row) {
  proxy.$modal
    .confirm('确认撤回数据项？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    .then(() => {
      ProjBizConsStartWorkReportApi.remove(row.id).then((res) => {
        proxy.$message.success('已删除')
        getPageList()
      })
    })
    .catch(() => {})
}

function onDownloadData(row) {
  let params = {
    relevantId: row.id,
    type: 'sceneStartWorkReport'
  }
  let timestamp = proxy.parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')
  proxy.download(`/project/sceneStartWorkReport/fillTemplateAndDownload/${row.id}`, params, '现场开工报审附件导出-' + row.receiptNumber + '-' + timestamp + '.zip')
}
</script>

<style lang="scss" scoped></style>
