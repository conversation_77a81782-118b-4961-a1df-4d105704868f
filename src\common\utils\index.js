/*
 * @Author: 王云飞
 * @Date: 2024-12-20 09:57:03
 * @LastEditTime: 2024-12-25 14:49:42
 * @LastEditors: 王云飞
 * @Description:
 *
 */
export const loadView = (view) => {
  const str = view.substr(0, 1)
  let path = view
  if (str == '/') {
    path = view.slice(1)
  }
  if (process.env.NODE_ENV === 'development') {
    return () => require.ensure([], (require) => require(`@/${path}`))
  } else {
    return () => import(`@/${path}`)
  }
}

// 初始化菜单使用获取项目下文件
export const readLocalSrcFiles = () => {
  return require.context('@/', true, /routerConfig.js|index.vue/)
}
export const validateUrl= (url)=>{
  const TEXT_WHITELIST = /^[a-zA-Z0-9\u4e00-\u9fa5，。！？、；：“”‘’（）&《》【】—……\-\s]*$/;
  if(TEXT_WHITELIST.test(url)){
    return '/404'
  }else{
    return url
  }
}