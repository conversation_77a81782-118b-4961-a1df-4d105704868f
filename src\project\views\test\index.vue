<template>
  <el-form :model="formData" :rules="formRules" ref="formRef" label-width="0px" label-position="right">
    <el-row :gutter="14" :span="24">
      <el-col :span="24">
        <sn-crud :data="listData" :option="option" v-model:page="queryForm.page" v-model:search="queryForm.filter"
                 @on-load="getPageList" @addBtnHandle="onAddData" @row-del="onDelData">
          <template #header = "{ row, size }" v-if="isShowAddBtn">
            <div style="margin: 0 0 10px 10px">
              <el-button type="primary"  @click="onAddData" plain><el-icon style="margin-right: 5px;"><Plus /></el-icon>新增</el-button>
            </div>
          </template>
          <template #menu="{ row, size }">
<!--            <el-button type="success" :size="size" icon="el-icon-check" link v-if="isHasAi" :disabled="row.reviewStatus"-->
<!--                       @click="onAiCheckData(row)">AI审查-->
<!--            </el-button>-->
            <el-button type="danger" :size="size" icon="el-icon-delete" link @click="onDelData([row])">删除</el-button>
            <el-button type="primary" :size="size" icon="el-icon-view" link @click="onPreview(row)">预览</el-button>
            <el-button type="primary" :size="size" icon="el-icon-download" link @click="onDownData(row)">下载
            </el-button>
          </template>
<!--                    <template #reviewStatus = "{ row, size }">-->
<!--                      <div class="ai-review-result" v-if="row.reviewStatus && row.reviewStatus ==='REVIEWING'">-->
<!--                        <el-icon><CircleCheck color="#67C23A" /></el-icon>-->
<!--                        <span style="color: #67C23A;margin-left: 5px">审查中</span>-->
<!--                      </div>-->
<!--                      <div class="ai-review-result" v-if="row.reviewStatus && row.reviewStatus ==='REVIEW_COMPLETE'">-->
<!--                        <el-icon><CircleCheck color="#F56C6C" /></el-icon>-->
<!--                        <span style="color: #67C23A;margin-left: 5px">查看结果</span>-->
<!--                      </div>-->
<!--                    </template>-->
        </sn-crud>
      </el-col>
    </el-row>
  </el-form>
  <el-dialog
      v-model="dialogVisible"
      title="文件上传"
      width="500"
  >
    <sn-upload :drag="true" :key="componentKey" :limit="1" listType="text" :autoUpload="true" :fileMaxSize="209715200"
               @input="(fileList) => uploadData(fileList, '')"/>
  </el-dialog>
</template>

<script>
import ProjBizDmStgMApi from "@/scx/ProjBizDmStgM.js";
import {formatBytes} from "@/scx/Bytes"

// export const routerConfig = [{
//   menuType: "C",
//   menuName: "XX管理",
// }, {
//   menuType: "F",
//   menuName: "查看",
//   perms: "show",
//   api: [ProjBizDmStgMApi.config.pageList],
// }, {
//   menuType: "F",
//   menuName: "新增",
//   perms: "add",
//   api: [ProjBizDmStgMApi.config.add],
// }, {
//   menuType: "F",
//   menuName: "修改",
//   perms: "update",
//   api: [ProjBizDmStgMApi.config.update, ProjBizDmStgMApi.config.view],
// }, {
//   menuType: "F",
//   menuName: "删除",
//   perms: "del",
//   api: [ProjBizDmStgMApi.config.remove],
// }];
</script>

<script setup>

import FileApi from "sn-base-layout-vue3/packLayout/api/File";
import * as Base64 from "js-base64";

defineOptions({name: 'table'})
import {
  ref, getCurrentInstance, toRef
} from 'vue';
import store from "@/store";
import {getCurrentFormattedTime} from "@/common/utils/datetime";

const props = defineProps({
  // 是否分页查询
  isPageSearch: {
    type: Boolean,
    default: true,
    required: true
  },
  // 业务类型,必传
  type: {
    type: String,
    default: '',
    required: true
  },
  // 业务关联ID
  relevantId: {
    type: String,
    default: "",
    required: false
  },
  // 是否删除minio
  isDeleteMinio: {
    type: Boolean,
    default: false,
    required: false
  },
  // 是否有AI审查
  isHasAi: {
    type: Boolean,
    default: true,
    required: false
  },
  // 文件序列号生成器,必传
  fileSerialNumberBuilder: {
    type: Function,
    required: true
  },
  // 预览配置
  previewConfig:{
    type: Object,
    required: true
  },
  // 按钮配置
  isShowAddBtn: {
    type: Boolean,
    default: true,
    required: false
  },
  // 按钮配置
  isShowDelBtn: {
    type: Boolean,
    default: true,
    required: false
  },
  // 按钮配置
  isShowPreviewBtn: {
    type: Boolean,
    default: true,
    required: false
  },
  // 按钮配置
  isShowDownloadBtn: {
    type: Boolean,
    default: true,
    required: false
  },
});
// 文档类型
const isPageSearch = toRef(props.isPageSearch)
// 文档类型
const type = toRef(props.type)
// 关联ID
const relevantId = toRef(props.relevantId)
// 是否删除minio
const isDeleteMinio = toRef(props.isDeleteMinio)
// 是否有AI审查
const isHasAi = toRef(props.isHasAi)
// form规则
let formRules = ref({});
// form数据
let formData = ref({})
// 是否弹窗上传框
let dialogVisible = ref(false)
// 上传组件key,保证重新渲染
const componentKey = ref(0);
// 预览服务器地址
const previewConfig = ref(props.previewConfig)
// 按钮组配置
const buttonConfig = ref(props.buttonGroupConfig)
const {
  proxy
} = getCurrentInstance()
let option = ref({
  tip: false,
  dialogType: "dialog",
  border: true,
  index: true,
  stripe: true,
  menu: true,
  header: false,
  searchSpan: 6,
  searchIcon: true,
  searchMenuSpan: 6,
  searchIndex: 3,
  selection: false,
  excelBtn: false,
  excelBtnText: "下载",
  delBtn: false,
  editBtn: false,
  delBtns: false,
  delBtnsText: "批量删除",
  addBtn: false,
  addBtnText: "新增",
  searchBtn: false,
  emptyBtn: false,
  refreshBtn: false,
  filterBtn: false,
  columnBtn: false,
  menuWidth: 360,
  customEditBtnText: "编辑",
  customDelBtnText: "删除",
  customViewBtnText: "查看",
  column: [{
    label: "文件名称",
    prop: "fileName",
    overHidden: true,
    search: false,
    columnSlot: false,
    searchSlot: false,
    queryType: "LIKE"
  }, {
    label: "文件大小",
    prop: "fileSizeFormat",
    columnSlot: false,
    searchSlot: false
  }, {
    label: "上传人",
    prop: "createName",
    columnSlot: false,
    searchSlot: false
  }, {
    label: "上传时间",
    prop: "createTime",
    columnSlot: false,
    searchSlot: false
  }]
});
let listData = ref([]);
const queryForm = toRef({
  page: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  filter: {}
});

/**
 * 查询列表数据
 * @returns {UnwrapRef<*[]>}
 */
function getListData() {
  return listData.value || [];
}

/**
 * 查询列表
 */
function getPageList() {
  // 判断type和relevantId
  if (type.value === '' || type.value === undefined || type.value === null || relevantId.value === '' || relevantId.value === undefined || relevantId.value === null) {
    return
  }
  const params = handleQueryForm();
  ProjBizDmStgMApi.pageList(params).then((res) => {
    listData.value = res.data.dataList;
    listData.value.forEach((v) => {
      v.fileSizeFormat = formatBytes(v.fileSize)
    })
    if (isPageSearch.value) {
      queryForm.value.page.total = res.data.totalCount;
    } else {
      queryForm.value.page.total = 0
    }
  });
}

/**
 * 构建查询条件
 * @returns {{filter: {}, page: {pageSize: number, pageNum: number}}}
 */
function handleQueryForm() {
  // 处理参数
  let {
    pageSize,
    pageNum
  } = queryForm.value.page;
  const filter = {}
  for (let key in queryForm.value.filter) {
    if (queryForm.value.filter[key]?.length) {
      // sn-curd搜索select会生成$prop的变量
      if (key.startsWith("$")) continue;
      filter[key] = queryForm.value.filter[key];
    }
  }
  delete filter.createTime;
  if (Array.isArray(queryForm.value.filter.createTime) && queryForm.value.filter.createTime?.length === 2) {
    filter["beginCreateTime"] = queryForm.value.filter.createTime[0];
    filter["endCreateTime"] = queryForm.value.filter.createTime[1];
  }
  filter.type = type.value
  filter.relevantId = relevantId.value
  if (!isPageSearch.value) {
    pageNum = -1
    pageSize = -1
  }
  return {
    page: {
      pageSize,
      pageNum,
    },
    filter,
  }
}

/**
 * 下载文件
 * @param row
 */
function onDownData(row) {
  proxy.download("/project/document/storage/exportFile", [row.id], row.fileName);
}

/**
 * 文件预览
 * @param row
 */
function onPreview(row) {
  if (previewConfig.value.isExternalPreview) {
    proxy.$emit("onPreview", row)
  } else {
    builtInPreview(row)
  }
}

/**
 * 内置预览方法
 */
function builtInPreview(row) {
  let fileId = row.fileId
  FileApi.getFileNameAndUrlById(fileId).then(res => {
    let url = res.data.filePreviewUrl;
    if(previewConfig.value.substitute){ // 替换字符串
      Object.entries(previewConfig.value.substitute).forEach((ent) => {
        url = url.replace(ent[0],ent[1])
      })
    }
    let base64Url = encodeURIComponent(Base64.encode(url))
    let previewUrl = previewConfig.value.previewServerUrl + "?url=" + base64Url;
    window.open(previewUrl,'_blank')
  }).catch((err) => {

  })
}

/**
 * AI审查数据
 * @param row
 */
function onAiCheckData(row) {
  listData.value.forEach((v) => {
    if (row.fileId === v.fileId) {
      v.reviewStatus = 'REVIEWING'
    }
  })
  ProjBizDmStgMApi.aiReview(row.fileId).then((res)=>{
    if (!res.data) {
      listData.value.forEach((v) => {
        if (row.fileId === v.fileId) {
          v.reviewStatus = ''
        }
      })
    }
    proxy.$message.info("AI审查中，请稍后查看结果!");
    proxy.$emit("onAiReview",row)
  })
}

/**
 * 上传文件
 * @param e
 */
function uploadData(e) {
  if (e.length) {
    if (relevantId.value === '' || relevantId.value === undefined || relevantId.value === null) {
      e.forEach((v) => {
        let fileId = v.id
        let fileName = v.fileNameOld
        let fileSize = v.fileSize
        listData.value.push({
          fileId,
          fileName,
          fileSize,
          fileSerialNumber: props.fileSerialNumberBuilder(),
          fileSizeFormat: formatBytes(fileSize),
          createName: store.state.user && store.state.user.userName ? store.state.user.userName : '-',
          createTime: getCurrentFormattedTime()
        })
        proxy.$emit("onAddData",e)
      })
    } else {
      let addList = []
      e.forEach((v) => {
        let fileId = v.id
        let fileName = v.fileNameOld
        let fileSize = v.fileSize
        addList.push({
          type: type.value,
          relevantId: relevantId.value,
          fileId: fileId,
          fileName: fileName,
          fileSize: fileSize,
          fileSerialNumber: props.fileSerialNumberBuilder(),
        })
      })
      ProjBizDmStgMApi.addBatch(addList).then((res) => {
        proxy.$emit("onAddData",e)
        getPageList()
      })
    }
  }
  dialogVisible.value = false // 暂时关闭弹窗
}

/**
 * 上传文件
 */
function onAddData() {
  // 重新计算key,保证上传组件重新渲染
  if (componentKey.value > 100) {
    componentKey.value = 0
  } else {
    componentKey.value += 1
  }
  dialogVisible.value = true
}

/**
 * 删除数据
 * @param rows
 * @returns {boolean}
 */
function onDelData(rows) {
  //删除行
  if (!rows || rows.length == 0) {
    proxy.$message.info("请选择要删除的数据！");
    return false;
  }
  let ids = rows.map((item) => {
    return item.id;
  });
  proxy.$modal.confirm("确认删除数据项？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    rows.forEach((v) => {
      if (v.id) {
        ProjBizDmStgMApi.remove(ids).then((res) => {
          proxy.$message.success("已删除");
          getPageList();
        });
        // if (isDeleteMinio.value) { // 暂时这俩方法一样,因为没找到平台组封装的mix有删除minio文件的方法
        //   ProjBizDmStgMApi.deleteAndDeleteMinio(ids).then((res) => {
        //     proxy.$message.success("已删除");
        //     getPageList();
        //   });
        // } else {
        //   ProjBizDmStgMApi.remove(ids).then((res) => {
        //     proxy.$message.success("已删除");
        //     getPageList();
        //   });
        // }
      } else {
        listData.value = listData.value.filter((value) => {
          value.fileId = v.fileId
        })
      }
    })
    proxy.$emit("onDelete",rows)

  }).catch(() => {
  });
}

// 暴露方法
defineExpose({
  getListData,
});

</script>

<style lang="scss" scoped>

.tree-container {
  background-color: #fff;
  border-radius: 4px;
  max-height: 100vh;
  overflow-y: auto;
}

.ai-review-result {
  display: flex;
  align-items: center;
}
</style>
