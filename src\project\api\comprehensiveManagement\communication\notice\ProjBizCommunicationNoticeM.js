import { request, replaceUrl } from "sn-base-utils";

export default class ProjBizCommunicationNoticeMApi {
    static config = {
        add: {
            url: '/communication/notice/add',
            method: 'POST'
        },
        remove: {
            url: '/communication/notice/delete',
            method: 'DELETE'
        },
        update: {
            url: '/communication/notice/update',
            method: 'PUT'
        },
        view: {
            url: '/communication/notice/get/{id}',
            method: 'GET'
        },
        pageList: {
            url: '/communication/notice/page',
            method: "POST"
        },
        list: {
            url: '/communication/notice/list',
            method: "POST"
        },
        startFlow: {
            url: '/communication/notice/saveAndSubmitProc',
            method: "POST"
        },
        submitTask: {
            url: '/communication/notice/saveAndSubmitTask',
            method: "POST"
        },
        operateFlow: {
            url: `/communication/notice/operateFlow`,
            method: "POST"
        }
    };

    /**
     * 新增通知单
     * @param data
     * @returns {*}
     */
    static add(data) {
        return request({
            url: this.config.add.url,
            method: this.config.add.method,
            data: data
        });
    }

    /**
     * 删除通知单
     * @param data
     * @returns {*}
     */
    static remove(data) {
        return request({
            url: this.config.remove.url,
            method: this.config.remove.method,
            data: data
        });
    }

    /**
     * 工作流-启动流程
     * @returns {*}
     */
    static startFlow(data) {
        return request({
            url: this.config.startFlow.url,
            method: this.config.startFlow.method,
            data: data
        });
    }

    /**
     * 工作流-完成任务
     * @returns {*}
     */
    static submitTask(data) {
        return request({
            url: this.config.submitTask.url,
            method: this.config.submitTask.method,
            data: data
        });
    }

    /**
     * 更新通知单
     * @param data
     * @returns {*}
     */
    static update(data) {
        return request({
            url: this.config.update.url,
            method: this.config.update.method,
            data: data
        });
    }

    /**
     * 查询通知单详细
     * @param id
     * @returns {*}
     */
    static view(id) {
        return request({
            url: replaceUrl(this.config.view.url, { id }),
            method: this.config.view.method
        });
    }

    /**
     * 分页查询通知单列表
     * @param data
     * @returns {*}
     */
    static pageList(data) {
        return request({
            url: this.config.pageList.url,
            method: this.config.pageList.method,
            data: data
        });
    }

    /**
     * 全部通知单列表
     * @returns {*}
     */
    static list(data) {
        return request({
            url: this.config.list.url,
            method: this.config.list.method,
            data: data
        });
    }

    /**
     * 更新变更申请单
     * @param data
     * @returns {*}
     */
    static operateFlow(data) {
        return request({
            url: this.config.operateFlow.url,
            method: this.config.operateFlow.method,
            data: data
        });
    }
}
