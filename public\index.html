<!DOCTYPE html>
<html lang="" class="dark">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <link rel="icon" href="<%= BASE_URL %>logo.png">
  <!-- <script src="https://cdn.bootcdn.net/ajax/libs/jsencrypt/3.0.0-rc.1/jsencrypt.min.js"></script> -->
  <script>
    window.BASE_URL="http://gateway.dev.snpit.com:9999"
    window.KK_FILE_VIEW_URL="http://*************:8012/onlinePreview"; // 测试环境kkfileview
    // window.KK_FILE_VIEW_URL="http://*************:8012/onlinePreview";
  </script>
  <title>
    <%= webpackConfig.name %>
  </title>
  <style>
    html {
      height: 100%;
    }
#app-container{
  height:100%;
}
    body {
      margin: 0;
      height: 100%;
    }
    #loader-wrapper {
      width: 100vw;
      height: 100vh;
      background-image: linear-gradient(239deg, #F7FBFF 0%, #EBF5FB 100%);
      display: flex;
      align-items: center;
      justify-content: center;
    }




  </style>
</head>

<body>
  <noscript>
    <strong>We're sorry but <%= webpackConfig.name %> doesn't work properly without JavaScript enabled.
        Please enable it to continue.</strong>
  </noscript>
  <div id="app-container">

    <div id="loader-wrapper">
      <div class="loader">
        <img src="/imgs/loading.png" style="width: 200px;height: 140px;"></img>
      </div>

      <!-- <div id="text">正在加载系统资源，请耐心等待</div> -->
    </div>
  </div>
  <!-- built files will be auto injected -->
</body>

</html>
