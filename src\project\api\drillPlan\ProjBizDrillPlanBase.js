import { request, replaceUrl } from "sn-base-utils";

export default class ProjBizDrillPlanBaseApi {
    static config = {
        add: {
            url: '/project/drillPlan/add',
            method: 'POST'
        },
        remove: {
            url: '/project/drillPlan/delete',
            method: 'DELETE'
        },
        update: {
            url: '/project/drillPlan/update',
            method: 'PUT'
        },
        view: {
            url: '/project/drillPlan/get/{id}',
            method: 'GET'
        },
        pageList: {
            url: '/project/drillPlan/page',
            method: "POST"
        },
        list: {
            url: '/project/drillPlan/list',
            method: "POST"
        }
    };

    /**
     * 新增应急计划
     * @param data
     * @returns {*}
     */
    static add(data) {
        return request({
            url: this.config.add.url,
            method: this.config.add.method,
            data: data
        });
    }

    /**
     * 删除应急计划
     * @param data
     * @returns {*}
     */
    static remove(data) {
        return request({
            url: this.config.remove.url,
            method: this.config.remove.method,
            data: data
        });
    }

    /**
     * 更新应急计划
     * @param data
     * @returns {*}
     */
    static update(data) {
        return request({
            url: this.config.update.url,
            method: this.config.update.method,
            data: data
        });
    }

    /**
     * 查询应急计划详细
     * @param id
     * @returns {*}
     */
    static view(id) {
        return request({
            url: replaceUrl(this.config.view.url, { id }),
            method: this.config.view.method
        });
    }

    /**
     * 分页查询应急计划列表
     * @param data
     * @returns {*}
     */
    static pageList(data) {
        return request({
            url: this.config.pageList.url,
            method: this.config.pageList.method,
            data: data
        });
    }

    /**
     * 全部应急计划列表
     * @returns {*}
     */
    static list(data) {
        return request({
            url: this.config.list.url,
            method: this.config.list.method,
            data: data
        });
    }
}
