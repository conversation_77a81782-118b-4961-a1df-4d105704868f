<template>
  <div>
    <sn-crud :data="listData" :option="option" v-model:page="queryForm.page" v-model:search="queryForm.filter" @on-load="getPageList" @search-change="onChangeSearch" @search-reset="onResetSearch">
      <template #businessNumber="scope">
        <el-link type="primary" @click="onEditData(scope.row, 'view')">{{ scope.row.businessNumber }}</el-link>
      </template>
      <template #menuLeft="{ row, index, size }">
        <el-button type="primary" :size="size" icon="el-icon-plus" @click="onEditData(row)">新增</el-button>
        <el-button type="primary" :size="size" icon="el-icon-download" @click="onExportData(row)">列表导出</el-button>
      </template>
      <template #tellDoc="scope">
        <!--  <el-link type="primary" @click="toPreview(scope.row)">{{ scope.row.tellDoc }}</el-link> -->
        {{ scope.row.tellDoc }}
      </template>

      <template #publishStatus="scope">
        <span :class="['status-text', scope.row.publishStatus === '1' ? 'draft' : 'published']">
          {{ scope.row.publishStatus === '1' ? '草稿' : '已发布' }}
        </span>
      </template>
      <template #menu="{ row, index, size }">
        <!--未发布-->
        <el-button type="primary" :size="size" icon="el-icon-edit" link v-if="row.publishStatus === '1'" @click="onEditData(row)"> 编辑 </el-button>
        <!--         <el-button type="primary" :size="size" icon="el-icon-plus" link v-if="row.publishStatus === '1'" @click="onPutData(row)"> 发布 </el-button> -->
        <el-button type="danger" :size="size" icon="el-icon-delete" link v-if="row.publishStatus === '1'" @click="onDelData([row])"> 删除 </el-button>
        <!--已发布-->
        <el-button type="primary" :size="size" icon="el-icon-view" link v-if="row.publishStatus === '2'" @click="onEditData(row, 'view')"> 查看 </el-button>
        <el-button type="danger" :size="size" icon="el-icon-minus" link v-if="row.publishStatus === '2'" @click="onPutData(row)"> 撤回 </el-button>
        <el-button type="primary" :size="size" icon="el-icon-download" link v-if="row.publishStatus === '2'" @click="onDownloadData(row)"> 下载 </el-button>
      </template>
    </sn-crud>
    <div ref="myRef"></div>
  </div>
</template>

<script>
import ProjBizConsPlanTellApi from '@/project/api/constructionManagement/planTell/ProjBizConsPlanTell.js'
import EditProjBizConsPlanTell from '@/project/views/constructionManagement/technologyManagement/planTell/components/EditProjBizConsPlanTell.vue'
import { getToken } from 'sn-base-utils'
import ProjBizConsStartWorkReportApi from '@/project/api/constructionManagement/sceneStartWorkReport/ProjBizConsStartWorkReport'

export const routerConfig = [
  {
    menuType: 'C',
    menuName: '施工技术交底'
  },
  {
    menuType: 'F',
    menuName: '查看',
    perms: 'planTell:show',
    api: [ProjBizConsPlanTellApi.config.pageList]
  },
  {
    menuType: 'F',
    menuName: '新增',
    perms: 'planTell:add',
    api: [ProjBizConsPlanTellApi.config.add]
  },
  {
    menuType: 'F',
    menuName: '修改',
    perms: 'planTell:update',
    api: [ProjBizConsPlanTellApi.config.update, ProjBizConsPlanTellApi.config.view]
  },
  {
    menuType: 'F',
    menuName: '删除',
    perms: 'planTell:del',
    api: [ProjBizConsPlanTellApi.config.remove]
  },
  {
    menuType: 'F',
    menuName: '发布',
    perms: 'planTell:publish',
    api: [ProjBizConsPlanTellApi.config.update]
  },
  {
    menuType: 'F',
    menuName: '撤回',
    perms: 'planTell:unpublish',
    api: [ProjBizConsPlanTellApi.config.update]
  },
  {
    menuType: 'F',
    menuName: '下载',
    perms: 'planTell:download',
    api: [ProjBizConsPlanTellApi.config.view, ProjBizConsPlanTellApi.config.pageList, ProjBizConsStartWorkReportApi.config.download]
  }
]
</script>

<script setup>
import { ref, getCurrentInstance } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getCurrentFormattedTime } from '@/common/utils/datetime'

const { proxy } = getCurrentInstance()
const route = useRoute()
const router = useRouter()
let option = ref({
  tip: false,
  dialogType: 'page',
  border: true,
  index: true,
  stripe: true,
  menu: true,
  header: true,
  height: 'auto',
  searchSpan: 6,
  searchIcon: true,
  searchMenuSpan: 6,
  searchIndex: 3,
  selection: true,
  showTree: false,
  excelBtn: false,
  addBtn: false,
  delBtn: false,
  delBtns: false,
  editBtn: false,
  // delBtnsText: '批量删除',
  // addBtnText: '新增',
  // customEditBtnText: '编辑',
  // customDelBtnText: '删除',
  // customViewBtnText: '查看',
  column: [
    {
      label: '业务编号',
      prop: 'businessNumber',
      search: false,
      overHidden: true
    },
    {
      label: '交底内容',
      prop: 'tellContent',
      search: true,
      overHidden: true
    },
    {
      label: '交底日期',
      prop: 'tellDate',
      search: true,
      type: 'daterange',
      valueFormat: 'YYYY-MM-DD',
      searchRange: true,
      // hide: true,
      overHidden: true,
      formatter: (val, value) => {
        return value
      }
    },
    {
      label: '交底文档',
      prop: 'tellDoc',
      search: false,
      overHidden: true,
      hide: true
    },
    {
      label: '编制人',
      prop: 'organizationUniter',
      search: true,
      overHidden: true
    },
    {
      label: '编制单位',
      prop: 'organizationUnit',
      search: true,
      overHidden: true
    },
    {
      label: '发布日期',
      prop: 'submitDate',
      search: true,
      type: 'daterange',
      valueFormat: 'YYYY-MM-DD',
      searchRange: true,
      // hide: true,
      overHidden: true,
      formatter: (val, value) => {
        return value
      }
    },
    {
      label: '发布状态',
      prop: 'publishStatus',
      search: true,
      type: 'select',
      overHidden: true,
      dicData: [
        { label: '全部', value: '' },
        { label: '草稿', value: '1' },
        { label: '已发布', value: '2' }
      ],
      formatter: (val, value) => {
        if (value === '1') {
          return '草稿'
        } else {
          return '已发布'
        }
      }
    }
  ]
})
let listData = ref([])
const myRef = ref(null)
let queryForm = ref({
  page: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  filter: {
    createTime: null,
    tellDate: null,
    submitDate: null
  }
})
let formRules = ref({})

function getPageList() {
  //查询分页列表
  // 查询前处理参数
  const params = handleQueryForm()
  ProjBizConsPlanTellApi.pageList(params).then((res) => {
    listData.value = res.data.dataList
    queryForm.value.page.total = res.data.totalCount
  })
}

function onResetSearch() {
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20
  }
  getPageList()
}

function handleQueryForm() {
  // 处理参数
  const { pageSize, pageNum } = queryForm.value.page
  const filter = {}
  for (let key in queryForm.value.filter) {
    if (queryForm.value.filter[key]?.length) {
      // sn-curd搜索select会生成$prop的变量
      if (key.startsWith('$')) continue
      filter[key] = queryForm.value.filter[key]
    }
  }
  delete filter.createTime
  if (Array.isArray(queryForm.value.filter.createTime) && queryForm.value.filter.createTime?.length === 2) {
    filter['beginCreateTime'] = queryForm.value.filter.createTime[0]
    filter['endCreateTime'] = queryForm.value.filter.createTime[1]
  }

  delete filter.tellDate
  if (Array.isArray(queryForm.value.filter.tellDate) && queryForm.value.filter.tellDate?.length === 2) {
    filter['tellDateStart'] = queryForm.value.filter.tellDate[0]
    filter['tellDateEnd'] = queryForm.value.filter.tellDate[1]
  }

  delete filter.submitDate
  if (Array.isArray(queryForm.value.filter.submitDate) && queryForm.value.filter.submitDate?.length === 2) {
    filter['submitDateStart'] = queryForm.value.filter.submitDate[0]
    filter['submitDateEnd'] = queryForm.value.filter.submitDate[1]
  }

  const searchParams = {
    page: {
      pageSize,
      pageNum
    },
    filter
  }
  return searchParams
}

function onChangeSearch(params, done) {
  //搜索按钮操作
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20
  }
  getPageList()
  done && done()
}

function onPutData(row) {
  if (row.publishStatus === '2') {
    proxy.$modal
      .confirm('确认撤回数据项？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      .then(() => {
        row.publishStatus = '1'
        ProjBizConsJournalApi.update(row)
          .then(() => {
            ElMessage.success('撤回成功')

            getPageList()
          })
          .catch((error) => {
            ElMessage.error('撤回失败')
          })
      })
      .catch(() => {})
  }
}

function generateButtons(editType, row) {
  if (editType !== 'view') {
    const hasProcId = row?.procInstanceId
    const buttons = [
      { key: 'cancelDirect', text: '关闭', buttonType: '', icon: 'el-icon-close' }
      /*   { key: 'downloadForm', text: '下载表单', buttonType: 'primary', icon: 'el-icon-download' } */
    ]

    if (hasProcId) {
      buttons.push(
        {
          key: 'save',
          text: '保存',
          buttonType: 'primary',
          icon: 'el-icon-check'
        },
        {
          key: 'publish',
          text: '发布',
          buttonType: 'primary',
          icon: 'sn-button-fasong'
        }
      )
    } else {
      buttons.push(
        {
          key: 'save',
          text: '保存',
          buttonType: 'primary',
          icon: 'el-icon-check'
        },
        {
          key: 'publish',
          text: '发布',
          buttonType: 'primary',
          icon: 'sn-button-fasong'
        }
      )
    }
    return buttons
  }
  return row.publishStatus != '1'
    ? [{ key: 'cancelDirect', text: '关闭', buttonType: '', icon: 'el-icon-close' }]
    : [
        { key: 'cancelDirect', text: '关闭', buttonType: '', icon: 'el-icon-close' },
        { key: 'edit', text: '编辑', buttonType: 'primary', icon: 'el-icon-edit' }
      ]
}

async function onEditData(row, viewtype) {
  //编辑,新增按钮操作
  let editType = viewtype === 'view' ? 'view' : row ? 'edit' : 'add'
  let rowInfo = await (editType !== 'add' ? ProjBizConsPlanTellApi.view(row.id) : {})
  const formData = editType !== 'add' ? rowInfo.data : rowInfo
  //编辑,新增按钮操作
  proxy.$DialogForm.show({
    title: '施工技术交底',
    type: option.value.dialogType,
    el: myRef.value,
    width: '80%',
    content: EditProjBizConsPlanTell,
    data: {
      formData: formData,
      type: editType
    },
    option: {
      submitBtn: false,

      emptyBtn: false,
      submitText: '保存',

      emptyText: '取消',
      extendButton: generateButtons(editType, row)
    },
    callback: (res) => {
      let vm = res.dialogRefs
      if (vm) {
        let formData = vm.getFormData()
        switch (res.type) {
          // 取消
          case 'cancelDirect':
            proxy.$modal
              .confirm('确认关闭？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              })
              .then(() => {
                res.close()
              })
            break
          // 保存
          case 'save':
            let publishStatus3 = '1'
            res.dialogRefs.submitData(publishStatus3).then((flag) => {
              if (flag) {
                getPageList()
                res.close()
              }
            })
            break
          // 发布
          case 'publish':
            let publishStatus = '2'
            res.dialogRefs.submitData(publishStatus).then((flag) => {
              if (flag) {
                getPageList()
                res.close()
              }
            })
            break
          case 'downloadForm':
            res.dialogRefs.downloadWord()
            break
          // 编辑
          case 'edit':
            res.close()
            onEditData(row)
            break
        }
      }

      /*   let vm = res.dialogRefs
      if (vm) {
        if (res.type === 'downloadForm') {
          vm.downloadWord()
        } else if (res.type === 'save') {
          let publishStatus = '1'
          res.dialogRefs.submitData(publishStatus).then((flag) => {
            // if (flag) {
            //   getPageList()
            // }
          })
        } else if (res.type === 'publish') {
          let publishStatus = '2'
          res.dialogRefs.submitData(publishStatus).then((flag) => {
            if (flag) {
              getPageList()
              res.close()
            }
          })
        } else if (res.type === 'nopublish') {
          let publishStatus = '1'
          res.dialogRefs.submitData(publishStatus).then((flag) => {
            if (flag) {
              getPageList()
              res.close()
            }
          })
        } else {
          getPageList()
          res.close()
        }
      } */
    }
  })
}

function onDelData(rows) {
  //删除行
  if (!rows || rows.length == 0) {
    proxy.$message.info('请勾选数据！')
    return false
  }
  let ids = rows.map((item) => {
    return item.id
  })
  proxy.$modal
    .confirm('确认删除数据项？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    .then(() => {
      ProjBizConsPlanTellApi.remove(ids).then((res) => {
        proxy.$message.success('已删除')
        getPageList()
      })
    })
    .catch(() => {})
}

function toReportDetail(row) {
  router.push({
    path: '/project/views/constructionManagement/technologyManagement/planTell/components/EditProjBizConsPlanTell',
    query: {
      businessNumber: row.businessNumber,
      businessKey: row.id
    }
  })
}

function onExportData() {
  const params = handleQueryForm()
  let queryForm = JSON.parse(JSON.stringify(params))
  let timestamp = proxy.parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')
  proxy.download('/project/technologyTell/export', queryForm, '施工技术交底-' + timestamp + '.xlsx')
}

function onDownloadData(row) {
  let params = {
    id: row.id,
    type: 'planTell'
  }
  let timestamp = proxy.parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')
  proxy.download('/project/sceneStartWorkReport/export/attachments', params, '施工技术交底-' + row.businessNumber + '-' + timestamp + '.zip')
}
</script>

<style lang="scss" scoped>
.status-text {
  &.draft {
    color: #ff0000; // 红色
  }
  &.published {
    color: #008000; // 绿色
  }
}
</style>
