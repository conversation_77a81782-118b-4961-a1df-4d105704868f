{"name": "sn-admin-v3", "version": "2.1.7", "main": "src/main.js", "private": false, "scripts": {"dev": " vue-cli-service serve --open", "test-build": "cross-env VUE_APP_APPLATION_NAME=project vue-cli-service build", "shuke-build": "cross-env VUE_APP_APPLATION_NAME=guohe-project vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@codemirror/lang-json": "^6.0.1", "@codemirror/theme-one-dark": "^6.1.2", "@element-plus/icons-vue": "^2.3.1", "@vue-office/docx": "^1.6.2", "@vueuse/core": "^13.1.0", "clipboard": "^2.0.11", "codemirror": "^6.0.1", "core-js": "^3.32.1", "echarts": "4.9.0", "element-plus": "^2.9.1", "enos-app-portal-sdk": "^0.0.16", "highlight.js": "10.5", "js-base64": "^3.7.7", "js-sha256": "^0.9.0", "jsencrypt": "3.3.2", "json-bigint": "^1.0.0", "json5": "^2.2.3", "jsonlint": "^1.6.3", "lodash-es": "^4.17.21", "mockjs": "^1.1.0", "net": "^1.0.2", "node-rsa": "^1.1.1", "postcss": "^8.2.15", "project_document_storage_ui": "^1.0.1", "qiankun": "^2.10.16", "sass": "^1.83.0", "sn-base-layout-vue3": "2.1.7-qzq.3", "sn-base-utils": "^2.1.7-qzq.5", "sn-bpm-v3": "^0.0.16", "sn-plugin-modules": "^0.0.1", "snpit-plus": "2.1.7-qzq.6", "sockjs": "^0.3.24", "sockjs-client": "^1.6.1", "stompjs": "^2.3.3", "vue": "^3.2.13", "vue-codemirror": "^6.1.1", "vue-cropper": "^1.1.4", "vue-i18n": "12.0.0-alpha.2", "vue-router": "^4.4.5", "vue3-treeselect": "^0.1.10", "vuex": "^4.1.0", "file-saver": "^2.0.5", "jszip-utils": "^0.1.0", "docxtemplater": "^3.65.1", "pizzip": "^3.2.0"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@babel/preset-env": "^7.22.15", "@babel/standalone": "^7.22.17", "@vue/babel-preset-app": "^5.0.8", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-router": "^5.0.8", "@vue/cli-plugin-vuex": "^5.0.8", "@vue/cli-service": "^5.0.8", "ajv": "^8.17.1", "babel-loader": "^9.2.1", "babel-plugin-root-import": "^6.6.0", "babel-polyfill": "^6.26.0", "compression-webpack-plugin": "^11.1.0", "cross-env": "^7.0.3", "es6-promise": "^4.2.8", "eslint": "^9.17.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.32.0", "node-polyfill-webpack-plugin": "^2.0.1", "prettier": "^3.4.2", "sass-loader": "^16.0.4", "script-loader": "^0.7.2", "svg-sprite-loader": "^6.0.11", "unplugin-auto-import": "^0.19.0", "unplugin-vue-components": "^0.28.0", "webpack-bundle-analyzer": "^4.10.2", "webpack-dev-server": "^4.11.0", "xml-loader": "^1.2.1"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}}