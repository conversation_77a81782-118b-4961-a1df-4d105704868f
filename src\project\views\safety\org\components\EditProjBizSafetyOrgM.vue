<template>
  <el-form :model="formData" :rules="formRules" ref="formRef" label-width="130px" label-position="right" :disabled="type == 'view'">
    <el-row :gutter="16" :span="24">
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <el-row :gutter="16" :span="24">
            <el-col :span='8'>
              <el-form-item label="业务编码" prop="code">
                <el-input v-model="formData.code" type="text" placeholder="请输入业务编码" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="组织机构类型" prop="orgType">
                <el-select v-model="formData.orgType" clearable placeholder="请选择组织机构类型">
                  <el-option v-for="(item, index) in safety_org_type" :key="index" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="成立日期" prop="foundTime">
                <el-date-picker type="date" v-model="formData.foundTime" style="width: 100%;" format="YYYY-MM-DD" value-format="YYYY-MM-DD" clearable></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16" :span="24">
            <el-col :span='24'>
              <el-form-item label="职责" prop="duty">
                <el-input type="textarea" v-model="formData.duty" placeholder="请输入职责" rows="3" clearable></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </fieldset>
      </el-card>
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>职位设定
          </legend>
          <el-row :gutter="16" :span="24">
            <sn-crud :data="formData.projBizSafetyOrgOpsSDtoList" :option="projBizSafetyOrgOpsSOption" @row-save="subRowSave" @row-update="subRowUpdate" @row-del="(row, index) => {subDelRow(row, index, 'projBizSafetyOrgOpsSDtoList');}">
              <template #empty>
                <div>无数据</div>
              </template>
              <template #position = "{ row }">
                <div v-permi="formPermi({fieldModelId: 'projBizSafetyOrgOpsS', field:'position', fieldName: '职位'}, customFormPermi)">
                  <el-select :disabled="type == 'view' || !row.$cellEdit" v-model="row.position" placeholder="请选择" filterable allow-create >
                    <el-option v-for="(item, index) in positionOption" :key="index" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </div>
              </template>
              <template #affiliatedUnit = "{ row }">
                <div v-permi="formPermi({fieldModelId: 'projBizSafetyOrgOpsS', field:'affiliatedUnit', fieldName: '所属单位'}, customFormPermi)">
                  <el-select :disabled="type == 'view' || !row.$cellEdit" v-model="row.affiliatedUnit" placeholder="请选择" filterable >
                    <el-option v-for="(item, index) in orgData" :key="index" :label="item.name" :value="item.id"></el-option>
                  </el-select>
                </div>
              </template>
              <template #sort = "{ row }">
                <div v-permi="formPermi({fieldModelId: 'projBizSafetyOrgOpsS', field:'sort', fieldName: '排序'}, customFormPermi)">
                  <el-input-number :disabled="type == 'view' || !row.$cellEdit" v-model="row.sort" :min="1" label=""></el-input-number>
                </div>
              </template>
            </sn-crud>
            <el-button @click="subAddRow('projBizSafetyOrgOpsSDtoList')" type="primary" plain style="display: block; width: 100%; margin-top: 10px">添 加</el-button>
          </el-row>
        </fieldset>
      </el-card>
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>附件信息
          </legend>
          <el-row :gutter="16" :span="24">
            <project-document-storage-ui-table
                ref="childRef"
                :type="fileType"
                :relevantId="relevantId"
                :isPageSearch="false"
                :isDeleteMinio = "isDeleteMinio"
                :isHasAi = "isHasAi"
                @on-add-data="onAddfileData"
                @on-ai-review="onfileAiReview"
                @on-preview="onfilePreview"
                @on-delete="onfileDelete"
                :file-serial-number-builder="fileSerialNumberBuilder"
                :preview-config="previewConfig"
                :isShowAddBtn="type !== 'view'"
                :isShowDelBtn="type !== 'view'"
                :isShowPreviewBtn="isShowPreviewBtn"
                :isShowDownloadBtn="type !== 'view'"
                :isShowLinkBtn="false"
                :isShowDownloadBatchBtn="type !== 'view'"
            ></project-document-storage-ui-table>
          </el-row>
        </fieldset>
      </el-card>
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>单据信息
          </legend>
          <el-row :gutter="16" :span="24">
            <el-col :span='8'>
              <el-form-item label="创建人" prop="createName">
                <el-input v-model="formData.createName" disabled type="text" placeholder="" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="创建时间" prop="createTime">
                <el-date-picker type="dateTime" v-model="formData.createTime" disabled format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" clearable></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="单据状态" prop="status">
                <el-select v-model="formData.status" disabled clearable placeholder="请选择单据状态">
                  <el-option v-for="(item, index) in statusOption" :key="index" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="最近修改人" prop="updateName">
                <el-input v-model="formData.updateName" disabled type="text" placeholder="" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="最近修改时间" prop="updateTime">
                <el-date-picker type="dateTime" v-model="formData.updateTime" disabled format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" clearable></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
        </fieldset>
      </el-card>
    </el-row>
  </el-form>
</template>

<script setup>
import ProjBizSafetyOrgMApi from '@/project/api/safety/ProjBizSafetyOrgM.js'
import {
  nextTick,
  onMounted,
  ref,
  reactive,
  toRef,
  defineProps,
  defineExpose,
  getCurrentInstance
} from 'vue';
import {
  useRoute,
  useRouter
} from 'vue-router'
const {
  proxy
} = getCurrentInstance()
const route = useRoute();
const router = useRouter();
const props = defineProps({
  data: Object,
});
const previewConfig = ref({
  // 是否外置预览,必传
  isExternalPreview: false,
  // 内置预览返回的替换字符,akey代表要替换的字符串，avalue代码替换的字符串
  substitute: {
    "akey":"avalue"
  },
  // 内置预览服务地址
  previewServerUrl: "http://*************:8012/onlinePreview",
})
const formRef = ref()
const type = toRef(props.data?.type);
const fileType = ref("safety_org")
let relevantId = toRef(props.data?.id)
import {
  useDicts
} from "@/common/hooks/useDicts";
const {
  safety_org_type
} = useDicts(["safety_org_type"])
import store from "@/store";
import {getCurrentFormattedTime} from "@/common/utils/datetime";
import ProjBizProjectTeamOrgApi from "@/project/api/projectTeam/ProjBizProjectTeamOrg";
let formData = ref({
  projectId: sessionStorage.getItem('projectId'),
  projectName: "",
  orgType: "",
  foundTime: "",
  code: "",
  duty: "",
  projBizSafetyOrgOpsSDtoList: [],
  fileDtoList: [],
  createName: store.state.user && store.state.user.userInfo.userName ? store.state.user.userInfo.userName : null,
  createTime: getCurrentFormattedTime(),
  status: "0",
  updateName: "",
  updateTime: ""
});
let formRules = ref({
  orgType: [{
    required: true,
    message: "组织机构类型不能为空"
  }],
  foundTime: [{
    required: true,
    message: "成立时间不能为空"
  }],
  code: [{
    required: true,
    message: "业务编码不能为空"
  }],
  duty: [],
  createName: [],
  createTime: [],
  status: [],
  updateName: [],
  updateTime: []
});
let delRowData = ref({});
let projBizSafetyOrgOpsSOption = ref({
  tip: false,
  border: true,
  index: true,
  stripe: true,
  menu: type.value !== 'view',
  header: false,
  menuType: "text",
  addBtn: true,
  addBtnText: "新增",
  editBtn: true,
  editBtnText: "编辑",
  delBtn: true,
  delBtnText: "删除",
  cellBtn: true,
  maxHeight: "200px",
  column: [{
    label: "职位",
    prop: "position",
    type: "select",
    cell: false,
    columnSlot: true
  }, {
    label: "姓名",
    prop: "positionName",
    type: "input",
    cell: true
  }, {
    label: "所属单位",
    prop: "affiliatedUnit",
    type: "input",
    cell: false,
    columnSlot: true
  }, {
    label: "排序",
    prop: "sort",
    type: "input",
    cell: false,
    columnSlot: true
  }, {
    label: "说明",
    prop: "explain",
    type: "input",
    cell: true
  }]
});
let Option = ref({
  tip: false,
  border: true,
  index: true,
  stripe: true,
  menu: true,
  header: false,
  menuType: "text",
  addBtn: true,
  addBtnText: "新增",
  editBtn: true,
  editBtnText: "编辑",
  delBtn: true,
  delBtnText: "删除",
  cellBtn: true,
  maxHeight: "200px",
  column: [{
    label: "文件名称",
    prop: "fileName",
    type: "input",
    columnSlot: false,
    searchSlot: false,
    cell: true
  }, {
    label: "文件大小",
    prop: "fileSize",
    type: "input",
    columnSlot: false,
    searchSlot: false,
    cell: true
  }, {
    label: "上传人",
    prop: "cueateName",
    type: "input",
    columnSlot: false,
    searchSlot: false,
    cell: true
  }, {
    label: "上传时间",
    prop: "createTime",
    type: "input",
    columnSlot: false,
    searchSlot: false,
    cell: true
  }]
});
let statusOption = ref([{
  label: "草稿",
  value: "0"
}, {
  label: "已发布",
  value: "1"
}]);
let positionOption = ref([{
  label: "主任",
  value: "主任"
}, {
  label: "副主任",
  value: "副主任"
}]);

const state = reactive({
  flowData: {
    businessKey: "",
    procInstId: "",
    procDefKey: "",
    taskId: "",
    formConfig: [],
    variableList: [],
    customPropertyConfigList: []
  },
  formAuth: {},
  taskFlag: 1,
  firstAct: false,
  handlerClose: null,
  rootFields: {},
  fullFieldNameMap: {},
  formAuthList: {}
})

//组织数据
let orgData = ref([]);

ProjBizProjectTeamOrgApi.queryOrgCompanyList({projectId : sessionStorage.getItem('projectId')}).then((res) => {
  orgData.value = res.data;
})

function subRowSave(form, done) {
  //编辑行
  done();
}

function subAddRow(name) {
  //新增一行
  if (formData.value[name]) {
    formData.value[name].push({
      $cellEdit: true,
    });
  } else {
    let arr = new Array();
    arr.push({
      $cellEdit: true
    });
    formData.value[name] = arr
  }
}

function subRowUpdate(form, index, done, loading) {
  //编辑行
  done();
}

function subDelRow(row, index, name) {
  //删除行
  proxy.$confirm('确认删除？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    if (row[0].id) {
      let data = JSON.parse(JSON.stringify(row[0]));
      if (delRowData.value[name]) {
        delRowData.value[name].push(Object.assign(data, {
          delFlag: 1,
        }));
      } else {
        delRowData.value[name] = [
          Object.assign(data, {
            delFlag: 1,
          }),
        ]
      }
    }
    formData.value[name].splice(index, 1);
  }).catch(() => {
    // 用户点击取消，不关闭弹窗
  });
}

function getFormData() {
  //获取formData数据
  for (let item in delRowData.value) {
    formData.value[item] = formData.value[item].concat(delRowData.value[item]);
  }
  return formData.value;
}
formData = toRef(Object.keys(props.data?.formData || {}).length > 0 ? {
  ...props.data?.formData
} : toRef(formData.value));

const buttonType = ref()
//错误信息
const errorMessage = ref('');
function submitData(btnType) {
  return new Promise((resolve) => {
    formRef.value.validate((flag) => {
      if (!flag) {
        return false;
      } else {
        buttonType.value = btnType

        // 重置错误消息
        errorMessage.value = '';
        const hasDirector = formData.value['projBizSafetyOrgOpsSDtoList'].some((staff) => staff.position==='主任');
        const hasFDirector = formData.value['projBizSafetyOrgOpsSDtoList'].some((staff) => staff.position==='副主任');
        if (!hasDirector || !hasFDirector) {
          proxy.$message.error("职位设定必须存在主任和副主任职位！");
          return false;
        }
        let result = 0;
        formData.value['projBizSafetyOrgOpsSDtoList'].forEach((item) => {
          if (item.position==='主任' && item.sort !== 1) {
            proxy.$message.error("职位设定中主任排序应为1！");
            result++;
          } else if (item.position==='副主任' && item.sort !== 2) {
            proxy.$message.error("职位设定中副主任排序应2！");
            result++;
          }
        })
        if (result > 0) {
          return false;
        }

        if (type.value === "add") {
          if (childRef.value) {
            childRef.value.getListData().forEach((item) => {
              formData.value['fileDtoList'].push({
                fileId:item.fileId,
                fileName: item.fileName,
                fileSize: item.fileSize,
                createTime: item.createBy,
                fileSerialNumber: item.fileSerialNumber,
                fileSizeFormat: item.fileSizeFormat
              })
            })
          }
          resolve(saveData());
        } else {
          resolve(editData());
        }
      }
    });
  });
}

function saveData() {
  //新增操作
  const formData1 = getFormData();
  if (buttonType.value == 'submit') {
    formData1.status = '1'
  }
  return ProjBizSafetyOrgMApi.add(formData1).then((res) => {
    if(res.data){
      proxy.$message.success("保存成功");
      formData.value.id = res.data.id;
      relevantId.value = res.data.id;
      formData.value.projBizSafetyOrgOpsSDtoList = res.data.projBizSafetyOrgOpsSDtoList
      type.value = "edit"
      return true;
    }
  });
}

function editData() {
  //编辑操作
  const formData1 = getFormData();
  if (buttonType.value == 'submit') {
    formData1.status = '1'
  }
  return ProjBizSafetyOrgMApi.update(formData1).then((res) => {
    proxy.$message.success("修改成功");
    delRowData = ref({});
    formData.value.projBizSafetyOrgOpsSDtoList = res.data.projBizSafetyOrgOpsSDtoList
    return true;
  });
}

//处理附件上传相关方法---------begin
let listfileData = ref([]);
function onAddfileData(list) {
}

function onAifileReview(row) {
}

function onfilePreview(row) {
}

function onfileDelete(list) {
}
const childRef = ref(null);
function getfileListData() {
  if(childRef.value){
    let list = childRef.value.getListData()
  }
}
// 文件序列号生成函数
function fileSerialNumberBuilder() {
  return "file" + Math.floor(Math.random()*10000)
}
//处理附件上传相关方法---------end

let auth = new Map();
function customFormPermi(obj) {
  /**
   * 工作流表单权限功能：自定义表单权限处理函数，将对应的对象的属性还原成对应的权限值
   */
  let {
    field,
    fieldModelId
  } = obj
  let fullFieldName = `${fieldModelId}_${field}`
  return auth.get(fullFieldName) || 0
};
/**
 * 处理表单权限相关操作, 开发者在template的v-permi设置中必须使用此函数
 * @param {Object} obj - 包含字段名称的对象
 * @param {Function} [callback] - 可选的回调函数，接收 obj 作为参数
 * 如果传入对象的 fieldName 不在 rootFields 中，则将其添加到 rootFields 数组，然后执行回调函数（如果存在）
 */
function formPermi(obj, callback) {
  if (!state.rootFields[obj.fieldModelId + '__' + obj.field]) {
    state.rootFields[obj.fieldModelId + '__' + obj.field] = obj
  }
  return callback && callback(obj)
};

defineExpose({
  getFormData,
  submitData,
});
</script>

<style lang="scss" scoped>

</style>
