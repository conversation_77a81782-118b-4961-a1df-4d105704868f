<template>
  <el-form :model="formData" :rules="formRules" ref="formRef" label-width="130px" label-position="right" :disabled="type == 'view'">
    <el-row :gutter="16" :span="24">
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>基本信息
          </legend>
          <el-row :gutter="16" :span="24">
            <el-col :span='8'>
              <el-form-item label="文件编号" prop="fileId" >
                <el-input v-model="formData.fileId" type="text" placeholder="手动录入" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="文件名称" prop="fileName">
                <el-input v-model="formData.fileName" type="text" placeholder="手动录入" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="文件类别" prop="fileType">
                <el-select v-model="formData.fileType" clearable  placeholder="请选择">
                  <el-option v-for="(item, index) in service_file_type" :key="index" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="设计单位" prop="designOrg">
                <el-input v-model="formData.designOrg" type="text" placeholder="请输入" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='16'>
              <el-form-item label="设计单位负责人" prop="designLeader">
                <el-input v-model="formData.designLeader" type="text" placeholder="请输入" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='24'>
              <el-form-item label="备注" prop="comments">
                <el-input type="textarea" v-model="formData.comments" placeholder="请输入内容" rows="3" clearable></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </fieldset>
      </el-card>
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>附件
          </legend>
          <el-row :gutter="16" :span="24"></el-row>
          <project-document-storage-ui-table
              ref="childRef"
              :type="serviceFile"
              :relevantId="formData.id"
              :isPageSearch="false"
              :isDeleteMinio="false"
              :isHasAi="false"
              @on-add-data="onAddData"
              :file-serial-number-builder="fileSerialNumberBuilder"
              :preview-config="previewConfig"
              :isShowAddBtn="type !== 'view'"
              :isShowDelBtn="type !== 'view'"
              :isShowPreviewBtn="true"
              :isShowDownloadBtn="type !== 'view'"
              :isShowLinkBtn="type !== 'view'"
              :isShowDownloadBatchBtn="type !== 'view'"
          ></project-document-storage-ui-table>
        </fieldset>
      </el-card>
      
      <el-card class="box-card" style="width: 100%;">
        <fieldset class="fieldset2">
          <legend>
            <span class="el-button--primary"></span>单据信息
          </legend>
          <el-row :gutter="16" :span="24">
            <el-col :span='8'>
              <el-form-item label="创建人" prop="createName">
                <el-input v-model="formData.createName" type="text" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="创建时间" prop="createTime">
                <el-date-picker type="datetime" v-model="formData.createTime"  format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%;" clearable></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="单据状态" prop="publishStatus">
                <el-input v-if="type === 'view' || type === 'edit'" :value="getPublishStatusLabel(formData.publishStatus)" readonly />
                <el-select v-else v-model="formData.publishStatus" clearable placeholder="请选择">
                  <el-option v-for="item in publishStatusOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="最近修改人" prop="updateName">
                <el-input v-model="formData.updateName" type="text" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="最近修改时间" prop="updateTime">
                <el-date-picker type="datetime" v-model="formData.updateTime" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%;"  clearable></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
        </fieldset>
      </el-card>
    </el-row>
  </el-form>
</template>

<script setup>
import store from "@/store";
import {getCurrentFormattedTime} from "@/common/utils/datetime";
import ProjBizServiceFileMApi from '@/project/api/serviceFile/ProjBizServiceFileM.js' 
import {
  nextTick,
  onMounted,
  ref,
  reactive,
  toRef,
  defineProps,
  defineExpose,
  getCurrentInstance
} from 'vue';
import {
  useRoute,
  useRouter
} from 'vue-router'
const setting = require('../../../../config.js')
const {
  proxy
} = getCurrentInstance()
const route = useRoute();
const router = useRouter();
const props = defineProps({
  data: Object,
});
const formRef = ref()
const serviceFile = ref("serviceFile")
const type = toRef(props.data?.type);
import {
  useDicts
} from "@/common/hooks/useDicts";
const {
  service_file_type
} = useDicts(["service_file_type"]);
const publishStatusOptions = [
    { label: '草稿', value: 0 },
    { label: '已发布', value: 1 }
  ];
let formData = ref({
  fileId: "",
  fileName: "",
  fileType: "",
  designOrg: JSON.parse(store.state.user.orgName)?.find((item) => item.id === store.state.user.defaultOrg)?.orgName,
  designLeader: store.state.user && store.state.user.userInfo.userName ? store.state.user.userInfo.userName : null,
  comments: "",
  createName: store.state.user && store.state.user.userInfo.userName ? store.state.user.userInfo.userName : null,
  createTime: getCurrentFormattedTime(),
  publishStatus: 0,
  updateName: "",
  updateTime:"",
  projBizDmStgMDtoList: []
});
let formRules = ref({
  fileId: [{
    required: true,
    message: ""
  }],
  fileName: [{
    required: true,
    message: ""
  }],
  fileType: [{
    required: true,
    message: ""
  }],
  designOrg: [{
    required: true,
    message: ""
  }],
  designLeader: [{
    required: true,
    message: ""
  }],
  comments: [],
  createName: [],
  createTime: [],
  publishStatus: [],
  updateName: [],
  updateTime: [],
  projBizDmStgMDtoList: []
});
let option = ref({
  tip: false,
  dialogType: "dialog",
  border: true,
  index: true,
  stripe: true,
  menu: true,
  header: true,
  height: "auto",
  searchSpan: 6,
  searchIcon: true,
  searchMenuSpan: 6,
  searchIndex: 3,
  selection: true,
  showTree: false,
  excelBtn: false,
  delBtn: false,
  editBtn: false,
  delBtnsText: "批量删除",
  addBtnText: "新增",
  customEditBtnText: "编辑",
  customDelBtnText: "删除",
  customViewBtnText: "查看",
  column: [{
    label: "文件名称",
    prop: "file_name",
    columnSlot: false,
    searchSlot: false
  }, {
    label: "文件大小",
    prop: "file_size",
    columnSlot: false,
    searchSlot: false
  }, {
    label: "上传人",
    prop: "upload_person",
    columnSlot: false,
    searchSlot: false
  }, {
    label: "上传时间",
    prop: "upload_time",
    columnSlot: false,
    searchSlot: false
  }]
});
let listData = ref([]);
let queryForm = ref({
  page: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  filter: {
    createTime: null
  }
});

const childRef = ref(null);

function getPageList() {
  //查询分页列表
  // 查询前处理参数
  const params = handleQueryForm();
  ProjBizServiceFileMApi.pageList(params).then((res) => {
    listData.value = res.data.dataList;
    queryForm.value.page.total = res.data.totalCount;
  });
}

function onResetSearch() {
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
}

function handleQueryForm() {
  // 处理参数
  const {
    pageSize,
    pageNum
  } = queryForm.value.page;
  const filter = {}
  for (let key in queryForm.value.filter) {
    if (queryForm.value.filter[key]?.length) {
      // sn-curd搜索select会生成$prop的变量
      if (key.startsWith("$")) continue;
      filter[key] = queryForm.value.filter[key];
    }
  }
  delete filter.createTime;
  if (Array.isArray(queryForm.value.filter.createTime) && queryForm.value.filter.createTime?.length === 2) {
    filter["beginCreateTime"] = queryForm.value.filter.createTime[0];
    filter["endCreateTime"] = queryForm.value.filter.createTime[1];
  }
  const searchParams = {
    page: {
      pageSize,
      pageNum,
    },
    filter,
  }
  return searchParams
}

function onChangeSearch(params, done) {
  //搜索按钮操作
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
  done && done();
}
async function onEditData(row) {
  //编辑,新增按钮操作
  let editType = row ? "edit" : "add";
  let rowInfo = await (editType !== "add" ? ProjBizServiceFileMApi.view(row.id) : {});
  const formData = editType !== "add" ? rowInfo.data : rowInfo;
  //编辑,新增按钮操作
  proxy.$DialogForm.show({
    title: editType == "edit" ? "编辑" : "新增",
    type: option.value.dialogType,
    width: "80%",
    content: EditProjBizServiceFileM,
    data: {
      formData: formData,
      type: editType,
    },
    callback: (res) => {
      if (res.type && res.type !== 'close') {
        if (res.dialogRefs) {
          res.dialogRefs.submitData().then((flag) => {
            if (flag) {
              getPageList();
              res.close();
            }
          });
        }
      } else {
        res.close();
      }
    }
  });
}

function onDelData(rows) {
  //删除行
  if (!rows || rows.length == 0) {
    proxy.$message.info("请勾选数据！");
    return false;
  }
  let ids = rows.map((item) => {
    return item.id;
  });
  proxy.$modal.confirm("确认删除数据项？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    ProjBizServiceFileMApi.remove(ids).then((res) => {
      proxy.$message.success("已删除");
      getPageList();
    });
  }).catch(() => {});
}
formData = toRef(Object.keys(props.data?.formData || {}).length > 0 ? {
  ...props.data?.formData
} : toRef(formData.value));

const getAttachmentList = () => {
  // 假设子组件有 getListData 方法
  return childRef.value?.getListData() || [];
};

function getFormData() {
  // 保证publishStatus默认为0（仅在新增时）
  if (type.value === "add" && (formData.value.publishStatus === undefined || formData.value.publishStatus === "" || formData.value.publishStatus === null)) {
      formData.value.publishStatus = 0;
  }
  // 获取附件列表并赋值
  formData.value.projBizDmStgMDtoList = getAttachmentList();
  return formData.value;
};

function submitData(buttonType) {
  return new Promise((resolve) => {
    formRef.value.validate((flag) => {
      if (!flag) {
        return false;
      } else {
        if (type.value === "add") {
          resolve(saveData(buttonType));
        } else {
          resolve(editData(buttonType));
        }
      }
    });
  });
}

function saveData(buttonType) {
  //新增操作
  const formData = getFormData();
  if(buttonType==='submit'){
    formData.publishStatus = 1
  }
  if(formData.id===null || formData.id === undefined){
    return ProjBizServiceFileMApi.add(formData).then((resp) => {
      if(resp.data){
        proxy.$message.success("保存成功");
        formData.id = resp.data.id;
        return true;
      }
    });
  }else{
    return ProjBizServiceFileMApi.update(formData).then(() => {
      proxy.$message.success("保存成功");
      return true;
    });
  }
}

function editData(buttonType) {
  //编辑操作
  const formData = getFormData();
  if(buttonType==='submit'){
    formData.publishStatus = 1
  }
  return ProjBizServiceFileMApi.update(formData).then(() => {
    proxy.$message.success("修改成功");
    return true;
  });
}
function getPublishStatusLabel(val) {
    const found = publishStatusOptions.find(item => item.value === val || String(item.value) === String(val));
    return found ? found.label : val;
  }

// 文件序列号生成函数
function fileSerialNumberBuilder() {
  return "TEST" + Math.floor(Math.random()*10000)
}

const previewConfig = ref({
  // 内置预览返回的替换字符,akey代表要替换的字符串，avalue代码替换的字符串
  substitute: {
    "akey": "avalue"
  },
  // 内置预览服务地址
  previewServerUrl: setting.kkFileViewUrl,
})

defineExpose({
  getFormData,
  submitData,
});
</script>

<style lang="scss" scoped>

</style>
