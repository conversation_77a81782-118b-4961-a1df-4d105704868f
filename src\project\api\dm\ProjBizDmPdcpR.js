import { request, replaceUrl } from "sn-base-utils";

export default class ProjBizDmPdcpRApi {
    static config = {
        addBatch: {
            url: '/project/dmPdcp/addBatch/{pdcId}',
            method: 'PUT'
        },
        setAdmin: {
            url: '/project/dmPdcp/setAdmin/{id}',
            method: 'GET'
        },
        cancelAdmin: {
            url: '/project/dmPdcp/cancelAdmin/{id}',
            method: 'GET'
        },
        remove: {
            url: '/project/dmPdcp/delete/{id}',
            method: 'DELETE'
        },
        listByPdcId: {
            url: '/project/dmPdcp/listByPdcId/{pdcId}',
            method: "GET"
        }
    };

    /**
     * 新增文档管理-文档资料权限表
     * @param data
     * @param pdcId
     * @returns {*}
     */
    static addBatch(data,pdcId) {
        return request({
            url: replaceUrl(this.config.addBatch.url,{pdcId}),
            method: this.config.addBatch.method,
            data: data
        });
    }

    /**
     * 查询文档管理-设置管理员
     * @param id
     * @returns {*}
     */
    static setAdmin(id) {
        return request({
            url: replaceUrl(this.config.setAdmin.url, { id }),
            method: this.config.setAdmin.method
        });
    }

    /**
     * 查询文档管理-取消管理员
     * @param id
     * @returns {*}
     */
    static cancelAdmin(id) {
        return request({
            url: replaceUrl(this.config.cancelAdmin.url, { id }),
            method: this.config.cancelAdmin.method
        });
    }

    /**
     * 删除文档管理-文档资料权限表
     * @param id
     * @returns {*}
     */
    static remove(id) {
        return request({
            url: replaceUrl(this.config.remove.url, { id }),
            method: this.config.remove.method,
        });
    }

    /**
     * 文档管理列表-文档资料权限表
     * @param pdcId 分类ID
     * @returns {*}
     */
    static listByPdcId(pdcId) {
        return request({
            url: replaceUrl(this.config.listByPdcId.url, { pdcId }),
            method: this.config.listByPdcId.method
        });
    }

}
