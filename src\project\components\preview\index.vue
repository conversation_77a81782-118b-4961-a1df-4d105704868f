<template>
  <div class="file-preview-div">
    <!-- 文件预览对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="1200px !important"
      height="675px !important"
      custom-class="import-dialog"
      append-to-body>
      <file-preview
        ref="filePreview"
        previewType="iframe"
        height="100%"></file-preview>
    </el-dialog>
  </div>
</template>
<script>
import filePreview from './filePreview.vue'
export default {
  components: {
    filePreview,
  },
  data() {
    return {
      // 弹出层标题
      title: '文件预览',
      // 遮罩层
      loading: true,
      // 是否显示弹出层
      open: false,
    }
  },
  methods: {
    preview(id, name) {
      this.open = true
      this.$nextTick(() => {
        this.title = name
        this.$refs.filePreview.preview(id)
      })
    },
  },
}
</script>
<style lang="scss" scoped>
.file-preview-div .el-dialog {
  height: 580px !important;
}
::v-deep .el-dialog {
  height: 600px !important;
  max-height: 90vh;
  /*overflow-y: auto;*/
}
::v-deep .el-dialog__body {
  height: 520px !important;
}
</style>
