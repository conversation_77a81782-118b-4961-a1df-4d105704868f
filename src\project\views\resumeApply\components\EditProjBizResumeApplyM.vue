<template>
  <flow-page-container ref="flowPageContainerRef" @handlerAction="handlerAction" @initData="initData" @handlerPrint="handlerPrint" :closeBtn="isShowCloseBtn" :approvalOption="approvalOption" @approvalOptionCallback="getApprovalOption">
    <el-form :model="formData" :rules="formRules" ref="formRef" label-width="120px" label-position="right" :disabled="type == 'view'">
      <el-row :gutter="16" :span="24">
        <el-card class="box-card" style="width: 100%;">
          <fieldset class="fieldset2">
            <legend>
              <span class="el-button--primary"></span>复工申请表
            </legend>
            <el-row :gutter="16" :span="24">
              <el-col :span='8' v-permi="formPermi({fieldModelId:'projBizResumeApplyM', field:'relatedStopworkNo', fieldName: '关联停工令'}, customFormPermi)">
                <el-form-item label="关联停工令" prop="relatedStopworkNo">
                  <el-input v-model="formData.relatedStopworkNo" type="text" placeholder="选择停工令编号" clearable>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span='8' v-permi="formPermi({fieldModelId:'projBizResumeApplyM', field:'projectName', fieldName: '工程名称'}, customFormPermi)">
                <el-form-item label="业务编号" prop="projectName">
                  <el-input v-model="formData.projectName" :disabled="true" type="text" placeholder="自动填充" clearable>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span='8' v-permi="formPermi({fieldModelId:'projBizResumeApplyM', field:'applyNo', fieldName: '申请单编号'}, customFormPermi)">
                <el-form-item label="申请单编号" prop="applyNo">
                  <el-input v-model="formData.applyNo" type="text" placeholder="必填" clearable>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span='8' v-permi="formPermi({fieldModelId:'projBizResumeApplyM', field:'supervisionUnit', fieldName: '监理单位'}, customFormPermi)">
                <el-form-item label="监理单位" prop="supervisionUnit">
                  <el-input v-model="formData.supervisionUnit" :disabled="true" type="text" placeholder="自动填充" clearable>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span='8' v-permi="formPermi({fieldModelId:'projBizResumeApplyM', field:'constructionUnit', fieldName: '施工单位'}, customFormPermi)">
                <el-form-item label="施工单位" prop="constructionUnit">
                  <el-input v-model="formData.constructionUnit" :disabled="true" type="text" placeholder="自动填充" clearable>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span='8' v-permi="formPermi({fieldModelId:'projBizResumeApplyM', field:'applyResumeDate', fieldName: '申请复工时间'}, customFormPermi)">
                <el-form-item label="申请复工时间" prop="applyResumeDate">
                  <el-date-picker type="date" placeholder="必填" v-model="formData.applyResumeDate" format="YYYY-MM-DD" value-format="YYYY-MM-DD" style="width: 100%;" clearable></el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
          </fieldset>
        </el-card>
        <el-card class="box-card" style="width: 100%;">
          <fieldset class="fieldset2">
            <legend>
              <span class="el-button--primary"></span>附件信息（请上传复工申请及相关材料）
            </legend>
            <el-row :gutter="16" :span="24"></el-row>
              <project-document-storage-ui-table
                  ref="childRef"
                  :type="resumeApply"
                  :relevantId="formData.id"
                  :isPageSearch="false"
                  :isDeleteMinio="false"
                  :isHasAi="false"
                  @on-add-data="onAddData"
                  :file-serial-number-builder="fileSerialNumberBuilder"
                  :preview-config="previewConfig"
                  :isShowAddBtn="type !== 'view'"
                  :isShowDelBtn="type !== 'view'"
                  :isShowPreviewBtn="true"
                  :isShowDownloadBtn="type !== 'view'"
                  :isShowLinkBtn="type !== 'view'"
                  :isShowDownloadBatchBtn="type !== 'view'"
              ></project-document-storage-ui-table>
          </fieldset>
        </el-card>
        <el-card class="box-card" style="width: 100%;">
          <fieldset class="fieldset2">
            <legend>
              <span class="el-button--primary"></span>单据信息
            </legend>
            <el-row :gutter="16" :span="24">
              <el-col :span='8' v-permi="formPermi({fieldModelId:'projBizResumeApplyM', field:'createName', fieldName: '创建人'}, customFormPermi)">
                <el-form-item label="创建人" prop="createName">
                  <el-input v-model="formData.createName" :readonly="true" type="text" clearable>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span='8' v-permi="formPermi({fieldModelId:'projBizResumeApplyM', field:'createTime', fieldName: '创建时间'}, customFormPermi)">
                <el-form-item label="创建时间" prop="createTime">
                  <el-date-picker type="datetime" v-model="formData.createTime" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%;" clearable></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span='8' v-permi="formPermi({fieldModelId:'projBizResumeApplyM', field:'billStatus', fieldName: '单据状态'}, customFormPermi)">
                <el-form-item label="单据状态" prop="billStatus">
                  <el-select v-model="formData.billStatus" :disabled="true" clearable>
                    <el-option v-for="(item, index) in flow_status" :key="index" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span='8' v-permi="formPermi({fieldModelId:'projBizResumeApplyM', field:'updateName', fieldName: '最近修改人'}, customFormPermi)">
                <el-form-item label="最近修改人" prop="updateName">
                  <el-input v-model="formData.updateName" :readonly="true" type="text" clearable>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span='8' v-permi="formPermi({fieldModelId:'projBizResumeApplyM', field:'updateTime', fieldName: '最近修改时间'}, customFormPermi)">
                <el-form-item label="最近修改时间" prop="updateTime">
                  <el-date-picker type="datetime" v-model="formData.updateTime" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%;" clearable></el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
          </fieldset>
        </el-card>
      </el-row>
    </el-form>
  </flow-page-container>
</template>

<script setup>
import store from "@/store";
import {getCurrentFormattedTime} from "@/common/utils/datetime";
import ProjBizResumeApplyMApi from '@/project/api/resumeApply/ProjBizResumeApplyM.js'
import FlowPageContainer from '@/common/components/exportFile/FlowPageContainer.vue'
const setting = require('../../../../config.js')
let auth = new Map();
import {
  nextTick,
  onMounted,
  ref,
  reactive,
  toRef,
  defineProps,
  defineExpose,
  getCurrentInstance
} from 'vue';
import {
  useRoute,
  useRouter
} from 'vue-router'
const {
  proxy
} = getCurrentInstance()
const route = useRoute();
const router = useRouter();
const resumeApply = ref("resumeApply");
const childRef = ref(null);
const props = defineProps({
  data: Object,
});
const formRef = ref()
const isShowCloseBtn = ref(false);
const FlowActionType = ref(proxy.FlowActionType);
let approvalOption = props.data?.approvalOption ? props.data?.approvalOption : route.query?.procInstId ? ref({
  isShowApprovalList: true,
  isShowFlowDiagram: true,
  procInstId: route.query?.procInstId
}) : ref({
  isShowApprovalList: false,
  isShowFlowDiagram: false,
  procInstId: ''
});
const state = reactive({
  flowData: {
    businessKey: "",
    procInstId: "",
    procDefKey: "",
    taskId: "",
    formConfig: [],
    variableList: [],
    customPropertyConfigList: []
  },
  formAuth: {},
  taskFlag: 1,
  firstAct: false,
  handlerClose: null,
  rootFields: {},
  fullFieldNameMap: {},
  formAuthList: {}
})
const type = toRef(props.data?.type);
import {
  useDicts
} from "@/common/hooks/useDicts";
const {} = useDicts([])
let formData = ref({
  relatedStopworkNo: "",
  projectName: "",
  applyNo: "",
  supervisionUnit: "",
  constructionUnit: "",
  applyResumeDate: "",
  createName: store.state.user && store.state.user.userInfo.userName ? store.state.user.userInfo.userName : null,
  createTime: getCurrentFormattedTime(),
  billStatus: "1",
  updateName: "",
  updateTime:"",
  projBizDmStgMDtoList: []
});
let formRules = ref({
  relatedStopworkNo: [{
    required: true,
    message: "关联停工令不能为空"
  }],
  projectName: [],
  applyNo: [{
    required: true,
    message: "申请单编号号不能为空"
  }],
  supervisionUnit: [],
  constructionUnit: [],
  applyResumeDate: [{
    required: true,
    message: "复工申请时间不能为空"
  }],
  createName: [],
  createTime: [],
  billStatus: [],
  updateName: [],
  updateTime: [],
  projBizDmStgMDtoList: []
});
let option = ref({
  tip: false,
  dialogType: "dialog",
  border: true,
  index: true,
  stripe: true,
  menu: true,
  header: true,
  height: "auto",
  searchSpan: 6,
  searchIcon: true,
  searchMenuSpan: 6,
  searchIndex: 3,
  selection: true,
  showTree: false,
  excelBtn: false,
  delBtn: false,
  editBtn: false,
  delBtnsText: "批量删除",
  addBtnText: "新增",
  customEditBtnText: "编辑",
  customDelBtnText: "删除",
  customViewBtnText: "查看",
  column: [{
    label: "文件名",
    prop: "file_name",
    columnSlot: false,
    searchSlot: false
  }]
});
const {
  flow_status
} = useDicts([ "flow_status"]);
let listData = ref([]);
let queryForm = ref({
  page: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  filter: {
    createTime: null
  }
});


function getPageList() {
  //查询分页列表
  // 查询前处理参数
  const params = handleQueryForm();
  ProjBizResumeApplyMApi.pageList(params).then((res) => {
    listData.value = res.data.dataList;
    queryForm.value.page.total = res.data.totalCount;
  });
}

function onResetSearch() {
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
}

function handleQueryForm() {
  // 处理参数
  const {
    pageSize,
    pageNum
  } = queryForm.value.page;
  const filter = {}
  for (let key in queryForm.value.filter) {
    if (queryForm.value.filter[key]?.length) {
      // sn-curd搜索select会生成$prop的变量
      if (key.startsWith("$")) continue;
      filter[key] = queryForm.value.filter[key];
    }
  }
  delete filter.createTime;
  if (Array.isArray(queryForm.value.filter.createTime) && queryForm.value.filter.createTime?.length === 2) {
    filter["beginCreateTime"] = queryForm.value.filter.createTime[0];
    filter["endCreateTime"] = queryForm.value.filter.createTime[1];
  }
  const searchParams = {
    page: {
      pageSize,
      pageNum,
    },
    filter,
  }
  return searchParams
}

function onChangeSearch(params, done) {
  //搜索按钮操作
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
  done && done();
}
async function onEditData(row) {
  //编辑,新增按钮操作
  let editType = row ? "edit" : "add";
  let rowInfo = await (editType !== "add" ? ProjBizResumeApplyMApi.view(row.id) : {});
  const formData = editType !== "add" ? rowInfo.data : rowInfo;
  //编辑,新增按钮操作
  proxy.$DialogForm.show({
    title: editType == "edit" ? "编辑" : "新增",
    type: option.value.dialogType,
    width: "80%",
    content: EditProjBizResumeApplyM,
    data: {
      formData: formData,
      type: editType,
    },
    callback: (res) => {
      if (res.type && res.type !== 'close') {
        if (res.dialogRefs) {
          res.dialogRefs.submitData().then((flag) => {
            if (flag) {
              getPageList();
              res.close();
            }
          });
        }
      } else {
        res.close();
      }
    }
  });
}

function onDelData(rows) {
  //删除行
  if (!rows || rows.length == 0) {
    proxy.$message.info("请勾选数据！");
    return false;
  }
  let ids = rows.map((item) => {
    return item.id;
  });
  proxy.$modal.confirm("确认删除数据项？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    ProjBizResumeApplyMApi.remove(ids).then((res) => {
      proxy.$message.success("已删除");
      getPageList();
    });
  }).catch(() => {});
}
formData = toRef(Object.keys(props.data?.formData || {}).length > 0 ? {
  ...props.data?.formData
} : toRef(formData.value));

const getAttachmentList = () => {
  // 假设子组件有 getListData 方法
  return childRef.value?.getListData() || [];
};

function getFormData() {
  // 获取附件列表并赋值
  formData.value.projBizDmStgMDtoList = getAttachmentList();
  return formData.value
};

function processAuthFieldForm(fieldList) {
  /**
   * 工作流表单权限功能：在扫描完成之前，业务可对表单字段进行修改。
   */
  //TODO 这里可以对权限表单的字段进行任意的修改,如果不满足业务需求，可以在这里手动添加你想要的字段
  return fieldList
};

function getApprovalOption(fun) {
  /**
   * 工作流表单权限功能(重要)：应对弹窗无法及时相应的问题，特别增加该函数修复该bug
   */
  nextTick(() => {
    fun(approvalOption.value)
  })
};

function customFormPermi(obj) {
  /**
   * 工作流表单权限功能：自定义表单权限处理函数，将对应的对象的属性还原成对应的权限值
   */
  let {
    field,
    fieldModelId
  } = obj
  let fullFieldName = `${fieldModelId}_${field}`
  //TODO 这里可以自定义处理，默认返回0即可
  return auth.get(fullFieldName) || 0
};

function handlerPrint(taskComment) {
  /**
   * 工作流打印模板功能
   */
  ProjBizResumeApplyMApi.printTemplate({
    id: formData.value.id,
    templateId: taskComment.templateId,
    formData: formData.value
  }).then(
    (res) => {
      router.push({
        name: "PrintDoc",
        query: {
          fileId: res.data,
        },
      });
      taskComment.close && taskComment.close();
    });
};

function getSubmitTask(taskActionDto) {
  /**
   * 工作流提交任务功能
   */
  return ProjBizResumeApplyMApi.submitTask({
    busiDto: formData.value,
    taskActionDto: taskActionDto
  });
}

function getStartFlow(formData, startProcDto) {
  /**
   * 工作流启动流程功能
   */
  formRef.value.validate((flag) => {
    if (!flag) {
      return false;
    } 
  });
  return ProjBizResumeApplyMApi.startFlow({
    busiData: formData,
    startProcDto: startProcDto
  });
};

function handlerAction(operation, taskComment, handlerClose) {
  /**
   * 工作流操作功能
   */
  // 启动并保存草稿后再次提交
  if (
    (operation.type == FlowActionType.value.SAVESTART || operation.type == FlowActionType.value.SAVE || operation.type == FlowActionType.value.START) && !formData.value.taskId) {
    let startProcDto = {
      procDefKey: formData.value.procDefKey,
      businessKey: null,
      clientId: null,
      ...taskComment?.dialogRefs?.getFormData(),
    };
    let httpCall = null;
    if (operation.type == FlowActionType.value.SAVESTART) {
      httpCall = getStartFlow(formData, startProcDto);
    } else if (operation.type == FlowActionType.value.SAVE) {
      httpCall = getSaveFormData(formData);
    }
    httpCall.then(() => {
      proxy.$modal.msgSuccess("提交成功");
      taskComment.close && taskComment.close();
      handlerClose();
    });
  } else {
    operation.type == FlowActionType.value.START || operation.type == FlowActionType.value.SAVESTART ? (operation.type = FlowActionType.value.AGREE) : operation.type;
    let taskActionDto = {
      taskId: state.flowData.taskId,
      procInstId: state.flowData.procInstId,
      actionType: operation.type,
      ...taskComment?.dialogRefs?.getFormData(),
    };
    getSubmitTask(taskActionDto).then(() => {
      proxy.$modal.msgSuccess("任务办理成功");
      taskComment.close && taskComment.close();
      handlerClose();
    }).catch(() => {
      taskComment.close && taskComment.close();
    });
  }
};

function initBusiForm() {
  /**
   * 工作流初始化表单功能
   */
  let that = proxy
  let routerQueryParams = route.query;
  auth = new Map(Object.entries(state.formAuth));
  if (routerQueryParams) {
    if (routerQueryParams.busiData) {
      formData.value = routerQueryParams.busiData;
    } else {
      if (routerQueryParams.businessKey) {
        let businessKey = route.query.businessKey;
        ProjBizResumeApplyMApi.view(businessKey).then(resp => {
          formData.value = resp.data;
        });
      } else {
        that.$message.error("初始化失败,因为工作流未将流程信息传入！")
      }
    }
  }
};

function getSaveFormData(formData) {
  formRef.value.validate((flag) => {
    if (!flag) {
      return false;
    }
  });
  if (formData.id) {
    return ProjBizResumeApplyMApi.update(formData);
  } else {
    return  ProjBizResumeApplyMApi.add(formData).then((res) =>{
      formData.id = res.data.id;
    });
  }
};

function getFieldForm() {
  let rootFields = {
    ...state.rootFields
  }
  let fieldList = []
  let keys = []
  for (let key in rootFields) {
    keys.push(key)
    fieldList.push(rootFields[key])
  }
  const result = processAuthFieldForm ? processAuthFieldForm(fieldList) : fieldList;
  const message = {
    type: "getTemplateRoot",
    pathname: window.location.pathname,
    actKey: getQueryParams("actKey"),
    content: result
  };
  window.parent.postMessage(JSON.parse(JSON.stringify(message)), "*");
};

function getQueryParams(key) {
  let url = window.location.href;
  // 使用正则表达式解析URL中的查询字符串
  var queryString = url.split('?')[1];
  if (!queryString) {
    return {};
  }
  var params = {};
  // 分割查询字符串成单个参数
  var vars = queryString.split("&");
  for (var i = 0; i < vars.length; i++) {
    var pair = vars[i].split("=");
    params[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);
  }
  return params[key];
};

function initData(urlParams, taskInfo, handlerClose) {
  // 如果用户从流程设计点击启动, 如果是在线表单, 则阻止执行initData, 防止在线表单报错
  if (urlParams.menuId && urlParams.openInStart) {
    return
  }
  state.flowData.procDefKey = taskInfo.procDefKey;
  state.flowData.procInstId = urlParams.procInstId;
  state.flowData.businessKey = urlParams.businessKey;
  state.flowData.taskId = urlParams.taskId;
  state.flowData.fiedPermission = taskInfo.fiedPermission;
  state.flowData.variableList = taskInfo.variableList;
  state.taskFlag = urlParams.taskFlag;
  state.firstAct = taskInfo.firstAct;
  state.handlerClose = handlerClose;
  state.flowData.customPropertyConfigList = taskInfo.customPropertyConfigList
  let fieldPerList = taskInfo.formConfig?.formFieldConfig?.fieldPerList || []
  handleFormAuth(fieldPerList);
  initBusiForm();
};

function handleFormAuth(data) {
  let formAuth = {};
  for (let item of data) {
    let permi = 1;
    if (item.readonly) {
      permi = 2;
    }
    if (item.hidden) {
      permi = 3;
    }
    if (!isCamelCase(item.fieldModelId)) {
      item.fieldModelId = toCamelCase(item.fieldModelId)
    }
    formAuth = {
      ...formAuth,
      [`${item.fieldModelId}_${item.field}`]: permi,
    };
  }
  state.formAuth = formAuth;
  state.formAuthList = JSON.parse(JSON.stringify(formAuth))
};
/**
 * 处理表单权限相关操作, 开发者在template的v-permi设置中必须使用此函数
 * @param {Object} obj - 包含字段名称的对象
 * @param {Function} [callback] - 可选的回调函数，接收 obj 作为参数
 * 如果传入对象的 fieldName 不在 rootFields 中，则将其添加到 rootFields 数组，然后执行回调函数（如果存在）
 */
function formPermi(obj, callback) {
  if (!state.rootFields[obj.fieldModelId + '__' + obj.field]) {
    state.rootFields[obj.fieldModelId + '__' + obj.field] = obj
  }
  return callback && callback(obj)
};

function toCamelCase(s) {
  return s.toLowerCase().replace(/_(.)/g, function(match, group1) {
    return group1.toUpperCase();
  });
};

function isCamelCase(str) {
  return /^[a-z][a-zA-Z0-9]*$/.test(str)
};
defineExpose({
  getFormData,
  getSaveFormData,
  getStartFlow,
});
onMounted(() => {
  nextTick(() => {
    getFieldForm()
  })
})

// 文件序列号生成函数
function fileSerialNumberBuilder() {
  return "TEST" + Math.floor(Math.random()*10000)
}

const previewConfig = ref({
  // 内置预览返回的替换字符,akey代表要替换的字符串，avalue代码替换的字符串
  substitute: {
    "akey": "avalue"
  },
  // 内置预览服务地址
  previewServerUrl: setting.kkFileViewUrl,
})

</script>

<style lang="scss" scoped>

</style>