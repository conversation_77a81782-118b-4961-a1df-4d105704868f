<template>
  <sn-crud :data="listData" :option="option" v-model:page="queryForm.page" v-model:search="queryForm.filter" @on-load="getPageList" @search-change="onChangeSearch" @search-reset="onResetSearch" @addBtnHandle="onEditData" @row-del="onDelData">
    <template #menu="{ row, index, size }">

      <template v-if="row.docStatus === '0' || !row.docStatus">
        <el-button type="primary" :size="size" icon="el-icon-edit" link @click="onEditData(row)">编辑</el-button>
        <el-button type="danger" :size="size" icon="el-icon-delete" link @click="onDelData([row])">删除</el-button>
      </template>

      <template v-else>
        <el-button type="warning" :size="size" icon="el-icon-refresh-left" link @click="onRevoke(row)">撤回</el-button>
        <el-button type="primary" :size="size" icon="el-icon-download" link @click="onDownData(row)">下载</el-button>
      </template>
    </template>
    <template #docStatus="{row}">
      <span :style="{color : row.docStatus && row.docStatus==='1' ? 'green' : 'red'}">
          {{ row.docStatus && row.docStatus === '1' ? '已发布' : '草稿' }}
        </span>
    </template>

    <template #code="{ row }">
      <el-button type="text" @click="onDetailClick(row)">{{ row.code }}</el-button>
    </template>
    <template #header>
      <el-button type="primary" icon="el-icon-plus" style="margin-left: 10px;" @click="onEditData(row)">新增</el-button>
<!--      <el-button type="primary" icon="el-icon-document" @click="onListTemplate">列表模板</el-button>-->
<!--      <el-button type="primary" icon="el-icon-upload" @click="onListImport">列表导入</el-button>-->
      <el-button type="primary" icon="el-icon-download" @click="onExportData">列表导出</el-button>
    </template>
  </sn-crud>
  <div ref="myRef"></div>
</template>

<script>
import ProjBizSafefeeplanMApi from '@/project/api/safeFeeManage/safeFeePlan/ProjBizSafefeeplanM.js'
  import EditProjBizSafefeeplanM from "./components/EditProjBizSafefeeplanM.vue";
import dayjs from 'dayjs';
  import { getToken } from "sn-base-utils";
  export const routerConfig = [{
    menuType: "C",
    menuName: "安全费用计划申报管理",
  }, {
    menuType: "F",
    menuName: "查看",
    perms: "show",
    api: [ProjBizSafefeeplanMApi.config.pageList],
  }, {
    menuType: "F",
    menuName: "新增",
    perms: "add",
    api: [ProjBizSafefeeplanMApi.config.add],
  }, {
    menuType: "F",
    menuName: "修改",
    perms: "update",
    api: [ProjBizSafefeeplanMApi.config.update, ProjBizSafefeeplanMApi.config.view],
  }, {
    menuType: "F",
    menuName: "删除",
    perms: "del",
    api: [ProjBizSafefeeplanMApi.config.remove],
  }];
</script>

<script setup>
import {
  ref,
  getCurrentInstance
} from 'vue';
const {
  proxy
} = getCurrentInstance()
let option = ref({
  tip: false,
  dialogType: "page",
  border: true,
  index: true,
  stripe: true,
  menu: true,
  header: false,
  height: "auto",
  searchSpan: 6,
  searchIcon: true,
  searchMenuSpan: 6,
  searchIndex: 3,
  selection: true,
  showTree: false,
  excelBtn: false,
  delBtn: false,
  editBtn: false,
  delBtnsText: "批量删除",
  addBtnText: "新增",
  customEditBtnText: "编辑",
  customDelBtnText: "删除",
  customViewBtnText: "查看",
  column: [{
    label: "业务编号",
    prop: "code",
    search: true,
    columnSlot: true,
    overHidden: true
  }, {
    label: "版本号",
    prop: "planVersion",
    columnSlot: true
  }, {
    label: "计划金额",
    prop: "planAmount",
    search: true,
    columnSlot: true
  }, {
    label: "创建时间",
    prop: "createTime",
    columnSlot: true,
    formatter: (row) => dayjs(row.createTime).format('YYYY-MM-DD')
  }, {
    label: "创建人姓名",
    prop: "createName",
    columnSlot: true
  }, {
    label: "单据状态",
    prop: "docStatus",
    search: true,
    columnSlot: true,
    type: "select",
    dicData: [
      { label: "草稿", value: "0" },
      { label: "已发布", value: "1" }]
  }]
});
const myRef = ref(null);
let listData = ref([]);
let queryForm = ref({
  page: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  filter: {
    createTime: null
  }
});
let formRules = ref({});

function getPageList() {
  //查询分页列表
  // 查询前处理参数
  const params = handleQueryForm();
  ProjBizSafefeeplanMApi.pageList(params).then((res) => {
    listData.value = res.data.dataList;
    queryForm.value.page.total = res.data.totalCount;
  });
}

function onResetSearch() {
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
}
async function onDetailClick(row) {
  //编辑,新增按钮操作
  let editType = "show";
  let rowInfo = await (editType !== "add" ? ProjBizSafefeeplanMApi.view(row.id) : {});
  const formData = editType !== "add" ? rowInfo.data : rowInfo;
  // 根据文档状态动态添加编辑按钮
  let extendButtons = []
  // 首先检查是否需要添加“编辑”按钮
  if (row.docStatus === '0') {
    extendButtons.push({
      key: 'edit',
      text: '编辑',
      icon: 'el-icon-edit',
      buttonType: 'primary',
      click: () => {
        res.close();
        onEditData(row);
      },
    });
  }
// 然后添加“关闭”按钮
  extendButtons.push({
    key: 'close',
    text: '关闭',
    icon: 'el-icon-close',
    buttonType: '',
  });
  //编辑,新增按钮操作
  proxy.$DialogForm.show({
    title: editType === "show" ? "查看" : "编辑",
    type: option.value.dialogType,
    el: myRef.value,
    width: "80%",
    content: EditProjBizSafefeeplanM,
    data: {
      formData: formData,
      type: editType,
      id: row ? row.id : null,
    },
    option: {
      submitBtn: false,
      emptyBtn: false,
      emptyText: '取消',
      extendButton: extendButtons
    },
    callback: (res) => {
      if (res.type === 'edit'){
        // 当文档状态为草稿时， 显示编辑按钮
        onEditData(row);
        res.close();
      }
      if (res.type && res.type !== 'close') {
        if (res.dialogRefs) {
          res.dialogRefs.submitData(res.type).then((flag) => {
            if (flag) {
              getPageList();
              res.close();
            }
          });
        }
      } else {
        res.close();
      }
    }
  });

  // 动态添加编辑按钮
  if (row.docStatus === '0') {
    proxy.$DialogForm.extendButton.push({
      key: 'edit',
      text: '编辑',
      icon: 'el-icon-edit',
      buttonType: 'primary',
      click: () => {
        onEditData(row);
      },
    });
  }

}
function onDownData(row) {
  proxy.download("/project/safeFeePlan/exportZip", [row.id],  "安全费用计划"+".zip");
}
function onRevoke(row) {
  proxy.$modal.confirm("确认撤回该条数据项？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    row.docStatus = '0'
    ProjBizSafefeeplanMApi.update(row).then((res) => {
      proxy.$message.success("已撤回");
      getPageList();
    });
  }).catch(() => {
  });
}
function handleQueryForm() {
  // 处理参数
  const {
    pageSize,
    pageNum
  } = queryForm.value.page;
  const filter = {}
  filter.projectId = sessionStorage.getItem('projectId')
  for (let key in queryForm.value.filter) {
    if (queryForm.value.filter[key]?.length) {
      // sn-curd搜索select会生成$prop的变量
      if (key.startsWith("$")) continue;
      filter[key] = queryForm.value.filter[key];
    }
  }
  delete filter.createTime;
  if (Array.isArray(queryForm.value.filter.createTime) && queryForm.value.filter.createTime?.length === 2) {
    filter["beginCreateTime"] = queryForm.value.filter.createTime[0];
    filter["endCreateTime"] = queryForm.value.filter.createTime[1];
  }
  const searchParams = {
    page: {
      pageSize,
      pageNum,
    },
    filter,
  }
  return searchParams
}

function onChangeSearch(params, done) {
  //搜索按钮操作
  queryForm.value.page = {
    total: 0,
    pageNum: 1,
    pageSize: 20,
  }
  getPageList();
  done && done();
}
async function onEditData(row) {
  //编辑,新增按钮操作
  let editType = row ? "edit" : "add";
  let rowInfo = await (editType !== "add" ? ProjBizSafefeeplanMApi.view(row.id) : {});
  const formData = editType !== "add" ? rowInfo.data : rowInfo;
  //编辑,新增按钮操作
  proxy.$DialogForm.show({
    title: editType == "edit" ? "安全费用计划详情页" : "安全费用计划详情页",
    type: option.value.dialogType,
    el: myRef.value,
    width: "80%",
    content: EditProjBizSafefeeplanM,
    data: {
      formData: formData,
      type: editType,
      id:row ? row.id:null,
    },
    option: {
      submitBtn: false,
      emptyBtn: false,
      emptyText: '取消',
      extendButton: [
        {
          key: 'save',
          text: '保存',
          icon: 'el-icon-plus',
          buttonType: 'primary',
        },
        {
          key: 'submit',
          text: '发布',
          icon: 'el-icon-check',
          buttonType: 'primary',
        },
        {
          key: 'close',
          text: '关闭',
          icon: 'el-icon-close',
          buttonType: '',
        },
      ],
    },
    callback: (res) => {
      if (res.type === 'save') {
        if (res.dialogRefs) {
          res.dialogRefs.submitData(res.type).then((flag) => {
            if (flag) {
              getPageList();
            }
          });
        }
      } else if (res.type === 'close' ) {
        // 当点击关闭按钮且处于编辑模式时，弹出确认对话框
        proxy.$confirm('确认关闭？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          res.close();
        }).catch(() => {
          // 用户点击取消，不关闭弹窗
        });
      }else if(res.type === 'submit'){
        if (res.dialogRefs) {
          res.dialogRefs.submitData(res.type).then((flag) => {
            if (flag) {
              getPageList();
              res.close();
            }
          });
        }
      }else {
        res.close();
      }
    }
  });
}

function onExportData() {
  const params = handleQueryForm();
  let queryForm = JSON.parse(JSON.stringify(params));
  debugger
  proxy.download("/project/safeFeePlan/export", queryForm.filter, '安全费用计划申报.xlsx');
}
function onDelData(rows) {
  //删除行
  if (!rows || rows.length == 0) {
    proxy.$message.info("请勾选数据！");
    return false;
  }
  let ids = rows.map((item) => {
    return item.id;
  });
  proxy.$modal.confirm("确认删除数据项？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    ProjBizSafefeeplanMApi.remove(ids).then((res) => {
      proxy.$message.success("已删除");
      getPageList();
    });
  }).catch(() => {});
}
</script>

<style lang="scss" scoped>

</style>

